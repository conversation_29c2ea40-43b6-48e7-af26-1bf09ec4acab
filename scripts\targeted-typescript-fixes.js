#!/usr/bin/env node

/**
 * Targeted TypeScript Fixes Script
 * Conservative approach to fix critical TypeScript issues
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 TARGETED TYPESCRIPT FIXES');
console.log('============================');
console.log('🔧 Conservative approach to fix critical issues\n');

// Conservative patterns that are safe to apply
const SAFE_PATTERNS = {
    // Fix obvious syntax errors
    '= >': ' =>',
    '= > ': ' => ',
    '() = >': '() =>',
    ') = >': ') =>',
    
    // Fix malformed arrow functions with extra spaces
    '= >  >': ' =>',
    ') = >  >': ') =>',
    '() = >  >': '() =>',
    
    // Fix missing spaces in logical operators
    '||': ' || ',
    '&&': ' && ',
    
    // Fix missing spaces around assignment operators
    '=any': ' = any',
    '=unknown': ' = unknown',
    '=null': ' = null',
    '=undefined': ' = undefined',
    
    // Fix type annotation spacing
    ':any': ': any',
    ':unknown': ': unknown',
    ':string': ': string',
    ':number': ': number',
    ':boolean': ': boolean',
    
    // Fix function call spacing
    'if(': 'if (',
    'for(': 'for (',
    'while(': 'while (',
    'switch(': 'switch (',
    'catch(': 'catch (',
    
    // Fix object syntax issues (conservative)
    '{,': '{',
    ',}': '}',
    
    // Fix array syntax issues (conservative)
    '[,': '[',
    ',]': ']'
};

// Files to prioritize (most critical)
const PRIORITY_FILES = [
    'src/index.ts',
    'src/config/index.ts',
    'src/lib/logger.ts',
    'src/lib/prisma.ts',
    'src/lib/database-init.ts',
    'src/types/index.ts',
    'src/types/common.ts'
];

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        try {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage', 'backups'].includes(item)) {
                    scanDirectory(fullPath);
                } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            console.warn(`⚠️  Warning: Could not scan directory ${currentDir}: ${error.message}`);
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorOutput = error.stdout || error.stderr || '';
        const errorMatches = errorOutput.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply safe patterns only
        for (const [oldPattern, newPattern] of Object.entries(SAFE_PATTERNS)) {
            const beforeLength = modifiedContent.length;
            modifiedContent = modifiedContent.split(oldPattern).join(newPattern);
            if (modifiedContent.length !== beforeLength) {
                fixCount++;
            }
        }
        
        // Additional conservative regex fixes
        const originalContent = modifiedContent;
        
        // Fix malformed JSON.stringify calls
        modifiedContent = modifiedContent.replace(/JSON\.stringify\(([^,]+),\s*null,\s*2\s*\)/g, 'JSON.stringify($1, null, 2)');
        
        // Fix malformed Object.keys calls
        modifiedContent = modifiedContent.replace(/Object\.keys([^(])/g, 'Object.keys($1');
        
        if (modifiedContent !== originalContent) {
            fixCount++;
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount, success: true };
        }
        
        return { filePath, fixCount: 0, success: true };
    } catch (error) {
        return { filePath, error: error.message, success: false };
    }
}

function generateReport(results, initialErrors, finalErrors, duration) {
    const report = {
        summary: {
            startTime: new Date().toISOString(),
            duration: `${duration}ms`,
            initialErrors,
            finalErrors,
            errorsFixed: initialErrors - finalErrors,
            successRate: initialErrors > 0 ? ((initialErrors - finalErrors) / initialErrors * 100).toFixed(1) : 0,
            filesProcessed: results.length,
            filesWithFixes: results.filter(r => r.fixCount > 0).length,
            totalFixes: results.reduce((sum, r) => sum + (r.fixCount || 0), 0)
        },
        files: results.filter(r => r.fixCount > 0).map(r => ({
            file: path.relative(process.cwd(), r.filePath),
            fixes: r.fixCount
        })),
        errors: results.filter(r => r.error).map(r => ({
            file: path.relative(process.cwd(), r.filePath),
            error: r.error
        }))
    };
    
    fs.writeFileSync('TARGETED_TYPESCRIPT_FIXES_REPORT.json', JSON.stringify(report, null, 2));
    return report;
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    const allFiles = findAllTypeScriptFiles('./src');
    
    // Prioritize critical files
    const priorityFiles = PRIORITY_FILES.filter(file => fs.existsSync(file));
    const otherFiles = allFiles.filter(file => !PRIORITY_FILES.includes(file));
    const files = [...priorityFiles, ...otherFiles];
    
    console.log(`📁 Found ${files.length} TypeScript files (${priorityFiles.length} priority)`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    if (initialErrors === 0) {
        console.log('🎉 No TypeScript errors found! Project is already clean.');
        return;
    }
    
    console.log('🚀 Starting targeted TypeScript fixes...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    // Process priority files first
    console.log('\n🎯 Processing priority files...');
    for (const file of priorityFiles) {
        const result = processFile(file);
        results.push(result);
        if (result.fixCount) {
            totalFixedIssues += result.fixCount;
            console.log(`✅ ${path.relative(process.cwd(), file)}: ${result.fixCount} fixes`);
        }
    }
    
    // Check progress after priority files
    const midErrors = getErrorCount();
    console.log(`📊 Errors after priority files: ${midErrors} (${initialErrors - midErrors} fixed)`);
    
    // Process remaining files
    console.log('\n🔧 Processing remaining files...');
    let processedCount = 0;
    for (const file of otherFiles) {
        const result = processFile(file);
        results.push(result);
        if (result.fixCount) {
            totalFixedIssues += result.fixCount;
            console.log(`✅ ${path.relative(process.cwd(), file)}: ${result.fixCount} fixes`);
        }
        
        processedCount++;
        if (processedCount % 50 === 0) {
            console.log(`📈 Processed ${processedCount}/${otherFiles.length} files...`);
        }
    }
    
    const duration = Date.now() - startTime;
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    
    const report = generateReport(results, initialErrors, finalErrors, duration);
    
    console.log('\n🎯 TARGETED TYPESCRIPT FIXES RESULTS:');
    console.log('====================================');
    console.log(`⏱️  Duration: ${duration}ms`);
    console.log(`📁 Files processed: ${results.length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 Initial errors: ${initialErrors}`);
    console.log(`✅ Final errors: ${finalErrors}`);
    console.log(`📈 Errors fixed: ${initialErrors - finalErrors}`);
    console.log(`🎯 Success rate: ${report.summary.successRate}%`);
    
    if (finalErrors < initialErrors) {
        console.log('\n🎉 SUCCESS! TypeScript errors reduced');
        if (finalErrors === 0) {
            console.log('✅ Zero compilation errors achieved!');
            console.log('🚀 Project is now fully modernized and production-ready');
        } else {
            console.log(`⚠️  ${finalErrors} errors remaining - continue with manual review`);
        }
    } else {
        console.log('\n⚠️  No improvement detected - may need manual intervention');
    }
    
    console.log('\n📋 Report saved to: TARGETED_TYPESCRIPT_FIXES_REPORT.json');
}

main().catch(console.error);
