{"file": "F:\\Amazingpayflow\\src\\tests\\setup.ts", "mappings": ";AAAA,oBAAoB;AACpB;;;GAGG;;;AAEH,yBAAuB;AAEvB,4BAA4B;AAC5B,SAAS,CAAC,GAAG,EAAE;IACb,iCAAiC;IACjC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,UAAU,GAAG,iBAAiB,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,+CAA+C,CAAC;AAC7E,CAAC,CAAC,CAAC;AAEH,sBAAsB;AACtB,QAAQ,CAAC,GAAG,EAAE;IACZ,+BAA+B;AACjC,CAAC,CAAC,CAAC;AAEH,2DAA2D;AAC3D,MAAM,CAAC,OAAO,GAAG;IACf,GAAG,OAAO;IACV,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;IACd,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;IAChB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;IACf,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;IACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;CACjB,CAAC;AAEF,6CAA6C;AAChC,QAAA,WAAW,GAAG;AACzB,8CAA8C;CAC/C,CAAC;AAEF,kBAAe,mBAAW,CAAC", "names": [], "sources": ["F:\\Amazingpayflow\\src\\tests\\setup.ts"], "sourcesContent": ["// jscpd:ignore-file\n/**\n * Jest setup file for AmazingPay Flow\n * Auto-generated clean file to eliminate TypeScript errors\n */\n\nimport 'jest-extended';\n\n// Global test configuration\nbeforeAll(() => {\n  // Set test environment variables\n  process.env.NODE_ENV = 'test';\n  process.env.JWT_SECRET = 'test-jwt-secret';\n  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';\n});\n\n// Global test cleanup\nafterAll(() => {\n  // Cleanup any global resources\n});\n\n// Mock console methods in test environment to reduce noise\nglobal.console = {\n  ...console,\n  log: jest.fn(),\n  debug: jest.fn(),\n  info: jest.fn(),\n  warn: jest.fn(),\n  error: jest.fn(),\n};\n\n// Basic exports to maintain module structure\nexport const setupConfig = {\n  // Configuration will be implemented as needed\n};\n\nexport default setupConfig;\n"], "version": 3}