/**
 * Identity Verification Controller
 *
 * Modular controller for identity verification operations.
 */

import { Response } from 'express';
import { BaseController } from '../base.controller';
import { asyncHandler } from '../../utils/asyncHandler';
import { IdentityVerificationService as ImportedIdentityVerificationService } from '../../services/identity-verification';
import prisma from '../../lib/prisma';

import { IdentityVerificationAuthService as ImportedIdentityVerificationAuthService } from './services/IdentityVerificationAuthService';
import { IdentityVerificationValidationService as ImportedIdentityVerificationValidationService } from './services/IdentityVerificationValidationService';
import { IdentityVerificationResponseMapper as ImportedIdentityVerificationResponseMapper } from './mappers/IdentityVerificationResponseMapper';

import { AuthenticatedRequest as ImportedAuthenticatedRequest } from './types/IdentityVerificationControllerTypes';

/**
 * Modular Identity Verification Controller
 */
export class IdentityVerificationController extends BaseController {
  private readonly authService: IdentityVerificationAuthService;
  private readonly validationService: IdentityVerificationValidationService;
  private readonly identityService: IdentityVerificationService;

  constructor() {
    super();
    this.authService = new IdentityVerificationAuthService();
    this.validationService = new IdentityVerificationValidationService();
    this.identityService = new IdentityVerificationService(prisma);
  }

  /**
   * Verify identity using Ethereum signature
   */
  verifyEthereumSignature = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateEthereumSignature(req.body);
      this.authService.extractUserContext(req);

      // Business logic
      const result = await this.identityService.verifyEthereumSignature({
        address: (validatedData).address,
        message: (validatedData).message,
        signature: (validatedData).signature,
        userId,
        merchantId,
      });

      // Response
      (IdentityVerificationResponseMapper).sendVerificationResult(res, result);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using ERC-1484
   */
  verifyERC1484Identity = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateERC1484Identity(req.body);
      this.authService.extractUserContext(req);

      // Business logic - Placeholder for ERC1484 verification
      const result = {
        success: false,
        message: 'ERC1484 verification not yet implemented',
        verificationId: 'placeholder',
        method: 'ERC1484',
      };

      // Response
      (IdentityVerificationResponseMapper).sendVerificationResult(res, result);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using ERC-725
   */
  verifyERC725Identity = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateERC725Identity(req.body);
      this.authService.extractUserContext(req);

      // Business logic - Placeholder for ERC725 verification
      const result = {
        success: false,
        message: 'ERC725 verification not yet implemented',
        verificationId: 'placeholder',
        method: 'ERC725',
      };

      // Response
      (IdentityVerificationResponseMapper).sendVerificationResult(res, result);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using ENS
   */
  verifyENS = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateENSVerification(req.body);
      this.authService.extractUserContext(req);

      // Business logic - Placeholder for ENS verification
      const result = {
        success: false,
        message: 'ENS verification not yet implemented',
        verificationId: 'placeholder',
        method: 'ENS',
      };

      // Response
      (IdentityVerificationResponseMapper).sendVerificationResult(res, result);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using Polygon ID
   */
  verifyPolygonID = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validatePolygonID(req.body);
      this.authService.extractUserContext(req);

      // Business logic - Placeholder for Polygon ID verification
      const result = {
        success: false,
        message: 'Polygon ID verification not yet implemented',
        verificationId: 'placeholder',
        method: 'PolygonID',
      };

      // Response
      (IdentityVerificationResponseMapper).sendVerificationResult(res, result);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using Worldcoin
   */
  verifyWorldcoin = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateWorldcoin(req.body);
      this.authService.extractUserContext(req);

      // Business logic - Placeholder for Worldcoin verification
      const result = {
        success: false,
        message: 'Worldcoin verification not yet implemented',
        verificationId: 'placeholder',
        method: 'Worldcoin',
      };

      // Response
      (IdentityVerificationResponseMapper).sendVerificationResult(res, result);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Verify identity using Unstoppable Domains
   */
  verifyUnstoppableDomains = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateUnstoppableDomains(req.body);
      this.authService.extractUserContext(req);

      // Business logic - Placeholder for Unstoppable Domains verification
      const result = {
        success: false,
        message: 'Unstoppable Domains verification not yet implemented',
        verificationId: 'placeholder',
        method: 'UnstoppableDomains',
      };

      // Response
      (IdentityVerificationResponseMapper).sendVerificationResult(res, result);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Get verification by ID
   */
  getVerificationById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'read',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const verificationId = this.validationService.validateId(req.params.id, 'Verification ID');

      // Business logic
      const verification = await this.identityService.getVerificationById(verificationId);

      // Response
      const transformedVerification =
        (IdentityVerificationResponseMapper).transformVerification(verification);
      (IdentityVerificationResponseMapper).sendVerification(res, transformedVerification);
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Get verifications for user
   */
  getVerificationsForUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Get user ID
      const { userId } = this.authService.extractUserContext(req);
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Business logic
      const verifications = await this.identityService.getVerificationsForUser(userId);

      // Response
      const transformedVerifications = (verifications).map(
        (IdentityVerificationResponseMapper).transformVerification
      );
      (IdentityVerificationResponseMapper).sendVerificationsList(
        res,
        transformedVerifications,
        (transformedVerifications).length
      );
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });

  /**
   * Health check endpoint
   */
  healthCheck = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      (IdentityVerificationResponseMapper).sendSuccess(
        res,
        {
          status: 'healthy',
          timestamp: new Date(),
          version: '1.0.0',
          services: {
            authorization: 'active',
            validation: 'active',
            identityService: 'active',
          },
        },
        'Identity Verification Controller is healthy'
      );
    } catch (error) {
      (IdentityVerificationResponseMapper).sendError(res, error as Error);
    }
  });
}
