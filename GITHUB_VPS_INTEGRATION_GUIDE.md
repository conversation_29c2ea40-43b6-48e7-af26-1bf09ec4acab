# 🔗 GitHub-to-VPS Integration Guide

## 📋 **OVERVIEW**

This guide explains how to set up a complete CI/CD pipeline between GitHub and your VPS (************) with automated deployment and rollback capabilities.

## 🚀 **DEPLOYMENT WORKFLOW**

```mermaid
graph LR
    A[Push to GitHub] --> B[GitHub Actions]
    B --> C[Tests & Build]
    C --> D[Webhook Trigger]
    D --> E[VPS Deployment]
    E --> F[Health Check]
    F --> G[Success/Rollback]
```

### **Automatic Deployment Process**
1. **Developer pushes** code to `main` branch
2. **GitHub Actions** runs tests, security scans, and builds
3. **Webhook triggers** VPS deployment automatically
4. **VPS creates backup** before deployment
5. **Application updates** with zero-downtime
6. **Health checks** verify deployment success
7. **Automatic rollback** if deployment fails

## 🔧 **SETUP INSTRUCTIONS**

### **Step 1: VPS Setup**

```bash
# SSH to your VPS
ssh root@************

# Navigate to application directory
cd /www/wwwroot/amazingpayme.com

# Run GitHub integration setup
chmod +x scripts/setup-github-integration.sh
./scripts/setup-github-integration.sh
```

### **Step 2: GitHub Repository Configuration**

#### **2.1 Add Webhook**
1. Go to your GitHub repository
2. Navigate to **Settings** → **Webhooks**
3. Click **Add webhook**
4. Configure:
   - **Payload URL**: `https://amazingpayme.com/webhook/deploy`
   - **Content type**: `application/json`
   - **Secret**: Use the generated webhook secret
   - **Events**: Select "Push" and "Workflow runs"

#### **2.2 Configure GitHub Secrets**
Go to **Settings** → **Secrets and variables** → **Actions** and add:

```
VPS_HOST=************
VPS_USERNAME=root
VPS_SSH_KEY=[Your SSH private key]
WEBHOOK_SECRET=[Generated webhook secret]
```

### **Step 3: Test Deployment**

```bash
# Make a small change and push to main
git add .
git commit -m "Test automated deployment"
git push origin main

# Monitor deployment
curl https://amazingpayme.com/webhook/status
```

## 📊 **MONITORING & STATUS**

### **Status Endpoints**

| Endpoint | Purpose | Example |
|----------|---------|---------|
| `/api/health` | Application health | `curl https://amazingpayme.com/api/health` |
| `/webhook/status` | Deployment status | `curl https://amazingpayme.com/webhook/status` |
| `/api/deployment/status` | Detailed app status | `curl https://amazingpayme.com/api/deployment/status` |

### **Log Monitoring**

```bash
# Webhook handler logs
journalctl -u amazingpay-webhook -f

# Deployment logs
tail -f /var/log/amazingpay/deployment.log

# Application logs
pm2 logs amazingpay-main

# Backup logs
tail -f /var/log/amazingpay/backup.log
```

## 🔄 **ROLLBACK CAPABILITIES**

### **Automatic Rollback**
- Triggered automatically on deployment failure
- Restores previous application version
- Restores database to pre-deployment state
- Restarts application with previous configuration

### **Manual Rollback**

```bash
# List available backups
./scripts/create-backup.sh --list

# Restore specific backup
./scripts/create-backup.sh --restore backup-20241201-143022

# Quick rollback to previous deployment
./scripts/create-backup.sh --restore $(ls -t /var/backups/amazingpay/deploy-* | head -1 | basename)
```

### **Emergency Rollback**

```bash
# If webhook is not working, manual deployment
cd /www/wwwroot/amazingpayme.com
git checkout HEAD~1  # Go back one commit
./scripts/vps-update.sh main
```

## 🔐 **SECURITY FEATURES**

### **Webhook Security**
- **Signature verification** using HMAC-SHA256
- **Secret-based authentication** 
- **IP filtering** (optional)
- **Rate limiting** on webhook endpoints

### **Deployment Security**
- **Backup before deployment** (automatic)
- **Database transaction safety**
- **File permission preservation**
- **Environment variable protection**

### **Access Control**
- **Systemd service isolation**
- **Limited file system access**
- **Non-root execution** (www-data user)
- **Resource limits** (CPU, memory, file descriptors)

## 🎯 **DEPLOYMENT STRATEGIES**

### **Branch-Based Deployment**

| Branch | Environment | Auto-Deploy | Approval Required |
|--------|-------------|-------------|-------------------|
| `main` | Production | ✅ Yes | ❌ No |
| `staging` | Staging | ✅ Yes | ❌ No |
| `develop` | Development | ❌ No | ❌ No |

### **Manual Deployment Triggers**

```bash
# Deploy specific branch
curl -X POST https://amazingpayme.com/deploy/main

# Deploy with custom parameters
curl -X POST https://amazingpayme.com/webhook/deploy \
  -H "Content-Type: application/json" \
  -d '{"ref": "refs/heads/main", "force": true}'
```

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

#### **1. Webhook Not Triggered**
```bash
# Check webhook service
systemctl status amazingpay-webhook

# Check GitHub webhook deliveries
# Go to GitHub → Settings → Webhooks → Recent Deliveries

# Test webhook manually
curl -X POST https://amazingpayme.com/webhook/deploy \
  -H "X-GitHub-Event: push" \
  -H "Content-Type: application/json" \
  -d '{"ref": "refs/heads/main"}'
```

#### **2. Deployment Fails**
```bash
# Check deployment logs
tail -f /var/log/amazingpay/deployment.log

# Check application status
pm2 status
pm2 logs amazingpay-main

# Manual deployment
cd /www/wwwroot/amazingpayme.com
./scripts/vps-update.sh main
```

#### **3. Database Issues**
```bash
# Check database connection
sudo -u postgres psql -d Amazingpay -c "SELECT 1;"

# Check migration status
cd /www/wwwroot/amazingpayme.com
npx prisma migrate status

# Reset database (CAUTION: Data loss)
npx prisma migrate reset
```

#### **4. Permission Issues**
```bash
# Fix file permissions
chown -R www-data:www-data /www/wwwroot/amazingpayme.com
chmod +x /www/wwwroot/amazingpayme.com/scripts/*.sh

# Fix log permissions
chown -R www-data:www-data /var/log/amazingpay
```

### **Emergency Procedures**

#### **Complete System Recovery**
```bash
# 1. Stop all services
systemctl stop amazingpay-webhook
pm2 stop all

# 2. Restore from backup
./scripts/create-backup.sh --restore [latest-backup]

# 3. Restart services
systemctl start amazingpay-webhook
pm2 start ecosystem.config.js

# 4. Verify health
curl https://amazingpayme.com/api/health
```

## 📈 **PERFORMANCE OPTIMIZATION**

### **Deployment Speed**
- **Incremental builds** using npm ci
- **Parallel processing** with PM2 clustering
- **Database migration optimization**
- **Asset caching** and compression

### **Resource Management**
- **Memory limits** for deployment processes
- **CPU throttling** during builds
- **Disk space monitoring**
- **Network bandwidth optimization**

## 🔄 **MAINTENANCE**

### **Regular Tasks**
```bash
# Weekly backup cleanup
./scripts/create-backup.sh --cleanup

# Monthly log rotation
logrotate /etc/logrotate.d/amazingpay

# Quarterly security updates
apt update && apt upgrade -y
npm audit fix
```

### **Health Monitoring**
```bash
# Setup monitoring cron job
echo "*/5 * * * * curl -f https://amazingpayme.com/api/health || echo 'Health check failed'" | crontab -

# Monitor disk space
echo "0 */6 * * * df -h | grep -E '(8[5-9]|9[0-9])%' && echo 'Disk space warning'" | crontab -
```

## 🎉 **SUCCESS VERIFICATION**

After setup, verify everything works:

1. **✅ Push test commit** to main branch
2. **✅ Check GitHub Actions** completes successfully  
3. **✅ Verify webhook** receives and processes request
4. **✅ Confirm deployment** updates application
5. **✅ Test rollback** functionality
6. **✅ Monitor logs** for any issues

## 📞 **SUPPORT**

If you encounter issues:

1. **Check logs** first (webhook, deployment, application)
2. **Verify configuration** (secrets, permissions, services)
3. **Test manually** (deployment scripts, health checks)
4. **Review documentation** for troubleshooting steps

---

**🚀 Your GitHub-to-VPS CI/CD pipeline is now ready for seamless deployments with automatic rollback capabilities!**
