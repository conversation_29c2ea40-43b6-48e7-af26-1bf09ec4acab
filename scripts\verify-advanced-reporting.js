#!/usr/bin/env node

/**
 * Advanced Reporting Verification Script
 *
 * This script verifies that all advanced reporting components are working correctly.
 * It performs comprehensive checks on:
 * - Database models and relationships
 * - API endpoints
 * - Service functionality
 * - File system permissions
 * - Health monitoring
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

const prisma = new PrismaClient();

// Configuration
const BASE_URL = process.env.BASE_URL ?? 'http://localhost:3002';
const TEST_TOKEN = process.env.TEST_TOKEN ?? 'test-token';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Utility functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Verification functions
async function verifyDatabaseModels() {
  info('Verifying database models...');

  try {
    // Check if all required tables exist
    const tables = [
      'ReportTemplate',
      'ScheduledReport',
      'SavedReport',
      'ReportRun',
      'Dashboard',
      'DashboardWidget',
    ];

    for (const table of tables) {
      try {
        await prisma[table.charAt(0).toLowerCase() + table.slice(1)].findFirst();
        success(`Table ${table} exists and is accessible`);
      } catch (err) {
        error(`Table ${table} is not accessible: ${err.message}`);
        return false;
      }
    }

    // Check relationships
    try {
      await prisma.savedReport.findFirst({
        include: {
          template: true,
        },
      });
      success('SavedReport -> ReportTemplate relationship works');
    } catch (err) {
      warning('SavedReport -> ReportTemplate relationship issue (may be empty)');
    }

    try {
      await prisma.dashboard.findFirst({
        include: {
          widgets: true,
        },
      });
      success('Dashboard -> DashboardWidget relationship works');
    } catch (err) {
      warning('Dashboard -> DashboardWidget relationship issue (may be empty)');
    }

    return true;
  } catch (err) {
    error(`Database verification failed: ${err.message}`);
    return false;
  }
}

async function verifyFileSystem() {
  info('Verifying file system setup...');

  try {
    const reportsDir = path.join(__dirname, '../reports');

    // Check if reports directory exists
    if (!fs.existsSync(reportsDir)) {
      warning('Reports directory does not exist, creating...');
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    success('Reports directory exists');

    // Check write permissions
    const testFile = path.join(reportsDir, 'test-write.tmp');
    try {
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      success('Reports directory is writable');
    } catch (err) {
      error(`Reports directory is not writable: ${err.message}`);
      return false;
    }

    // Check public directory for dashboard
    const publicDir = path.join(__dirname, '../src/public/reports');
    if (fs.existsSync(publicDir)) {
      success('Public reports directory exists');
    } else {
      warning('Public reports directory does not exist');
    }

    return true;
  } catch (err) {
    error(`File system verification failed: ${err.message}`);
    return false;
  }
}

async function verifyApiEndpoints() {
  info('Verifying API endpoints...');

  const endpoints = [
    { method: 'GET', path: '/health', auth: false },
    { method: 'GET', path: '/api/health/reports', auth: true },
    { method: 'GET', path: '/api/advanced-reports/templates', auth: true },
    { method: 'GET', path: '/api/advanced-reports/saved', auth: true },
    { method: 'GET', path: '/api/advanced-reports/scheduled', auth: true },
    { method: 'GET', path: '/api/dashboards', auth: true },
  ];

  let successCount = 0;

  for (const endpoint of endpoints) {
    try {
      const config = {
        method: endpoint.method,
        url: `${BASE_URL}${endpoint.path}`,
        timeout: 5000,
      };

      if (endpoint.auth) {
        config.headers = {
          Authorization: `Bearer ${TEST_TOKEN}`,
        };
      }

      const response = await axios(config);

      if (response.status === 200 || response.status === 401) {
        success(`${endpoint.method} ${endpoint.path} - Endpoint accessible`);
        successCount++;
      } else {
        warning(`${endpoint.method} ${endpoint.path} - Unexpected status: ${response.status}`);
      }
    } catch (err) {
      if (err.response && err.response.status === 401 && endpoint.auth) {
        success(`${endpoint.method} ${endpoint.path} - Endpoint accessible (auth required)`);
        successCount++;
      } else if (err.code === 'ECONNREFUSED') {
        error(`${endpoint.method} ${endpoint.path} - Server not running`);
      } else {
        error(`${endpoint.method} ${endpoint.path} - ${err.message}`);
      }
    }
  }

  return successCount === endpoints.length;
}

async function verifyServices() {
  info('Verifying service functionality...');

  try {
    // Test AdvancedReportService (try both compiled and source)
    let AdvancedReportService;
    try {
      ({ AdvancedReportService } = require('../dist/src/services/advanced-report.service'));
    } catch (err) {
      // Fallback to TypeScript source (requires ts-node)
      require('ts-node/register');
      ({ AdvancedReportService } = require('../src/services/advanced-report.service'));
    }
    const reportService = new AdvancedReportService();

    if (typeof reportService.generateReport === 'function') {
      success('AdvancedReportService.generateReport method exists');
    } else {
      error('AdvancedReportService.generateReport method missing');
      return false;
    }

    if (typeof reportService.createReportTemplate === 'function') {
      success('AdvancedReportService.createReportTemplate method exists');
    } else {
      error('AdvancedReportService.createReportTemplate method missing');
      return false;
    }

    if (typeof reportService.initializeScheduledReports === 'function') {
      success('AdvancedReportService.initializeScheduledReports method exists');
    } else {
      error('AdvancedReportService.initializeScheduledReports method missing');
      return false;
    }

    // Test ReportOptimizationService
    let ReportOptimizationService;
    try {
      ({ ReportOptimizationService } = require('../dist/src/services/report-optimization.service'));
    } catch (err) {
      require('ts-node/register');
      ({ ReportOptimizationService } = require('../src/services/report-optimization.service'));
    }
    const optimizationService = new ReportOptimizationService();

    if (typeof optimizationService.estimateReportSize === 'function') {
      success('ReportOptimizationService.estimateReportSize method exists');
    } else {
      error('ReportOptimizationService.estimateReportSize method missing');
      return false;
    }

    // Test ReportMonitoringService
    let ReportMonitoringService;
    try {
      ({ ReportMonitoringService } = require('../dist/src/services/report-monitoring.service'));
    } catch (err) {
      require('ts-node/register');
      ({ ReportMonitoringService } = require('../src/services/report-monitoring.service'));
    }
    const monitoringService = new ReportMonitoringService();

    if (typeof monitoringService.getSystemHealth === 'function') {
      success('ReportMonitoringService.getSystemHealth method exists');
    } else {
      error('ReportMonitoringService.getSystemHealth method missing');
      return false;
    }

    return true;
  } catch (err) {
    error(`Service verification failed: ${err.message}`);
    return false;
  }
}

async function verifyControllers() {
  info('Verifying controller functionality...');

  try {
    // Test AdvancedReportController (try both compiled and source)
    let AdvancedReportController;
    try {
      ({
        AdvancedReportController,
      } = require('../dist/src/controllers/advanced-report.controller'));
    } catch (err) {
      // Fallback to TypeScript source (requires ts-node)
      require('ts-node/register');
      ({ AdvancedReportController } = require('../src/controllers/advanced-report.controller'));
    }
    const reportController = new AdvancedReportController();

    const requiredMethods = [
      'generateReport',
      'getReportTemplates',
      'createReportTemplate',
      'getSavedReports',
      'getSavedReportById',
      'downloadSavedReport',
      'getScheduledReports',
      'createScheduledReport',
    ];

    for (const method of requiredMethods) {
      if (typeof reportController[method] === 'function') {
        success(`AdvancedReportController.${method} method exists`);
      } else {
        error(`AdvancedReportController.${method} method missing`);
        return false;
      }
    }

    // Test DashboardController
    let DashboardController;
    try {
      ({ DashboardController } = require('../dist/src/controllers/dashboard.controller'));
    } catch (err) {
      require('ts-node/register');
      ({ DashboardController } = require('../src/controllers/dashboard.controller'));
    }
    const dashboardController = new DashboardController();

    const dashboardMethods = [
      'getDashboards',
      'createDashboard',
      'getDashboardById',
      'updateDashboard',
      'deleteDashboard',
    ];

    for (const method of dashboardMethods) {
      if (typeof dashboardController[method] === 'function') {
        success(`DashboardController.${method} method exists`);
      } else {
        error(`DashboardController.${method} method missing`);
        return false;
      }
    }

    // Test HealthController
    let HealthController;
    try {
      ({ HealthController } = require('../dist/src/controllers/health.controller'));
    } catch (err) {
      require('ts-node/register');
      ({ HealthController } = require('../src/controllers/health.controller'));
    }
    const healthController = new HealthController();

    const healthMethods = [
      'healthCheck',
      'detailedHealthCheck',
      'getMetrics',
      'getSystemInfo',
      'cleanupReports',
    ];

    for (const method of healthMethods) {
      if (typeof healthController[method] === 'function') {
        success(`HealthController.${method} method exists`);
      } else {
        error(`HealthController.${method} method missing`);
        return false;
      }
    }

    return true;
  } catch (err) {
    error(`Controller verification failed: ${err.message}`);
    return false;
  }
}

async function verifyDependencies() {
  info('Verifying dependencies...');

  const requiredPackages = [
    'json2csv',
    'pdfkit',
    'exceljs',
    'dayjs',
    'uuid',
    'nodemailer',
    'node-cron',
  ];

  let allInstalled = true;

  for (const pkg of requiredPackages) {
    try {
      require(pkg);
      success(`Package ${pkg} is installed`);
    } catch (err) {
      error(`Package ${pkg} is missing`);
      allInstalled = false;
    }
  }

  return allInstalled;
}

async function verifyConfiguration() {
  info('Verifying configuration...');

  const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET'];

  const optionalEnvVars = [
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_USER',
    'EMAIL_FROM',
    'REPORTS_DIR',
    'MAX_MEMORY_USAGE',
    'BATCH_SIZE',
  ];

  let configValid = true;

  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      success(`Required environment variable ${envVar} is set`);
    } else {
      error(`Required environment variable ${envVar} is missing`);
      configValid = false;
    }
  }

  for (const envVar of optionalEnvVars) {
    if (process.env[envVar]) {
      success(`Optional environment variable ${envVar} is set`);
    } else {
      warning(`Optional environment variable ${envVar} is not set`);
    }
  }

  return configValid;
}

// Main verification function
async function runVerification() {
  log('🔍 Starting Advanced Reporting System Verification', 'cyan');
  log('=' * 60, 'cyan');

  const results = {
    database: false,
    fileSystem: false,
    apiEndpoints: false,
    services: false,
    controllers: false,
    dependencies: false,
    configuration: false,
  };

  try {
    results.database = await verifyDatabaseModels();
    results.fileSystem = await verifyFileSystem();
    results.apiEndpoints = await verifyApiEndpoints();
    results.services = await verifyServices();
    results.controllers = await verifyControllers();
    results.dependencies = await verifyDependencies();
    results.configuration = await verifyConfiguration();

    log('\n' + '=' * 60, 'cyan');
    log('📊 Verification Summary', 'cyan');
    log('=' * 60, 'cyan');

    const totalChecks = Object.keys(results).length;
    const passedChecks = Object.values(results).filter(Boolean).length;

    for (const [check, passed] of Object.entries(results)) {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      const color = passed ? 'green' : 'red';
      log(`${check.padEnd(20)} ${status}`, color);
    }

    log('\n' + '=' * 60, 'cyan');

    if (passedChecks === totalChecks) {
      success(`🎉 All verification checks passed! (${passedChecks}/${totalChecks})`);
      success('Advanced Reporting system is ready for production!');
      return true;
    } else {
      error(
        `❌ ${
          totalChecks - passedChecks
        } verification checks failed! (${passedChecks}/${totalChecks})`
      );
      error('Please fix the issues before deploying to production.');
      return false;
    }
  } catch (err) {
    error(`Verification failed with error: ${err.message}`);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification if called directly
if (require.main === module) {
  runVerification()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((err) => {
      error(`Verification script failed: ${err.message}`);
      process.exit(1);
    });
}

module.exports = {
  runVerification,
  verifyDatabaseModels,
  verifyFileSystem,
  verifyApiEndpoints,
  verifyServices,
  verifyControllers,
  verifyDependencies,
  verifyConfiguration,
};
