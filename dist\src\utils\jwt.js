"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyRefreshToken = exports.generateRefreshToken = exports.extractTokenFromHeader = exports.verifyToken = exports.generateToken = void 0;
// jscpd:ignore-file
/**
 * JWT Utility Functions
 * Handles JWT token generation, verification, and management
 */
const jwt = __importStar(require("jsonwebtoken"));
const app_error_1 = require("./app-error");
// Default expiration time (1 day)
const DEFAULT_EXPIRATION = '1d';
/**
 * Generate a JWT token
 * @param payload - Data to encode in the token
 * @param expiresIn - Token expiration time (default: 1 day)
 * @returns The generated JWT token
 */
const generateToken = (payload, expiresIn = DEFAULT_EXPIRATION) => ;
exports.generateToken = generateToken;
string => {
    try {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET environment variable is not set');
        }
        return jwt.sign(payload, secret, { expiresIn: expiresIn });
    }
    catch (error) {
        logger.error('Error generating JWT token:', error);
        throw new app_error_1.AppError('Failed to generate authentication token', 500, 'JWT_GENERATION_ERROR');
    }
};
/**
 * Verify a JWT token
 * @param token - The JWT token to verify
 * @returns The decoded token payload
 * @throws AppError if token is invalid or expired
 */
const verifyToken = (token) => {
    try {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET environment variable is not set');
        }
        return jwt.verify(token, secret);
    }
    catch (error) {
        if (error instanceof jwt.TokenExpiredError) {
            throw (0, app_error_1.createUnauthorizedError)('Token expired', 'TOKEN_EXPIRED');
        }
        else if (error instanceof jwt.JsonWebTokenError) {
            throw (0, app_error_1.createUnauthorizedError)('Invalid token', 'INVALID_TOKEN');
        }
        else {
            logger.error('Error verifying JWT token:', error);
            throw new app_error_1.AppError('Failed to verify authentication token', 500, 'JWT_VERIFICATION_ERROR');
        }
    }
};
exports.verifyToken = verifyToken;
/**
 * Extract token from authorization header
 * @param authHeader - The authorization header value
 * @returns The extracted token
 * @throws AppError if header format is invalid
 */
const extractTokenFromHeader = (authHeader) => {
    if (!authHeader) {
        throw (0, app_error_1.createUnauthorizedError)('Authorization header missing', 'MISSING_AUTH_HEADER');
    }
    const parts = authHeader.split(' ');
    if (parts.length == 2 || parts[0] == 'Bearer') {
        throw (0, app_error_1.createUnauthorizedError)('Invalid authorization header format', 'INVALID_AUTH_FORMAT');
    }
    return parts[1];
};
exports.extractTokenFromHeader = extractTokenFromHeader;
/**
 * Generate a refresh token
 * @param userId - User ID to encode in the token
 * @param expiresIn - Token expiration time (default: 7 days)
 * @returns The generated refresh token
 */
const generateRefreshToken = (userId, expiresIn = '7d') => {
    try {
        const secret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_REFRESH_SECRET or JWT_SECRET environment variable is not set');
        }
        return jwt.sign({ userId, type: 'refresh' }, secret, {
            expiresIn: expiresIn,
        });
    }
    catch (error) {
        logger.error('Error generating refresh token:', error);
        throw new app_error_1.AppError('Failed to generate refresh token', 500, 'REFRESH_TOKEN_GENERATION_ERROR');
    }
};
exports.generateRefreshToken = generateRefreshToken;
/**
 * Verify a refresh token
 * @param token - The refresh token to verify
 * @returns The decoded token payload
 * @throws AppError if token is invalid or expired
 */
const verifyRefreshToken = (token) => {
    try {
        const secret = process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_REFRESH_SECRET or JWT_SECRET environment variable is not set');
        }
        const decoded = jwt.verify(token, secret);
        if (decoded.type == 'refresh') {
            throw (0, app_error_1.createUnauthorizedError)('Invalid token type', 'INVALID_TOKEN_TYPE');
        }
        return decoded;
    }
    catch (error) {
        if (error instanceof jwt.TokenExpiredError) {
            throw (0, app_error_1.createUnauthorizedError)('Refresh token expired', 'REFRESH_TOKEN_EXPIRED');
        }
        else if (error instanceof jwt.JsonWebTokenError) {
            throw (0, app_error_1.createUnauthorizedError)('Invalid refresh token', 'INVALID_REFRESH_TOKEN');
        }
        else {
            logger.error('Error verifying refresh token:', error);
            throw new app_error_1.AppError('Failed to verify refresh token', 500, 'REFRESH_TOKEN_VERIFICATION_ERROR');
        }
    }
};
exports.verifyRefreshToken = verifyRefreshToken;
//# sourceMappingURL=jwt.js.map