// jscpd:ignore-file
/**
 * Verification Optimization Routes
 * 
 * This file defines routes for the verification optimization API.
 */

import { Router as ImportedRouter } from "express";
import { VerificationOptimizationController as ImportedVerificationOptimizationController } from "../controllers/optimization/verification-(optimization).controller";
import { authenticate, authorize } from "../middlewares/(auth).middleware";
import { VerificationOptimizationController as ImportedVerificationOptimizationController } from "../controllers/optimization/verification-(optimization).controller";
import { authenticate, authorize } from "../middlewares/(auth).middleware";

const router =Router();
const verificationOptimizationController = new VerificationOptimizationController();

// All routes require authentication and admin authorization
(router).use(authenticate);
(router).use(authorize(["admin"]);

/**
 * @route GET /api/optimization/verification/performance
 * @desc Analyze verification performance
 * @access Private (Admin)
 */
(router).get(
    "/performance",
    (verificationOptimizationController).analyzePerformance.bind(verificationOptimizationController)
);

/**
 * @route GET /api/optimization/verification/recommendations
 * @desc Generate optimization recommendations
 * @access Private (Admin)
 */
(router).get(
    "/recommendations",
    (verificationOptimizationController).generateRecommendations.bind(verificationOptimizationController)
);

/**
 * @route POST /api/optimization/verification/apply
 * @desc Apply optimization recommendations
 * @access Private (Admin)
 */
(router).post(
    "/apply",
    (verificationOptimizationController).applyRecommendations.bind(verificationOptimizationController)
);

export default router;
