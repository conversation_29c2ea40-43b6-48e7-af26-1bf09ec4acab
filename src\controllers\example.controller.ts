// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { BaseController } from "./(base).controller";
import { ExampleService as ImportedExampleService } from "../services/(example).service";
import { BaseController } from "./(base).controller";
import { ExampleService as ImportedExampleService } from "../services/(example).service";

/**
 * Example controller
 */
export class ExampleController extends BaseController {
    private exampleService: ExampleService;
  
    constructor() {
        super();
        this.exampleService = new ExampleService();
    }
  
    /**
   * Get all examples
   */
    getAll = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            // Get pagination parameters
            const { skip, take, page, limit } = this.getPaginationParams(req);
      
            // Get sort parameters
            const { field, order } = this.getSortParams(req);
      
            // Get filter parameters
            const filters = this.getFilterParams(req, ["status", "type"]);
      
            // Get search parameters
            const search = this.getSearchParams(req, ["name", "description"]);
      
            // Get examples
            const { examples, total } = await this.exampleService.getAll({
                skip,
                take,
                orderBy: { [field]: order },
                where: {
                    ...filters,
                    ...search
                }
            });
      
            // Create pagination info
            const pagination = this.createPaginationInfo(req, total);
      
            // Send paginated response
            this.sendPaginated(res, examples, pagination, "Examples retrieved successfully");
        } catch (error) {
            this.sendError(res, error);
        }
    });
  
    /**
   * Get example by ID
   */
    getById = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const id: string = req.params.id;
      
            // Get example
            const example = await this.exampleService.getById(id);
      
            // Send success response
            this.sendSuccess(res, example, "Example retrieved successfully");
        } catch (error) {
            this.sendError(res, error);
        }
    });
  
    /**
   * Create example
   */
    create = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const data = req.body;
      
            // Create example
            const example = await this.exampleService.create(data);
      
            // Send success response
            this.sendSuccess(res, example, "Example created successfully");
        } catch (error) {
            this.sendError(res, error);
        }
    });
  
    /**
   * Update example
   */
    update = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const id: string = req.params.id;
            const data = req.body;
      
            // Update example
            const example = await this.exampleService.update(id, data);
      
            // Send success response
            this.sendSuccess(res, example, "Example updated successfully");
        } catch (error) {
            this.sendError(res, error);
        }
    });
  
    /**
   * Delete example
   */
    delete = this.asyncHandler(async (req: Request, res: Response) => {
        try {
            const id: string = req.params.id;
      
            // Delete example
            await this.exampleService.delete(id);
      
            // Send success response
            this.sendSuccess(res, null, "Example deleted successfully");
        } catch (error) {
            this.sendError(res, error);
        }
    });
}
