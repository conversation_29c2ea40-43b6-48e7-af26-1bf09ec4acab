#!/bin/bash

# 🔒 COMPLETE SECURITY FIX FOR AMAZINGPAY
# Fixes ALL security validation issues for production deployment
# Domain: amazingpayme.com | VPS: 159.65.92.74

set -e

# 🎯 CONFIGURATION
DOMAIN="amazingpayme.com"
APP_DIR="/www/wwwroot/amazingpayme.com"

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 🔧 FIX NGINX SECURITY HEADERS
fix_nginx_security() {
    log "🌐 Fixing Nginx security headers..."
    
    if [ -f "$APP_DIR/scripts/fix-security-headers.sh" ]; then
        chmod +x "$APP_DIR/scripts/fix-security-headers.sh"
        "$APP_DIR/scripts/fix-security-headers.sh"
        success "Nginx security headers fixed"
    else
        warning "Nginx security fix script not found"
    fi
}

# 🔧 FIX APPLICATION SECURITY
fix_application_security() {
    log "💻 Fixing application-level security..."
    
    if [ -f "$APP_DIR/scripts/fix-cloudflare-security.sh" ]; then
        chmod +x "$APP_DIR/scripts/fix-cloudflare-security.sh"
        "$APP_DIR/scripts/fix-cloudflare-security.sh"
        success "Application security headers fixed"
    else
        warning "Application security fix script not found"
    fi
}

# 🔒 CREATE SECURITY MIDDLEWARE (FALLBACK)
create_security_middleware_fallback() {
    log "🛡️ Creating security middleware fallback..."
    
    local middleware_dir="$APP_DIR/src/middlewares"
    local middleware_file="$middleware_dir/security.ts"
    
    mkdir -p "$middleware_dir"
    
    cat > "$middleware_file" << 'EOF'
import { Request, Response, NextFunction } from 'express';

export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
    // Force all security headers
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';");
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');
    next();
};
EOF
    
    success "Security middleware created"
}

# 🔄 UPDATE APPLICATION
update_application() {
    log "🔄 Updating application with security fixes..."
    
    cd "$APP_DIR"
    
    # Check if main app file exists
    if [ -f "src/index.ts" ]; then
        # Add security middleware to main app
        if ! grep -q "securityHeaders" src/index.ts; then
            # Create backup
            cp src/index.ts src/index.ts.backup.$(date +%Y%m%d-%H%M%S)
            
            # Add security import and middleware
            sed -i '/import.*express/a import { securityHeaders } from '\''./middlewares/security'\'';' src/index.ts
            sed -i '/const app = express()/a app.use(securityHeaders);' src/index.ts
            
            success "Security middleware added to main application"
        else
            info "Security middleware already present"
        fi
    else
        warning "Main application file not found"
    fi
}

# 🔨 REBUILD AND RESTART
rebuild_and_restart() {
    log "🔨 Rebuilding and restarting application..."
    
    cd "$APP_DIR"
    
    # Build application
    if [ -f "package.json" ]; then
        npm run build
        success "Application built successfully"
    else
        warning "package.json not found"
    fi
    
    # Restart with PM2
    if command -v pm2 &> /dev/null; then
        pm2 restart all || pm2 start ecosystem.config.js
        success "Application restarted"
    else
        warning "PM2 not found"
    fi
}

# 🧪 VALIDATE SECURITY
validate_security() {
    log "🧪 Validating security fixes..."
    
    sleep 15  # Wait for services to fully restart
    
    # Run security validation script
    if [ -f "$APP_DIR/scripts/pre-launch-security-validation.sh" ]; then
        chmod +x "$APP_DIR/scripts/pre-launch-security-validation.sh"
        "$APP_DIR/scripts/pre-launch-security-validation.sh" "https://$DOMAIN"
    else
        # Manual validation
        log "Running manual security validation..."
        
        local test_url="https://$DOMAIN"
        local headers=$(curl -I -s "$test_url" 2>/dev/null || echo "")
        
        if echo "$headers" | grep -i "x-frame-options" > /dev/null; then
            success "✅ X-Frame-Options header present"
        else
            error "❌ X-Frame-Options header missing"
        fi
        
        if echo "$headers" | grep -i "strict-transport-security" > /dev/null; then
            success "✅ HSTS header present"
        else
            error "❌ HSTS header missing"
        fi
        
        if echo "$headers" | grep -i "x-content-type-options" > /dev/null; then
            success "✅ X-Content-Type-Options header present"
        else
            error "❌ X-Content-Type-Options header missing"
        fi
    fi
}

# 📋 DISPLAY FINAL INSTRUCTIONS
display_final_instructions() {
    log "📋 Final Security Configuration Instructions"
    
    echo ""
    success "🎉 SECURITY FIXES COMPLETED!"
    echo ""
    info "🔍 VERIFICATION STEPS:"
    echo "1. Run security validation: ./scripts/pre-launch-security-validation.sh https://$DOMAIN"
    echo "2. Check application: curl -I https://$DOMAIN"
    echo "3. Test API health: curl https://$DOMAIN/api/health"
    echo ""
    info "🌐 CLOUDFLARE CONFIGURATION (if using Cloudflare):"
    echo "1. Login to Cloudflare Dashboard"
    echo "2. Go to SSL/TLS → Edge Certificates"
    echo "3. Enable 'Always Use HTTPS'"
    echo "4. Enable HSTS with 12-month max age"
    echo "5. Set SSL mode to 'Full (strict)'"
    echo ""
    info "🚀 DEPLOYMENT READY:"
    echo "If all security checks pass, your application is ready for production!"
    echo ""
}

# 🎯 MAIN FUNCTION
main() {
    log "🔒 Starting Complete Security Fix"
    log "Domain: $DOMAIN"
    log "App Directory: $APP_DIR"
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
    fi
    
    # Check if app directory exists
    if [ ! -d "$APP_DIR" ]; then
        error "Application directory not found: $APP_DIR"
    fi
    
    # Execute security fixes
    fix_nginx_security
    fix_application_security
    create_security_middleware_fallback
    update_application
    rebuild_and_restart
    validate_security
    display_final_instructions
    
    success "🎉 Complete security fix completed!"
}

# Run main function
main "$@"
