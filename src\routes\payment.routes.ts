// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param, query } from "express-validator";
import paymentController from "../controllers/(payment).controller";
import transactionAnalyticsController from "../controllers/transaction-(analytics).controller";
import { authenticate, authorize } from "../middlewares/(auth).middleware";
import { validate as Importedvalidate } from "../middlewares/validation.middleware";
import { body, param, query } from "express-validator";
import { authenticate, authorize } from "../middlewares/(auth).middleware";
import { validate as Importedvalidate } from "../middlewares/validation.middleware";

const router =Router();

// Admin routes
(router).get(
    "/",
    authenticate,
    authorize(["admin"]),
    (paymentController).getAllPayments
);

// Get a specific payment - accessible by admin or merchant who owns the payment
(router).get(
    "/:id",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("id").notEmpty()
    ]),
    (paymentController).getPaymentById
);

// Get payments by merchant - accessible by admin or the merchant themselves
(router).get(
    "/merchant/:merchantId",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("merchantId").notEmpty()
    ]),
    (paymentController).getPaymentsByMerchant
);

// Create a payment - this might be called from a public endpoint for a customer
// or by the merchant/admin directly
(router).post(
    "/",
    validate([
        body("merchantId").notEmpty(),
        body("amount").isNumeric(),
        body("currency").notEmpty(),
        body("method").notEmpty(),
        body("verificationMethod").notEmpty()
    ]),
    (paymentController).createPayment
);

// Update a payment status - admin only
(router).put(
    "/:id",
    authenticate,
    authorize(["admin"]),
    validate([
        param("id").notEmpty()
    ]),
    (paymentController).updatePayment
);

// Basic Analytics endpoints
(router).get(
    "/analytics/:merchantId",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("merchantId").notEmpty(),
        query("dateRange").optional().isString()
    ]),
    (paymentController).getMerchantAnalytics
);

// Advanced Analytics endpoints
(router).get(
    "/analytics/:merchantId/advanced",
    authenticate,
    authorize(["admin", "merchant"]),
    validate([
        param("merchantId").notEmpty(),
        query("dateRange").optional().isString()
    ]),
    (transactionAnalyticsController).getAdvancedTransactionAnalytics
);

export default router;
