{"version": 3, "file": "ResponseFactory.js", "sourceRoot": "", "sources": ["../../../../src/utils/response/ResponseFactory.ts"], "names": [], "mappings": ";;;AAQA;;GAEG;AACH,IAAY,cAIX;AAJD,WAAY,cAAc;IACxB,qCAAmB,CAAA;IACnB,iCAAe,CAAA;IACf,+BAAa,CAAA;AACf,CAAC,EAJW,cAAc,8BAAd,cAAc,QAIzB;AAcD;;;GAGG;AACH,MAAa,eAAe;IAC1B;;;;;;OAMG;IACH,MAAM,CAAC,OAAO,CACZ,GAAa,EACb,IAAO,EACP,OAAO,GAAG,sBAAsB,EAChC,UAAU,GAAG,GAAG;QAEhB,OAAO,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC/B,MAAM,EAAE,cAAc,CAAC,OAAO;YAC9B,OAAO;YACP,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,SAAS,CACd,GAAa,EACb,IAAS,EACT,UAA0B,EAC1B,OAAO,GAAG,sBAAsB,EAChC,UAAU,GAAG,GAAG;QAEhB,OAAO,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC/B,MAAM,EAAE,cAAc,CAAC,OAAO;YAC9B,OAAO;YACP,IAAI;YACJ,IAAI,EAAE;gBACJ,UAAU;aACX;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CACZ,GAAa,EACb,IAAO,EACP,OAAO,GAAG,+BAA+B;QAEzC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,GAAG;QAClB,OAAO,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,GAAa,EAAE,KAAuB;QACjD,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC9B,gCAAgC;YAChC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE;gBAC7C,UAAU,EAAE,KAAK,CAAC,UAAU;gBAC5B,OAAO,EAAE,KAAK,CAAC,OAAO,IAAM,SAAS;aACtC,CAAC,CAAC;YAEH,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CACtC,KAAK,CAAC,MAAM,EAAE,CACf,CAAC;QACJ,CAAC;QAED,wBAAwB;QACxB,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,CAAC,OAAO,EAAE,EAAE;YAChD,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACxB,MAAM,EAAE,cAAc,CAAC,KAAK;YAC5B,OAAO,EAAE,uBAAuB;YAChC,GAAG,CAAC,aAAa,EAAE,IAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;SACnD,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,eAAe,CACpB,GAAa,EACb,MAAmC,EACnC,OAAO,GAAG,mBAAmB;QAE7B,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACxB,MAAM,EAAE,cAAc,CAAC,IAAI;YAC3B,OAAO;YACP,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,YAAY,CACjB,GAAa,EACb,OAAO,GAAG,yBAAyB;QAEnC,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACxB,MAAM,EAAE,cAAc,CAAC,KAAK;YAC5B,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,SAAS,CACd,GAAa,EACb,OAAO,GAAG,mDAAmD;QAE7D,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACxB,MAAM,EAAE,cAAc,CAAC,KAAK;YAC5B,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CACb,GAAa,EACb,OAAO,GAAG,oBAAoB;QAE9B,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;YACxB,MAAM,EAAE,cAAc,CAAC,KAAK;YAC5B,OAAO;SACR,CAAC,CAAC;IACL,CAAC;CACF;AAhKD,0CAgKC;AAED,kBAAe,eAAe,CAAC"}