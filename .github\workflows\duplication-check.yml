name: Code Quality Check

on:
  pull_request:
    branches: [main, develop]
  push:
    branches: [main, develop]
  workflow_dispatch:

jobs:
  test-and-duplication-check:
    name: Run tests and check for code duplication
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Fetch all history for git diff

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Create test setup if missing
        run: |
          mkdir -p src/tests
          if [ ! -f "src/tests/setup.ts" ]; then
            echo "// Jest setup file" > src/tests/setup.ts
            echo "import 'jest-extended';" >> src/tests/setup.ts
          fi

      - name: Run unit tests
        run: |
          # Check if there are any actual tests (not just empty test files)
          if find src -name "*.test.ts" -exec grep -l "describe\|test\|it(" {} \; | head -1 | grep -q .; then
            npm test
          else
            echo "No actual tests found in test files, skipping test execution"
            echo "All test files appear to be empty placeholders"
          fi

      - name: Check new code for duplication
        id: new_code_duplication
        if: github.event_name == 'pull_request'
        run: |
          npm run test:new-code-duplication || echo "failed=true" >> $GITHUB_OUTPUT

      - name: Run full duplication check
        id: duplication
        run: |
          npm run check:duplication || echo "failed=true" >> $GITHUB_OUTPUT

      - name: Generate duplication report
        if: steps.duplication.outputs.failed == 'true'
        run: |
          mkdir -p reports/duplication
          npm run check:duplication:report

      - name: Upload duplication report
        if: steps.duplication.outputs.failed == 'true'
        uses: actions/upload-artifact@v3
        with:
          name: duplication-report
          path: reports/duplication/
          if-no-files-found: warn

      - name: Check if coverage directory exists
        id: coverage_check
        run: |
          if [ -d "coverage" ]; then
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Upload test coverage
        if: steps.coverage_check.outputs.exists == 'true'
        uses: actions/upload-artifact@v3
        with:
          name: test-coverage
          path: coverage/
          if-no-files-found: warn

      - name: Comment on PR with duplication report
        if: steps.duplication.outputs.failed == 'true' && github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            const path = require('path');

            // Read the duplication statistics
            const reportPath = path.join(process.env.GITHUB_WORKSPACE, 'reports/duplication/jscpd-report.json');

            if (!fs.existsSync(reportPath)) {
              console.log('Duplication report not found, skipping detailed comment');
              return;
            }

            const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));

            const stats = report.statistics.total;
            const duplicates = report.duplicates.slice(0, 5); // Show top 5 duplications

            let comment = `## ⚠️ Code Duplication Detected\n\n`;
            comment += `This pull request contains code duplication that exceeds the threshold.\n\n`;
            comment += `### Statistics\n\n`;
            comment += `- **Duplication Percentage**: ${stats.percentage}%\n`;
            comment += `- **Duplicated Lines**: ${stats.duplicatedLines} of ${stats.lines} lines\n`;
            comment += `- **Duplicated Tokens**: ${stats.duplicatedTokens} of ${stats.tokens} tokens\n`;
            comment += `- **Clones Found**: ${report.duplicates.length}\n\n`;

            if (duplicates.length > 0) {
              comment += `### Top Duplications\n\n`;

              duplicates.forEach((dup, index) => {
                comment += `#### ${index + 1}. Between \`${dup.firstFile.name}\` and \`${dup.secondFile.name}\`\n\n`;
                comment += `- First file: Lines ${dup.firstFile.start}-${dup.firstFile.end}\n`;
                comment += `- Second file: Lines ${dup.secondFile.start}-${dup.secondFile.end}\n`;
                comment += `- Fragment size: ${dup.lines} lines, ${dup.tokens} tokens\n\n`;
              });

              comment += `### How to Fix\n\n`;
              comment += `1. Run \`npm run check:duplication:fix\` locally to get suggestions\n`;
              comment += `2. Consider using shared modules for common functionality\n`;
              comment += `3. Extract duplicated code into reusable functions or classes\n`;
              comment += `4. See the [duplication strategy document](../blob/main/docs/duplication-strategy.md) for more guidance\n\n`;

              comment += `A full report has been uploaded as an artifact for this workflow run.`;
            }

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: Comment on PR with new code duplication report
        if: steps.new_code_duplication.outputs.failed == 'true' && github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            let comment = `## ⚠️ Duplication in New Code Detected\n\n`;
            comment += `This pull request introduces new code duplication.\n\n`;
            comment += `### How to Fix\n\n`;
            comment += `1. Run \`npm run test:new-code-duplication\` locally to see details\n`;
            comment += `2. Use shared modules for common functionality\n`;
            comment += `3. Extract duplicated code into reusable functions\n`;
            comment += `4. Run \`npm run check:duplication:fix\` for assistance\n`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: Fail if duplication threshold exceeded
        if: steps.duplication.outputs.failed == 'true'
        run: |
          echo "❌ Code duplication check failed. Please fix the duplicated code."
          echo "Run 'npm run check:duplication:fix' locally to get suggestions."
          exit 1

      - name: Fail if new code introduces duplication
        if: steps.new_code_duplication.outputs.failed == 'true'
        run: |
          echo "❌ New code introduces duplication. Please fix the duplicated code."
          echo "Run 'npm run test:new-code-duplication' locally to see details."
          exit 1
