{"summary": {"totalFiles": 18, "totalIssues": 360, "fixedIssues": 59, "remainingIssues": 301, "successRate": 16.4}, "files": [{"file": "src\\lib\\logger.ts", "originalCount": 4, "fixedCount": 1, "remainingCount": 3}, {"file": "src\\middlewares\\auth.middleware.ts", "originalCount": 9, "fixedCount": 5, "remainingCount": 4}, {"file": "src\\middlewares\\auth.ts", "originalCount": 6, "fixedCount": 6, "remainingCount": 0}, {"file": "src\\middlewares\\security.middleware.ts", "originalCount": 13, "fixedCount": 13, "remainingCount": 0}, {"file": "src\\routes\\monitoring-dashboard.routes.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}, {"file": "src\\services\\advanced-report.service.ts", "originalCount": 8, "fixedCount": 8, "remainingCount": 0}, {"file": "src\\services\\alert-aggregation.service.ts", "originalCount": 7, "fixedCount": 7, "remainingCount": 0}, {"file": "src\\services\\analytics\\payment-analytics.service.ts", "originalCount": 3, "fixedCount": 2, "remainingCount": 1}, {"file": "src\\services\\audit.service.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}, {"file": "src\\services\\blockchain\\binance-api.service.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}, {"file": "src\\services\\blockchain-verification.service.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}, {"file": "src\\services\\fee-management.service.ts", "originalCount": 2, "fixedCount": 2, "remainingCount": 0}, {"file": "src\\services\\identity-verification.service.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}, {"file": "src\\services\\notification.service.ts", "originalCount": 2, "fixedCount": 2, "remainingCount": 0}, {"file": "src\\services\\report-optimization.service.ts", "originalCount": 5, "fixedCount": 5, "remainingCount": 0}, {"file": "src\\services\\reporting\\scheduling\\ReportScheduler.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}, {"file": "src\\services\\webhook.service.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}, {"file": "src\\utils\\logger.ts", "originalCount": 1, "fixedCount": 1, "remainingCount": 0}], "timestamp": "2025-05-25T21:04:29.208Z"}