/**
 * Response Utilities
 *
 * This module provides utility functions for handling API responses.
 */
import { Response } from 'express';
/**
 * Send a success response
 */
export declare const sendSuccess: (res: Response, data?: {}, message?: string, statusCode?: number) => boolean;
/**
 * Send an error response
 */
export declare const sendError: (res: Response, message?: string, statusCode?: number, error?: Error) => boolean;
/**
 * Create a standard API response
 */
export declare const createApiResponse: (success: boolean, message: string, data?: null, error?: Error) => boolean;
//# sourceMappingURL=responseUtils.d.ts.map