{"F:\\Amazingpayflow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\services\\verification-optimization.service.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\verification\\blockchain-verification.service.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\integration\\advanced-reporting-integration.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\services\\verification-realtime.service.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\services\\identity-verification.service.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\verification\\binance-verification.service.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\advanced-report.controller.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\payment-methods\\payment-method.controller.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\advanced-report.service.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\services\\fraud-detection\\__tests__\\FraudDetectionService.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\e2e\\advanced-reporting.e2e.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\dashboard.controller.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\integration\\module-interactions.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\middlewares\\error.middleware.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\controllers\\admin\\__tests__\\AdminController.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\utils\\db-optimization.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\production-mode.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\advanced-report.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\shared\\modules\\controllers\\CrudController.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\shared\\modules\\controllers\\BaseController.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\integration\\shared-modules.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\shared\\modules\\utils\\responseUtils.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\services\\cryptoService.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\vitest-simple.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\shared\\modules\\utils\\asyncHandler.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\payment\\verification.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\verification.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\shared\\modules\\services\\BaseService.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\performance\\performance.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\unit\\alert-types.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\utils\\jwt.utils.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\utils\\alerting.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\modules\\example\\example.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\utils\\cache.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\environment-separation.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\unit\\database.config.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\unit\\auth.service.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\middlewares\\auth.middleware.test.ts": [1, 0], "F:\\Amazingpayflow\\src\\tests\\basic.test.ts": [1, 2476]}