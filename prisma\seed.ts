import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { seedReportTemplates } from './seeds/report-templates';

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding database...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
      admin: {
        create: {
          department: 'IT',
          permissions: ['ALL'],
        },
      },
    },
  });

  console.log('Created admin user:', adminUser.email);

  // Create merchant user
  const merchantPassword = await bcrypt.hash('merchant123', 10);
  const merchantUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: merchantPassword,
      firstName: 'Merchant',
      lastName: 'User',
      role: 'MERCHANT',
      merchant: {
        create: {
          businessName: 'Example Business',
          businessType: 'E-commerce',
          contactEmail: '<EMAIL>',
          contactPhone: '+1234567890',
          website: 'https://example.com',
          country: 'US',
          address: '123 Main St',
          city: 'New York',
          state: 'NY',
          postalCode: '10001',
        },
      },
    },
  });

  console.log('Created merchant user:', merchantUser.email);

  // Create roles
  const adminRole = await prisma.role.upsert({
    where: { name: 'ADMIN' },
    update: {},
    create: {
      name: 'ADMIN',
      description: 'Administrator role with full access',
      permissions: ['ALL'],
    },
  });

  const merchantRole = await prisma.role.upsert({
    where: { name: 'MERCHANT' },
    update: {},
    create: {
      name: 'MERCHANT',
      description: 'Merchant role with limited access',
      permissions: ['READ_OWN', 'WRITE_OWN'],
    },
  });

  console.log('Created roles:', adminRole.name, merchantRole.name);

  // Create system settings
  const systemSettings = await prisma.systemSetting.upsert({
    where: { key: 'SYSTEM_VERSION' },
    update: {},
    create: {
      key: 'SYSTEM_VERSION',
      value: '1.0.0',
      updatedById: adminUser.id,
    },
  });

  console.log('Created system settings:', systemSettings.key);

  // Create payment methods for merchant
  const paymentMethod = await prisma.paymentMethod.create({
    data: {
      merchantId: (await prisma.merchant.findUnique({ where: { userId: merchantUser.id } }))!.id,
      type: 'CRYPTO',
      name: 'Bitcoin',
      isDefault: true,
      details: {
        currency: 'BTC',
        address: '**********************************',
      },
    },
  });

  console.log('Created payment method:', paymentMethod.name);

  // Seed report templates
  await seedReportTemplates();

  console.log('Database seeding completed.');
}

main()
  .catch((e) => {
    console.error('Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
