df164faa9a966abee70588c59d46fce6
"use strict";
// jscpd:ignore-file
/**
 * Jest setup file for AmazingPay Flow
 * Auto-generated clean file to eliminate TypeScript errors
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupConfig = void 0;
require("jest-extended");
// Global test configuration
beforeAll(() => {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET = 'test-jwt-secret';
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
});
// Global test cleanup
afterAll(() => {
    // Cleanup any global resources
});
// Mock console methods in test environment to reduce noise
global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
};
// Basic exports to maintain module structure
exports.setupConfig = {
// Configuration will be implemented as needed
};
exports.default = exports.setupConfig;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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