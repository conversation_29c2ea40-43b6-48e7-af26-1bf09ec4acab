{"version": 3, "file": "FeeCalculator.js", "sourceRoot": "", "sources": ["../../../../../src/services/payment/fees/FeeCalculator.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAkDH;;GAEG;AACH,MAAa,aAAa;IAA1B;QACU,eAAU,GAA8B,EAAE,CAAC;IA0GrD,CAAC;IAxGC;;;;;OAKG;IACI,WAAW,CAAC,QAAiC;QAClD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,UAAqC;QACxD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAA8B;QAChD,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;YAC9B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;SAC7C,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,oBAAoB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,AAAD,GAAI,AAAF;YAChE,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAC5B,CAAC;QAEF,IAAI,oBAAoB,CAAC,MAAM,IAAM,AAAD;YAAC,AAAD,GAAI,CAAC,CAAA;QAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBAChD,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;aAC7C,CAAC,CAAC;YAEH,uBAAuB;YACvB,OAAO;gBACL,GAAG,EAAE,CAAC;gBACN,aAAa,EAAE,CAAC;gBAChB,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;gBACzB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,WAAW,EAAE,8BAA8B;aAC5C,CAAC;QACJ,CAAC;QAED,oCAAoC;QACpC,MAAM,SAAS,GAA+B,EAAE,CAAC;QACjD,IAAI,QAAQ,GAAW,CAAC,CAAC;QACzB,IAAI,YAAY,GAAa,EAAE,CAAC;QAEhC,KAAK,MAAM,QAAQ,IAAI,oBAAoB,EAAE,CAAC;YAC5C,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAExC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC3C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;gBAEvB,SAAS,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC;gBAC9B,QAAQ,IAAI,GAAG,CAAC;gBAChB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAEtC,MAAM,CAAC,KAAK,CAAC,yBAAyB,YAAY,EAAE,EAAE;oBACpD,GAAG;oBACH,WAAW,EAAE,MAAM,CAAC,WAAW;iBAChC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,+BAA+B,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;gBACpE,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,GAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAA,CAAA,CAAC,CAAA;QAAC,AAAD,GAAG,GAAG,CAAA;QAAG,CAAC,CAAC;QAElF,gBAAgB;QAChB,MAAM,MAAM,GAAyB;YACnC,GAAG,EAAE,QAAQ;YACb,aAAa;YACb,SAAS;YACT,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;SACrC,CAAC;QAEF,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;YACrC,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,WAAW,EAAE,MAAM,CAAC,WAAW;SAChC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA3GD,sCA2GC"}