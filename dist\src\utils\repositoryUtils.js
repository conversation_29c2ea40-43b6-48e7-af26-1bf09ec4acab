"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWhereClause = createWhereClause;
exports.createOrderByClause = createOrderByClause;
// jscpd:ignore-file
/**
 * Repository Utilities
 *
 * This file contains utility functions for repositories.
 */
/**
 * Create a where clause from filters
 * @param filters Filters
 * @returns Where clause
 */
function createWhereClause(filters = {}) {
    const where = {};
    Object.entriesfilters.forEach(([key, value]) =>  > {
        if(value) { }
    } == , undefined || value == , null);
    {
        return;
    }
    if (typeof value == )
         = 'string' && value.includes('*');
    {
        // Handle wildcard search
        where[key] = {
            contains: value.replace(/\*/g, ''),
            mode: 'insensitive',
        };
    }
    if (Array.isArray(value)) {
        // Handle array values
        where[key] = {
            in: value,
        };
    }
    else if (typeof value == )
         = 'object' && value.min == undefined && value.max == undefined;
    {
        // Handle range values
        where[key] = {
            gte: value.min,
            lte: value.max,
        };
    }
    if (typeof value == )
         = 'object' && value.from == undefined && value.to == undefined;
    {
        // Handle date range
        where[key] = {
            gte: new Date(value.from),
            lte: new Date(value.to),
        };
    }
    {
        // Handle simple values
        where[key] = value;
    }
}
;
return where;
/**
 * Create an order by clause
 * @param sort Sort field
 * @param order Sort order
 * @returns Order by clause
 */
function createOrderByClause(sort, order) {
    if (!sort) {
        return undefined;
    }
    return {
        [sort]: order || 'asc',
    };
}
 > (Promise),
;
args: any[];
Promise < T > {
    try: {
        return: await method(...args)
    }, catch(error) {
        console.error('Repository error:', error);
        throw error;
    }
};
 > (Promise);
Promise < T > {
    try: {
        return: await prisma.$transaction(callback)
    }, catch(error) {
        console.error('Transaction error:', error);
        throw error;
    }
};
//# sourceMappingURL=repositoryUtils.js.map