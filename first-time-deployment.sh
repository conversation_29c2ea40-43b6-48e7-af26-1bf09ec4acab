#!/bin/bash

# 🚀 FIRST TIME DEPLOYMENT SCRIPT FOR AMAZINGPAY
# Perfect for beginners - Step by step deployment
# VPS: ************ | Domain: amazingpayme.com

set -e

# 🎯 CONFIGURATION
DOMAIN="amazingpayme.com"
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"
NODE_PORT="3002"
APP_DIR="/www/wwwroot/Amazingpayflow"

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

step() {
    echo -e "${PURPLE}🔸 $1${NC}"
}

# 🎉 WELCOME MESSAGE
welcome_message() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║           🚀 AMAZINGPAY FIRST TIME DEPLOYMENT 🚀             ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  Welcome to your first deployment! This script will guide   ║${NC}"
    echo -e "${CYAN}║  you through every step to get your application running.    ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${BLUE}📋 DEPLOYMENT INFORMATION:${NC}"
    echo -e "   🌐 Domain: ${GREEN}$DOMAIN${NC}"
    echo -e "   🗄️ Database: ${GREEN}$DB_NAME${NC}"
    echo -e "   🔌 Port: ${GREEN}$NODE_PORT${NC}"
    echo -e "   📁 Directory: ${GREEN}$APP_DIR${NC}"
    echo ""
    echo -e "${YELLOW}Press Enter to start deployment...${NC}"
    read
}

# 🔍 STEP 1: ENVIRONMENT CHECK
check_environment() {
    step "STEP 1: Checking Environment"
    
    # Check if we're in the right directory
    if [[ "$PWD" != "$APP_DIR" ]]; then
        warning "Not in application directory. Changing to $APP_DIR"
        cd "$APP_DIR" || error "Cannot access $APP_DIR"
    fi

    success "Working in directory: $(pwd)"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        error "package.json not found. Are you in the right directory?"
    fi
    
    # Check Node.js version
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        success "Node.js found: $NODE_VERSION"
    else
        error "Node.js not found. Please install Node.js first."
    fi
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        success "Running as root (required for system configuration)"
    else
        warning "Not running as root. Some steps may require sudo."
    fi
    
    success "Environment check completed"
    echo ""
}

# 📦 STEP 2: INSTALL DEPENDENCIES
install_dependencies() {
    step "STEP 2: Installing Dependencies"
    
    log "Cleaning previous installation..."
    rm -rf node_modules package-lock.json 2>/dev/null || true
    npm cache clean --force
    
    log "Setting production environment..."
    export NODE_ENV=production
    export HUSKY=0
    
    log "Installing production dependencies..."
    npm install --omit=dev --ignore-scripts --no-audit --no-fund
    
    log "Installing PM2 globally..."
    npm install -g pm2
    
    success "Dependencies installed successfully"
    echo ""
}

# 🗄️ STEP 3: DATABASE SETUP
setup_database() {
    step "STEP 3: Setting Up Database"
    
    log "Checking PostgreSQL installation..."
    if ! command -v psql &> /dev/null; then
        log "Installing PostgreSQL..."
        apt update
        apt install -y postgresql postgresql-contrib
        systemctl start postgresql
        systemctl enable postgresql
    else
        success "PostgreSQL already installed"
    fi
    
    log "Configuring database..."
    sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
    sudo -u postgres psql -c "CREATE DATABASE \"$DB_NAME\";" 2>/dev/null || echo "Database already exists"
    
    log "Generating Prisma client..."
    npx prisma generate
    
    log "Running database migrations..."
    npx prisma migrate deploy
    
    success "Database setup completed"
    echo ""
}

# 🔨 STEP 4: BUILD APPLICATION
build_application() {
    step "STEP 4: Building Application"
    
    log "Creating production environment file..."
    if [ -f ".env.production" ]; then
        cp .env.production .env
    else
        # Create .env file directly if template missing
        cat > .env << 'EOF'
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
DATABASE_URL=postgresql://postgres:CepWrkdzE5TL@localhost:5432/Amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=CepWrkdzE5TL
DB_NAME=Amazingpay
FRONTEND_URL=https://amazingpayme.com
API_URL=https://amazingpayme.com/api
DOMAIN=amazingpayme.com
VPS_IP=************
JWT_SECRET=AzP4y_Pr0d_JWT_S3cr3t_2024_V3ry_L0ng_4nd_S3cur3_K3y
JWT_EXPIRES_IN=1d
EOF
    fi

    # Load environment variables
    set -a
    source .env
    set +a
    
    log "Building TypeScript application..."
    npm run build
    
    success "Application built successfully"
    echo ""
}

# 🚀 STEP 5: START APPLICATION
start_application() {
    step "STEP 5: Starting Application"
    
    log "Stopping any existing processes..."
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true
    
    log "Starting application with PM2..."
    pm2 start ecosystem.config.js --env production
    pm2 save
    pm2 startup
    
    success "Application started successfully"
    echo ""
}

# 🔒 STEP 6: SECURITY CONFIGURATION
configure_security() {
    step "STEP 6: Configuring Security"
    
    if [ -f "scripts/complete-security-fix.sh" ]; then
        log "Running security configuration..."
        chmod +x scripts/complete-security-fix.sh
        ./scripts/complete-security-fix.sh
        success "Security configuration completed"
    else
        warning "Security script not found, skipping..."
    fi
    echo ""
}

# 🧪 STEP 7: TESTING DEPLOYMENT
test_deployment() {
    step "STEP 7: Testing Deployment"
    
    log "Waiting for application to start..."
    sleep 15
    
    log "Testing local health endpoint..."
    if curl -f http://localhost:$NODE_PORT/api/health &> /dev/null; then
        success "✅ Local health check PASSED"
    else
        warning "⚠️ Local health check failed"
    fi
    
    log "Testing PM2 status..."
    pm2 status
    
    success "Deployment testing completed"
    echo ""
}

# 📋 STEP 8: FINAL CONFIGURATION
final_configuration() {
    step "STEP 8: Final Configuration & Next Steps"
    
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    🎉 DEPLOYMENT COMPLETED! 🎉               ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    echo -e "${GREEN}✅ WHAT'S BEEN COMPLETED:${NC}"
    echo "   📦 Dependencies installed"
    echo "   🗄️ Database configured and migrated"
    echo "   🔨 Application built"
    echo "   🚀 PM2 process started"
    echo "   🔒 Security configured"
    echo ""
    
    echo -e "${BLUE}📊 APPLICATION STATUS:${NC}"
    echo "   🌐 Local URL: http://localhost:$NODE_PORT"
    echo "   🔗 Health Check: http://localhost:$NODE_PORT/api/health"
    echo "   📁 Directory: $APP_DIR"
    echo "   🔌 Port: $NODE_PORT"
    echo ""
    
    echo -e "${YELLOW}🔧 MANAGEMENT COMMANDS:${NC}"
    echo "   📈 Check Status: pm2 status"
    echo "   📝 View Logs: pm2 logs"
    echo "   🔄 Restart: pm2 restart all"
    echo "   🛑 Stop: pm2 stop all"
    echo ""
    
    echo -e "${PURPLE}🌐 NEXT STEPS FOR DOMAIN ACCESS:${NC}"
    echo "   1. Configure domain in aaPanel dashboard"
    echo "   2. Set up SSL certificate"
    echo "   3. Configure reverse proxy to port $NODE_PORT"
    echo "   4. Test domain: https://$DOMAIN"
    echo ""
    
    echo -e "${CYAN}🎯 TROUBLESHOOTING:${NC}"
    echo "   🔍 Check logs: pm2 logs"
    echo "   🗄️ Test database: sudo -u postgres psql -d $DB_NAME -c \"SELECT 1;\""
    echo "   🌐 Test local: curl http://localhost:$NODE_PORT/api/health"
    echo ""
    
    success "🎉 First deployment completed successfully!"
    echo -e "${GREEN}Your AmazingPay application is now running! 🚀${NC}"
}

# 🎯 MAIN DEPLOYMENT FUNCTION
main() {
    welcome_message
    check_environment
    install_dependencies
    setup_database
    build_application
    start_application
    configure_security
    test_deployment
    final_configuration
}

# 🚀 RUN DEPLOYMENT
main "$@"
