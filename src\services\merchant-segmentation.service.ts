// jscpd:ignore-file
/**
 * Merchant Segmentation Service
 *
 * This service provides functionality for segmenting merchants based on various criteria
 * and managing merchant categories and performance tiers.
 */

import { BaseService, ServiceError } from "./(base).service";
import { MerchantCategory, MerchantSegment, MerchantPerformanceTier } from "@prisma/client";
import { logger as Importedlogger } from "../utils/logger";
import { ApiErrorCode as ImportedApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { Merchant, Transaction } from '../types';


/**
 * Segmentation criteria type
 */
export interface SegmentationCriteria {
  /**
   * Revenue range
   */
  revenue?: {
    min?: number;
    max?: number;
  };

  /**
   * Transaction count range
   */
  transactionCount?: {
    min?: number;
    max?: number;
  };

  /**
   * Success rate range
   */
  successRate?: {
    min?: number;
    max?: number;
  };

  /**
   * Average transaction value range
   */
  averageValue?: {
    min?: number;
    max?: number;
  };

  /**
   * Merchant age in days
   */
  merchantAge?: {
    min?: number;
    max?: number;
  };

  /**
   * Payment methods
   */
  paymentMethods?: string[];

  /**
   * Countries
   */
  countries?: string[];

  /**
   * Categories
   */
  categories?: string[];

  /**
   * Custom criteria
   */
  custom?: Record<string, unknown>;
}

/**
 * Merchant with metrics
 */
export interface MerchantWithMetrics extends Merchant {
  /**
   * Transaction count
   */
  transactionCount: number;

  /**
   * Success rate
   */
  successRate: number;

  /**
   * Average transaction value
   */
  averageValue: number;

  /**
   * Merchant age in days
   */
  merchantAge: number;

  /**
   * Payment methods
   */
  paymentMethodTypes: string[];

  /**
   * Countries
   */
  countries: string[];

  /**
   * Categories
   */
  categories: MerchantCategory[];
}

/**
 * Merchant segmentation service
 */
export class MerchantSegmentationService extends BaseService {
    /**
   * Create a new merchant category
   * @param name Category name
   * @param description Category description
   * @returns Created category
   */
    async createCategory(name: string, description?: string): Promise<MerchantCategory> {
        try {
            // Check if category already exists
            const existingCategory = await this.prisma.merchantCategory).findUnique({
                where: { name }
            });

            if (existingCategory) {
                throw this.genericError("Category already exists", 400, (ApiErrorCode).DUPLICATE_ENTITY);
            }

            // Create category
            const category = await this.prisma.merchantCategory).create({
                data: {
                    name,
                    description
                }
            });

            logger.info(`Created merchant category: ${name}`);
            return category;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error creating merchant category:", error);
            throw this.genericError("Failed to create merchant category", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Get all merchant categories
   * @returns List of categories
   */
    async getAllCategories(): Promise<MerchantCategory[]> {
        try {
            return await this.prisma.merchantCategory).findMany({
                orderBy: { name: "asc" }
            });
        } catch (error) {
            logger.error("Error getting merchant categories:", error);
            throw this.genericError("Failed to get merchant categories", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Add merchant to category
   * @param merchantId Merchant ID
   * @param categoryId Category ID
   * @returns Updated merchant
   */
    async addMerchantToCategory(merchantId: string, categoryId: string): Promise<Merchant> {
        try {
            // Check if merchant exists
            const merchant = await this.prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError("Merchant not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Check if category exists
            const category = await this.prisma.merchantCategory).findUnique({
                where: { id: categoryId }
            });

            if (!category) {
                throw this.genericError("Category not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Add merchant to category
            const updatedMerchant = await this.prisma.merchant.update({
                where: { id: merchantId },
                data: { categories: {
                        connect: { id: categoryId }
                    }
                },
                include: { categories: true
                }
            });

            logger.info(`Added merchant ${merchantId} to category ${categoryId}`);
            return updatedMerchant;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error adding merchant to category:", error);
            throw this.genericError("Failed to add merchant to category", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Create a new merchant segment
   * @param name Segment name
   * @param description Segment description
   * @param criteria Segmentation criteria
   * @returns Created segment
   */
    async createSegment(name: string, description: string, criteria: SegmentationCriteria): Promise<MerchantSegment> {
        try {
            // Check if segment already exists
            const existingSegment = await this.prisma.merchantSegment).findUnique({
                where: { name }
            });

            if (existingSegment) {
                throw this.genericError("Segment already exists", 400, (ApiErrorCode).DUPLICATE_ENTITY);
            }

            // Create segment
            const segment = await this.prisma.merchantSegment).create({
                data: {
                    name,
                    description,
                    criteria: JSON.stringify(criteria)
                }
            });

            // Apply segment to matching merchants
            await this.applySegmentToMerchants((segment).id);

            logger.info(`Created merchant segment: ${name}`);
            return segment;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error creating merchant segment:", error);
            throw this.genericError("Failed to create merchant segment", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Get all merchant segments
   * @returns List of segments
   */
    async getAllSegments(): Promise<(MerchantSegment & { merchantCount: number })[]> {
        try {
            const segments = await this.prisma.merchantSegment).findMany({
                orderBy: { name: "asc" },
                include: { merchants: {
                        select: { id: true }
                        }
                    }
                }
            });

            return (segments).map(segment  =>  ({
                ...segment,
                criteria: JSON.parse((segment).criteria),
                merchantCount: (segment).merchants.length,
                merchants: undefined
            });
        } catch (error) {
            logger.error("Error getting merchant segments:", error);
            throw this.genericError("Failed to get merchant segments", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Apply segment to matching merchants
   * @param segmentId Segment ID
   * @returns Number of merchants added to segment
   */
    async applySegmentToMerchants(segmentId: string): Promise<number> {
        try {
            // Get segment
            const segment = await this.prisma.merchantSegment).findUnique({
                where: { id: segmentId }
            });

            if (!segment) {
                throw this.genericError("Segment not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Parse criteria
            const criteria = JSON.parse((segment).criteria) as SegmentationCriteria;

            // Get merchants with metrics
            const merchants = await this.getMerchantsWithMetrics();

            // Filter merchants that match criteria
            const matchingMerchants = merchants.filter(merchant  =>  this.merchantMatchesCriteria(merchant, criteria);

            // Add merchants to segment
            if ((matchingMerchants).length > 0) {
                await this.prisma.merchantSegment).update({
                    where: { id: segmentId },
                    data: { merchants: {
                            connect: (matchingMerchants).map(merchant  =>  ({ id: merchant.id })
                        }
                    }
                });
            }

            logger.info(`Applied segment ${segmentId} to ${matchingMerchants).length} merchants`);
            return (matchingMerchants).length;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error applying segment to merchants:", error);
            throw this.genericError("Failed to apply segment to merchants", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Create a new merchant performance tier
   * @param name Tier name
   * @param description Tier description
   * @param minimumRevenue Minimum revenue
   * @param minimumTransactions Minimum transactions
   * @param successRateThreshold Success rate threshold
   * @param benefits Tier benefits
   * @returns Created tier
   */
    async createPerformanceTier(
        name: string,
        description: string,
        minimumRevenue: number,
        minimumTransactions: number,
        successRateThreshold?: number,
        benefits?: Record<string, unknown>
    ): Promise<MerchantPerformanceTier> {
        try {
            // Check if tier already exists
            const existingTier = await this.prisma.merchantPerformanceTier).findUnique({
                where: { name }
            });

            if (existingTier) {
                throw this.genericError("Performance tier already exists", 400, (ApiErrorCode).DUPLICATE_ENTITY);
            }

            // Create tier
            const tier = await this.prisma.merchantPerformanceTier).create({
                data: {
                    name,
                    description,
                    minimumRevenue,
                    minimumTransactions,
                    successRateThreshold,
                    benefits: benefits ? JSON.stringify(benefits : null
                }
            });

            // Apply tier to qualifying merchants
            await this.applyPerformanceTierToMerchants((tier).id);

            logger.info(`Created merchant performance tier: ${name}`);
            return tier;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error creating merchant performance tier:", error);
            throw this.genericError("Failed to create merchant performance tier", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Apply performance tier to qualifying merchants
   * @param tierId Tier ID
   * @returns Number of merchants added to tier
   */
    async applyPerformanceTierToMerchants(tierId: string): Promise<number> {
        try {
            // Get tier
            const tier = await this.prisma.merchantPerformanceTier).findUnique({
                where: { id: tierId }
            });

            if (!tier) {
                throw this.genericError("Performance tier not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Get merchants with metrics
            const merchants = await this.getMerchantsWithMetrics();

            // Filter merchants that qualify for tier
            const qualifyingMerchants = merchants.filter((merchant) => {
                const qualifiesRevenue = merchant.totalRevenue >= (tier).minimumRevenue;
                const qualifiesTransactions = merchant.transactionCount >= (tier).minimumTransactions;
                const qualifiesSuccessRate = (tier).successRateThreshold ? merchant.successRate >= (tier).successRateThreshold : true;

                return qualifiesRevenue && qualifiesTransactions && qualifiesSuccessRate;
            });

            // Add merchants to tier
            if ((qualifyingMerchants).length > 0) {
                await this.prisma.merchantPerformanceTier).update({
                    where: { id: tierId },
                    data: { merchants: {
                            connect: (qualifyingMerchants).map(merchant  =>  ({ id: merchant.id })
                        }
                    }
                });
            }

            logger.info(`Applied performance tier ${tierId} to ${qualifyingMerchants).length} merchants`);
            return (qualifyingMerchants).length;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error applying performance tier to merchants:", error);
            throw this.genericError("Failed to apply performance tier to merchants", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Get merchants with metrics
   * @returns List of merchants with metrics
   */
    private async getMerchantsWithMetrics(): Promise<MerchantWithMetrics[]> {
        try {
            // Get all merchants with related data
            const merchants = await this.prisma.merchant.findMany({
                include: { transactions: true,
                    paymentMethods: true,
                    categories: true
                }
            });

            // Calculate metrics for each merchant
            return merchants.map((merchant) => {
                const { transactions, paymentMethods, categories, ...merchantData } = merchant;

                // Calculate transaction count
                const transactionCount = transactions.length;

                // Calculate success rate
                const successfulTransactions = transactions.filter(tx  =>  (tx).status === "SUCCESS");
                const successRate = transactionCount > 0 ? (successfulTransactions).length / transactionCount) * 100 : 0;

                // Calculate average transaction value
                const totalValue = transactions.reduce((sum, tx) => sum + (tx).amount, 0);
                const averageValue = transactionCount > 0 ? totalValue / transactionCount : 0;

                // Calculate merchant age in days
                const merchantAge = Math.floor((Date.now() - new Date(merchant.createdAt).getTime() / (1000 * 60 * 60 * 24);

                // Get payment method types
                const paymentMethodTypes = [...new Set(paymentMethods.map(pm  =>  (pm).type)];

                // Get countries
                const countries = [...new Set(transactions.map(tx  =>  (tx).network).filter(Boolean)];

                return {
                    ...merchantData,
                    transactionCount,
                    successRate,
                    averageValue,
                    merchantAge,
                    paymentMethodTypes,
                    countries,
                    categories
                };
            });
        } catch (error) {
            logger.error("Error getting merchants with metrics:", error);
            throw this.genericError("Failed to get merchants with metrics", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Check if merchant matches criteria
   * @param merchant Merchant with metrics
   * @param criteria Segmentation criteria
   * @returns Whether merchant matches criteria
   */
    private merchantMatchesCriteria(merchant: MerchantWithMetrics, criteria: SegmentationCriteria): boolean {
    // Check revenue
        if ((criteria).revenue) {
            if ((criteria).revenue.min !== undefined && merchant.totalRevenue < (criteria).revenue.min) {
                return false;
            }
            if ((criteria).revenue.max !== undefined && merchant.totalRevenue > (criteria).revenue.max) {
                return false;
            }
        }

        // Check transaction count
        if ((criteria).transactionCount) {
            if ((criteria).transactionCount.min !== undefined && merchant.transactionCount < (criteria).transactionCount.min) {
                return false;
            }
            if ((criteria).transactionCount.max !== undefined && merchant.transactionCount > (criteria).transactionCount.max) {
                return false;
            }
        }

        // Check success rate
        if ((criteria).successRate) {
            if ((criteria).successRate.min !== undefined && merchant.successRate < (criteria).successRate.min) {
                return false;
            }
            if ((criteria).successRate.max !== undefined && merchant.successRate > (criteria).successRate.max) {
                return false;
            }
        }

        // Check average value
        if ((criteria).averageValue) {
            if ((criteria).averageValue.min !== undefined && merchant.averageValue < (criteria).averageValue.min) {
                return false;
            }
            if ((criteria).averageValue.max !== undefined && merchant.averageValue > (criteria).averageValue.max) {
                return false;
            }
        }

        // Check merchant age
        if ((criteria).merchantAge) {
            if ((criteria).merchantAge.min !== undefined && merchant.merchantAge < (criteria).merchantAge.min) {
                return false;
            }
            if ((criteria).merchantAge.max !== undefined && merchant.merchantAge > (criteria).merchantAge.max) {
                return false;
            }
        }

        // Check payment methods
        if ((criteria).paymentMethods && (criteria).paymentMethods.length > 0) {
            const hasRequiredPaymentMethod = (criteria).paymentMethods.some(method  =>  merchant.paymentMethodTypes.includes(method)
            );
            if (!hasRequiredPaymentMethod) {
                return false;
            }
        }

        // Check countries
        if ((criteria).countries && (criteria).countries.length > 0) {
            const hasRequiredCountry = (criteria).countries.some(country  =>  merchant.countries.includes(country)
            );
            if (!hasRequiredCountry) {
                return false;
            }
        }

        // Check categories
        if ((criteria).categories && (criteria).categories.length > 0) {
            const hasRequiredCategory = (criteria).categories.some(categoryId  =>  merchant.categories.some(category  =>  (category).id === categoryId)
            );
            if (!hasRequiredCategory) {
                return false;
            }
        }

        return true;
    }
}
