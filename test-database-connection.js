#!/usr/bin/env node
/**
 * 🔍 DATABASE CONNECTION TEST
 * Tests connection with your provided credentials
 */

const { Pool } = require('pg');

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testConnection() {
  log('cyan', '🔍 TESTING DATABASE CONNECTION');
  log('cyan', '==============================');
  
  // Your database configuration
  const config = {
    user: 'amazingpay_app',
    host: 'localhost',
    database: 'amazingpay_app',
    password: 'Amz12344321',
    port: 5432,
  };
  
  log('blue', '📋 Connection Details:');
  log('blue', `   Host: ${config.host}`);
  log('blue', `   Port: ${config.port}`);
  log('blue', `   Database: ${config.database}`);
  log('blue', `   User: ${config.user}`);
  log('blue', `   Password: ${config.password}`);
  
  const pool = new Pool(config);
  
  try {
    log('blue', '\n🔄 Attempting connection...');
    
    // Test basic connection
    const result = await pool.query('SELECT NOW() as current_time, version() as postgres_version');
    log('green', '✅ Database connection successful!');
    
    log('blue', '\n📊 Database Information:');
    log('green', `   Current Time: ${result.rows[0].current_time}`);
    log('green', `   PostgreSQL Version: ${result.rows[0].postgres_version.split(' ')[0]}`);
    
    // Test database permissions
    log('blue', '\n🔐 Testing permissions...');
    
    try {
      await pool.query('CREATE TABLE IF NOT EXISTS connection_test (id SERIAL PRIMARY KEY, test_data TEXT)');
      log('green', '✅ CREATE TABLE permission: OK');
      
      await pool.query('INSERT INTO connection_test (test_data) VALUES ($1)', ['Connection test successful']);
      log('green', '✅ INSERT permission: OK');
      
      const selectResult = await pool.query('SELECT * FROM connection_test ORDER BY id DESC LIMIT 1');
      log('green', '✅ SELECT permission: OK');
      log('green', `   Test Data: ${selectResult.rows[0].test_data}`);
      
      await pool.query('DROP TABLE connection_test');
      log('green', '✅ DROP TABLE permission: OK');
      
    } catch (permError) {
      log('yellow', '⚠️  Permission test failed (but connection works):');
      log('yellow', `   ${permError.message}`);
    }
    
    await pool.end();
    
    log('cyan', '\n🎉 DATABASE CONNECTION TEST COMPLETED!');
    log('green', '✅ Your database is ready for the application!');
    
    return true;
    
  } catch (error) {
    log('red', '\n❌ Database connection failed:');
    log('red', `   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      log('yellow', '\n💡 Troubleshooting:');
      log('yellow', '   • PostgreSQL service is not running');
      log('yellow', '   • Check if PostgreSQL is installed');
      log('yellow', '   • Verify host and port settings');
    } else if (error.code === '28P01') {
      log('yellow', '\n💡 Authentication failed:');
      log('yellow', '   • Check username and password');
      log('yellow', '   • Verify user exists in PostgreSQL');
    } else if (error.code === '3D000') {
      log('yellow', '\n💡 Database not found:');
      log('yellow', '   • Create database: CREATE DATABASE amazingpay_app;');
      log('yellow', '   • Verify database name is correct');
    }
    
    await pool.end();
    return false;
  }
}

// Run the test
if (require.main === module) {
  testConnection().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testConnection };
