{"dashboard": {"id": null, "title": "AmazingPay Flow Dashboard", "tags": ["amazingpay"], "timezone": "browser", "panels": [{"id": 1, "title": "Application Health", "type": "stat", "targets": [{"expr": "up{job=\"amazingpay-app\"}", "legendFormat": "App Status"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "Requests/sec"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Database Connections", "type": "graph", "targets": [{"expr": "pg_stat_database_numbackends", "legendFormat": "Active Connections"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}