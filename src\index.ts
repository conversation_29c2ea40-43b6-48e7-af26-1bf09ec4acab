// jscpd:ignore-file
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import path from 'path';
import { createServer } from 'http';
import { initializeMonitoring, monitorRequest } from './utils/monitoring';
import { apiResponseMiddleware, errorHandlerMiddleware } from './middlewares/apiResponseMiddleware';

// Import routes - only the ones we're currently using
import healthRoutes from './routes/health.routes';
import feeManagementTestRoutes from './routes/fee-management-test.routes';

// Load environment variables
dotenv.config();

// Initialize Prisma client
export const prisma = new PrismaClient();

// Test database connection
const testDatabaseConnection = async (): Promise<void> => {
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    console.error('Please check your database configuration in .env file');
    process.exit(1);
  }
};

// Create Express app and HTTP server
const app = express();
const httpServer = createServer(app);
const PORT = process.env.PORT || 3002; // Use port 3002 as default

// Middleware
app.use(
  cors({
    origin: [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:5176',
      'http://localhost:5177',
      'http://localhost:3000',
      'http://localhost:3002',
      'https://amazingpayme.com',
      '*',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Site-Domain'],
  })
);

app.use(
  helmet({
    crossOriginResourcePolicy: { policy: 'cross-origin' },
  })
);

app.use(morgan('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));

// Initialize monitoring
const cleanupMonitoring = initializeMonitoring();

// Add monitoring middleware
app.use(monitorRequest);

// Add API response middleware
app.use(apiResponseMiddleware);

// API routes - temporarily disable most routes to identify the issue
app.use('/api/health', healthRoutes);
app.use('/api/fee-management', feeManagementTestRoutes);

// Static dashboard page
app.use('/dashboard/reports', express.static(path.join(__dirname, '../public/reports')));

// Basic health check endpoint (for load balancers)
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is healthy and running' });
});

// Error handling middleware
app.use(errorHandlerMiddleware);

// Start server
const startServer = async (): Promise<void> => {
  // Test database connection first
  await testDatabaseConnection();

  console.log('🚀 Starting AmazingPay Flow server...');

  httpServer.listen(PORT, () => {
    console.log(`🌟 Server running on port ${PORT}`);
    console.log(`🌐 API available at http://localhost:${PORT}/api`);
    console.log(`🩺 Health check at http://localhost:${PORT}/health`);
  });
};

// Start the server
startServer().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🔄 Graceful shutdown initiated...');

  // Close server
  httpServer.close(() => {
    console.log('🔌 HTTP server closed');
  });

  // Disconnect Prisma
  await prisma.$disconnect();
  console.log('🗄️ Database connection closed');

  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🔄 Graceful shutdown initiated...');

  // Close server
  httpServer.close(() => {
    console.log('🔌 HTTP server closed');
  });

  // Disconnect Prisma
  await prisma.$disconnect();
  console.log('🗄️ Database connection closed');

  process.exit(0);
});

export default app;
