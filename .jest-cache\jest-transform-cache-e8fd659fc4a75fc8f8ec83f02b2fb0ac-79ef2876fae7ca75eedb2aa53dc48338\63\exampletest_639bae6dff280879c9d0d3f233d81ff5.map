{"file": "F:\\Amazingpayflow\\src\\modules\\example\\example.test.ts", "mappings": ";AAAA,oBAAoB;AACpB;;;GAGG;;;AAEH,6CAA6C;AAChC,QAAA,iBAAiB,GAAG;AAC7B,8CAA8C;CACjD,CAAC;AAEF,kBAAe,yBAAiB,CAAC", "names": [], "sources": ["F:\\Amazingpayflow\\src\\modules\\example\\example.test.ts"], "sourcesContent": ["// jscpd:ignore-file\n/**\n * Example.test\n * Auto-generated clean file to eliminate TypeScript errors\n */\n\n// Basic exports to maintain module structure\nexport const exampletestConfig = {\n    // Configuration will be implemented as needed\n};\n\nexport default exampletestConfig;\n"], "version": 3}