window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":9,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":9,"startTime":1748401481610,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":9,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1748401484439,"runtime":2476,"slow":false,"start":1748401481963},"testFilePath":"F:\\Amazingpayflow\\src\\tests\\basic.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["Basic Application Tests"],"duration":2,"failureMessages":[],"fullName":"Basic Application Tests should have working test environment","status":"passed","title":"should have working test environment"},{"ancestorTitles":["Basic Application Tests"],"duration":0,"failureMessages":[],"fullName":"Basic Application Tests should have NODE_ENV set to test","status":"passed","title":"should have NODE_ENV set to test"},{"ancestorTitles":["Basic Application Tests"],"duration":0,"failureMessages":[],"fullName":"Basic Application Tests should have basic math operations working","status":"passed","title":"should have basic math operations working"},{"ancestorTitles":["Basic Application Tests"],"duration":1,"failureMessages":[],"fullName":"Basic Application Tests should handle string operations","status":"passed","title":"should handle string operations"},{"ancestorTitles":["Basic Application Tests"],"duration":3,"failureMessages":[],"fullName":"Basic Application Tests should handle array operations","status":"passed","title":"should handle array operations"},{"ancestorTitles":["Basic Application Tests"],"duration":1,"failureMessages":[],"fullName":"Basic Application Tests should handle object operations","status":"passed","title":"should handle object operations"},{"ancestorTitles":["Basic Application Tests"],"duration":13,"failureMessages":[],"fullName":"Basic Application Tests should handle async operations","status":"passed","title":"should handle async operations"},{"ancestorTitles":["Environment Configuration Tests"],"duration":1,"failureMessages":[],"fullName":"Environment Configuration Tests should have test environment variables","status":"passed","title":"should have test environment variables"},{"ancestorTitles":["Environment Configuration Tests"],"duration":1,"failureMessages":[],"fullName":"Environment Configuration Tests should have correct test database URL format","status":"passed","title":"should have correct test database URL format"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["src/**/*.{js,ts}","!src/**/*.d.ts","!src/**/*.test.ts","!src/**/__tests__/**","!src/**/index.ts","!src/types/**","!src/migrations/**"],"coverageDirectory":"F:\\Amazingpayflow\\coverage","coverageProvider":"babel","coverageReporters":["json","lcov","text","text-summary","clover","html"],"coverageThreshold":{"global":{"branches":80,"functions":85,"lines":90,"statements":90},"./src/services/":{"branches":85,"functions":90,"lines":95,"statements":95},"./src/controllers/":{"branches":80,"functions":85,"lines":90,"statements":90}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":2,"noStackTrace":false,"nonFlagArgs":["src/tests/basic.test.ts"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\Amazingpayflow\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./test-reports","filename":"jest-report.html","expand":true}]],"rootDir":"F:\\Amazingpayflow","runTestsByPath":false,"seed":-1082993718,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"src\\\\tests\\\\basic.test.ts","testSequencer":"F:\\Amazingpayflow\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":true,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1748401484447,"_reporterOptions":{"publicPath":"./test-reports","filename":"jest-report.html","expand":true,"pageTitle":"","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})