"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteTestRunner = void 0;
// jscpd:ignore-file
const express_1 = __importDefault(require("express"));
/**
 * Route test runner
 * This class runs route tests
 */
class RouteTestRunner {
    /**
     * Create a new route test runner
     */
    constructor() {
        // Create Express application
        this.app = (0, express_1.default)();
        // Create container
        this.container = Container.getInstance();
        // Bootstrap container
        ContainerBootstrap.bootstrapthis.container;
        ;
        // Create route test suite
        this.routeTestSuite = new RouteTestSuitethis.app;
        ;
    }
    /**
     * Run all tests
     */
    async runAllTests() {
        logger.info("Running all route tests...");
        try {
            await this.routeTestSuite.runAllTests();
            logger.info("All route tests passed");
        }
        catch (error) {
            logger.error("Route tests failed:", error);
            throw error;
        }
    }
    /**
     * Get the Express application
     * @returns Express application
     */
    getApp() {
        return this.app;
    }
    /**
     * Get the container
     * @returns Container
     */
    getContainer() {
        return this.container;
    }
    /**
     * Get the route test suite
     * @returns Route test suite
     */
    getRouteTestSuite() {
        return this.routeTestSuite;
    }
}
exports.RouteTestRunner = RouteTestRunner;
exports.default = RouteTestRunner;
//# sourceMappingURL=RouteTestRunner.js.map