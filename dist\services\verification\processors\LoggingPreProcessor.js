"use strict";
// jscpd:ignore-file
/**
 * Logging Pre-Processor
 *
 * Pre-processor that logs verification requests.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggingPreProcessor = void 0;
const logger_1 = require("../../../lib/logger");
/**
 * Logging pre-processor
 */
class LoggingPreProcessor {
    /**
     * Get the name of the pre-processor
     */
    getName() {
        return 'logging_pre_processor';
    }
    /**
     * Process a verification request
     */
    async process(request) {
        logger_1.logger.info(`Processing verification request for transaction: ${request.transactionId}`, {
            verificationMethod: request.verificationMethod,
            merchantId: request.merchantId,
            paymentMethodId: request.paymentMethodId,
            paymentMethodType: request.paymentMethodType,
            amount: request.amount,
            currency: request.currency,
        });
        // Return the request unchanged
        return request;
    }
}
exports.LoggingPreProcessor = LoggingPreProcessor;
