/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',

  // Root directories
  roots: ['<rootDir>/src', '<rootDir>/tests'],

  // Test file patterns
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.test.ts',
    '<rootDir>/src/**/*.test.ts',
    '<rootDir>/tests/**/*.test.ts',
  ],

  // Transform configuration
  transform: {
    '^.+\\.ts$': [
      'ts-jest',
      {
        tsconfig: {
          module: 'commonjs',
          target: 'es2020',
          lib: ['es2020'],
          moduleResolution: 'node',
          allowSyntheticDefaultImports: true,
          esModuleInterop: true,
          skipLibCheck: true,
          strict: false,
        },
      },
    ],
  },

  // Module name mapping
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@controllers/(.*)$': '<rootDir>/src/controllers/$1',
  },

  // Coverage configuration
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/__tests__/**',
    '!src/**/index.ts',
    '!src/types/**',
    '!src/migrations/**',
  ],

  // Enhanced coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 90,
      statements: 90,
    },
    './src/services/': {
      branches: 85,
      functions: 90,
      lines: 95,
      statements: 95,
    },
    './src/controllers/': {
      branches: 80,
      functions: 85,
      lines: 90,
      statements: 90,
    },
  },

  // Enhanced coverage reporters
  coverageReporters: ['json', 'lcov', 'text', 'text-summary', 'clover', 'html'],
  coverageDirectory: 'coverage',

  // Test configuration
  verbose: true,
  testTimeout: 30000,
  clearMocks: true,
  restoreMocks: true,

  // Path ignores
  testPathIgnorePatterns: ['/node_modules/', '/dist/', '/build/'],

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts'],

  // Global configuration removed - using transform config instead

  // Disable projects for now to fix TypeScript issues
  // projects: [
  //   {
  //     displayName: 'unit',
  //     testMatch: ['<rootDir>/src/**/*.test.ts'],
  //     testEnvironment: 'node',
  //   },
  //   {
  //     displayName: 'integration',
  //     testMatch: ['<rootDir>/tests/integration/**/*.test.ts'],
  //     testEnvironment: 'node',
  //   },
  //   {
  //     displayName: 'performance',
  //     testMatch: ['<rootDir>/tests/performance/**/*.test.ts'],
  //     testEnvironment: 'node',
  //   },
  // ],

  // Reporters
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './test-reports',
        filename: 'jest-report.html',
        expand: true,
      },
    ],
  ],

  // Cache and performance
  cacheDirectory: '<rootDir>/.jest-cache',
  maxWorkers: '50%',
};
