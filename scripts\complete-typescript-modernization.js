#!/usr/bin/env node

/**
 * Complete TypeScript Modernization Script
 * Final automation to complete the TypeScript modernization project
 * Based on the comprehensive analysis and existing progress
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 COMPLETE TYPESCRIPT MODERNIZATION - FINAL PHASE');
console.log('==================================================');
console.log('📋 Based on comprehensive project analysis');
console.log('🎯 Goal: Complete the remaining TypeScript modernization work\n');

// Critical syntax patterns that need fixing
const CRITICAL_SYNTAX_PATTERNS = {
    // Fix malformed winston calls
    '=(winston)': ' = winston',
    '(winston).': 'winston.',
    '(logger).': 'logger.',
    
    // Fix malformed function assignments
    '=(': ' = (',
    ') =': ') =>',
    
    // Fix missing closing parentheses in JSON.stringify
    'JSON.stringify(rest, null, 2 :': 'JSON.stringify(rest, null, 2) :',
    'JSON.stringify(rest, null, 2': 'JSON.stringify(rest, null, 2)',
    
    // Fix malformed arrow functions
    '= >': ' =>',
    '=()': ' = ()',
    '=(x)': ' = (x)',
    '=(x, y)': ' = (x, y)',
    
    // Fix object syntax issues
    '{,': '{',
    ',}': '}',
    '{ ,': '{ ',
    ', }': ' }',
    
    // Fix array syntax issues
    '[,': '[',
    ',]': ']',
    '[ ,': '[ ',
    ', ]': ' ]',
    
    // Fix function call spacing
    'function(': 'function (',
    'if(': 'if (',
    'for(': 'for (',
    'while(': 'while (',
    'switch(': 'switch (',
    'catch(': 'catch (',
    
    // Fix type annotation spacing
    ':any': ': any',
    ':unknown': ': unknown',
    ':string': ': string',
    ':number': ': number',
    ':boolean': ': boolean',
    ':object': ': object',
    
    // Fix assignment operator spacing
    '=any': ' = any',
    '=unknown': ' = unknown',
    '=null': ' = null',
    '=undefined': ' = undefined',
    
    // Fix logical operators
    '&&': ' && ',
    '||': ' || ',
    '??': ' ?? ',
    
    // Fix comparison operators
    '===': ' === ',
    '!==': ' !== ',
    '==': ' == ',
    '!=': ' != ',
    '<=': ' <= ',
    '>=': ' >= ',
    '<': ' < ',
    '>': ' > ',
};

// Advanced patterns for complex fixes
const ADVANCED_PATTERNS = [
    // Fix malformed method calls
    {
        pattern: /\((\w+)\)\.(\w+)\(/g,
        replacement: '$1.$2(',
        description: 'Fix malformed method calls (obj).method() → obj.method()'
    },
    
    // Fix malformed property access
    {
        pattern: /\((\w+)\)\.(\w+)/g,
        replacement: '$1.$2',
        description: 'Fix malformed property access (obj).prop → obj.prop'
    },
    
    // Fix arrow function spacing
    {
        pattern: /(\w+)\s*=\s*>\s*/g,
        replacement: '$1 => ',
        description: 'Fix arrow function spacing'
    },
    
    // Fix function parameter parentheses
    {
        pattern: /\.(\w+)\(([^)]+)\)\s*=>/g,
        replacement: '.$1(($2)) =>',
        description: 'Fix function parameter parentheses'
    },
    
    // Fix missing semicolons
    {
        pattern: /^(\s*)(export\s+(?:default\s+)?(?:class|interface|enum|function|const|let|var)\s+\w+[^;{]*)\s*$/gm,
        replacement: '$1$2;',
        description: 'Add missing semicolons'
    }
];

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        try {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage', 'backups'].includes(item)) {
                    scanDirectory(fullPath);
                } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            console.warn(`⚠️  Warning: Could not scan directory ${currentDir}: ${error.message}`);
        }
    }
    
    scanDirectory(dir);
    return files;
}

function getErrorCount() {
    try {
        const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
        const errorMatches = output.match(/error TS/g) || [];
        return errorMatches.length;
    } catch (error) {
        const errorOutput = error.stdout || error.stderr || '';
        const errorMatches = errorOutput.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function processFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        let modifiedContent = content;
        let fixCount = 0;
        
        // Apply critical syntax patterns
        for (const [oldPattern, newPattern] of Object.entries(CRITICAL_SYNTAX_PATTERNS)) {
            const beforeLength = modifiedContent.length;
            modifiedContent = modifiedContent.split(oldPattern).join(newPattern);
            if (modifiedContent.length !== beforeLength) {
                fixCount++;
            }
        }
        
        // Apply advanced regex patterns
        for (const { pattern, replacement, description } of ADVANCED_PATTERNS) {
            const beforeContent = modifiedContent;
            modifiedContent = modifiedContent.replace(pattern, replacement);
            if (modifiedContent !== beforeContent) {
                fixCount++;
            }
        }
        
        if (fixCount > 0) {
            fs.writeFileSync(filePath, modifiedContent, 'utf8');
            return { filePath, fixCount, success: true };
        }
        
        return { filePath, fixCount: 0, success: true };
    } catch (error) {
        return { filePath, error: error.message, success: false };
    }
}

function generateReport(results, initialErrors, finalErrors, duration) {
    const report = {
        summary: {
            startTime: new Date().toISOString(),
            duration: `${duration}ms`,
            initialErrors,
            finalErrors,
            errorsFixed: initialErrors - finalErrors,
            successRate: initialErrors > 0 ? ((initialErrors - finalErrors) / initialErrors * 100).toFixed(1) : 0,
            filesProcessed: results.length,
            filesWithFixes: results.filter(r => r.fixCount > 0).length,
            totalFixes: results.reduce((sum, r) => sum + (r.fixCount || 0), 0)
        },
        files: results.filter(r => r.fixCount > 0).map(r => ({
            file: path.relative(process.cwd(), r.filePath),
            fixes: r.fixCount
        })),
        errors: results.filter(r => r.error).map(r => ({
            file: path.relative(process.cwd(), r.filePath),
            error: r.error
        }))
    };
    
    fs.writeFileSync('TYPESCRIPT_MODERNIZATION_FINAL_REPORT.json', JSON.stringify(report, null, 2));
    return report;
}

async function main() {
    console.log('🔍 Scanning for TypeScript files...');
    const files = findAllTypeScriptFiles('./src');
    console.log(`📁 Found ${files.length} TypeScript files`);
    
    console.log('📊 Getting initial error count...');
    const initialErrors = getErrorCount();
    console.log(`🚨 Initial TypeScript errors: ${initialErrors}`);
    
    if (initialErrors === 0) {
        console.log('🎉 No TypeScript errors found! Project is already fully modernized.');
        return;
    }
    
    console.log('🚀 Starting final TypeScript modernization...');
    const startTime = Date.now();
    
    const results = [];
    let totalFixedIssues = 0;
    
    for (const file of files) {
        const result = processFile(file);
        results.push(result);
        if (result.fixCount) {
            totalFixedIssues += result.fixCount;
            console.log(`✅ ${path.relative(process.cwd(), file)}: ${result.fixCount} fixes`);
        }
    }
    
    const duration = Date.now() - startTime;
    
    console.log('📊 Getting final error count...');
    const finalErrors = getErrorCount();
    
    const report = generateReport(results, initialErrors, finalErrors, duration);
    
    console.log('\n🎯 FINAL TYPESCRIPT MODERNIZATION RESULTS:');
    console.log('==========================================');
    console.log(`⏱️  Duration: ${duration}ms`);
    console.log(`📁 Files processed: ${results.length}`);
    console.log(`🔧 Total fixes applied: ${totalFixedIssues}`);
    console.log(`🚨 Initial errors: ${initialErrors}`);
    console.log(`✅ Final errors: ${finalErrors}`);
    console.log(`📈 Errors fixed: ${initialErrors - finalErrors}`);
    console.log(`🎯 Success rate: ${report.summary.successRate}%`);
    
    if (finalErrors === 0) {
        console.log('\n🎉 CONGRATULATIONS! TypeScript modernization completed successfully!');
        console.log('✅ Zero compilation errors achieved');
        console.log('🚀 Project is now fully modernized and production-ready');
    } else {
        console.log(`\n⚠️  ${finalErrors} errors remaining - manual review may be needed`);
        console.log('📋 Check TYPESCRIPT_MODERNIZATION_FINAL_REPORT.json for details');
    }
    
    console.log('\n📋 Report saved to: TYPESCRIPT_MODERNIZATION_FINAL_REPORT.json');
}

main().catch(console.error);
