# WEB SECURITY DEPLOYMENT CHECKLIST
## Critical Financial Application File Protection Framework

### 🔒 **SECURITY OVERVIEW**

This comprehensive checklist ensures your critical financial application is completely protected against unauthorized file access, directory traversal, and sensitive data exposure when deployed to production.

**Document Version**: 1.0  
**Security Classification**: Critical  
**Last Updated**: [Current Date]  
**Review Cycle**: Before each deployment  

---

## 🎯 **CRITICAL SECURITY OBJECTIVES**

### **Primary Goals**
1. **Prevent unauthorized access** to sensitive configuration files
2. **Block directory listing** and file browsing
3. **Secure environment variables** and secrets
4. **Implement proper file permissions** and access controls
5. **Validate web server configuration** security
6. **Enable comprehensive security testing** and monitoring

---

## 📁 **SECURE PROJECT FOLDER STRUCTURE**

### **Recommended Directory Layout**

```
/var/www/
├── html/                          # Public web root (ONLY public files)
│   ├── index.html
│   ├── assets/
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   └── api/                       # Public API endpoints only
│       └── public-endpoints.php
├── app/                           # Application code (OUTSIDE web root)
│   ├── src/
│   ├── config/
│   ├── controllers/
│   ├── models/
│   ├── services/
│   └── middleware/
├── storage/                       # Data storage (OUTSIDE web root)
│   ├── logs/
│   ├── uploads/
│   └── cache/
├── config/                        # Configuration (OUTSIDE web root)
│   ├── database.config.ts
│   ├── auth.config.ts
│   └── app.config.ts
├── .env                          # Environment variables (OUTSIDE web root)
├── .env.production               # Production env (OUTSIDE web root)
├── package.json                  # Dependencies (OUTSIDE web root)
├── tsconfig.json                 # TypeScript config (OUTSIDE web root)
└── node_modules/                 # Dependencies (OUTSIDE web root)
```

### **Critical Security Principles**
- ✅ **Web root contains ONLY public assets**
- ✅ **All application code is OUTSIDE web root**
- ✅ **Configuration files are OUTSIDE web root**
- ✅ **Environment variables are OUTSIDE web root**
- ✅ **Source code is OUTSIDE web root**

---

## 🔧 **WEB SERVER CONFIGURATION**

### **Nginx Security Configuration**

#### **Primary Configuration (/etc/nginx/sites-available/amazingpay)**
```nginx
server {
    listen 443 ssl http2;
    server_name amazingpay.com www.amazingpay.com;
    
    # Document root - ONLY public files
    root /var/www/html;
    index index.html index.php;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'" always;
    
    # Hide Nginx version
    server_tokens off;
    
    # CRITICAL: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # Block access to configuration files
    location ~* \.(env|config|conf|ini|log|sql|bak|backup|old)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # Block access to source code files
    location ~* \.(ts|js\.map|json|md|txt|yml|yaml)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # Block access to package files
    location ~* (package\.json|package-lock\.json|tsconfig\.json|\.gitignore|composer\.json|composer\.lock)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # Block access to common sensitive directories
    location ~* /(config|src|app|storage|logs|node_modules|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # Disable directory listing
    autoindex off;
    
    # Block access to backup and temporary files
    location ~* \.(bak|backup|swp|tmp|temp|~)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # API routing (if needed)
    location /api/ {
        try_files $uri $uri/ /api/index.php?$query_string;
        
        # Additional API security
        limit_req zone=api burst=10 nodelay;
        
        # Block direct access to PHP files in API
        location ~* \.php$ {
            include fastcgi_params;
            fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        }
    }
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/amazingpay.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/amazingpay.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
    limit_req zone=general burst=20 nodelay;
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name amazingpay.com www.amazingpay.com;
    return 301 https://$server_name$request_uri;
}
```

### **Apache Security Configuration**

#### **Primary Configuration (.htaccess in web root)**
```apache
# CRITICAL: Block access to sensitive files and directories
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# Block access to configuration files
<FilesMatch "\.(env|config|conf|ini|log|sql|bak|backup|old)$">
    Require all denied
</FilesMatch>

# Block access to source code files
<FilesMatch "\.(ts|js\.map|json|md|txt|yml|yaml)$">
    Require all denied
</FilesMatch>

# Block access to package files
<FilesMatch "(package\.json|package-lock\.json|tsconfig\.json|\.gitignore|composer\.json|composer\.lock)$">
    Require all denied
</FilesMatch>

# Block access to sensitive directories
<DirectoryMatch "/(config|src|app|storage|logs|node_modules|vendor)/">
    Require all denied
</DirectoryMatch>

# Disable directory browsing
Options -Indexes

# Block access to backup and temporary files
<FilesMatch "\.(bak|backup|swp|tmp|temp|~)$">
    Require all denied
</FilesMatch>

# Security headers
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-Content-Type-Options "nosniff"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Content-Security-Policy "default-src 'self'"

# Hide Apache version
ServerTokens Prod
ServerSignature Off

# Force HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

---

## 🔐 **FILE AND FOLDER PERMISSIONS**

### **Secure Permission Settings**

#### **Directory Permissions**
```bash
# Web root (public files only)
chmod 755 /var/www/html
chmod 644 /var/www/html/*.html
chmod 644 /var/www/html/assets/**/*

# Application directories (outside web root)
chmod 750 /var/www/app
chmod 750 /var/www/config
chmod 750 /var/www/storage

# Logs directory
chmod 750 /var/www/storage/logs
chmod 640 /var/www/storage/logs/*.log

# Environment files (MOST CRITICAL)
chmod 600 /var/www/.env
chmod 600 /var/www/.env.production
chown www-data:www-data /var/www/.env*

# Configuration files
chmod 640 /var/www/config/*.ts
chmod 640 /var/www/config/*.js
chown www-data:www-data /var/www/config/*

# Application source code
chmod 640 /var/www/app/src/**/*.ts
chmod 640 /var/www/app/src/**/*.js
chown www-data:www-data /var/www/app/src/**/*
```

#### **Critical Permission Rules**
- ✅ **Environment files**: 600 (owner read/write only)
- ✅ **Configuration files**: 640 (owner read/write, group read)
- ✅ **Source code**: 640 (owner read/write, group read)
- ✅ **Public files**: 644 (world readable)
- ✅ **Directories**: 750 (owner full, group read/execute)
- ✅ **Log files**: 640 (owner read/write, group read)

---

## 🚫 **PREVENT DIRECTORY LISTING**

### **Nginx Configuration**
```nginx
# Disable autoindex globally
autoindex off;

# Explicit deny for any directory access
location ~ ^/(.*)/$ {
    return 403;
}
```

### **Apache Configuration**
```apache
# Disable directory browsing
Options -Indexes

# Custom error page for directory access
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
```

### **Additional Protection**
```bash
# Create empty index.html files in sensitive directories
touch /var/www/app/index.html
touch /var/www/config/index.html
touch /var/www/storage/index.html
echo "Access Denied" > /var/www/app/index.html
```

---

## 🧪 **SECURITY TESTING AND VALIDATION**

### **Manual Testing Checklist**

#### **Critical File Access Tests**
```bash
# Test 1: Environment files
curl -I https://amazingpay.com/.env
# Expected: 403 Forbidden or 404 Not Found

curl -I https://amazingpay.com/.env.production
# Expected: 403 Forbidden or 404 Not Found

# Test 2: Configuration files
curl -I https://amazingpay.com/config/database.config.ts
# Expected: 403 Forbidden or 404 Not Found

curl -I https://amazingpay.com/tsconfig.json
# Expected: 403 Forbidden or 404 Not Found

# Test 3: Source code files
curl -I https://amazingpay.com/src/app.ts
# Expected: 403 Forbidden or 404 Not Found

curl -I https://amazingpay.com/package.json
# Expected: 403 Forbidden or 404 Not Found

# Test 4: Hidden files
curl -I https://amazingpay.com/.git/config
# Expected: 403 Forbidden or 404 Not Found

curl -I https://amazingpay.com/.gitignore
# Expected: 403 Forbidden or 404 Not Found

# Test 5: Directory listing
curl https://amazingpay.com/config/
# Expected: 403 Forbidden or custom error page

curl https://amazingpay.com/src/
# Expected: 403 Forbidden or custom error page

# Test 6: Backup files
curl -I https://amazingpay.com/.env.bak
# Expected: 403 Forbidden or 404 Not Found

curl -I https://amazingpay.com/config.backup
# Expected: 403 Forbidden or 404 Not Found

# Test 7: Log files
curl -I https://amazingpay.com/logs/app.log
# Expected: 403 Forbidden or 404 Not Found

# Test 8: Node modules
curl -I https://amazingpay.com/node_modules/package.json
# Expected: 403 Forbidden or 404 Not Found
```

### **Automated Security Testing Script**

#### **Create Security Test Script**
```bash
#!/bin/bash
# File: scripts/security-test.sh

echo "🔒 SECURITY TESTING: File Access Protection"
echo "=========================================="

DOMAIN="https://amazingpay.com"
FAILED_TESTS=0

# Function to test file access
test_file_access() {
    local url="$1"
    local description="$2"
    
    echo -n "Testing $description... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    if [[ "$response" == "403" || "$response" == "404" ]]; then
        echo "✅ SECURE ($response)"
    else
        echo "❌ VULNERABLE ($response)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Critical file tests
test_file_access "$DOMAIN/.env" "Environment file"
test_file_access "$DOMAIN/.env.production" "Production environment"
test_file_access "$DOMAIN/tsconfig.json" "TypeScript config"
test_file_access "$DOMAIN/package.json" "Package file"
test_file_access "$DOMAIN/.gitignore" "Git ignore file"
test_file_access "$DOMAIN/src/app.ts" "Source code"
test_file_access "$DOMAIN/config/database.config.ts" "Database config"
test_file_access "$DOMAIN/node_modules/express/package.json" "Node modules"

# Directory listing tests
echo ""
echo "Testing directory listing protection..."
test_file_access "$DOMAIN/config/" "Config directory"
test_file_access "$DOMAIN/src/" "Source directory"
test_file_access "$DOMAIN/logs/" "Logs directory"

echo ""
echo "=========================================="
if [ $FAILED_TESTS -eq 0 ]; then
    echo "🏆 ALL TESTS PASSED - SECURITY VALIDATED"
else
    echo "⚠️  $FAILED_TESTS TESTS FAILED - SECURITY ISSUES FOUND"
    exit 1
fi
```

---

## 🛠️ **SECURITY AUDIT TOOLS**

### **Automated Security Scanning**

#### **Nikto Web Vulnerability Scanner**
```bash
# Install Nikto
sudo apt-get install nikto

# Scan for web vulnerabilities
nikto -h https://amazingpay.com -C all -Format txt -output nikto-report.txt

# Check for sensitive file exposure
nikto -h https://amazingpay.com -Tuning 6 -Format txt
```

#### **Dirb Directory Brute Force**
```bash
# Install dirb
sudo apt-get install dirb

# Scan for hidden directories and files
dirb https://amazingpay.com /usr/share/dirb/wordlists/common.txt

# Custom wordlist for sensitive files
echo -e ".env\n.env.production\ntsconfig.json\npackage.json\n.git\nconfig\nsrc\nnode_modules" > sensitive-files.txt
dirb https://amazingpay.com sensitive-files.txt
```

#### **Gobuster Fast Directory Scanner**
```bash
# Install gobuster
sudo apt-get install gobuster

# Scan for directories
gobuster dir -u https://amazingpay.com -w /usr/share/wordlists/dirb/common.txt

# Scan for specific file extensions
gobuster dir -u https://amazingpay.com -w /usr/share/wordlists/dirb/common.txt -x .env,.json,.ts,.js,.config,.log
```

#### **Custom Security Audit Script**
```bash
#!/bin/bash
# File: scripts/comprehensive-security-audit.sh

echo "🔍 COMPREHENSIVE SECURITY AUDIT"
echo "==============================="

DOMAIN="https://amazingpay.com"
REPORT_DIR="security-audit-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$REPORT_DIR"

echo "1. Testing file access protection..."
bash scripts/security-test.sh > "$REPORT_DIR/file-access-test.txt"

echo "2. Running Nikto vulnerability scan..."
nikto -h "$DOMAIN" -Format txt -output "$REPORT_DIR/nikto-scan.txt"

echo "3. Scanning for sensitive files..."
gobuster dir -u "$DOMAIN" -w sensitive-files.txt -q > "$REPORT_DIR/sensitive-files-scan.txt"

echo "4. Testing SSL/TLS configuration..."
sslscan "$DOMAIN" > "$REPORT_DIR/ssl-scan.txt"

echo "5. Checking security headers..."
curl -I "$DOMAIN" > "$REPORT_DIR/security-headers.txt"

echo ""
echo "🏆 AUDIT COMPLETE - Reports saved to: $REPORT_DIR"
```

---

## 🔒 **ADDITIONAL SECURITY RECOMMENDATIONS**

### **Environment Variable Protection**

#### **Secure Environment Management**
```bash
# 1. Use dedicated secrets management
# Install HashiCorp Vault or AWS Secrets Manager

# 2. Environment file encryption
gpg --symmetric --cipher-algo AES256 .env
# Store .env.gpg, delete .env

# 3. Runtime environment injection
export DATABASE_URL="postgresql://..."
export JWT_SECRET="..."
# Never store in files on production

# 4. Docker secrets (if using containers)
echo "super-secret-password" | docker secret create db_password -
```

### **Advanced Security Measures**

#### **Web Application Firewall (WAF)**
```nginx
# ModSecurity rules for Nginx
load_module modules/ngx_http_modsecurity_module.so;

http {
    modsecurity on;
    modsecurity_rules_file /etc/nginx/modsec/main.conf;
}
```

#### **Intrusion Detection**
```bash
# Install and configure fail2ban
sudo apt-get install fail2ban

# Create custom jail for file access attempts
cat > /etc/fail2ban/jail.local << EOF
[nginx-sensitive-files]
enabled = true
port = http,https
filter = nginx-sensitive-files
logpath = /var/log/nginx/access.log
maxretry = 3
bantime = 3600
EOF
```

### **Monitoring and Alerting**

#### **Real-time Security Monitoring**
```bash
# Monitor for sensitive file access attempts
tail -f /var/log/nginx/access.log | grep -E "\.(env|config|json|ts)" | while read line; do
    echo "🚨 SECURITY ALERT: Sensitive file access attempt - $line"
    # Send alert to security team
done
```

---

## ✅ **DEPLOYMENT SECURITY CHECKLIST**

### **Pre-Deployment Verification**
- [ ] ✅ Web root contains ONLY public files
- [ ] ✅ All sensitive files are outside web root
- [ ] ✅ Web server configuration blocks sensitive files
- [ ] ✅ Directory listing is disabled
- [ ] ✅ File permissions are properly set
- [ ] ✅ Environment variables are secured
- [ ] ✅ Security headers are configured
- [ ] ✅ SSL/TLS is properly configured
- [ ] ✅ Rate limiting is enabled
- [ ] ✅ Security testing has been completed

### **Post-Deployment Validation**
- [ ] ✅ All sensitive file access tests pass
- [ ] ✅ Directory listing returns 403/404
- [ ] ✅ Security headers are present
- [ ] ✅ SSL configuration is secure
- [ ] ✅ No sensitive information in error messages
- [ ] ✅ Monitoring and alerting is active
- [ ] ✅ Backup and recovery procedures tested
- [ ] ✅ Incident response plan is ready

---

**🔒 SECURITY CLASSIFICATION**: Critical - Implementation Required  
**📅 LAST UPDATED**: [Current Date]  
**🔄 REVIEW CYCLE**: Before each deployment  
**✅ APPROVAL**: Security Team Required
