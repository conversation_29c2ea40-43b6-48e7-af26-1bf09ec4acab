# PostgreSQL Database Setup Guide for AmazingPay

This guide provides step-by-step instructions for setting up a PostgreSQL database for the AmazingPay application.

## 1. Install PostgreSQL

### Windows

1. Download the PostgreSQL installer from the [official website](https://www.postgresql.org/download/windows/).
2. Run the installer and follow the installation wizard.
3. When prompted, set a password for the `postgres` user. Remember this password as you'll need it later.
4. Keep the default port (5432) unless you have a specific reason to change it.
5. Complete the installation.

### macOS

Using Homebrew:
```bash
brew install postgresql
brew services start postgresql
```

### Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Linux (RHEL/CentOS/Fedora)

```bash
sudo dnf install postgresql-server postgresql-contrib
sudo postgresql-setup --initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 2. Create the Database

After installing PostgreSQL, you need to create a database for the AmazingPay application.

### Using psql Command Line

1. Open a terminal or command prompt.
2. Log in to PostgreSQL as the postgres user:

   **Windows:**
   ```bash
   psql -U postgres
   ```

   **macOS/Linux:**
   ```bash
   sudo -u postgres psql
   ```

3. Create the database:
   ```sql
   CREATE DATABASE amazingpay;
   ```

4. Create a user (optional, you can use the postgres user):
   ```sql
   CREATE USER amazingpay_user WITH ENCRYPTED PASSWORD 'your_password';
   ```

5. Grant privileges to the user:
   ```sql
   GRANT ALL PRIVILEGES ON DATABASE amazingpay TO amazingpay_user;
   ```

6. Exit psql:
   ```sql
   \q
   ```

### Using pgAdmin

1. Install pgAdmin from the [official website](https://www.pgadmin.org/download/).
2. Open pgAdmin and connect to your PostgreSQL server.
3. Right-click on "Databases" and select "Create" > "Database".
4. Enter "amazingpay" as the database name and click "Save".

## 3. Update Environment Variables

Update the `.env` file in the AmazingPay application with your database connection details:

```
# Database Configuration
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=amazingpay
DB_SSL=false
DB_CONNECTION_POOL_MIN=5
DB_CONNECTION_POOL_MAX=20
DB_STATEMENT_TIMEOUT=30000
```

Replace `your_password` with the password you set for the PostgreSQL user.

## 4. Run Database Migrations

After setting up the database and updating the environment variables, run the database migrations to create the necessary tables:

```bash
cd AMAZINGPAY\ APP
npm run prisma:generate
npm run prisma:migrate
```

## 5. Seed the Database

Seed the database with initial data:

```bash
cd AMAZINGPAY\ APP
npm run prisma:seed
```

## 6. Verify the Setup

To verify that the database is set up correctly, you can run the following command:

```bash
cd AMAZINGPAY\ APP
npx prisma studio
```

This will open Prisma Studio in your browser, where you can view and manage your database.

## Troubleshooting

### Connection Issues

If you encounter connection issues, check the following:

1. Ensure PostgreSQL is running:
   ```bash
   # Windows
   sc query postgresql

   # macOS
   brew services list

   # Linux
   sudo systemctl status postgresql
   ```

2. Verify your connection details in the `.env` file.

3. Check PostgreSQL authentication settings in `pg_hba.conf`:
   - Windows: `C:\Program Files\PostgreSQL\<version>\data\pg_hba.conf`
   - macOS: `/usr/local/var/postgres/pg_hba.conf`
   - Linux: `/etc/postgresql/<version>/main/pg_hba.conf`

### Migration Issues

If you encounter issues with migrations:

1. Reset the database (warning: this will delete all data):
   ```bash
   npx prisma migrate reset
   ```

2. Check the Prisma schema file for errors:
   ```bash
   npx prisma validate
   ```

## Additional Resources

- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Prisma Documentation](https://www.prisma.io/docs/)
- [Prisma Migration Guide](https://www.prisma.io/docs/concepts/components/prisma-migrate)
