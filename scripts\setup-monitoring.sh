#!/bin/bash

# AmazingPay Flow - Monitoring & Backup Setup Script
# This script sets up comprehensive monitoring and backup systems

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}📊 AmazingPay Flow - Monitoring & Backup Setup${NC}"
    echo -e "${BLUE}==============================================\n${NC}"
}

print_status() {
    echo -e "${CYAN}📦 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to create monitoring directories
create_monitoring_structure() {
    print_status "Creating monitoring directory structure..."
    
    mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources},alerts}
    mkdir -p logs/{application,nginx,database}
    mkdir -p backups/{database,application,logs}
    
    print_success "Monitoring directories created"
}

# Function to create Prometheus configuration
create_prometheus_config() {
    print_status "Creating Prometheus configuration..."
    
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'amazingpay-app'
    static_configs:
      - targets: ['amazingpay-app:3002']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
EOF
    
    print_success "Prometheus configuration created"
}

# Function to create Grafana datasource
create_grafana_datasource() {
    print_status "Creating Grafana datasource configuration..."
    
    cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF
    
    print_success "Grafana datasource created"
}

# Function to create Grafana dashboard
create_grafana_dashboard() {
    print_status "Creating Grafana dashboard..."
    
    cat > monitoring/grafana/dashboards/amazingpay-dashboard.json << 'EOF'
{
  "dashboard": {
    "id": null,
    "title": "AmazingPay Flow Dashboard",
    "tags": ["amazingpay"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Application Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"amazingpay-app\"}",
            "legendFormat": "App Status"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends",
            "legendFormat": "Active Connections"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      }
    ],
    "time": {"from": "now-1h", "to": "now"},
    "refresh": "30s"
  }
}
EOF
    
    print_success "Grafana dashboard created"
}

# Function to create alert rules
create_alert_rules() {
    print_status "Creating alert rules..."
    
    cat > monitoring/alerts/amazingpay-alerts.yml << 'EOF'
groups:
  - name: amazingpay-alerts
    rules:
      - alert: ApplicationDown
        expr: up{job="amazingpay-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AmazingPay application is down"
          description: "The AmazingPay application has been down for more than 1 minute."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is above 2 seconds."

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database has more than 80 active connections."

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is below 10%."

      - alert: MemoryUsageHigh
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90%."
EOF
    
    print_success "Alert rules created"
}

# Function to create backup script
create_backup_script() {
    print_status "Creating backup script..."
    
    cat > scripts/backup-system.sh << 'EOF'
#!/bin/bash

# AmazingPay Flow - Comprehensive Backup Script
# This script creates backups of database, application, and logs

set -e

# Configuration
BACKUP_DIR="backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to backup database
backup_database() {
    echo "📦 Backing up database..."
    
    DB_BACKUP_DIR="$BACKUP_DIR/database"
    mkdir -p $DB_BACKUP_DIR
    
    # Load environment variables
    source .env.production
    
    # Create database backup
    pg_dump $DATABASE_URL > "$DB_BACKUP_DIR/amazingpay_$DATE.sql"
    
    # Compress backup
    gzip "$DB_BACKUP_DIR/amazingpay_$DATE.sql"
    
    print_success "Database backup created: amazingpay_$DATE.sql.gz"
}

# Function to backup application files
backup_application() {
    echo "📦 Backing up application files..."
    
    APP_BACKUP_DIR="$BACKUP_DIR/application"
    mkdir -p $APP_BACKUP_DIR
    
    # Create application backup
    tar -czf "$APP_BACKUP_DIR/app_$DATE.tar.gz" \
        --exclude=node_modules \
        --exclude=dist \
        --exclude=logs \
        --exclude=backups \
        .
    
    print_success "Application backup created: app_$DATE.tar.gz"
}

# Function to backup logs
backup_logs() {
    echo "📦 Backing up logs..."
    
    LOG_BACKUP_DIR="$BACKUP_DIR/logs"
    mkdir -p $LOG_BACKUP_DIR
    
    if [ -d "logs" ]; then
        tar -czf "$LOG_BACKUP_DIR/logs_$DATE.tar.gz" logs/
        print_success "Logs backup created: logs_$DATE.tar.gz"
    else
        print_warning "No logs directory found"
    fi
}

# Function to cleanup old backups
cleanup_old_backups() {
    echo "🧹 Cleaning up old backups..."
    
    # Remove backups older than retention period
    find $BACKUP_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete
    find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete
    
    print_success "Old backups cleaned up (older than $RETENTION_DAYS days)"
}

# Function to verify backups
verify_backups() {
    echo "🔍 Verifying backups..."
    
    # Check if backup files exist and are not empty
    LATEST_DB_BACKUP=$(ls -t $BACKUP_DIR/database/*.gz 2>/dev/null | head -1)
    LATEST_APP_BACKUP=$(ls -t $BACKUP_DIR/application/*.tar.gz 2>/dev/null | head -1)
    
    if [ -f "$LATEST_DB_BACKUP" ] && [ -s "$LATEST_DB_BACKUP" ]; then
        print_success "Database backup verified"
    else
        print_error "Database backup verification failed"
    fi
    
    if [ -f "$LATEST_APP_BACKUP" ] && [ -s "$LATEST_APP_BACKUP" ]; then
        print_success "Application backup verified"
    else
        print_error "Application backup verification failed"
    fi
}

# Main backup process
main() {
    echo "🔄 Starting AmazingPay Flow backup process..."
    echo "============================================="
    
    backup_database
    backup_application
    backup_logs
    cleanup_old_backups
    verify_backups
    
    echo ""
    print_success "🎉 Backup process completed successfully!"
    echo "📊 Backup summary:"
    echo "   Database: $(ls -lh $BACKUP_DIR/database/*$DATE* 2>/dev/null | awk '{print $5}' || echo 'N/A')"
    echo "   Application: $(ls -lh $BACKUP_DIR/application/*$DATE* 2>/dev/null | awk '{print $5}' || echo 'N/A')"
    echo "   Logs: $(ls -lh $BACKUP_DIR/logs/*$DATE* 2>/dev/null | awk '{print $5}' || echo 'N/A')"
}

# Run backup
main "$@"
EOF
    
    chmod +x scripts/backup-system.sh
    print_success "Backup script created"
}

# Function to create health check script
create_health_check_script() {
    print_status "Creating health check script..."
    
    cat > scripts/health-check.sh << 'EOF'
#!/bin/bash

# AmazingPay Flow - Comprehensive Health Check Script
# This script monitors all system components and sends alerts

set -e

# Configuration
HEALTH_URL="http://localhost:3002/api/health"
ALERT_EMAIL="<EMAIL>"
LOG_FILE="logs/health-check.log"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

# Function to check application health
check_application() {
    echo "🔍 Checking application health..."
    
    if curl -f $HEALTH_URL > /dev/null 2>&1; then
        print_success "Application is healthy"
        log_message "Application health check: PASS"
        return 0
    else
        print_error "Application health check failed"
        log_message "Application health check: FAIL"
        return 1
    fi
}

# Function to check database
check_database() {
    echo "🔍 Checking database connection..."
    
    # Load environment variables
    source .env.production
    
    if psql $DATABASE_URL -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "Database is accessible"
        log_message "Database health check: PASS"
        return 0
    else
        print_error "Database connection failed"
        log_message "Database health check: FAIL"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    echo "🔍 Checking disk space..."
    
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ $DISK_USAGE -lt 80 ]; then
        print_success "Disk space is adequate ($DISK_USAGE% used)"
        log_message "Disk space check: PASS ($DISK_USAGE% used)"
        return 0
    elif [ $DISK_USAGE -lt 90 ]; then
        print_warning "Disk space is getting low ($DISK_USAGE% used)"
        log_message "Disk space check: WARNING ($DISK_USAGE% used)"
        return 1
    else
        print_error "Disk space is critically low ($DISK_USAGE% used)"
        log_message "Disk space check: CRITICAL ($DISK_USAGE% used)"
        return 1
    fi
}

# Function to check memory usage
check_memory() {
    echo "🔍 Checking memory usage..."
    
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ $MEMORY_USAGE -lt 80 ]; then
        print_success "Memory usage is normal ($MEMORY_USAGE% used)"
        log_message "Memory check: PASS ($MEMORY_USAGE% used)"
        return 0
    elif [ $MEMORY_USAGE -lt 90 ]; then
        print_warning "Memory usage is high ($MEMORY_USAGE% used)"
        log_message "Memory check: WARNING ($MEMORY_USAGE% used)"
        return 1
    else
        print_error "Memory usage is critical ($MEMORY_USAGE% used)"
        log_message "Memory check: CRITICAL ($MEMORY_USAGE% used)"
        return 1
    fi
}

# Function to send alert
send_alert() {
    local message="$1"
    echo "📧 Sending alert: $message"
    
    # Send email alert (requires mail command)
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "AmazingPay Flow Alert" $ALERT_EMAIL
    fi
    
    log_message "Alert sent: $message"
}

# Main health check
main() {
    echo "🏥 AmazingPay Flow - Health Check"
    echo "================================="
    
    mkdir -p logs
    
    FAILED_CHECKS=0
    
    # Run all checks
    check_application || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    check_database || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    check_disk_space || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    check_memory || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        print_success "🎉 All health checks passed!"
        log_message "Health check summary: ALL PASS"
    else
        print_error "❌ $FAILED_CHECKS health check(s) failed!"
        log_message "Health check summary: $FAILED_CHECKS FAILED"
        send_alert "AmazingPay Flow health check failed: $FAILED_CHECKS issue(s) detected"
    fi
    
    exit $FAILED_CHECKS
}

# Run health check
main "$@"
EOF
    
    chmod +x scripts/health-check.sh
    print_success "Health check script created"
}

# Function to setup cron jobs
setup_cron_jobs() {
    print_status "Setting up cron jobs..."
    
    # Create cron job entries
    cat > scripts/setup-cron.sh << 'EOF'
#!/bin/bash

# Setup cron jobs for AmazingPay Flow

# Add cron jobs
(crontab -l 2>/dev/null; echo "# AmazingPay Flow automated tasks") | crontab -
(crontab -l 2>/dev/null; echo "0 2 * * * cd $(pwd) && ./scripts/backup-system.sh") | crontab -
(crontab -l 2>/dev/null; echo "*/5 * * * * cd $(pwd) && ./scripts/health-check.sh") | crontab -
(crontab -l 2>/dev/null; echo "0 0 * * 0 cd $(pwd) && ./scripts/monitor-services.sh") | crontab -

echo "✅ Cron jobs set up successfully:"
echo "   - Daily backup at 2:00 AM"
echo "   - Health check every 5 minutes"
echo "   - Weekly service monitoring"
EOF
    
    chmod +x scripts/setup-cron.sh
    print_success "Cron setup script created"
}

# Main execution
main() {
    print_header
    
    create_monitoring_structure
    create_prometheus_config
    create_grafana_datasource
    create_grafana_dashboard
    create_alert_rules
    create_backup_script
    create_health_check_script
    setup_cron_jobs
    
    echo ""
    print_success "🎉 Monitoring & backup system setup completed!"
    echo ""
    print_info "📋 Created components:"
    print_info "   ✅ Prometheus monitoring"
    print_info "   ✅ Grafana dashboards"
    print_info "   ✅ Alert rules"
    print_info "   ✅ Backup system"
    print_info "   ✅ Health checks"
    print_info "   ✅ Cron job automation"
    echo ""
    print_info "🔧 Next steps:"
    print_info "1. Run: ./scripts/setup-cron.sh (to enable automated tasks)"
    print_info "2. Start monitoring: docker-compose --profile monitoring up -d"
    print_info "3. Access Grafana: http://localhost:3001"
    print_info "4. Test backup: ./scripts/backup-system.sh"
    print_info "5. Test health check: ./scripts/health-check.sh"
}

# Run main function
main "$@"
