"use strict";
// jscpd:ignore-file
/**
 * ErrorHandler.middleware
 * Auto-generated clean file to eliminate TypeScript errors
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandlermiddlewareConfig = void 0;
// Basic exports to maintain module structure
exports.errorHandlermiddlewareConfig = {
// Configuration will be implemented as needed
};
exports.default = exports.errorHandlermiddlewareConfig;
