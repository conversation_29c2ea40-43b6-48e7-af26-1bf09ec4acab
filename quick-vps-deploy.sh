#!/bin/bash

# 🚀 QUICK VPS DEPLOYMENT SCRIPT FOR AMA<PERSON>INGPAY
# VPS: ************ | Domain: amazingpayme.com

set -e

# 🎯 CONFIGURATION
VPS_IP="************"
DOMAIN="amazingpayme.com"
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"

# 🎨 COLORS
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 AmazingPay Quick VPS Deployment${NC}"
echo -e "${CYAN}===================================${NC}"
echo ""
echo -e "${BLUE}VPS IP:${NC} $VPS_IP"
echo -e "${BLUE}Domain:${NC} $DOMAIN"
echo -e "${BLUE}Database:${NC} $DB_NAME"
echo ""

# 📦 STEP 1: BUILD APPLICATION
echo -e "${YELLOW}📦 Step 1: Building application...${NC}"
npm ci
npm run build
echo -e "${GREEN}✅ Application built successfully${NC}"
echo ""

# 🗄️ STEP 2: PREPARE DATABASE
echo -e "${YELLOW}🗄️ Step 2: Preparing database configuration...${NC}"
npx prisma generate
echo -e "${GREEN}✅ Prisma client generated${NC}"
echo ""

# 📁 STEP 3: PREPARE FILES
echo -e "${YELLOW}📁 Step 3: Preparing deployment files...${NC}"

# Make scripts executable
chmod +x scripts/vps-deployment.sh
chmod +x scripts/setup-production-database.sh

# Create deployment package
echo -e "${BLUE}Creating deployment package...${NC}"
tar -czf amazingpay-deployment.tar.gz \
    --exclude=node_modules \
    --exclude=.git \
    --exclude=coverage \
    --exclude=test-reports \
    --exclude=logs \
    --exclude=backups \
    .

echo -e "${GREEN}✅ Deployment package created: amazingpay-deployment.tar.gz${NC}"
echo ""

# 🚀 STEP 4: DEPLOYMENT INSTRUCTIONS
echo -e "${YELLOW}🚀 Step 4: VPS Deployment Instructions${NC}"
echo ""
echo -e "${CYAN}Now run these commands on your VPS (************):${NC}"
echo ""
echo -e "${BLUE}# 1. Upload the deployment package:${NC}"
echo "scp amazingpay-deployment.tar.gz root@$VPS_IP:/tmp/"
echo ""
echo -e "${BLUE}# 2. SSH to VPS and extract:${NC}"
echo "ssh root@$VPS_IP"
echo "mkdir -p /www/wwwroot/$DOMAIN"
echo "cd /www/wwwroot/$DOMAIN"
echo "tar -xzf /tmp/amazingpay-deployment.tar.gz"
echo ""
echo -e "${BLUE}# 3. Run automated deployment:${NC}"
echo "chmod +x scripts/vps-deployment.sh"
echo "./scripts/vps-deployment.sh"
echo ""
echo -e "${BLUE}# 4. Verify deployment:${NC}"
echo "curl https://$DOMAIN/api/health"
echo ""

# 📋 STEP 5: MANUAL DEPLOYMENT OPTION
echo -e "${YELLOW}📋 Alternative: Manual Deployment Steps${NC}"
echo ""
echo -e "${CYAN}If you prefer manual deployment:${NC}"
echo ""
echo -e "${BLUE}1. System Setup:${NC}"
echo "   apt update && apt upgrade -y"
echo "   apt install -y nodejs npm postgresql nginx certbot python3-certbot-nginx"
echo "   npm install -g pm2"
echo ""
echo -e "${BLUE}2. Database Setup:${NC}"
echo "   systemctl start postgresql"
echo "   sudo -u postgres psql -c \"ALTER USER postgres PASSWORD '$DB_PASSWORD';\""
echo "   sudo -u postgres psql -c \"CREATE DATABASE \\\"$DB_NAME\\\";\""
echo ""
echo -e "${BLUE}3. Application Setup:${NC}"
echo "   cd /www/wwwroot/$DOMAIN"
echo "   npm ci --production"
echo "   npx prisma generate"
echo "   npx prisma migrate deploy"
echo "   pm2 start ecosystem.config.js"
echo ""
echo -e "${BLUE}4. Nginx & SSL:${NC}"
echo "   cp deployment/nginx-amazingpay.conf /etc/nginx/sites-available/$DOMAIN"
echo "   ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/"
echo "   nginx -t && systemctl restart nginx"
echo "   certbot --nginx -d $DOMAIN -d www.$DOMAIN"
echo ""

# 🔧 STEP 6: CONFIGURATION SUMMARY
echo -e "${YELLOW}🔧 Configuration Summary${NC}"
echo ""
echo -e "${CYAN}Environment Variables Updated:${NC}"
echo "  NODE_ENV=production"
echo "  PORT=3002"
echo "  HOST=0.0.0.0"
echo "  DOMAIN=$DOMAIN"
echo "  VPS_IP=$VPS_IP"
echo "  DATABASE_URL=postgresql://postgres:$DB_PASSWORD@localhost:5432/$DB_NAME"
echo ""
echo -e "${CYAN}Files Created/Updated:${NC}"
echo "  ✅ .env.production"
echo "  ✅ scripts/vps-deployment.sh"
echo "  ✅ scripts/setup-production-database.sh"
echo "  ✅ ecosystem.config.js"
echo "  ✅ deployment/nginx-amazingpay.conf"
echo "  ✅ VPS_DEPLOYMENT_GUIDE.md"
echo ""

# 🎯 STEP 7: NEXT STEPS
echo -e "${YELLOW}🎯 Next Steps${NC}"
echo ""
echo -e "${GREEN}1.${NC} Upload deployment package to VPS"
echo -e "${GREEN}2.${NC} Run automated deployment script"
echo -e "${GREEN}3.${NC} Verify application is running"
echo -e "${GREEN}4.${NC} Test all endpoints"
echo -e "${GREEN}5.${NC} Configure monitoring and backups"
echo ""

# 📞 STEP 8: SUPPORT INFORMATION
echo -e "${YELLOW}📞 Support & Troubleshooting${NC}"
echo ""
echo -e "${CYAN}If you encounter issues:${NC}"
echo "  🔍 Check logs: pm2 logs amazingpay-main"
echo "  🗄️ Test database: sudo -u postgres psql -d $DB_NAME -c \"SELECT 1;\""
echo "  🌐 Test Nginx: nginx -t"
echo "  🔥 Check firewall: ufw status"
echo ""
echo -e "${CYAN}Important URLs after deployment:${NC}"
echo "  🌐 Website: https://$DOMAIN"
echo "  🔗 API Health: https://$DOMAIN/api/health"
echo "  📊 API Docs: https://$DOMAIN/api/docs"
echo ""

echo -e "${GREEN}🎉 Quick deployment preparation completed!${NC}"
echo -e "${CYAN}Your AmazingPay application is ready for VPS deployment.${NC}"
echo ""
echo -e "${YELLOW}⚡ Run the upload command above to start deployment!${NC}"
