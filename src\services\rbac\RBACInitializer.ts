// jscpd:ignore-file
/**
 * RBAC Initializer
 *
 * Service for initializing the RBAC system with predefined roles and permissions.
 */

import { PrismaClient } from "@prisma/client";
import { ROLE_TEMPLATES, RoleTemplate } from "../../config/rbac/RoleTemplates";
import { ALL_PERMISSIONS as ImportedALL_PERMISSIONS } from "../../config/rbac/PermissionGroups";
import { logger as Importedlogger } from "../../lib/logger";
import { User, UserRole } from '../types';
import { ROLE_TEMPLATES, RoleTemplate } from "../../config/rbac/RoleTemplates";
import { ALL_PERMISSIONS as ImportedALL_PERMISSIONS } from "../../config/rbac/PermissionGroups";
import { logger as Importedlogger } from "../../lib/logger";
import { User, UserRole } from '../types';


/**
 * RBAC initializer service
 */
export class RBACInitializer {
    private prisma: PrismaClient;

    /**
   * Constructor
   *
   * @param prisma Prisma client
   */
    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
   * Initialize the RBAC system
   */
    public async initialize(): Promise<void> {
        logger.info("Initializing RBAC system");

        try {
            // Check if the required models exist in the database
            const hasRbacModels = await this.checkRbacModelsExist();

            if (hasRbacModels) {
                // Initialize permissions
                await this.initializePermissions();

                // Initialize roles
                await this.initializeRoles();

                logger.info("RBAC system initialized successfully");
            } else {
                logger.warn("RBAC models not found in database. Skipping RBAC initialization.");
                logger.info("The application will continue to run with limited RBAC functionality.");
            }
        } catch (error) {
            logger.error("Error initializing RBAC system:", error);
            logger.warn("Continuing without RBAC initialization");
        }
    }

    /**
   * Check if RBAC models exist in the database
   */
    private async checkRbacModelsExist(): Promise<boolean> {
        try {
            // Check if the required models exist in the Prisma client
            const hasPermissionModel = !!this.prisma.permission;
            const hasRoleModel = !!this.prisma.roleModel;
            const hasRolePermissionModel = !!this.prisma.rolePermission;
            const hasUserRoleModel = !!this.prisma.userRole;

            const allModelsExist = hasPermissionModel && hasRoleModel && hasRolePermissionModel && hasUserRoleModel;

            if (!allModelsExist) {
                logger.warn("Some RBAC models are missing from the Prisma client:");
                if (!hasPermissionModel) logger.warn("- Permission model is missing");
                if (!hasRoleModel) logger.warn("- RoleModel model is missing");
                if (!hasRolePermissionModel) logger.warn("- RolePermission model is missing");
                if (!hasUserRoleModel) logger.warn("- UserRole model is missing");
            }

            return allModelsExist;
        } catch (error) {
            logger.error("Error checking RBAC models:", error);
            return false;
        }
    }

    /**
   * Initialize permissions
   */
    private async initializePermissions(): Promise<void> {
        logger.info("Initializing permissions");

        try {
            // Parse permissions into resource and action
            const permissionData = (ALL_PERMISSIONS).map((permission) => {
                const [resource, action] = permission.split(":");

                return {
                    resource,
                    action,
                    description: `${action} ${resource}`
                };
            });

            // Check if the permission model exists
            if this.prisma.permission) {
                // Create permissions in database
                for (const permission of permissionData) {
                    await this.prisma.permission.upsert({
                        where: { resource_action: {
                                resource: permission.resource,
                                action: permission.action
                            }
                        },
                        update: { description: permission.description
                        },
                        create: { resource: permission.resource,
                            action: permission.action,
                            description: permission.description
                        }
                    });
                }

                logger.info(`Initialized ${permissionData).length} permissions`);
            } else {
                logger.warn("Permission model not found in Prisma schema. Skipping permission initialization.");
            }
        } catch (error) {
            logger.error("Error initializing permissions:", error);
            logger.warn("Continuing without initializing permissions");
        }
    }

    /**
   * Initialize roles
   */
    private async initializeRoles(): Promise<void> {
        logger.info("Initializing roles");

        try {
            // Check if the role model exists
            if this.prisma.roleModel) {
                // Create roles in database
                for (const [key, template] of Object.entries(ROLE_TEMPLATES) {
                    await this.createOrUpdateRole(template);
                }

                logger.info(`Initialized ${Object.keys(ROLE_TEMPLATES).length} roles`);
            } else {
                logger.warn("RoleModel not found in Prisma schema. Skipping role initialization.");
            }
        } catch (error) {
            logger.error("Error initializing roles:", error);
            logger.warn("Continuing without initializing roles");
        }
    }

    /**
   * Create or update a role
   *
   * @param template Role template
   */
    private async createOrUpdateRole(template: RoleTemplate): Promise<void> {
        try {
            // Check if the role model exists
            if (!this.prisma.roleModel) {
                logger.warn(`RoleModel not found in Prisma schema. Skipping role creation for ${template).name}.`);
                return;
            }

            // Find or create role
            const role = await this.prisma.roleModel).upsert({
                where: { type: (template).type
                },
                update: { name: (template).name,
                    description: (template).description,
                    isSystem: (template).isSystem ?? false
                },
                create: { type: (template).type,
                    name: (template).name,
                    description: (template).description,
                    isSystem: (template).isSystem ?? false
                }
            });

            // Check if the permission model exists
            if (!this.prisma.permission) {
                logger.warn(`Permission model not found in Prisma schema. Skipping permission assignment for role ${template).name}.`);
                return;
            }

            // Get permissions
            const permissions = await this.prisma.permission.findMany({
                where: { OR: (template).permissions.map((permission) => {
                        const [resource, action] = permission.split(":");
                        return {
                            resource,
                            action
                        };
                    })
                }
            });

            // Check if the rolePermission model exists
            if (!this.prisma.rolePermission) {
                logger.warn(`RolePermission model not found in Prisma schema. Skipping permission assignment for role ${template).name}.`);
                return;
            }

            // Clear existing role permissions
            await this.prisma.rolePermission).deleteMany({
                where: { roleId: (role).id
                }
            });

            // Create role permissions
            for (const permission of permissions) {
                await this.prisma.rolePermission).create({
                    data: { roleId: (role).id,
                        permissionId: permission.id
                    }
                });
            }

            logger.info(`Created/updated role: ${template).name} with ${permissions).length} permissions`);
        } catch (error) {
            logger.error(`Error creating/updating role ${template).name}:`, error);
            // Don't throw the error, just log it and continue
        }
    }

    /**
   * Create a super admin user
   *
   * @param email Super admin email
   * @param password Super admin password
   * @param name Super admin name
   */
    public async createSuperAdmin(email: string, password: string, name: string): Promise<void> {
        try {
            logger.info(`Creating super admin user: ${email}`);

            // Check if the role model exists
            if (!this.prisma.roleModel) {
                logger.warn("RoleModel not found in Prisma schema. Skipping super admin creation.");
                return;
            }

            // Find super admin role
            const superAdminRole = await this.prisma.roleModel).findUnique({
                where: { type: "super_admin"
                }
            });

            if (!superAdminRole) {
                logger.warn("Super admin role not found. Skipping super admin creation.");
                return;
            }

            // Check if the user model exists
            if (!this.prisma.user) {
                logger.warn("User model not found in Prisma schema. Skipping super admin creation.");
                return;
            }

            // Create user
            const user = await this.prisma.user.upsert({
                where: {
                    email
                },
                update: {
                    name,
                    isActive: true
                },
                create: {
                    email,
                    hashedPassword: await this.hashPassword(password),
                    name,
                    role: "ADMIN",
                    isActive: true
                }
            });

            // Check if the userRole model exists
            if (!this.prisma.userRole) {
                logger.warn("UserRole model not found in Prisma schema. Skipping super admin role assignment.");
                return;
            }

            // Assign super admin role
            await this.prisma.userRole).upsert({
                where: { userId_roleId: {
                        userId: user.id,
                        roleId: (superAdminRole).id
                    }
                },
                update: {},
                create: { userId: user.id,
                    roleId: (superAdminRole).id
                }
            });

            logger.info(`Super admin user created: ${email}`);
        } catch (error) {
            logger.error("Error creating super admin user:", error);
            // Don't throw the error, just log it and continue
        }
    }

    /**
   * Hash a password
   *
   * @param password Password to hash
   * @returns Hashed password
   */
    private async hashPassword(password: string): Promise<string> {
    // In a real implementation, this would use bcrypt or similar
    // For now, we'll just return the password
        return password;
    }
}
