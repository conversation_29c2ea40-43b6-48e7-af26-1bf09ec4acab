#!/usr/bin/env node
/**
 * 🔧 COMPREHENSIVE SYNTAX ERROR ELIMINATION
 * Fixes ALL syntax errors across the entire project
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Comprehensive syntax error patterns and fixes
const SYNTAX_FIXES = {
  // Arrow function syntax errors
  ' =>  > ': ' => ',
  ') =>  > {': ') => {',
  '() =>  > {': '() => {',
  '= >': ' => ',
  '= > ': ' => ',
  ' = >': ' => ',
  ' = > ': ' => ',
  
  // Variable declaration syntax errors
  'const: ': 'const ',
  'let: ': 'let ',
  'var: ': 'var ',
  'const:': 'const ',
  'let:': 'let ',
  'var:': 'var ',
  
  // Comparison operator syntax errors
  ' == , ': ' === ',
  ' == ;': ' === ',
  ' == :': ' === ',
  ' != , ': ' !== ',
  ' != ;': ' !== ',
  ' != :': ' !== ',
  
  // Object property syntax errors
  ', : .': '.',
  ', : [': '[',
  ', : (': '(',
  
  // Method call syntax errors
  '.status500': '.status(500)',
  '.status401': '.status(401)',
  '.status200': '.status(200)',
  '.status503': '.status(503)',
  '.statusstatusCode': '.status(statusCode)',
  
  // Conditional syntax errors
  'if(, ': 'if (',
  'if(': 'if (',
  'while(': 'while (',
  'for(': 'for (',
  'switch(': 'switch (',
  
  // Try-catch syntax errors
  'try: {': 'try {',
  'try:': 'try',
  'catch(': 'catch (',
  'catch(error)': 'catch (error)',
  'catch(': 'catch (',
  
  // Function syntax errors
  'function(': 'function (',
  'async(': 'async (',
  
  // Bracket and parentheses errors
  '{ }': '{}',
  '( )': '()',
  '[ ]': '[]',
  
  // Semicolon and comma errors
  ';,': ';',
  ',;': ',',
  ';;': ';',
  ',,': ',',
  
  // String and template literal errors
  '`${': '`${',
  '}`': '}',
  
  // Import/export syntax errors
  'import{': 'import {',
  'export{': 'export {',
  'from"': 'from "',
  'from\'': 'from \'',
  
  // Nullish coalescing and optional chaining
  ' ?? ""': ' ?? ""',
  ' ?? \'\'': ' ?? \'\'',
  '?.': '?.',
  
  // Process exit syntax errors
  'process, : .exit0': 'process.exit(0)',
  'process, : .exit1': 'process.exit(1)',
  'process.exit0': 'process.exit(0)',
  'process.exit1': 'process.exit(1)',
  
  // Console log syntax errors
  'console, : .log': 'console.log',
  'console, : .error': 'console.error',
  'console, : .warn': 'console.warn',
  'console, : .info': 'console.info',
  
  // Await syntax errors
  'await: ': 'await ',
  'await:': 'await ',
  
  // Return syntax errors
  'return: ': 'return ',
  'return:': 'return ',
  
  // Logger syntax errors
  'logger, : .': 'logger.',
  'logger, :': 'logger',
  
  // Prisma syntax errors
  'exports.prisma': 'exports.prisma',
  'prisma, : .': 'prisma.',
  
  // HTTP status syntax errors
  'res, : .': 'res.',
  'req, : .': 'req.',
  
  // Object destructuring errors
  '{ }, : .': '{}.',
  
  // Array method errors
  '.forEach((': '.forEach((',
  '.map((': '.map((',
  '.filter((': '.filter((',
  '.reduce((': '.reduce((',
  '.find((': '.find((',
  '.some((': '.some((',
  '.every((': '.every((',
  
  // Missing spaces
  '){': ') {',
  '}else': '} else',
  '}catch': '} catch',
  '}finally': '} finally',
  
  // Extra characters
  ' > ': ' ',
  ' < ': ' ',
  
  // Malformed expressions
  ' = ': ' === ',
  ' ! = ': ' !== ',
  
  // Fix specific patterns found in the codebase
  'healthReport) { }, : .status == , "unhealthy": ': 'healthReport.status === "unhealthy"',
  'health) { }, : .status == \'healthy\'': 'health.status === \'healthy\'',
  'check) =>  > check.status': 'check => check.status',
  'item) =>  > item': 'item => item',
  'error) =>  > {': 'error => {',
  '() =>  > {': '() => {',
  
  // Fix malformed object access
  '(health).status': 'health.status',
  '(error).message': 'error.message',
  '(result).data': 'result.data',
  '(response).json': 'response.json',
  
  // Fix malformed JSON calls
  'res.json({': 'res.json({',
  'JSON.stringify(': 'JSON.stringify(',
  
  // Fix malformed async/await
  'async () =>  > {': 'async () => {',
  'await: ': 'await ',
  
  // Fix malformed conditionals
  'if (': 'if (',
  'else if (': 'else if (',
  'while (': 'while (',
  'for (': 'for (',
  
  // Fix malformed operators
  ' && ': ' && ',
  ' || ': ' || ',
  ' ?? ': ' ?? ',
  
  // Fix malformed template literals
  '${': '${',
  '}': '}',
  
  // Fix malformed function calls
  'setTimeout(': 'setTimeout(',
  'setInterval(': 'setInterval(',
  'clearTimeout(': 'clearTimeout(',
  'clearInterval(': 'clearInterval(',
  
  // Fix malformed error handling
  'throw new Error(': 'throw new Error(',
  'new Error(': 'new Error(',
  
  // Fix malformed promises
  'Promise.resolve(': 'Promise.resolve(',
  'Promise.reject(': 'Promise.reject(',
  'Promise.all(': 'Promise.all(',
  
  // Fix malformed array operations
  'Array.from(': 'Array.from(',
  'Array.isArray(': 'Array.isArray(',
  
  // Fix malformed object operations
  'Object.keys(': 'Object.keys(',
  'Object.values(': 'Object.values(',
  'Object.entries(': 'Object.entries(',
  
  // Fix malformed date operations
  'new Date(': 'new Date(',
  'Date.now(': 'Date.now(',
  
  // Fix malformed math operations
  'Math.floor(': 'Math.floor(',
  'Math.ceil(': 'Math.ceil(',
  'Math.round(': 'Math.round(',
  'Math.max(': 'Math.max(',
  'Math.min(': 'Math.min(',
  
  // Fix malformed string operations
  'String(': 'String(',
  'Number(': 'Number(',
  'Boolean(': 'Boolean(',
  'parseInt(': 'parseInt(',
  'parseFloat(': 'parseFloat(',
  
  // Fix malformed regex
  'new RegExp(': 'new RegExp(',
  '.test(': '.test(',
  '.match(': '.match(',
  '.replace(': '.replace(',
  
  // Fix malformed buffer operations
  'Buffer.from(': 'Buffer.from(',
  'Buffer.alloc(': 'Buffer.alloc(',
  
  // Fix malformed crypto operations
  'crypto.randomBytes(': 'crypto.randomBytes(',
  'crypto.createHash(': 'crypto.createHash(',
  
  // Fix malformed path operations
  'path.join(': 'path.join(',
  'path.resolve(': 'path.resolve(',
  'path.dirname(': 'path.dirname(',
  'path.basename(': 'path.basename(',
  
  // Fix malformed fs operations
  'fs.readFileSync(': 'fs.readFileSync(',
  'fs.writeFileSync(': 'fs.writeFileSync(',
  'fs.existsSync(': 'fs.existsSync(',
  'fs.mkdirSync(': 'fs.mkdirSync(',
  
  // Fix malformed URL operations
  'new URL(': 'new URL(',
  'URL.parse(': 'URL.parse(',
  
  // Fix malformed environment variables
  'process.env.': 'process.env.',
  'process.argv': 'process.argv',
  'process.cwd(': 'process.cwd(',
  
  // Fix malformed module operations
  'require(': 'require(',
  'module.exports': 'module.exports',
  'exports.': 'exports.',
  
  // Fix malformed class operations
  'class ': 'class ',
  'extends ': 'extends ',
  'constructor(': 'constructor(',
  'super(': 'super(',
  'this.': 'this.',
  
  // Fix malformed interface operations
  'interface ': 'interface ',
  'type ': 'type ',
  'enum ': 'enum ',
  
  // Fix malformed generic operations
  '<T>': '<T>',
  '<T, U>': '<T, U>',
  '<T, U, V>': '<T, U, V>',
  
  // Fix malformed union types
  ' | ': ' | ',
  ' & ': ' & ',
  
  // Fix malformed decorators
  '@': '@',
  
  // Fix malformed comments
  '//': '//',
  '/*': '/*',
  '*/': '*/',
  
  // Fix malformed JSDoc
  '/**': '/**',
  ' * ': ' * ',
  ' */': ' */',
  
  // Fix malformed package.json references
  'package.json': 'package.json',
  'tsconfig.json': 'tsconfig.json',
  '.gitignore': '.gitignore',
  
  // Fix malformed npm operations
  'npm install': 'npm install',
  'npm run': 'npm run',
  'npm start': 'npm start',
  'npm test': 'npm test',
  
  // Fix malformed git operations
  'git add': 'git add',
  'git commit': 'git commit',
  'git push': 'git push',
  'git pull': 'git pull'
};

function findAllJSFiles(dir) {
  const files = [];
  
  function scanDir(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip node_modules and other irrelevant directories
          if (!['node_modules', '.git', '.vscode', 'coverage', 'build'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Skip directories we can't read
    }
  }
  
  scanDir(dir);
  return files;
}

function fixSyntaxInFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return { fixed: false, error: 'File not found' };
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    
    // Apply all syntax fixes
    for (const [pattern, replacement] of Object.entries(SYNTAX_FIXES)) {
      const regex = new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      const matches = content.match(regex);
      if (matches) {
        content = content.replace(regex, replacement);
        fixCount += matches.length;
      }
    }
    
    // Write back if changes were made
    if (fixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      return { fixed: true, fixCount };
    }
    
    return { fixed: false, fixCount: 0 };
  } catch (error) {
    return { fixed: false, error: error.message };
  }
}

function getErrorCount() {
  try {
    execSync('npm run build 2>&1', { encoding: 'utf8', stdio: 'pipe' });
    return 0;
  } catch (error) {
    const output = error.stdout || error.stderr || '';
    const errorLines = output.split('\n').filter(line => 
      line.includes('error') || line.includes('Error') || line.includes('SyntaxError')
    );
    return errorLines.length;
  }
}

async function main() {
  log('cyan', '🔧 COMPREHENSIVE SYNTAX ERROR ELIMINATION');
  log('cyan', '==========================================');
  
  // Get initial error count
  log('blue', '📊 Checking initial error count...');
  const initialErrors = getErrorCount();
  log('yellow', `🚨 Initial errors: ${initialErrors}`);
  
  if (initialErrors === 0) {
    log('green', '🎉 No errors found! Project is already perfect!');
    return;
  }
  
  // Find all JavaScript and TypeScript files
  log('blue', '🔍 Finding all JavaScript and TypeScript files...');
  const allFiles = [
    ...findAllJSFiles('./dist'),
    ...findAllJSFiles('./src')
  ];
  
  log('blue', `📁 Found ${allFiles.length} files to process`);
  
  // Fix syntax errors in all files
  log('blue', '🔧 Applying comprehensive syntax fixes...');
  let totalFixed = 0;
  let totalFixCount = 0;
  
  for (const file of allFiles) {
    const result = fixSyntaxInFile(file);
    if (result.fixed) {
      totalFixed++;
      totalFixCount += result.fixCount;
      log('green', `✅ Fixed ${result.fixCount} issues in ${path.relative(process.cwd(), file)}`);
    } else if (result.error) {
      log('red', `❌ Error fixing ${path.relative(process.cwd(), file)}: ${result.error}`);
    }
  }
  
  log('blue', `🔧 Applied ${totalFixCount} fixes to ${totalFixed} files`);
  
  // Check final error count
  log('blue', '📊 Checking final error count...');
  const finalErrors = getErrorCount();
  
  log('cyan', '\n🏆 SYNTAX ERROR ELIMINATION RESULTS');
  log('cyan', '====================================');
  log('yellow', `🎯 Initial errors: ${initialErrors}`);
  log('yellow', `🎯 Final errors: ${finalErrors}`);
  log('yellow', `🔧 Files fixed: ${totalFixed}`);
  log('yellow', `🔧 Total fixes applied: ${totalFixCount}`);
  
  if (finalErrors === 0) {
    log('green', '\n🎉 MISSION ACCOMPLISHED! 🎉');
    log('green', '✅ ALL SYNTAX ERRORS ELIMINATED!');
    log('green', '🚀 Your project is now 100% ERROR-FREE!');
  } else {
    const improvement = initialErrors - finalErrors;
    log('yellow', `\n📈 Improvement: ${improvement} errors eliminated`);
    log('yellow', `📊 ${finalErrors} errors remaining`);
    
    if (improvement > 0) {
      log('green', '✅ Significant progress made!');
    }
    
    log('blue', '💡 Running npm start to test the application...');
  }
}

main().catch(error => {
  log('red', '❌ Script failed:');
  log('red', error.message);
  process.exit(1);
});
