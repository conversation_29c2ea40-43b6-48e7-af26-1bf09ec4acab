{"version": 3, "file": "errorHandling.js", "sourceRoot": "", "sources": ["../../../src/utils/errorHandling.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;AAaH,kCAUC;AArBD,4CAAiE;AAIjE;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,KAAY,EAAE,QAAiB,EAAE,EAAW;IACtE,MAAM,CAAC,KAAK,CAAC,YAAY,QAAQ,IAAQ,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAErF,yCAAyC;IACzC,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,yBAAyB;AAE3B,CAAC"}