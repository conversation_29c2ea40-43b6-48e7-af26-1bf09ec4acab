#!/bin/bash

# AmazingPay Flow - Comprehensive Health Check Script
# This script monitors all system components and sends alerts

set -e

# Configuration
HEALTH_URL="http://localhost:3002/api/health"
ALERT_EMAIL="<EMAIL>"
LOG_FILE="logs/health-check.log"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

# Function to check application health
check_application() {
    echo "🔍 Checking application health..."
    
    if curl -f $HEALTH_URL > /dev/null 2>&1; then
        print_success "Application is healthy"
        log_message "Application health check: PASS"
        return 0
    else
        print_error "Application health check failed"
        log_message "Application health check: FAIL"
        return 1
    fi
}

# Function to check database
check_database() {
    echo "🔍 Checking database connection..."
    
    # Load environment variables
    source .env.production
    
    if psql $DATABASE_URL -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "Database is accessible"
        log_message "Database health check: PASS"
        return 0
    else
        print_error "Database connection failed"
        log_message "Database health check: FAIL"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    echo "🔍 Checking disk space..."
    
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ $DISK_USAGE -lt 80 ]; then
        print_success "Disk space is adequate ($DISK_USAGE% used)"
        log_message "Disk space check: PASS ($DISK_USAGE% used)"
        return 0
    elif [ $DISK_USAGE -lt 90 ]; then
        print_warning "Disk space is getting low ($DISK_USAGE% used)"
        log_message "Disk space check: WARNING ($DISK_USAGE% used)"
        return 1
    else
        print_error "Disk space is critically low ($DISK_USAGE% used)"
        log_message "Disk space check: CRITICAL ($DISK_USAGE% used)"
        return 1
    fi
}

# Function to check memory usage
check_memory() {
    echo "🔍 Checking memory usage..."
    
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ $MEMORY_USAGE -lt 80 ]; then
        print_success "Memory usage is normal ($MEMORY_USAGE% used)"
        log_message "Memory check: PASS ($MEMORY_USAGE% used)"
        return 0
    elif [ $MEMORY_USAGE -lt 90 ]; then
        print_warning "Memory usage is high ($MEMORY_USAGE% used)"
        log_message "Memory check: WARNING ($MEMORY_USAGE% used)"
        return 1
    else
        print_error "Memory usage is critical ($MEMORY_USAGE% used)"
        log_message "Memory check: CRITICAL ($MEMORY_USAGE% used)"
        return 1
    fi
}

# Function to send alert
send_alert() {
    local message="$1"
    echo "📧 Sending alert: $message"
    
    # Send email alert (requires mail command)
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "AmazingPay Flow Alert" $ALERT_EMAIL
    fi
    
    log_message "Alert sent: $message"
}

# Main health check
main() {
    echo "🏥 AmazingPay Flow - Health Check"
    echo "================================="
    
    mkdir -p logs
    
    FAILED_CHECKS=0
    
    # Run all checks
    check_application || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    check_database || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    check_disk_space || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    check_memory || FAILED_CHECKS=$((FAILED_CHECKS + 1))
    
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        print_success "🎉 All health checks passed!"
        log_message "Health check summary: ALL PASS"
    else
        print_error "❌ $FAILED_CHECKS health check(s) failed!"
        log_message "Health check summary: $FAILED_CHECKS FAILED"
        send_alert "AmazingPay Flow health check failed: $FAILED_CHECKS issue(s) detected"
    fi
    
    exit $FAILED_CHECKS
}

# Run health check
main "$@"
