paths:
  /api/auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Create a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - firstName
                - lastName
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: Strong<PERSON>@ssw0rd
                firstName:
                  type: string
                  example: John
                lastName:
                  type: string
                  example: Doe
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      user:
                        $ref: '#/components/schemas/User'
                      message:
                        type: string
                        example: Registration successful. Please check your email to verify your account.
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: Email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/auth/login:
    post:
      tags:
        - Authentication
      summary: Login
      description: Authenticate a user and get a token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: StrongP@ssw0rd
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      user:
                        $ref: '#/components/schemas/User'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Forgot Password
      description: Request a password reset link
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
      responses:
        '200':
          description: Password reset email sent
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: If your email is registered, you will receive password reset instructions
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Reset Password
      description: Reset password using a token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - token
                - password
              properties:
                token:
                  type: string
                  example: 1a2b3c4d5e6f7g8h9i0j
                password:
                  type: string
                  format: password
                  example: NewStrongP@ssw0rd
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Password has been reset successfully. You can now log in with your new password.
        '400':
          description: Invalid input or token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/auth/me:
    get:
      tags:
        - Authentication
      summary: Get Current User
      description: Get the profile of the currently authenticated user
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
