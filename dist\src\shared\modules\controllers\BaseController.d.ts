/**
 * Base Controller
 *
 * This is a base controller class that provides common functionality
 * for all controllers in the application.
 */
import { Request, Response, NextFunction } from 'express';
export declare class BaseController {
    /**
     * Send a success response
     */
    protected sendSuccess(res: any, data?: {}, message?: string, statusCode?: number): any;
    /**
     * Send an error response
     */
    protected sendError(res: Response, message?: string, statusCode?: number, error?: Error): any;
    /**
     * Handle async controller methods
     */
    protected asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => ): any;
}
//# sourceMappingURL=BaseController.d.ts.map