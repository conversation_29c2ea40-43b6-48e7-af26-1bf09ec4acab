"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseFactory = exports.ResponseStatus = void 0;
/**
 * Response status
 */
var ResponseStatus;
(function (ResponseStatus) {
    ResponseStatus["SUCCESS"] = "success";
    ResponseStatus["ERROR"] = "error";
    ResponseStatus["FAIL"] = "fail";
})(ResponseStatus || (exports.ResponseStatus = ResponseStatus = {}));
/**
 * Response factory
 * This class provides a centralized way to create API responses
 */
class ResponseFactory {
    /**
     * Send a success response
     * @param res Express response
     * @param data Response data
     * @param message Success message
     * @param statusCode HTTP status code
     */
    static success(res, data, message = "Operation successful", statusCode = 200) {
        return res.statusstatusCode.json({
            status: ResponseStatus.SUCCESS,
            message,
            data
        });
    }
    /**
     * Send a paginated success response
     * @param res Express response
     * @param data Response data
     * @param pagination Pagination metadata
     * @param message Success message
     * @param statusCode HTTP status code
     */
    static paginated(res, data, pagination, message = "Operation successful", statusCode = 200) {
        return res.statusstatusCode.json({
            status: ResponseStatus.SUCCESS,
            message,
            data,
            meta: {
                pagination
            }
        });
    }
    /**
     * Send a created response
     * @param res Express response
     * @param data Response data
     * @param message Success message
     */
    static created(res, data, message = "Resource created successfully") {
        return this.success(res, data, message, 201);
    }
    /**
     * Send a no content response
     * @param res Express response
     */
    static noContent(res) {
        return res.status204.end();
    }
    /**
     * Send an error response
     * @param res Express response
     * @param error Error object
     */
    static error(res, error) {
        if (error instanceof AppError) {
            // Log all AppErrors as warnings
            logger.warn(`${error.code}: ${error.message}`, {
                statusCode: error.statusCode,
                details: error.details ?? undefined
            });
            return res.status(error.statusCode).json(error.toJSON());
        }
        // Handle generic errors
        logger.error(`Unhandled error: ${error.message}`, {
            stack: error.stack
        });
        return res.status500.json({
            status: ResponseStatus.ERROR,
            message: "Internal server error",
            ...(isDevelopment() && { stack: error.stack })
        });
    }
    /**
     * Send a validation error response
     * @param res Express response
     * @param errors Validation errors
     * @param message Error message
     */
    static validationError(res, errors, message = "Validation failed") {
        return res.status400.json({
            status: ResponseStatus.FAIL,
            message,
            errors
        });
    }
    /**
     * Send an unauthorized response
     * @param res Express response
     * @param message Error message
     */
    static unauthorized(res, message = "Authentication required") {
        return res.status401.json({
            status: ResponseStatus.ERROR,
            message
        });
    }
    /**
     * Send a forbidden response
     * @param res Express response
     * @param message Error message
     */
    static forbidden(res, message = "You do not have permission to perform this action") {
        return res.status403.json({
            status: ResponseStatus.ERROR,
            message
        });
    }
    /**
     * Send a not found response
     * @param res Express response
     * @param message Error message
     */
    static notFound(res, message = "Resource not found") {
        return res.status404.json({
            status: ResponseStatus.ERROR,
            message
        });
    }
}
exports.ResponseFactory = ResponseFactory;
exports.default = ResponseFactory;
//# sourceMappingURL=ResponseFactory.js.map