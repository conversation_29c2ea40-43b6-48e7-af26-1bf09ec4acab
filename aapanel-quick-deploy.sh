#!/bin/bash

# 🚀 AAPANEL QUICK DEPLOYMENT FOR AMAZINGPAY
# VPS: ************ | Domain: amazingpayme.com | aaPanel Optimized

set -e

# 🎯 AAPANEL CONFIGURATION
VPS_IP="************"
DOMAIN="amazingpayme.com"
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"
AAPANEL_DIR="/www/wwwroot/Amazingpayflow"
NODE_PORT="3002"

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 🔍 CHECK AAPANEL ENVIRONMENT
check_aapanel_environment() {
    log "🔍 Checking aaPanel environment..."
    
    # Check if we're in aaPanel directory
    if [[ "$PWD" == *"/www/wwwroot/"* ]]; then
        success "Running in aaPanel web directory"
    else
        warning "Not in aaPanel directory, current: $PWD"
    fi
    
    # Check if aaPanel services are running
    if systemctl is-active --quiet nginx; then
        success "Nginx is running (aaPanel managed)"
    else
        warning "Nginx not running"
    fi
    
    if systemctl is-active --quiet mysql || systemctl is-active --quiet mariadb; then
        success "MySQL/MariaDB is running (aaPanel managed)"
    else
        info "MySQL/MariaDB not detected"
    fi
    
    if systemctl is-active --quiet postgresql; then
        success "PostgreSQL is running"
    else
        warning "PostgreSQL not running - will need setup"
    fi
}

# 📦 INSTALL DEPENDENCIES
install_dependencies() {
    log "📦 Installing Node.js dependencies..."
    
    cd "$AAPANEL_DIR"
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js not installed. Please install Node.js through aaPanel App Store"
    fi
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        error "npm not available. Please install Node.js through aaPanel"
    fi
    
    # Install dependencies
    npm ci --production
    success "Dependencies installed"
    
    # Install PM2 globally if not present
    if ! command -v pm2 &> /dev/null; then
        npm install -g pm2
        success "PM2 installed globally"
    else
        success "PM2 already installed"
    fi
}

# 🗄️ SETUP DATABASE
setup_database() {
    log "🗄️ Setting up PostgreSQL database..."
    
    # Check if PostgreSQL is installed
    if ! command -v psql &> /dev/null; then
        warning "PostgreSQL not found. Installing..."
        apt update
        apt install -y postgresql postgresql-contrib
        systemctl start postgresql
        systemctl enable postgresql
    fi
    
    # Setup database
    sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" || true
    sudo -u postgres psql -c "CREATE DATABASE \"$DB_NAME\";" || true
    
    success "Database setup completed"
}

# 🔨 BUILD APPLICATION
build_application() {
    log "🔨 Building application..."
    
    cd "$AAPANEL_DIR"
    
    # Generate Prisma client
    npx prisma generate
    
    # Run database migrations
    npx prisma migrate deploy
    
    # Build application
    npm run build
    
    success "Application built successfully"
}

# 🌐 CONFIGURE NGINX (AAPANEL)
configure_nginx_aapanel() {
    log "🌐 Configuring Nginx for aaPanel..."
    
    # aaPanel Nginx configuration path
    local nginx_conf="/www/server/panel/vhost/nginx/$DOMAIN.conf"
    
    # Create aaPanel-compatible Nginx configuration
    cat > "$nginx_conf" << EOF
# aaPanel Nginx Configuration for AmazingPay
# Domain: $DOMAIN | Port: $NODE_PORT

server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Redirect to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL Configuration (aaPanel managed)
    ssl_certificate /www/server/panel/vhost/cert/$DOMAIN/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/$DOMAIN/privkey.pem;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';" always;
    
    # Static files
    root $AAPANEL_DIR/public;
    index index.html index.htm;
    
    # API proxy to Node.js application
    location /api/ {
        proxy_pass http://127.0.0.1:$NODE_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Health check
    location /health {
        proxy_pass http://127.0.0.1:$NODE_PORT/api/health;
        access_log off;
    }
    
    # Static file handling
    location / {
        try_files \$uri \$uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }
    
    # Block sensitive files
    location ~ /\\.(env|git|svn) {
        deny all;
        return 404;
    }
    
    # Error and access logs
    access_log /www/wwwlogs/$DOMAIN.log;
    error_log /www/wwwlogs/$DOMAIN.error.log;
}
EOF
    
    success "aaPanel Nginx configuration created"
}

# 🚀 START APPLICATION
start_application() {
    log "🚀 Starting application with PM2..."
    
    cd "$AAPANEL_DIR"
    
    # Stop any existing PM2 processes
    pm2 stop all || true
    pm2 delete all || true
    
    # Start application with PM2
    pm2 start ecosystem.config.js --env production
    pm2 save
    pm2 startup
    
    success "Application started with PM2"
}

# 🔒 FIX SECURITY HEADERS
fix_security_headers() {
    log "🔒 Fixing security headers..."
    
    cd "$AAPANEL_DIR"
    
    # Run security fix scripts
    if [ -f "scripts/complete-security-fix.sh" ]; then
        chmod +x scripts/complete-security-fix.sh
        ./scripts/complete-security-fix.sh
        success "Security headers fixed"
    else
        warning "Security fix script not found"
    fi
}

# 🧪 TEST DEPLOYMENT
test_deployment() {
    log "🧪 Testing deployment..."
    
    # Wait for application to start
    sleep 10
    
    # Test local health endpoint
    if curl -f http://localhost:$NODE_PORT/api/health &> /dev/null; then
        success "Local health check passed"
    else
        warning "Local health check failed"
    fi
    
    # Test domain health endpoint
    if curl -f https://$DOMAIN/api/health &> /dev/null; then
        success "Domain health check passed"
    else
        warning "Domain health check failed - may need SSL setup"
    fi
    
    # Run security validation
    if [ -f "scripts/pre-launch-security-validation.sh" ]; then
        chmod +x scripts/pre-launch-security-validation.sh
        ./scripts/pre-launch-security-validation.sh https://$DOMAIN
    fi
}

# 📋 DISPLAY STATUS
display_status() {
    log "📋 Deployment Status Summary"
    
    echo ""
    info "🎯 DEPLOYMENT INFORMATION:"
    echo "  🌐 Domain: https://$DOMAIN"
    echo "  🔗 API Health: https://$DOMAIN/api/health"
    echo "  📊 API Docs: https://$DOMAIN/api/docs"
    echo "  🖥️  VPS IP: $VPS_IP"
    echo "  📁 Directory: $AAPANEL_DIR"
    echo "  🔌 Port: $NODE_PORT"
    echo ""
    
    info "🔧 AAPANEL MANAGEMENT:"
    echo "  📊 aaPanel: http://$VPS_IP:7800"
    echo "  🗄️ Database: $DB_NAME"
    echo "  🔄 PM2 Status: pm2 status"
    echo "  📝 Logs: pm2 logs"
    echo ""
    
    info "🚀 NEXT STEPS:"
    echo "  1. Configure SSL certificate in aaPanel"
    echo "  2. Set up domain DNS to point to $VPS_IP"
    echo "  3. Test all application endpoints"
    echo "  4. Configure monitoring and backups"
    echo ""
}

# 🎯 MAIN DEPLOYMENT FUNCTION
main() {
    log "🚀 Starting aaPanel Quick Deployment for AmazingPay"
    log "VPS: $VPS_IP | Domain: $DOMAIN | Directory: $AAPANEL_DIR"
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
    fi
    
    # Check if in correct directory
    if [ ! -f "package.json" ]; then
        error "package.json not found. Please run from application root directory."
    fi
    
    # Execute deployment steps
    check_aapanel_environment
    install_dependencies
    setup_database
    build_application
    configure_nginx_aapanel
    start_application
    fix_security_headers
    test_deployment
    display_status
    
    success "🎉 aaPanel deployment completed successfully!"
    info "Your AmazingPay application is now running on aaPanel"
}

# Run main function
main "$@"
