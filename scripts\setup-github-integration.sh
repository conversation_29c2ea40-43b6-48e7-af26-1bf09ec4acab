#!/bin/bash

# 🔗 SETUP GITHUB INTEGRATION FOR AMAZINGPAY
# Configures GitHub-to-VPS CI/CD pipeline with automated deployment and rollback
# VPS: ************ | Domain: amazingpayme.com

set -e

# 🎯 CONFIGURATION
VPS_IP="************"
DOMAIN="amazingpayme.com"
APP_DIR="/www/wwwroot/amazingpayme.com"
WEBHOOK_PORT="9000"
WEBHOOK_SECRET=""

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 🔐 GENERATE WEBHOOK SECRET
generate_webhook_secret() {
    log "Generating webhook secret..."
    
    WEBHOOK_SECRET=$(openssl rand -hex 32)
    
    success "Webhook secret generated"
}

# 🔧 SETUP WEBHOOK HANDLER
setup_webhook_handler() {
    log "Setting up GitHub webhook handler..."
    
    # Make webhook handler executable
    chmod +x "$APP_DIR/scripts/github-webhook-handler.js"
    
    # Install webhook handler dependencies
    cd "$APP_DIR"
    if ! npm list express &>/dev/null; then
        npm install express --save
    fi
    
    # Create systemd service
    log "Creating systemd service..."
    
    cat > /etc/systemd/system/amazingpay-webhook.service << EOF
[Unit]
Description=AmazingPay GitHub Webhook Handler
Documentation=https://github.com/your-username/amazingpay-flow
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/node scripts/github-webhook-handler.js
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# Environment variables
Environment=NODE_ENV=production
Environment=WEBHOOK_PORT=$WEBHOOK_PORT
Environment=WEBHOOK_SECRET=$WEBHOOK_SECRET
Environment=APP_DIR=$APP_DIR
Environment=LOG_DIR=/var/log/amazingpay

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR /var/log/amazingpay /var/backups/amazingpay

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=amazingpay-webhook

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and start service
    systemctl daemon-reload
    systemctl enable amazingpay-webhook
    systemctl start amazingpay-webhook
    
    success "Webhook handler service created and started"
}

# 🔥 CONFIGURE FIREWALL
configure_firewall() {
    log "Configuring firewall for webhook..."
    
    # Allow webhook port
    ufw allow $WEBHOOK_PORT/tcp comment "AmazingPay Webhook"
    
    success "Firewall configured"
}

# 🌐 SETUP NGINX PROXY
setup_nginx_proxy() {
    log "Setting up Nginx proxy for webhook..."
    
    # Add webhook location to existing Nginx config
    local nginx_config="/etc/nginx/sites-available/$DOMAIN"
    
    if [ -f "$nginx_config" ]; then
        # Check if webhook location already exists
        if ! grep -q "location /webhook" "$nginx_config"; then
            # Add webhook proxy configuration before the last closing brace
            sed -i '/^}$/i\
    # GitHub Webhook Handler\
    location /webhook/ {\
        proxy_pass http://localhost:'$WEBHOOK_PORT';\
        proxy_http_version 1.1;\
        proxy_set_header Host $host;\
        proxy_set_header X-Real-IP $remote_addr;\
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\
        proxy_set_header X-Forwarded-Proto $scheme;\
        proxy_read_timeout 300s;\
        proxy_connect_timeout 75s;\
    }\
' "$nginx_config"
            
            # Test and reload Nginx
            nginx -t && systemctl reload nginx
            
            success "Nginx proxy configured"
        else
            info "Nginx webhook proxy already configured"
        fi
    else
        warning "Nginx configuration file not found, skipping proxy setup"
    fi
}

# 📋 SETUP GIT REPOSITORY
setup_git_repository() {
    log "Setting up Git repository..."
    
    cd "$APP_DIR"
    
    # Initialize git if not already done
    if [ ! -d ".git" ]; then
        git init
        git remote add origin https://github.com/your-username/amazingpay-flow.git
    fi
    
    # Configure git for automated pulls
    git config pull.rebase false
    git config user.name "AmazingPay VPS"
    git config user.email "<EMAIL>"
    
    success "Git repository configured"
}

# 🔧 MAKE SCRIPTS EXECUTABLE
make_scripts_executable() {
    log "Making deployment scripts executable..."
    
    chmod +x "$APP_DIR/scripts/vps-update.sh"
    chmod +x "$APP_DIR/scripts/create-backup.sh"
    chmod +x "$APP_DIR/scripts/setup-github-integration.sh"
    
    success "Scripts made executable"
}

# 📊 CREATE DEPLOYMENT STATUS ENDPOINT
create_status_endpoint() {
    log "Creating deployment status endpoint..."
    
    # Add status endpoint to main application (if not exists)
    local status_route="$APP_DIR/src/routes/deployment.ts"
    
    if [ ! -f "$status_route" ]; then
        mkdir -p "$(dirname "$status_route")"
        
        cat > "$status_route" << 'EOF'
import { Router } from 'express';
import { execSync } from 'child_process';

const router = Router();

// Get deployment status
router.get('/status', (req, res) => {
    try {
        const status = {
            timestamp: new Date().toISOString(),
            commit: execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim(),
            branch: execSync('git branch --show-current', { encoding: 'utf8' }).trim(),
            version: process.env.npm_package_version || 'unknown',
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || 'development'
        };
        
        res.json(status);
    } catch (error) {
        res.status(500).json({ error: 'Failed to get deployment status' });
    }
});

export default router;
EOF
        
        success "Deployment status endpoint created"
    else
        info "Deployment status endpoint already exists"
    fi
}

# 📝 CREATE DEPLOYMENT DOCUMENTATION
create_documentation() {
    log "Creating deployment documentation..."
    
    cat > "$APP_DIR/GITHUB_DEPLOYMENT_GUIDE.md" << EOF
# 🚀 GitHub-to-VPS Deployment Guide

## 📋 **DEPLOYMENT WORKFLOW**

### **Automatic Deployment**
1. Push code to \`main\` branch
2. GitHub Actions runs tests and builds
3. Webhook triggers VPS deployment
4. Application automatically updates

### **Manual Deployment**
\`\`\`bash
# Trigger manual deployment
curl -X POST https://$DOMAIN/deploy/main
\`\`\`

## 🔧 **CONFIGURATION**

### **GitHub Secrets Required**
- \`VPS_HOST\`: $VPS_IP
- \`VPS_USERNAME\`: root
- \`VPS_SSH_KEY\`: Your SSH private key
- \`WEBHOOK_SECRET\`: $WEBHOOK_SECRET

### **Webhook URL**
\`\`\`
https://$DOMAIN/webhook/deploy
\`\`\`

## 📊 **MONITORING**

### **Status Endpoints**
- **Application Status**: https://$DOMAIN/api/deployment/status
- **Webhook Status**: https://$DOMAIN/webhook/status
- **Health Check**: https://$DOMAIN/api/health

### **Logs**
\`\`\`bash
# Webhook logs
journalctl -u amazingpay-webhook -f

# Deployment logs
tail -f /var/log/amazingpay/deployment.log

# Application logs
pm2 logs amazingpay-main
\`\`\`

## 🔄 **ROLLBACK**

### **Automatic Rollback**
- Triggered on deployment failure
- Restores previous version automatically

### **Manual Rollback**
\`\`\`bash
# List available backups
./scripts/create-backup.sh --list

# Restore specific backup
./scripts/create-backup.sh --restore backup-name
\`\`\`

## 🚨 **TROUBLESHOOTING**

### **Common Issues**
1. **Webhook not triggered**: Check GitHub webhook configuration
2. **Deployment fails**: Check logs and permissions
3. **Application won't start**: Verify dependencies and database

### **Emergency Procedures**
\`\`\`bash
# Stop webhook handler
systemctl stop amazingpay-webhook

# Manual deployment
cd $APP_DIR
./scripts/vps-update.sh main

# Restart services
systemctl restart amazingpay-webhook
pm2 restart amazingpay-main
\`\`\`
EOF
    
    success "Documentation created"
}

# 📋 DISPLAY SETUP SUMMARY
display_summary() {
    log "GitHub Integration Setup Summary"
    echo "=================================="
    echo ""
    info "🔗 Webhook Configuration:"
    echo "  URL: https://$DOMAIN/webhook/deploy"
    echo "  Secret: $WEBHOOK_SECRET"
    echo "  Port: $WEBHOOK_PORT"
    echo ""
    info "📊 Status Endpoints:"
    echo "  Application: https://$DOMAIN/api/deployment/status"
    echo "  Webhook: https://$DOMAIN/webhook/status"
    echo "  Health: https://$DOMAIN/api/health"
    echo ""
    info "🔧 Service Management:"
    echo "  Start: systemctl start amazingpay-webhook"
    echo "  Stop: systemctl stop amazingpay-webhook"
    echo "  Status: systemctl status amazingpay-webhook"
    echo "  Logs: journalctl -u amazingpay-webhook -f"
    echo ""
    info "📝 Next Steps:"
    echo "  1. Add webhook URL to GitHub repository settings"
    echo "  2. Configure GitHub secrets for CI/CD"
    echo "  3. Test deployment by pushing to main branch"
    echo "  4. Monitor logs for successful deployment"
    echo ""
    success "🎉 GitHub integration setup completed!"
}

# 🎯 MAIN FUNCTION
main() {
    log "🔗 Setting up GitHub-to-VPS Integration"
    log "VPS: $VPS_IP | Domain: $DOMAIN"
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
    fi
    
    generate_webhook_secret
    setup_webhook_handler
    configure_firewall
    setup_nginx_proxy
    setup_git_repository
    make_scripts_executable
    create_status_endpoint
    create_documentation
    display_summary
}

# Run main function
main "$@"
