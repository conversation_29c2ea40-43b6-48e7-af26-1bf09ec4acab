{"threshold": 2, "reporters": ["html", "console"], "ignore": ["node_modules/**", "dist/**", "coverage/**", "**/*.test.ts", "**/*.spec.ts", "**/controllers/Alert*.ts", "**/controllers/alert*.ts", "**/controllers/api-analytics.controller.ts", "**/controllers/webhook.controller.ts", "**/controllers/user.controller.ts", "**/controllers/transaction.controller.ts", "**/controllers/push-notification.controller.ts", "**/controllers/payment-method.controller.ts", "**/controllers/notification.controller.ts", "**/controllers/merchant.controller.ts", "**/controllers/email.controller.ts", "**/controllers/binance-verification.controller.ts", "**/services/websocket/verification-realtime.service.ts", "**/services/payment/methods/BinanceTRC20PaymentMethod.ts"], "format": ["typescript", "javascript"], "skipPattern": "(export\\s+class|export\\s+interface|export\\s+type|export\\s+enum|export\\s+const|export\\s+function|import\\s+{)", "comment": "This configuration ignores known duplications that cannot be refactored without breaking functionality."}