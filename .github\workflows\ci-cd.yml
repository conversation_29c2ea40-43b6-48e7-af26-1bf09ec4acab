name: AmazingPay Flow CI/CD Pipeline

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: amazingpay_test

jobs:
  # Quality Gates - Code Analysis
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: TypeScript compilation check
        run: npx tsc --noEmit

      - name: ESLint analysis
        run: npm run lint
        continue-on-error: true

      - name: Security audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

      - name: Dependency vulnerability scan
        uses: actions/dependency-review-action@v4
        if: github.event_name == 'pull_request'

  # Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm test -- --coverage --testPathPattern=unit
        env:
          NODE_ENV: test

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

      - name: Coverage quality gate
        run: |
          COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
          echo "Coverage: $COVERAGE%"
          if (( $(echo "$COVERAGE < 80" | bc -l) )); then
            echo "Coverage is below 80%"
            exit 1
          fi

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup test database
        run: |
          npx prisma migrate deploy
          npx prisma db seed
        env:
          DATABASE_URL: postgresql://postgres:${{ env.POSTGRES_PASSWORD }}@localhost:5432/${{ env.POSTGRES_DB }}

      - name: Run integration tests
        run: npm test -- --testPathPattern=integration
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:${{ env.POSTGRES_PASSWORD }}@localhost:5432/${{ env.POSTGRES_DB }}
          REDIS_URL: redis://localhost:6379

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run performance tests
        run: npm test -- --testPathPattern=performance
        env:
          NODE_ENV: test

      - name: Performance benchmark
        run: node scripts/run-comprehensive-tests.js
        continue-on-error: true

  # Build and Package
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/staging'
    environment:
      name: staging
      url: https://staging-api.amazingpay.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          echo "Note: This is a placeholder deployment step"
          echo "Configure your staging deployment commands here"
          # Add your staging deployment commands here
          # Example: kubectl apply -f k8s/staging/
          # Example: docker-compose -f deployment/staging-config.yml up -d

      - name: Run smoke tests
        run: |
          echo "Running smoke tests..."
          # Add smoke test commands here
          # curl -f https://staging-api.amazingpay.com/health

      - name: Notify deployment
        if: always() && secrets.SLACK_WEBHOOK != ''
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#deployments'
          SLACK_MESSAGE: 'Staging deployment ${{ job.status }}!'
          SLACK_TITLE: 'Deployment Notification'
          SLACK_COLOR: ${{ job.status == 'success' && 'good' || 'danger' }}

  # Deploy to Production VPS
  deploy-production:
    name: Deploy to Production VPS
    runs-on: ubuntu-latest
    needs: [build, security-scan, performance-tests]
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://api.amazingpay.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Production deployment approval
        uses: trstringer/manual-approval@v1
        with:
          secret: ${{ github.TOKEN }}
          approvers: admin
          minimum-approvals: 1
          issue-title: 'Production Deployment Approval'
        if: github.event_name == 'workflow_dispatch'

      - name: Deploy to VPS via SSH
        if: ${{ secrets.VPS_HOST != '' && secrets.VPS_USER != '' && secrets.VPS_SSH_KEY != '' }}
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: 22
          script: |
            cd /www/wwwroot/amazingpay-flow

            # Create backup before deployment
            echo "🔄 Creating backup before deployment..."
            ./backup-vps.sh "pre-deploy-$(date +%Y%m%d-%H%M%S)"

            # Pull latest changes
            echo "📥 Pulling latest changes..."
            git pull origin main

            # Install dependencies
            echo "📦 Installing dependencies..."
            npm ci --production

            # Run database migrations
            echo "🗄️ Running database migrations..."
            npx prisma migrate deploy

            # Build application
            echo "🔨 Building application..."
            npm run build

            # Restart application
            echo "🚀 Restarting application..."
            pm2 restart amazingpay-flow

            # Wait for application to start
            sleep 10

            # Health check
            echo "🏥 Performing health check..."
            curl -f http://localhost:3002/api/health || exit 1

            echo "✅ Deployment completed successfully!"

      - name: Skip VPS deployment (secrets not configured)
        if: ${{ secrets.VPS_HOST == '' || secrets.VPS_USER == '' || secrets.VPS_SSH_KEY == '' }}
        run: |
          echo "⚠️  VPS deployment skipped - secrets not configured"
          echo "To enable VPS deployment, configure these secrets:"
          echo "- VPS_HOST: Your VPS server hostname/IP"
          echo "- VPS_USER: SSH username for VPS"
          echo "- VPS_SSH_KEY: SSH private key for VPS access"

      - name: Run production smoke tests
        run: |
          echo "🧪 Running production smoke tests..."
          sleep 15

          # Test health endpoint
          curl -f https://api.amazingpay.com/api/health

          # Test authentication endpoint
          curl -f https://api.amazingpay.com/api/auth/health

          echo "✅ Smoke tests passed!"

      - name: Rollback on failure
        if: ${{ failure() && secrets.VPS_HOST != '' && secrets.VPS_USER != '' && secrets.VPS_SSH_KEY != '' }}
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: 22
          script: |
            cd /www/wwwroot/amazingpay-flow
            echo "❌ Deployment failed - Rolling back..."

            # Get latest backup
            LATEST_BACKUP=$(ls -t /var/backups/amazingpay/ | grep "pre-deploy-" | head -1)

            if [ -n "$LATEST_BACKUP" ]; then
              echo "🔄 Rolling back to: $LATEST_BACKUP"
              ./restore-backup.sh "$LATEST_BACKUP"
              echo "✅ Rollback completed"
            else
              echo "❌ No backup found for rollback"
              exit 1
            fi

      - name: Notify production deployment
        if: always() && secrets.SLACK_WEBHOOK != ''
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#production'
          SLACK_MESSAGE: |
            Production Deployment ${{ job.status }}!
            Repository: ${{ github.repository }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
            URL: https://api.amazingpay.com
          SLACK_TITLE: 'Production Deployment'
          SLACK_COLOR: ${{ job.status == 'success' && 'good' || 'danger' }}

  # Rollback capability
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging')
    needs: [deploy-staging, deploy-production]
    steps:
      - name: Rollback deployment
        run: |
          echo "Rolling back deployment..."
          # Add rollback commands here

      - name: Notify rollback
        if: always() && secrets.SLACK_WEBHOOK != ''
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: '#alerts'
          SLACK_MESSAGE: 'Deployment rollback executed due to failure!'
          SLACK_TITLE: 'Rollback Alert'
          SLACK_COLOR: 'danger'
