import { PaymentMethod } from '../types';
/**
 * Payment verification result
 */
export interface PaymentVerificationResult {
    verified: boolean;
    method: PaymentMethod;
    amount: string;
    currency: string;
    transactionId: string;
    timestamp: string;
    sender?: string;
    recipient?: string;
    rawData?: unknown;
}
/**
 * Payment verification service
 */
export declare class PaymentVerificationService extends BaseService {
    private blockchainApiService;
    private binanceApiService;
    constructor();
    /**
     * Execute a database operation with error handling
     * @param operation Operation to execute
     * @param errorMessage Error message
     * @param context Error context
     * @returns Operation result
     */
    private executeDbOperation;
}
//# sourceMappingURL=payment-verification.service.d.ts.map