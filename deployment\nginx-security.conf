# NGINX SECURITY CONFIGURATION
# Critical Financial Application - Production Ready
# Place this in /etc/nginx/sites-available/amazingpay

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name amazingpay.com www.amazingpay.com;
    
    # CRITICAL: Document root - ONLY public files
    root /var/www/html;
    index index.html index.php;
    
    # SECURITY HEADERS (CRITICAL FOR FINANCIAL APPS)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'self';" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    
    # Hide server information
    server_tokens off;
    more_clear_headers Server;
    
    # CRITICAL: Block access to hidden files and directories
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to environment files
    location ~* \.(env|environment)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to configuration files
    location ~* \.(config|conf|ini|cfg|cnf)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to source code files
    location ~* \.(ts|tsx|js\.map|json|md|txt|yml|yaml|toml)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to package and build files
    location ~* (package\.json|package-lock\.json|yarn\.lock|composer\.json|composer\.lock|tsconfig\.json|webpack\.config\.js|vite\.config\.js|next\.config\.js|nuxt\.config\.js|\.gitignore|\.gitmodules|\.dockerignore|Dockerfile|docker-compose\.yml)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to log files
    location ~* \.(log|logs)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to backup and temporary files
    location ~* \.(bak|backup|old|orig|save|swp|tmp|temp|~|#)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to database files
    location ~* \.(sql|sqlite|db|mdb)$ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Block access to sensitive directories
    location ~* ^/(config|src|app|storage|logs|node_modules|vendor|\.git|\.svn|\.hg|tests|test|spec|docs|documentation|private|admin|wp-admin|phpmyadmin)/ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # CRITICAL: Disable directory listing
    autoindex off;
    
    # Block access to any directory
    location ~ ^/(.*)/$ {
        return 403;
    }
    
    # CRITICAL: Block access to version control
    location ~ /\.git {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    location ~ /\.svn {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    location ~ /\.hg {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # Block access to common admin panels
    location ~* ^/(admin|administrator|wp-admin|phpmyadmin|pma|adminer|cpanel|webmail)/ {
        deny all;
        access_log off;
        log_not_found off;
        return 404;
    }
    
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=2r/s;
    
    # Apply general rate limiting
    limit_req zone=general burst=20 nodelay;
    
    # API endpoints with stricter rate limiting
    location /api/ {
        limit_req zone=api burst=10 nodelay;
        
        # Additional API security headers
        add_header X-API-Version "1.0" always;
        add_header X-Rate-Limit "5 requests per second" always;
        
        # CORS for API (adjust origins as needed)
        add_header Access-Control-Allow-Origin "https://amazingpay.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With" always;
        add_header Access-Control-Max-Age "86400" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        
        # Proxy to application server (adjust as needed)
        try_files $uri $uri/ @api;
    }
    
    # Authentication endpoints with strictest rate limiting
    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;
        
        # Additional security for auth endpoints
        add_header X-Auth-Required "true" always;
        
        try_files $uri $uri/ @api;
    }
    
    # Proxy configuration for application server
    location @api {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Security headers for proxied requests
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Server $host;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static assets with caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff" always;
        
        # Security check for static assets
        location ~* \.(js)$ {
            add_header X-Content-Type-Options "nosniff" always;
            add_header Content-Type "application/javascript" always;
        }
        
        location ~* \.(css)$ {
            add_header X-Content-Type-Options "nosniff" always;
            add_header Content-Type "text/css" always;
        }
    }
    
    # SSL Configuration (Let's Encrypt)
    ssl_certificate /etc/letsencrypt/live/amazingpay.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/amazingpay.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/amazingpay.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security logging
    access_log /var/log/nginx/amazingpay_access.log combined;
    error_log /var/log/nginx/amazingpay_error.log warn;
    
    # Log security events
    location ~* \.(env|config|\.git|\.svn) {
        access_log /var/log/nginx/security_violations.log combined;
        deny all;
        return 404;
    }
    
    # Custom error pages
    error_page 403 /error/403.html;
    error_page 404 /error/404.html;
    error_page 500 502 503 504 /error/50x.html;
    
    location = /error/403.html {
        root /var/www/html;
        internal;
    }
    
    location = /error/404.html {
        root /var/www/html;
        internal;
    }
    
    location = /error/50x.html {
        root /var/www/html;
        internal;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name amazingpay.com www.amazingpay.com;
    
    # Security headers even for redirects
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

# Block access to IP address directly
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    listen 443 ssl default_server;
    listen [::]:443 ssl default_server;
    
    # Dummy SSL certificate for default server
    ssl_certificate /etc/ssl/certs/ssl-cert-snakeoil.pem;
    ssl_certificate_key /etc/ssl/private/ssl-cert-snakeoil.key;
    
    server_name _;
    return 444;
}
