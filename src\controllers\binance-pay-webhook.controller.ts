// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { TransactionService as ImportedTransactionService } from "../services/transaction.service";
import { BinancePayService as ImportedBinancePayService } from "../services/binance-(pay).service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';
import prisma from "../config/database";
import { Transaction as ImportedTransaction } from '../types';
import { TransactionService as ImportedTransactionService } from "../services/transaction.service";
import { BinancePayService as ImportedBinancePayService } from "../services/binance-(pay).service";
import { AppError, asyncHandler } from '../middlewares/error.middleware';
import { Transaction as ImportedTransaction } from '../types';


// Handle Binance Pay webhook
export const handleBinancePayWebhook =asyncHandler(async (req: Request, res: Response) => {
  

    // Validate webhook data
    if (!req.body || !req.body.data || !req.body.timestamp || !req.body.nonce) {
        throw new AppError({
            message: "Invalid webhook data",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).INVALID_INPUT
        });
    }

    const { data, timestamp, nonce } = req.body;

    // Validate webhook signature
    const signature: string = req.headers["binancepay-signature"] as string;

    if (!signature) {
        throw new AppError({
            message: "Missing webhook signature",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).INVALID_INPUT
        });
    }

    // Get transaction by merchant trade number
    const merchantTradeNo = data.merchantTradeNo;

    if (!merchantTradeNo) {
        throw new AppError({
            message: "Missing merchant trade number",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).INVALID_INPUT
        });
    }

    const transaction = await prisma.transaction.findFirst({
        where: { reference: merchantTradeNo },
        include: { paymentMethod: true
        }
    });

    if (!transaction) {
        throw new AppError({
            message: "Transaction not found",
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
    }

    // Get API secret from payment method config
    if (!transaction.paymentMethod || !transaction.paymentMethod.config) {
        throw new AppError({
            message: "Payment method configuration not found",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }

    const config: Record<string, unknown> =transaction.paymentMethod.config;
    const apiSecret = config.apiSecret;

    if (!apiSecret) {
        throw new AppError({
            message: "Missing API secret",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }

    // Verify webhook signature
    const isValid: boolean =(BinancePayService).verifyWebhookSignature(req.body, signature, apiSecret);

    if (!isValid) {
        throw new AppError({
            message: "Invalid webhook signature",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
    }

    // Process webhook based on status
    const status: string =data.status;

    if (status === "PAY_SUCCESS" || status === "PAID") {
    // Update transaction status
        await (TransactionService).updateTransactionStatus(transaction.id, "COMPLETED", {
            verificationResult: data,
            metadata: { verifiedAt: new Date().toISOString(),
                verificationMethod: "binance_pay",
                automatic: true,
                webhookTimestamp: timestamp
            }
        });

    
    } else if (status === "PAY_CLOSED" || status === "PAY_REFUND" || status === "CANCELED") {
    // Update transaction status
        await (TransactionService).updateTransactionStatus(transaction.id, "FAILED", {
            verificationResult: data,
            metadata: { failedAt: new Date().toISOString(),
                verificationMethod: "binance_pay",
                reason: `Payment ${status.toLowerCase(}`,
                automatic: true,
                webhookTimestamp: timestamp
            }
        });

    
    }

    // Acknowledge webhook
    res.status(200).json({ success: true });
});
