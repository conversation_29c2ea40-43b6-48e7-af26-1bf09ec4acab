@echo off
REM AmazingPay Flow - Production Database Setup Script (Windows)
REM This script sets up the production database with all necessary configurations

setlocal enabledelayedexpansion

echo.
echo 🗄️  AmazingPay Flow - Production Database Setup
echo ===============================================
echo.

REM Set PostgreSQL path
set PSQL_PATH="C:\Program Files\PostgreSQL\17\bin"
set PATH=%PSQL_PATH%;%PATH%

REM Default database configuration
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=amazingpay_production
set DB_USER=postgres

echo 📦 Configuring production database...
echo.

REM Prompt for database password
set /p DB_PASSWORD="Enter PostgreSQL password for user '%DB_USER%': "

REM Create database URL
set DATABASE_URL=postgresql://%DB_USER%:%DB_PASSWORD%@%DB_HOST%:%DB_PORT%/%DB_NAME%

echo.
echo ℹ️  Database configuration:
echo    Host: %DB_HOST%
echo    Port: %DB_PORT%
echo    Database: %DB_NAME%
echo    User: %DB_USER%
echo.

REM Check if .env.production exists
if not exist ".env.production" (
    echo ❌ .env.production file not found
    pause
    exit /b 1
)

REM Backup existing .env.production
copy ".env.production" ".env.production.backup" >nul

REM Update .env.production with database configuration
echo 📦 Updating .env.production with database configuration...

REM Create temporary PowerShell script to update the file
echo $content = Get-Content '.env.production' > temp_update.ps1
echo $content = $content -replace 'DATABASE_URL=.*', 'DATABASE_URL="%DATABASE_URL%"' >> temp_update.ps1
echo $content = $content -replace 'DB_HOST=.*', 'DB_HOST=%DB_HOST%' >> temp_update.ps1
echo $content = $content -replace 'DB_PORT=.*', 'DB_PORT=%DB_PORT%' >> temp_update.ps1
echo $content = $content -replace 'DB_USERNAME=.*', 'DB_USERNAME=%DB_USER%' >> temp_update.ps1
echo $content = $content -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%DB_PASSWORD%' >> temp_update.ps1
echo $content = $content -replace 'DB_NAME=.*', 'DB_NAME=%DB_NAME%' >> temp_update.ps1
echo $content ^| Set-Content '.env.production' >> temp_update.ps1

powershell -ExecutionPolicy Bypass -File temp_update.ps1
del temp_update.ps1

echo ✅ Updated .env.production with database configuration
echo.

REM Set environment variable for Prisma
set DATABASE_URL=%DATABASE_URL%

REM Create the database if it doesn't exist
echo 📦 Creating database if it doesn't exist...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -c "CREATE DATABASE %DB_NAME%;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Database created successfully
) else (
    echo ℹ️  Database already exists or creation failed - continuing...
)
echo.

REM Generate Prisma client
echo 📦 Generating Prisma client...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ Failed to generate Prisma client
    pause
    exit /b 1
)
echo ✅ Prisma client generated
echo.

REM Run database migrations
echo 📦 Running database migrations...
call npx prisma migrate deploy
if %errorlevel% neq 0 (
    echo ❌ Failed to run migrations
    pause
    exit /b 1
)
echo ✅ Database migrations completed
echo.

REM Create seed data
echo 📦 Creating initial seed data...

REM Create temporary seed script
echo const { PrismaClient } = require('@prisma/client'); > temp_seed.js
echo const bcrypt = require('bcryptjs'); >> temp_seed.js
echo. >> temp_seed.js
echo const prisma = new PrismaClient(); >> temp_seed.js
echo. >> temp_seed.js
echo async function main() { >> temp_seed.js
echo   console.log('🌱 Seeding production database...'); >> temp_seed.js
echo. >> temp_seed.js
echo   // Create system admin user >> temp_seed.js
echo   const hashedPassword = await bcrypt.hash('admin123!@#', 12); >> temp_seed.js
echo. >> temp_seed.js
echo   const adminUser = await prisma.user.upsert({ >> temp_seed.js
echo     where: { email: '<EMAIL>' }, >> temp_seed.js
echo     update: {}, >> temp_seed.js
echo     create: { >> temp_seed.js
echo       email: '<EMAIL>', >> temp_seed.js
echo       password: hashedPassword, >> temp_seed.js
echo       firstName: 'System', >> temp_seed.js
echo       lastName: 'Administrator', >> temp_seed.js
echo       role: 'ADMIN', >> temp_seed.js
echo       isActive: true >> temp_seed.js
echo     } >> temp_seed.js
echo   }); >> temp_seed.js
echo. >> temp_seed.js
echo   // Create system settings >> temp_seed.js
echo   const settings = [ >> temp_seed.js
echo     { key: 'SYSTEM_NAME', value: 'AmazingPay Flow' }, >> temp_seed.js
echo     { key: 'SYSTEM_VERSION', value: '1.0.0' }, >> temp_seed.js
echo     { key: 'MAINTENANCE_MODE', value: 'false' }, >> temp_seed.js
echo     { key: 'MAX_TRANSACTION_AMOUNT', value: '100000' }, >> temp_seed.js
echo     { key: 'DEFAULT_CURRENCY', value: 'USD' } >> temp_seed.js
echo   ]; >> temp_seed.js
echo. >> temp_seed.js
echo   for (const setting of settings) { >> temp_seed.js
echo     await prisma.systemSetting.upsert({ >> temp_seed.js
echo       where: { key: setting.key }, >> temp_seed.js
echo       update: { value: setting.value, updatedById: adminUser.id }, >> temp_seed.js
echo       create: { ...setting, updatedById: adminUser.id } >> temp_seed.js
echo     }); >> temp_seed.js
echo   } >> temp_seed.js
echo. >> temp_seed.js
echo   console.log('✅ Database seeded successfully'); >> temp_seed.js
echo } >> temp_seed.js
echo. >> temp_seed.js
echo main() >> temp_seed.js
echo   .catch((e) =^> { >> temp_seed.js
echo     console.error('❌ Seeding failed:', e); >> temp_seed.js
echo     process.exit(1); >> temp_seed.js
echo   }) >> temp_seed.js
echo   .finally(async () =^> { >> temp_seed.js
echo     await prisma.$disconnect(); >> temp_seed.js
echo   }); >> temp_seed.js

REM Run the seed script
call node temp_seed.js
if %errorlevel% neq 0 (
    echo ❌ Failed to seed database
    del temp_seed.js
    pause
    exit /b 1
)

REM Clean up
del temp_seed.js
echo ✅ Database seeded successfully
echo.

REM Verify database setup
echo 📦 Verifying database setup...

REM Create verification script
echo const { PrismaClient } = require('@prisma/client'); > temp_verify.js
echo const prisma = new PrismaClient(); >> temp_verify.js
echo async function verify() { >> temp_verify.js
echo   try { >> temp_verify.js
echo     const userCount = await prisma.user.count(); >> temp_verify.js
echo     const settingCount = await prisma.systemSetting.count(); >> temp_verify.js
echo     console.log('📊 Database Statistics:'); >> temp_verify.js
echo     console.log('   Users: ' + userCount); >> temp_verify.js
echo     console.log('   Settings: ' + settingCount); >> temp_verify.js
echo     console.log('✅ Database verification completed'); >> temp_verify.js
echo   } catch (error) { >> temp_verify.js
echo     console.error('❌ Database verification failed:', error.message); >> temp_verify.js
echo     process.exit(1); >> temp_verify.js
echo   } finally { >> temp_verify.js
echo     await prisma.$disconnect(); >> temp_verify.js
echo   } >> temp_verify.js
echo } >> temp_verify.js
echo verify(); >> temp_verify.js

call node temp_verify.js
del temp_verify.js

echo.
echo 🎉 Production database setup completed successfully!
echo.
echo 📋 Next steps:
echo 1. Verify your application can connect to the database
echo 2. Test the admin login: <EMAIL> / admin123!@#
echo 3. Change the default admin password
echo 4. Configure regular database backups
echo.
echo 🔗 Database URL: %DATABASE_URL%
echo.

pause
