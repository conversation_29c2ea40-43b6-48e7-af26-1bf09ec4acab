#!/bin/bash

# 🚀 COMPLETE FRESH SETUP FOR AMAZINGPAY
# Root Directory: /www/wwwroot/Amazingpayflow
# VPS: ************ | Domain: amazingpayme.com

set -e

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 🎯 CONFIGURATION
ROOT_DIR="/www/wwwroot/Amazingpayflow"
DOMAIN="amazingpayme.com"
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"
NODE_PORT="3002"

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo -e "${CYAN}🚀 COMPLETE FRESH AMAZINGPAY SETUP${NC}"
echo "=================================="
echo -e "${BLUE}Root Directory: $ROOT_DIR${NC}"
echo -e "${BLUE}Domain: $DOMAIN${NC}"
echo -e "${BLUE}Database: $DB_NAME${NC}"
echo -e "${BLUE}Port: $NODE_PORT${NC}"
echo ""

# 🗑️ STEP 1: CLEAN PREVIOUS INSTALLATION
log "Cleaning previous installation..."

# Stop any running PM2 processes
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true

# Remove existing directory
rm -rf "$ROOT_DIR"

success "Previous installation cleaned"

# 📁 STEP 2: CREATE PROJECT DIRECTORY
log "Creating project directory..."

mkdir -p "$ROOT_DIR"
cd "$ROOT_DIR"

success "Project directory created: $ROOT_DIR"

# 📥 STEP 3: CLONE PROJECT FROM GITHUB
log "Cloning project from GitHub..."

git clone https://github.com/Amazingteam-eg/Amazingpayflow.git .

# Fix Git ownership
git config --global --add safe.directory "$ROOT_DIR"

success "Project cloned successfully"

# 🔧 STEP 4: SET PROPER PERMISSIONS
log "Setting proper permissions..."

chown -R www-data:www-data "$ROOT_DIR" 2>/dev/null || chown -R root:root "$ROOT_DIR"
chmod -R 755 "$ROOT_DIR"

success "Permissions set correctly"

# 📦 STEP 5: INSTALL DEPENDENCIES
log "Installing dependencies..."

export HUSKY=0
export NODE_ENV=production
npm install --omit=dev --ignore-scripts --no-audit --no-fund

success "Dependencies installed"

# 🗄️ STEP 6: SETUP DATABASE
log "Setting up PostgreSQL database..."

# Ensure PostgreSQL is running
systemctl start postgresql 2>/dev/null || true
systemctl enable postgresql 2>/dev/null || true

# Setup database
sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
sudo -u postgres psql -c "CREATE DATABASE \"$DB_NAME\";" 2>/dev/null || true

success "Database configured"

# 📝 STEP 7: CREATE ENVIRONMENT FILE
log "Creating environment file..."

cat > .env << EOF
NODE_ENV=production
PORT=$NODE_PORT
HOST=0.0.0.0
DATABASE_URL=postgresql://postgres:$DB_PASSWORD@localhost:5432/$DB_NAME
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=$DB_PASSWORD
DB_NAME=$DB_NAME
FRONTEND_URL=https://$DOMAIN
API_URL=https://$DOMAIN/api
DOMAIN=$DOMAIN
VPS_IP=************
JWT_SECRET=AzP4y_Pr0d_JWT_S3cr3t_2024_V3ry_L0ng_4nd_S3cur3_K3y
JWT_EXPIRES_IN=1d
EOF

# Load environment
export $(cat .env | xargs)

success "Environment file created"

# 🔨 STEP 8: BUILD APPLICATION
log "Building application..."

npx prisma generate
npx prisma migrate deploy
npm run build

success "Application built successfully"

# 🚀 STEP 9: START APPLICATION WITH PM2
log "Starting application with PM2..."

# Install PM2 if not present
if ! command -v pm2 &> /dev/null; then
    npm install -g pm2
fi

# Start application
pm2 start dist/src/index.js --name "amazingpay" --instances 1 --env production
pm2 save
pm2 startup

success "Application started with PM2"

# 🌐 STEP 10: CONFIGURE NGINX
log "Configuring Nginx..."

# Create Nginx configuration
cat > /www/server/panel/vhost/nginx/$DOMAIN.conf << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Root directory for static files
    root $ROOT_DIR/public;
    index index.html index.htm;
    
    # API proxy to Node.js application
    location / {
        proxy_pass http://127.0.0.1:$NODE_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Health check
    location /health {
        proxy_pass http://127.0.0.1:$NODE_PORT/api/health;
        access_log off;
    }
    
    # Block sensitive files
    location ~ /\\.(env|git|svn) {
        deny all;
        return 404;
    }
    
    # Error and access logs
    access_log /www/wwwlogs/$DOMAIN.log;
    error_log /www/wwwlogs/$DOMAIN.error.log;
}
EOF

# Test and reload Nginx
nginx -t && systemctl reload nginx

success "Nginx configured"

# 🧪 STEP 11: TEST DEPLOYMENT
log "Testing deployment..."

sleep 10

# Test health endpoint
if curl -f http://localhost:$NODE_PORT/api/health >/dev/null 2>&1; then
    success "✅ Health check PASSED"
else
    warning "⚠️ Health check failed, but application may still be starting"
fi

# Test domain
if curl -f http://$DOMAIN/api/health >/dev/null 2>&1; then
    success "✅ Domain health check PASSED"
else
    warning "⚠️ Domain health check failed - may need DNS configuration"
fi

# 📊 STEP 12: DISPLAY FINAL STATUS
echo ""
echo -e "${CYAN}🎉 COMPLETE FRESH SETUP COMPLETED!${NC}"
echo "=================================="
echo ""
echo -e "${GREEN}✅ SETUP SUMMARY:${NC}"
echo "  📁 Root Directory: $ROOT_DIR"
echo "  🗄️ Database: $DB_NAME configured"
echo "  📦 Dependencies: Installed"
echo "  🔨 Application: Built and running"
echo "  🚀 PM2: Managing application"
echo "  🌐 Nginx: Configured for $DOMAIN"
echo ""
echo -e "${CYAN}🔗 APPLICATION URLS:${NC}"
echo "  🏠 Local: http://localhost:$NODE_PORT"
echo "  🏥 Health: http://localhost:$NODE_PORT/api/health"
echo "  🌐 Domain: http://$DOMAIN"
echo "  📊 API Docs: http://$DOMAIN/api/docs"
echo ""
echo -e "${CYAN}📊 MANAGEMENT COMMANDS:${NC}"
echo "  📈 Status: pm2 status"
echo "  📝 Logs: pm2 logs"
echo "  🔄 Restart: pm2 restart amazingpay"
echo "  🛑 Stop: pm2 stop amazingpay"
echo ""
echo -e "${CYAN}📁 PROJECT STRUCTURE:${NC}"
echo "  📂 Root: $ROOT_DIR"
echo "  📂 Public: $ROOT_DIR/public"
echo "  📂 Source: $ROOT_DIR/src"
echo "  📂 Built: $ROOT_DIR/dist"
echo ""

# Show PM2 status
echo -e "${CYAN}📊 PM2 STATUS:${NC}"
pm2 status

echo ""
success "🎉 AmazingPay is now fully deployed and configured!"
info "Your payment gateway is ready at: http://$DOMAIN"
