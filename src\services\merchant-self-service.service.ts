// jscpd:ignore-file
/**
 * Merchant Self-Service Service
 *
 * This service provides functionality for merchant self-service tools,
 * including API key management, webhook management, and notification preferences.
 */

import { BaseService as ImportedBaseService } from "../shared/modules/services/BaseService";
import { ServiceError as ImportedServiceError } from "../shared/modules/services/ServiceError";
import { MerchantApiKey, MerchantWebhook, MerchantNotificationPreference } from "../types";
import { logger as Importedlogger } from "../utils/logger";
import { ApiErrorCode as ImportedApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { randomBytes, createHmac } from "crypto";
import prisma from "../lib/prisma";
import { ServiceError as ImportedServiceError } from "../shared/modules/services/ServiceError";
import { MerchantApiKey, MerchantWebhook, MerchantNotificationPreference } from "../types";
import { logger as Importedlogger } from "../utils/logger";
import { ApiErrorCode as ImportedApiErrorCode } from '../middlewares/apiResponseMiddleware';
import { randomBytes, createHmac } from "crypto";

/**
 * API key permissions
 */
export enum ApiKeyPermission {
  READ_TRANSACTIONS = "READ_TRANSACTIONS",
  CREATE_TRANSACTIONS = "CREATE_TRANSACTIONS",
  READ_PAYMENT_METHODS = "READ_PAYMENT_METHODS",
  MANAGE_PAYMENT_METHODS = "MANAGE_PAYMENT_METHODS",
  READ_WEBHOOKS = "READ_WEBHOOKS",
  MANAGE_WEBHOOKS = "MANAGE_WEBHOOKS",
  READ_NOTIFICATIONS = "READ_NOTIFICATIONS",
  MANAGE_NOTIFICATIONS = "MANAGE_NOTIFICATIONS",
}

/**
 * Webhook event type
 */
export enum WebhookEventType {
  TRANSACTION_CREATED = "TRANSACTION_CREATED",
  TRANSACTION_UPDATED = "TRANSACTION_UPDATED",
  TRANSACTION_COMPLETED = "TRANSACTION_COMPLETED",
  TRANSACTION_FAILED = "TRANSACTION_FAILED",
  PAYMENT_METHOD_CREATED = "PAYMENT_METHOD_CREATED",
  PAYMENT_METHOD_UPDATED = "PAYMENT_METHOD_UPDATED",
  PAYMENT_METHOD_DELETED = "PAYMENT_METHOD_DELETED",
}

/**
 * Notification channel
 */
export enum NotificationChannel {
  EMAIL = "EMAIL",
  SMS = "SMS",
  PUSH = "PUSH",
  IN_APP = "IN_APP",
}

/**
 * Merchant self-service service
 */
export class MerchantSelfServiceService extends BaseService {
  /**
   * Constructor
   */
  constructor() {
    super();
  }

  /**
   * Create a generic service error
   * @param message Error message
   * @param statusCode HTTP status code
   * @param errorCode API error code
   * @returns Service error
   */
  private genericError(message: string, statusCode: number, errorCode: ApiErrorCode): ServiceError {
    return new ServiceError(message, statusCode, errorCode);
  }
    /**
   * Create API key
   * @param merchantId Merchant ID
   * @param name API key name
   * @param permissions API key permissions
   * @param expiresAt Expiration date
   * @returns Created API key
   */
    async createApiKey(
        merchantId: string,
        name: string,
        permissions: ApiKeyPermission[],
        expiresAt?: Date
    ): Promise<MerchantApiKey> {
        try {
            // Check if merchant exists
            const merchant = await prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError("Merchant not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Generate API key
            const key: string = this.generateApiKey();

            // Create API key
            const apiKey: string = await prisma.merchantApiKey.create({
                data: {
                    merchantId,
                    name,
                    key,
                    permissions,
                    expiresAt
                }
            });

            logger.info(`Created API key for merchant ${merchantId}`);
            return apiKey;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error creating API key:", error);
            throw this.genericError("Failed to create API key", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Get merchant API keys
   * @param merchantId Merchant ID
   * @returns List of API keys
   */
    async getMerchantApiKeys(merchantId: string): Promise<MerchantApiKey[]> {
        try {
            return await prisma.merchantApiKey.findMany({
                where: { merchantId },
                orderBy: { createdAt: "desc" }
            });
        } catch (error) {
            logger.error("Error getting merchant API keys:", error);
            throw this.genericError("Failed to get merchant API keys", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Delete API key
   * @param apiKeyId API key ID
   * @returns Deleted API key
   */
    async deleteApiKey(apiKeyId: string): Promise<MerchantApiKey> {
        try {
            // Check if API key exists
            const apiKey: string = await prisma.merchantApiKey.findUnique({
                where: { id: apiKeyId }
            });

            if (!apiKey) {
                throw this.genericError("API key not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Delete API key
            const deletedApiKey = await prisma.merchantApiKey.delete({
                where: { id: apiKeyId }
            });

            logger.info(`Deleted API key ${apiKeyId}`);
            return deletedApiKey;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error deleting API key:", error);
            throw this.genericError("Failed to delete API key", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Create webhook
   * @param merchantId Merchant ID
   * @param url Webhook URL
   * @param events Webhook events
   * @returns Created webhook
   */
    async createWebhook(
        merchantId: string,
        url: string,
        events: WebhookEventType[]
    ): Promise<MerchantWebhook> {
        try {
            // Check if merchant exists
            const merchant = await prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError("Merchant not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Check if webhook URL already exists for this merchant
            const existingWebhook = await prisma.merchantWebhook.findFirst({
                where: {
                    merchantId,
                    url
                }
            });

            if (existingWebhook) {
                throw this.genericError("Webhook URL already exists for this merchant", 400, (ApiErrorCode).DUPLICATE_ENTITY);
            }

            // Generate webhook secret
            const secret = this.generateWebhookSecret();

            // Create webhook
            const webhook = await prisma.merchantWebhook.create({
                data: {
                    merchantId,
                    url,
                    events,
                    secret,
                    isActive: true
                }
            });

            logger.info(`Created webhook for merchant ${merchantId}`);
            return webhook;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error creating webhook:", error);
            throw this.genericError("Failed to create webhook", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Get merchant webhooks
   * @param merchantId Merchant ID
   * @returns List of webhooks
   */
    async getMerchantWebhooks(merchantId: string): Promise<MerchantWebhook[]> {
        try {
            return await prisma.merchantWebhook.findMany({
                where: { merchantId },
                orderBy: { createdAt: "desc" }
            });
        } catch (error) {
            logger.error("Error getting merchant webhooks:", error);
            throw this.genericError("Failed to get merchant webhooks", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Update webhook
   * @param webhookId Webhook ID
   * @param url Webhook URL
   * @param events Webhook events
   * @param isActive Whether webhook is active
   * @returns Updated webhook
   */
    async updateWebhook(
        webhookId: string,
        url?: string,
        events?: WebhookEventType[],
        isActive?: boolean
    ): Promise<MerchantWebhook> {
        try {
            // Check if webhook exists
            const webhook = await prisma.merchantWebhook.findUnique({
                where: { id: webhookId }
            });

            if (!webhook) {
                throw this.genericError("Webhook not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            if (url && url !== (webhook).url) {
                const existingWebhook = await prisma.merchantWebhook.findFirst({
                    where: { merchantId: (webhook).merchantId,
                        url,
                        id: { not: webhookId }
                    }
                });

                if (existingWebhook) {
                    throw this.genericError("Webhook URL already exists for this merchant", 400, (ApiErrorCode).DUPLICATE_ENTITY);
                }
            }

            // Update webhook
            const updatedWebhook = await prisma.merchantWebhook.update({
                where: { id: webhookId },
                data: {
                    url,
                    events,
                    isActive
                }
            });

            logger.info(`Updated webhook ${webhookId}`);
            return updatedWebhook;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error updating webhook:", error);
            throw this.genericError("Failed to update webhook", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
     * Delete webhook
     * @param webhookId Webhook ID
     * @returns Deleted webhook
     */
    async deleteWebhook(webhookId: string): Promise<MerchantWebhook> {
        try {
            // Check if webhook exists
            const webhook = await prisma.merchantWebhook.findUnique({
                where: { id: webhookId }
            });

            if (!webhook) {
                throw this.genericError("Webhook not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            const deletedWebhook = await prisma.merchantWebhook.delete({
                where: { id: webhookId }
            });

            logger.info(`Deleted webhook ${webhookId}`);
            return deletedWebhook;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error deleting webhook:", error);
            throw this.genericError("Failed to delete webhook", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Set notification preference
   * @param merchantId Merchant ID
   * @param channel Notification channel
   * @param eventType Event type
   * @param enabled Whether notifications are enabled
   * @returns Created or updated notification preference
   */
    async setNotificationPreference(
        merchantId: string,
        channel: NotificationChannel,
        eventType: WebhookEventType,
        enabled: boolean
    ): Promise<MerchantNotificationPreference> {
        try {
            // Check if merchant exists
            const merchant = await prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError("Merchant not found", 404, (ApiErrorCode).NOT_FOUND);
            }

            // Check if preference already exists
            const existingPreference = await prisma.merchantNotificationPreference.findFirst({
                where: {
                    merchantId,
                    channel,
                    eventType
                }
            });

            if (existingPreference) {
                // Update existing preference
                const updatedPreference = await prisma.merchantNotificationPreference.update({
                    where: { id: (existingPreference).id },
                    data: { enabled }
                });

                logger.info(`Updated notification preference for merchant ${merchantId}`);
                return updatedPreference;
            } else {
                // Create new preference
                const newPreference = await prisma.merchantNotificationPreference.create({
                    data: {
                        merchantId,
                        channel,
                        eventType,
                        enabled
                    }
                });

                logger.info(`Created notification preference for merchant ${merchantId}`);
                return newPreference;
            }
        } catch (error) {
            if (error instanceof ServiceError) {
                throw error;
            }
            logger.error("Error setting notification preference:", error);
            throw this.genericError("Failed to set notification preference", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Get merchant notification preferences
   * @param merchantId Merchant ID
   * @returns List of notification preferences
   */
    async getMerchantNotificationPreferences(merchantId: string): Promise<MerchantNotificationPreference[]> {
        try {
            return await prisma.merchantNotificationPreference.findMany({
                where: { merchantId },
                orderBy: [
                    { channel: "asc" },
                    { eventType: "asc" }
                ]
            });
        } catch (error) {
            logger.error("Error getting merchant notification preferences:", error);
            throw this.genericError("Failed to get merchant notification preferences", 500, (ApiErrorCode).SERVER_ERROR);
        }
    }

    /**
   * Generate API key
   * @returns Generated API key
   */
    private generateApiKey(): string {
        const prefix = "apk_";
        const key: string = randomBytes(32).toString("hex");
        return `${prefix}${key}`;
    }

    /**
   * Generate webhook secret
   * @returns Generated webhook secret
   */
    private generateWebhookSecret(): string {
        const prefix = "whsec_";
        const secret = randomBytes(32).toString("hex");
        return `${prefix}${secret}`;
    }

    /**
   * Verify webhook signature
   * @param payload Webhook payload
   * @param signature Webhook signature
   * @param secret Webhook secret
   * @returns Whether signature is valid
   */
    verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
        try {
            const hmac = createHmac("sha256", secret);
            const expectedSignature = (hmac).update(payload).digest("hex");
            return signature === expectedSignature;
        } catch (error) {
            logger.error("Error verifying webhook signature:", error);
            return false;
        }
    }
}