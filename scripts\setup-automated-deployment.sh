#!/bin/bash

# AmazingPay Flow - Automated Deployment Setup
# This script sets up automated deployment from GitHub to your VPS

set -e

echo "🚀 Setting up Automated GitHub → VPS Deployment"
echo "================================================"

# Configuration
VPS_USER="root"
VPS_HOST=""
GITHUB_REPO=""
DOMAIN_NAME=""
WEBHOOK_SECRET=""

# Collect configuration
read -p "Enter your VPS IP address: " VPS_HOST
read -p "Enter your GitHub repository (username/repo): " GITHUB_REPO
read -p "Enter your domain name: " DOMAIN_NAME
read -p "Enter a webhook secret (or press Enter to generate): " WEBHOOK_SECRET

if [ -z "$WEBHOOK_SECRET" ]; then
    WEBHOOK_SECRET=$(openssl rand -hex 32)
    echo "Generated webhook secret: $WEBHOOK_SECRET"
fi

echo ""
echo "📋 Configuration Summary:"
echo "VPS Host: $VPS_HOST"
echo "GitHub Repo: $GITHUB_REPO"
echo "Domain: $DOMAIN_NAME"
echo "Webhook Secret: $WEBHOOK_SECRET"
echo ""

read -p "Continue with setup? (y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "Setup cancelled."
    exit 1
fi

# Create webhook handler script for VPS
cat > webhook-handler.js << 'EOF'
#!/usr/bin/env node

/**
 * AmazingPay Flow - GitHub Webhook Handler
 * Handles automatic deployment when code is pushed to GitHub
 */

const express = require('express');
const crypto = require('crypto');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.WEBHOOK_PORT || 9000;
const SECRET = process.env.WEBHOOK_SECRET;
const APP_DIR = '/www/wwwroot/amazingpay-flow';
const LOG_FILE = '/var/log/amazingpay-deployment.log';

app.use(express.json());

// Logging function
function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(logMessage.trim());
    fs.appendFileSync(LOG_FILE, logMessage);
}

// Verify GitHub webhook signature
function verifySignature(payload, signature) {
    if (!SECRET) {
        log('WARNING: No webhook secret configured');
        return true;
    }
    
    const hmac = crypto.createHmac('sha256', SECRET);
    hmac.update(payload);
    const calculatedSignature = `sha256=${hmac.digest('hex')}`;
    
    return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(calculatedSignature)
    );
}

// Execute deployment
async function deploy(branch = 'main') {
    log(`Starting deployment for branch: ${branch}`);
    
    try {
        // Change to app directory
        process.chdir(APP_DIR);
        
        // Create backup before deployment
        const backupName = `backup-${Date.now()}`;
        log(`Creating backup: ${backupName}`);
        execSync(`./backup-vps.sh ${backupName}`, { stdio: 'inherit' });
        
        // Pull latest changes
        log('Pulling latest changes from GitHub...');
        execSync(`git pull origin ${branch}`, { stdio: 'inherit' });
        
        // Install dependencies
        log('Installing dependencies...');
        execSync('npm ci --production', { stdio: 'inherit' });
        
        // Run database migrations
        log('Running database migrations...');
        execSync('npx prisma migrate deploy', { stdio: 'inherit' });
        
        // Build application
        log('Building application...');
        execSync('npm run build', { stdio: 'inherit' });
        
        // Restart PM2 processes
        log('Restarting application...');
        execSync('pm2 restart amazingpay-flow', { stdio: 'inherit' });
        
        // Health check
        log('Performing health check...');
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        
        try {
            execSync('curl -f http://localhost:3002/api/health', { stdio: 'pipe' });
            log('✅ Deployment successful - Health check passed');
            return { success: true, backup: backupName };
        } catch (error) {
            log('❌ Health check failed - Rolling back...');
            await rollback(backupName);
            throw new Error('Deployment failed health check');
        }
        
    } catch (error) {
        log(`❌ Deployment failed: ${error.message}`);
        throw error;
    }
}

// Rollback to previous version
async function rollback(backupName) {
    log(`Rolling back to backup: ${backupName}`);
    
    try {
        process.chdir(APP_DIR);
        
        // Restore from backup
        execSync(`./restore-backup.sh ${backupName}`, { stdio: 'inherit' });
        
        // Restart PM2
        execSync('pm2 restart amazingpay-flow', { stdio: 'inherit' });
        
        log('✅ Rollback completed successfully');
        return { success: true };
        
    } catch (error) {
        log(`❌ Rollback failed: ${error.message}`);
        throw error;
    }
}

// Webhook endpoint
app.post('/webhook', async (req, res) => {
    const signature = req.headers['x-hub-signature-256'];
    const payload = JSON.stringify(req.body);
    
    // Verify signature
    if (!verifySignature(payload, signature)) {
        log('❌ Invalid webhook signature');
        return res.status(401).send('Unauthorized');
    }
    
    const event = req.headers['x-github-event'];
    const body = req.body;
    
    log(`Received GitHub webhook: ${event}`);
    
    // Handle push events to main branch
    if (event === 'push' && body.ref === 'refs/heads/main') {
        log('Push to main branch detected - Starting deployment...');
        
        try {
            const result = await deploy('main');
            log('✅ Automated deployment completed successfully');
            res.json({ success: true, message: 'Deployment successful', backup: result.backup });
        } catch (error) {
            log(`❌ Automated deployment failed: ${error.message}`);
            res.status(500).json({ success: false, message: error.message });
        }
    } else {
        log(`Ignoring ${event} event for ref: ${body.ref || 'unknown'}`);
        res.json({ success: true, message: 'Event ignored' });
    }
});

// Manual deployment endpoint
app.post('/deploy', async (req, res) => {
    const { branch = 'main', secret } = req.body;
    
    if (secret !== SECRET) {
        return res.status(401).json({ success: false, message: 'Unauthorized' });
    }
    
    try {
        const result = await deploy(branch);
        res.json({ success: true, message: 'Manual deployment successful', backup: result.backup });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
});

// Manual rollback endpoint
app.post('/rollback', async (req, res) => {
    const { backup, secret } = req.body;
    
    if (secret !== SECRET) {
        return res.status(401).json({ success: false, message: 'Unauthorized' });
    }
    
    try {
        await rollback(backup);
        res.json({ success: true, message: 'Rollback successful' });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
});

// Status endpoint
app.get('/status', (req, res) => {
    try {
        const status = execSync('pm2 jlist', { encoding: 'utf8' });
        const processes = JSON.parse(status);
        const amazingpayProcess = processes.find(p => p.name === 'amazingpay-flow');
        
        res.json({
            success: true,
            status: amazingpayProcess ? amazingpayProcess.pm2_env.status : 'not found',
            uptime: amazingpayProcess ? amazingpayProcess.pm2_env.pm_uptime : null,
            restarts: amazingpayProcess ? amazingpayProcess.pm2_env.restart_time : null
        });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ success: true, message: 'Webhook handler is running', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
    log(`🚀 GitHub webhook handler started on port ${PORT}`);
    log(`📝 Logs are being written to: ${LOG_FILE}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
    log('Webhook handler shutting down...');
    process.exit(0);
});

process.on('SIGINT', () => {
    log('Webhook handler shutting down...');
    process.exit(0);
});
EOF

# Create systemd service for webhook handler
cat > webhook-handler.service << EOF
[Unit]
Description=AmazingPay Flow GitHub Webhook Handler
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/www/wwwroot/amazingpay-flow
ExecStart=/usr/bin/node /www/wwwroot/amazingpay-flow/webhook-handler.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=WEBHOOK_SECRET=$WEBHOOK_SECRET
Environment=WEBHOOK_PORT=9000

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=amazingpay-webhook

[Install]
WantedBy=multi-user.target
EOF

# Create backup restoration script
cat > restore-backup.sh << 'EOF'
#!/bin/bash

# AmazingPay Flow - Backup Restoration Script

set -e

BACKUP_NAME="$1"
BACKUP_DIR="/var/backups/amazingpay"
APP_DIR="/www/wwwroot/amazingpay-flow"

if [ -z "$BACKUP_NAME" ]; then
    echo "Usage: $0 <backup-name>"
    echo "Available backups:"
    ls -la "$BACKUP_DIR"
    exit 1
fi

BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"

if [ ! -d "$BACKUP_PATH" ]; then
    echo "❌ Backup not found: $BACKUP_PATH"
    exit 1
fi

echo "🔄 Restoring from backup: $BACKUP_NAME"

# Stop application
pm2 stop amazingpay-flow || echo "Application not running"

# Restore application files
echo "📁 Restoring application files..."
rsync -av --delete "$BACKUP_PATH/app/" "$APP_DIR/"

# Restore database
if [ -f "$BACKUP_PATH/database.sql" ]; then
    echo "🗄️ Restoring database..."
    psql -U postgres -d amazingpay_production < "$BACKUP_PATH/database.sql"
fi

# Restore environment
if [ -f "$BACKUP_PATH/.env.production" ]; then
    echo "⚙️ Restoring environment configuration..."
    cp "$BACKUP_PATH/.env.production" "$APP_DIR/.env.production"
fi

# Start application
echo "🚀 Starting application..."
cd "$APP_DIR"
pm2 start amazingpay-flow

echo "✅ Restoration completed successfully"
EOF

chmod +x restore-backup.sh

echo ""
echo "📁 Created files:"
echo "  - webhook-handler.js (GitHub webhook handler)"
echo "  - webhook-handler.service (systemd service)"
echo "  - restore-backup.sh (backup restoration script)"
echo ""
echo "🔧 Next steps:"
echo "1. Copy these files to your VPS"
echo "2. Run the VPS setup commands"
echo "3. Configure GitHub webhook"
echo ""
echo "📋 VPS Setup Commands:"
echo "====================="
echo ""
echo "# Copy files to VPS"
echo "scp webhook-handler.js $VPS_USER@$VPS_HOST:/www/wwwroot/amazingpay-flow/"
echo "scp webhook-handler.service $VPS_USER@$VPS_HOST:/etc/systemd/system/"
echo "scp restore-backup.sh $VPS_USER@$VPS_HOST:/www/wwwroot/amazingpay-flow/"
echo ""
echo "# SSH to VPS and run:"
echo "ssh $VPS_USER@$VPS_HOST"
echo "cd /www/wwwroot/amazingpay-flow"
echo "chmod +x webhook-handler.js restore-backup.sh"
echo "systemctl daemon-reload"
echo "systemctl enable webhook-handler"
echo "systemctl start webhook-handler"
echo ""
echo "# Configure Nginx reverse proxy for webhook"
echo "# Add to your Nginx config:"
echo "location /webhook {"
echo "    proxy_pass http://127.0.0.1:9000;"
echo "    proxy_set_header Host \$host;"
echo "    proxy_set_header X-Real-IP \$remote_addr;"
echo "    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;"
echo "    proxy_set_header X-Forwarded-Proto \$scheme;"
echo "}"
echo ""
echo "🌐 GitHub Webhook Configuration:"
echo "==============================="
echo "URL: https://$DOMAIN_NAME/webhook"
echo "Content type: application/json"
echo "Secret: $WEBHOOK_SECRET"
echo "Events: Just the push event"
echo ""
echo "✅ Setup files created successfully!"
echo "📝 Save this webhook secret: $WEBHOOK_SECRET"
