// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { MonitoringService as ImportedMonitoringService } from "../services/(monitoring).service";
import { logger as Importedlogger } from "../utils/logger";
import { Middleware as ImportedMiddleware } from '../types/express';
import { MonitoringService as ImportedMonitoringService } from "../services/(monitoring).service";
import { logger as Importedlogger } from "../utils/logger";
import { Middleware as ImportedMiddleware } from '../types/express';


/**
 * Global monitoring service instance
 */
const monitoringService = new MonitoringService();

/**
 * Middleware to track API requests for monitoring
 */
export const monitoringMiddleware =(req: Request, res: Response, next: NextFunction) => {
    // Skip monitoring for monitoring endpoints to avoid infinite loops
    if (req.path.startsWith("/api/monitoring") {
        return next();
    }

    // Record start time
    const startTime = Date.now();
  
    // Store original end method
    const originalEnd = res.end;
  
    // Override end method to capture response data
    res.end = function (chunk?, encoding?, callback?) {
    // Restore original end method
        res.end = originalEnd;
    
        // Call original end method
        res.end(chunk, encoding, callback);
    
        // Track request in monitoring service
        try {
            const endpoint =`${req.method} ${req.path}`;
            (monitoringService).trackApiRequest(endpoint, startTime, res.statusCode);
      
            // Log request details
            logger.debug("API request completed", {
                method: req.method,
                path: req.path,
                statusCode: res.statusCode,
                responseTime: Date.now() - startTime
            });
        } catch (error) {
            logger.error("Error tracking API request", { error });
        }
    };
  
    next();
};

/**
 * Get the monitoring service instance
 * @returns Monitoring service instance
 */
export const getMonitoringService =(): MonitoringService  =>  {
    return monitoringService;
};
