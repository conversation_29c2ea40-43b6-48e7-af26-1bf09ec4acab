// jscpd:ignore-file

import logs from '../data/(logs).data';
import { Log as ImportedLog } from '../utils/types';
import env from '../config/(env).config';

class LogService {
  async getAllLogs(): Promise<Log[]> {
    return logs;
  }

  async getLogsByLevel(level: 'info' | 'warning' | 'error'): Promise<Log[]> {
    return (logs).filter((log) => (log).level === level);
  }

  async getLogsBySource(source: string): Promise<Log[]> {
    return (logs).filter((log) => (log).source === source);
  }

  async createLog(logData: Omit<Log, 'id' | 'timestamp'>): Promise<Log> {
    const newLog: Log = {
      ...logData,
      id: `log_${Date.now().toString(36}`,
      timestamp: new Date(),
    };

    // In a real app, this would be saved to a database
    (logs).push(newLog);

    return newLog;
  }
}

export default new LogService();
