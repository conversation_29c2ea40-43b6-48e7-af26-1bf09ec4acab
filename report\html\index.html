<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Copy/Paste Detector Report</title><link href="styles/tailwind.css" rel="stylesheet"><link href="styles/prism.css" rel="stylesheet"></head><body class="bg-gray-100"><header class="bg-white shadow py-4"><div class="container mx-auto px-4"><h1 class="text-3xl font-semibold text-gray-800">jscpd - copy/paste report</h1></div></header><main class="container mx-auto my-8 p-4 bg-white shadow rounded"><section class="mb-8" id="dashboard"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Dashboard</h2><div class="grid grid-cols-4 gap-4"><div class="bg-blue-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-blue-800 mb-2">Total Files</h3><span class="text-4xl font-bold text-blue-800">418</span></div><div class="bg-green-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-green-800 mb-2">Total Lines of Code</h3><span class="text-4xl font-bold text-green-800">88973</span></div><div class="bg-yellow-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-yellow-800 mb-2">Number of Clones</h3><span class="text-4xl font-bold text-yellow-800">242</span></div><div class="bg-red-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-red-800 mb-2">Duplicated Lines</h3><span class="text-4xl font-bold text-red-800">4201 (4.72%)</span></div></div></section><section class="mb-8" id="formats"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Formats with Duplications</h2><table class="w-full table-auto"><thead><tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal"><th class="py-3 px-6 text-left">Format</th><th class="py-3 px-6 text-left">Files</th><th class="py-3 px-6 text-left">Lines</th><th class="py-3 px-6 text-left">Clones</th><th class="py-3 px-6 text-left">Duplicated Lines</th><th class="py-3 px-6 text-left">Duplicated Tokens</th></tr></thead><tbody><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#typescript-clones">typescript</a></td><td class="py-3 px-6">405</td><td class="py-3 px-6">86024</td><td class="py-3 px-6">237</td><td class="py-3 px-6">4163</td><td class="py-3 px-6">31812</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#javascript-clones">javascript</a></td><td class="py-3 px-6">13</td><td class="py-3 px-6">2949</td><td class="py-3 px-6">5</td><td class="py-3 px-6">38</td><td class="py-3 px-6">519</td></tr></tbody></table></section><section class="mb-8" id="txt-clones"><a name="typescript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">typescript</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">src\tests\utils\suites\TestSuiteBuilders.ts (Line 96:12 - Line 114:8), src\tests\utils\suites\TestSuiteBuilders.ts (Line 30:15 - Line 48:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup0"><code class="language-typescript text-sm text-gray-800">;

    // Global setup
    beforeAll(async () =&gt; {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () =&gt; {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Setup before each test
    beforeEach(async () =&gt; {
      service</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\suites\TestSuiteBuilders.ts (Line 120:7 - Line 147:12), src\tests\utils\suites\TestSuiteBuilders.ts (Line 50:7 - Line 77:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup1"><code class="language-typescript text-sm text-gray-800">if (config.beforeEach) {
        await config.beforeEach();
      }
    });

    // Cleanup after each test
    afterEach(async () =&gt; {
      if (config.afterEach) {
        await config.afterEach();
      }
    });

    // Create individual test cases
    Object.entries(tests).forEach(([method, testOptions]) =&gt; {
      const testName = testOptions.description || `should test ${method}`;
      const timeout = testOptions.timeout || config.timeout || 10000;

      if (testOptions.skip) {
        it.skip(testName, () =&gt; {});
        return;
      }

      const testFn = testOptions.only ? it.only : it;

      testFn(
        testName,
        async () =&gt; {
          await testService</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\suites\TestSuiteBuilders.ts (Line 165:4 - Line 183:11), src\tests\utils\suites\TestSuiteBuilders.ts (Line 30:15 - Line 48:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup2"><code class="language-typescript text-sm text-gray-800">;

    // Global setup
    beforeAll(async () =&gt; {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () =&gt; {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Setup before each test
    beforeEach(async () =&gt; {
      repository</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\suites\TestSuiteBuilders.ts (Line 183:16 - Line 212:15), src\tests\utils\suites\TestSuiteBuilders.ts (Line 48:16 - Line 77:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup3"><code class="language-typescript text-sm text-gray-800">();

      if (config.beforeEach) {
        await config.beforeEach();
      }
    });

    // Cleanup after each test
    afterEach(async () =&gt; {
      if (config.afterEach) {
        await config.afterEach();
      }
    });

    // Create individual test cases
    Object.entries(tests).forEach(([method, testOptions]) =&gt; {
      const testName = testOptions.description || `should test ${method}`;
      const timeout = testOptions.timeout || config.timeout || 10000;

      if (testOptions.skip) {
        it.skip(testName, () =&gt; {});
        return;
      }

      const testFn = testOptions.only ? it.only : it;

      testFn(
        testName,
        async () =&gt; {
          await testRepository</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\suites\TestSuiteBuilders.ts (Line 229:5 - Line 245:3), src\tests\utils\suites\TestSuiteBuilders.ts (Line 32:5 - Line 48:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup4"><code class="language-typescript text-sm text-gray-800">// Global setup
    beforeAll(async () =&gt; {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () =&gt; {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Setup before each test
    beforeEach(async () =&gt; {
      if</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\suites\TestSuiteBuilders.ts (Line 307:15 - Line 322:28), src\tests\utils\suites\TestSuiteBuilders.ts (Line 228:15 - Line 46:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup5"><code class="language-typescript text-sm text-gray-800">${name}`, () =&gt; {
    // Global setup
    beforeAll(async () =&gt; {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () =&gt; {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Create performance tests</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\suites\TestSuiteBuilders.ts (Line 381:7 - Line 396:20), src\tests\utils\suites\TestSuiteBuilders.ts (Line 228:15 - Line 46:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup6"><code class="language-typescript text-sm text-gray-800">${name}`, () =&gt; {
    // Global setup
    beforeAll(async () =&gt; {
      if (config.setup) {
        await config.setup();
      }
    });

    // Global teardown
    afterAll(async () =&gt; {
      if (config.teardown) {
        await config.teardown();
      }
    });

    // Create API tests</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\runners\TestRunners.ts (Line 206:5 - Line 215:8), src\tests\utils\runners\TestRunners.ts (Line 103:5 - Line 110:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn7" onclick="toggleCodeBlock('cloneGroup7', 'expandBtn7', 'collapseBtn7')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn7" onclick="toggleCodeBlock('cloneGroup7', 'expandBtn7', 'collapseBtn7')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup7"><code class="language-typescript text-sm text-gray-800">// Handle expected errors
    if (options.expectedError) {
      if (typeof options.expectedError === 'function') {
        expect(error).toEqual(expect.objectContaining(options.expectedError()));
      } else {
        expect(error).toEqual(options.expectedError);
      }

      return {
        success</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\runners\TestRunners.ts (Line 289:5 - Line 304:18), src\tests\utils\runners\TestRunners.ts (Line 169:5 - Line 184:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn8" onclick="toggleCodeBlock('cloneGroup8', 'expandBtn8', 'collapseBtn8')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn8" onclick="toggleCodeBlock('cloneGroup8', 'expandBtn8', 'collapseBtn8')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup8"><code class="language-typescript text-sm text-gray-800">// Validate result if validator provided
    if (options.validateResult) {
      await options.validateResult(result);
    }

    // Assert expected result
    if (options.expectedResult !== undefined) {
      if (typeof options.expectedResult === 'function') {
        expect(result).toEqual(expect.objectContaining(options.expectedResult()));
      } else {
        expect(result).toEqual(options.expectedResult);
      }
    }

    // Run cleanup hooks
    if (options.repositoryCleanup</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\runners\TestRunners.ts (Line 305:11 - Line 322:9), src\tests\utils\runners\TestRunners.ts (Line 185:8 - Line 202:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn9" onclick="toggleCodeBlock('cloneGroup9', 'expandBtn9', 'collapseBtn9')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn9" onclick="toggleCodeBlock('cloneGroup9', 'expandBtn9', 'collapseBtn9')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup9"><code class="language-typescript text-sm text-gray-800">);
    }

    if (options.cleanup) {
      await options.cleanup();
    }

    if (options.afterEach) {
      await options.afterEach();
    }

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      result,
      executionTime,
      metadata</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\runners\TestRunners.ts (Line 325:2 - Line 356:4), src\tests\utils\runners\TestRunners.ts (Line 201:14 - Line 232:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn10" onclick="toggleCodeBlock('cloneGroup10', 'expandBtn10', 'collapseBtn10')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn10" onclick="toggleCodeBlock('cloneGroup10', 'expandBtn10', 'collapseBtn10')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup10"><code class="language-typescript text-sm text-gray-800">,
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;

    // Handle expected errors
    if (options.expectedError) {
      if (typeof options.expectedError === 'function') {
        expect(error).toEqual(expect.objectContaining(options.expectedError()));
      } else {
        expect(error).toEqual(options.expectedError);
      }

      return {
        success: true,
        error: error instanceof Error ? error : new Error(String(error)),
        executionTime,
      };
    }

    // Return test failure result
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
      executionTime,
    };
  }
}

/**
 * Test middleware
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\runners\TestRunners.ts (Line 422:2 - Line 431:3), src\tests\utils\runners\TestRunners.ts (Line 101:2 - Line 110:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn11" onclick="toggleCodeBlock('cloneGroup11', 'expandBtn11', 'collapseBtn11')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn11" onclick="toggleCodeBlock('cloneGroup11', 'expandBtn11', 'collapseBtn11')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup11"><code class="language-typescript text-sm text-gray-800">};
  } catch (error) {
    // Handle expected errors
    if (options.expectedError) {
      if (typeof options.expectedError === 'function') {
        expect(error).toEqual(expect.objectContaining(options.expectedError()));
      } else {
        expect(error).toEqual(options.expectedError);
      }
      return { req: req as</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\runners\TestRunners.ts (Line 478:8 - Line 493:7), src\tests\utils\runners\TestRunners.ts (Line 193:10 - Line 208:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn12" onclick="toggleCodeBlock('cloneGroup12', 'expandBtn12', 'collapseBtn12')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn12" onclick="toggleCodeBlock('cloneGroup12', 'expandBtn12', 'collapseBtn12')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup12"><code class="language-typescript text-sm text-gray-800">();
    }

    const executionTime = Date.now() - startTime;

    return {
      success: true,
      result,
      executionTime,
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;

    // Handle expected errors
    if (options.expectedError) {
      expect</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\runners\TestRunners.ts (Line 494:7 - Line 508:2), src\tests\utils\runners\TestRunners.ts (Line 214:7 - Line 228:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn13" onclick="toggleCodeBlock('cloneGroup13', 'expandBtn13', 'collapseBtn13')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn13" onclick="toggleCodeBlock('cloneGroup13', 'expandBtn13', 'collapseBtn13')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup13"><code class="language-typescript text-sm text-gray-800">return {
        success: true,
        error: error instanceof Error ? error : new Error(String(error)),
        executionTime,
      };
    }

    // Return test failure result
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
      executionTime,
    };
  }
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\core\TestTypes.ts (Line 21:2 - Line 41:4), src\tests\utils\factories\MockFactories.ts (Line 17:2 - Line 33:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn14" onclick="toggleCodeBlock('cloneGroup14', 'expandBtn14', 'collapseBtn14')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn14" onclick="toggleCodeBlock('cloneGroup14', 'expandBtn14', 'collapseBtn14')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup14"><code class="language-typescript text-sm text-gray-800">{
  params?: any;
  query?: any;
  body?: any;
  headers?: any;
  user?: any;
  session?: any;
  cookies?: any;
  ip?: string;
  method?: string;
  url?: string;
  originalUrl?: string;
  path?: string;
  protocol?: string;
  secure?: boolean;
  xhr?: boolean;
}

/**
 * Mock response interface
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\identity-verification\core\IdentityVerificationService.ts (Line 153:11 - Line 160:6), src\services\identity-verification\core\IdentityVerificationService.ts (Line 102:7 - Line 110:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn15" onclick="toggleCodeBlock('cloneGroup15', 'expandBtn15', 'collapseBtn15')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn15" onclick="toggleCodeBlock('cloneGroup15', 'expandBtn15', 'collapseBtn15')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup15"><code class="language-typescript text-sm text-gray-800">;
      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;
        if (filters.dateTo) where.createdAt.lte = filters.dateTo;
      }

      const</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\fraud-detection\detectors\VelocityRiskDetector.ts (Line 230:11 - Line 241:2), src\services\fraud-detection\detectors\VelocityRiskDetector.ts (Line 120:11 - Line 131:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn16" onclick="toggleCodeBlock('cloneGroup16', 'expandBtn16', 'collapseBtn16')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn16" onclick="toggleCodeBlock('cloneGroup16', 'expandBtn16', 'collapseBtn16')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup16"><code class="language-typescript text-sm text-gray-800">createdAt: { gte: timeWindow },
          status: { not: 'FAILED' },
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      const currentCount = recentTransactions.length;
      const currentAmount = recentTransactions.reduce((sum, t) =&gt; sum + t.amount, 0);
      const maxAllowed = 5</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\fraud-detection\detectors\VelocityRiskDetector.ts (Line 242:7 - Line 257:36), src\services\fraud-detection\detectors\VelocityRiskDetector.ts (Line 132:7 - Line 147:36)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn17" onclick="toggleCodeBlock('cloneGroup17', 'expandBtn17', 'collapseBtn17')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn17" onclick="toggleCodeBlock('cloneGroup17', 'expandBtn17', 'collapseBtn17')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup17"><code class="language-typescript text-sm text-gray-800">const maxAmount = config.velocitySettings.maxAmount;

      const countViolation = currentCount &gt; maxAllowed;
      const amountViolation = currentAmount &gt; maxAmount;

      return {
        isViolation: countViolation || amountViolation,
        currentCount,
        currentAmount,
        maxAllowed,
        timeWindow: `${config.velocitySettings.timeWindowMinutes} minutes`,
        violationType:
          countViolation &amp;&amp; amountViolation ? 'BOTH' : countViolation ? 'COUNT' : 'AMOUNT',
      };
    } catch (error) {
      logger.error('Error checking customer velocity:'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 103:16 - Line 118:4), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 55:10 - Line 70:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn18" onclick="toggleCodeBlock('cloneGroup18', 'expandBtn18', 'collapseBtn18')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn18" onclick="toggleCodeBlock('cloneGroup18', 'expandBtn18', 'collapseBtn18')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup18"><code class="language-typescript text-sm text-gray-800">,
      });
    }

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      ein</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 126:2 - Line 139:4), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 78:2 - Line 91:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn19" onclick="toggleCodeBlock('cloneGroup19', 'expandBtn19', 'collapseBtn19')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn19" onclick="toggleCodeBlock('cloneGroup19', 'expandBtn19', 'collapseBtn19')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup19"><code class="language-typescript text-sm text-gray-800">{
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: data.address,
      });
    }

    if (!data.key</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 148:2 - Line 162:4), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 56:7 - Line 70:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn20" onclick="toggleCodeBlock('cloneGroup20', 'expandBtn20', 'collapseBtn20')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn20" onclick="toggleCodeBlock('cloneGroup20', 'expandBtn20', 'collapseBtn20')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup20"><code class="language-typescript text-sm text-gray-800">});
    }

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      key</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 179:5 - Line 189:7), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 81:5 - Line 91:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn21" onclick="toggleCodeBlock('cloneGroup21', 'expandBtn21', 'collapseBtn21')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn21" onclick="toggleCodeBlock('cloneGroup21', 'expandBtn21', 'collapseBtn21')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup21"><code class="language-typescript text-sm text-gray-800">if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({
        field: 'address',
        message: 'Invalid Ethereum address format',
        value: data.address,
      });
    }

    if (errors</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 207:2 - Line 213:25), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 78:2 - Line 86:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn22" onclick="toggleCodeBlock('cloneGroup22', 'expandBtn22', 'collapseBtn22')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn22" onclick="toggleCodeBlock('cloneGroup22', 'expandBtn22', 'collapseBtn22')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup22"><code class="language-typescript text-sm text-gray-800">{
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({ field: 'address', message: 'Invalid address format'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 219:2 - Line 233:6), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 56:7 - Line 70:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn23" onclick="toggleCodeBlock('cloneGroup23', 'expandBtn23', 'collapseBtn23')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn23" onclick="toggleCodeBlock('cloneGroup23', 'expandBtn23', 'collapseBtn23')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup23"><code class="language-typescript text-sm text-gray-800">});
    }

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      proof</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 240:2 - Line 249:10), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 78:2 - Line 216:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn24" onclick="toggleCodeBlock('cloneGroup24', 'expandBtn24', 'collapseBtn24')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn24" onclick="toggleCodeBlock('cloneGroup24', 'expandBtn24', 'collapseBtn24')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup24"><code class="language-typescript text-sm text-gray-800">{
    const errors: ValidationError[] = [];

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({ field: 'address', message: 'Invalid address format', value: data.address });
    }

    if (!data.nullifier</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 252:2 - Line 272:10), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 213:2 - Line 70:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn25" onclick="toggleCodeBlock('cloneGroup25', 'expandBtn25', 'collapseBtn25')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn25" onclick="toggleCodeBlock('cloneGroup25', 'expandBtn25', 'collapseBtn25')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup25"><code class="language-typescript text-sm text-gray-800">});
    }

    if (!data.proof) {
      errors.push({ field: 'proof', message: 'Proof is required' });
    } else if (typeof data.proof !== 'object') {
      errors.push({ field: 'proof', message: 'Proof must be an object' });
    }

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      address: data.address.toLowerCase(),
      nullifier</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 286:2 - Line 295:7), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 176:2 - Line 216:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn26" onclick="toggleCodeBlock('cloneGroup26', 'expandBtn26', 'collapseBtn26')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn26" onclick="toggleCodeBlock('cloneGroup26', 'expandBtn26', 'collapseBtn26')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup26"><code class="language-typescript text-sm text-gray-800">});
    }

    if (!data.address) {
      errors.push({ field: 'address', message: 'Address is required' });
    } else if (!this.isValidEthereumAddress(data.address)) {
      errors.push({ field: 'address', message: 'Invalid address format', value: data.address });
    }

    if (errors</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 371:2 - Line 389:10), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 51:2 - Line 69:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn27" onclick="toggleCodeBlock('cloneGroup27', 'expandBtn27', 'collapseBtn27')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn27" onclick="toggleCodeBlock('cloneGroup27', 'expandBtn27', 'collapseBtn27')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup27"><code class="language-typescript text-sm text-gray-800">!this.isValidSignature(data.signature)) {
      errors.push({
        field: 'signature',
        message: 'Invalid signature format',
        value: data.signature,
      });
    }

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      requestId</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 413:2 - Line 422:2), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 142:2 - Line 151:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn28" onclick="toggleCodeBlock('cloneGroup28', 'expandBtn28', 'collapseBtn28')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn28" onclick="toggleCodeBlock('cloneGroup28', 'expandBtn28', 'collapseBtn28')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup28"><code class="language-typescript text-sm text-gray-800">});
    }

    if (!data.value) {
      errors.push({ field: 'value', message: 'Value is required' });
    } else if (typeof data.value !== 'string' || data.value.trim().length === 0) {
      errors.push({ field: 'value', message: 'Value must be a non-empty string' });
    }

    if (!</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\validators\BaseValidator.ts (Line 97:2 - Line 106:2), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 476:3 - Line 484:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn29" onclick="toggleCodeBlock('cloneGroup29', 'expandBtn29', 'collapseBtn29')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn29" onclick="toggleCodeBlock('cloneGroup29', 'expandBtn29', 'collapseBtn29')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup29"><code class="language-typescript text-sm text-gray-800">} {
    const page = query.page ? parseInt(query.page, 10) : 1;
    const limit = query.limit ? parseInt(query.limit, 10) : 10;

    if (isNaN(page) || page &lt; 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT
      }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\validators\BaseValidator.ts (Line 126:9 - Line 137:2), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 505:9 - Line 515:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn30" onclick="toggleCodeBlock('cloneGroup30', 'expandBtn30', 'collapseBtn30')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn30" onclick="toggleCodeBlock('cloneGroup30', 'expandBtn30', 'collapseBtn30')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup30"><code class="language-typescript text-sm text-gray-800">});
      }
      result.sortBy = query.sortBy;
    }

    if (query.sortOrder) {
      if (!['asc', 'desc'].includes(query.sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either &quot;asc&quot; or &quot;desc&quot;',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT
        }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\types\FraudDetectionControllerTypes.ts (Line 158:7 - Line 183:4), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 176:2 - Line 201:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn31" onclick="toggleCodeBlock('cloneGroup31', 'expandBtn31', 'collapseBtn31')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn31" onclick="toggleCodeBlock('cloneGroup31', 'expandBtn31', 'collapseBtn31')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup31"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * API response wrapper
 */
export interface ApiResponse&lt;T = any&gt; {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Fraud detection filters
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\types\FraudDetectionControllerTypes.ts (Line 192:7 - Line 232:34), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 210:7 - Line 250:40)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn32" onclick="toggleCodeBlock('cloneGroup32', 'expandBtn32', 'collapseBtn32')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn32" onclick="toggleCodeBlock('cloneGroup32', 'expandBtn32', 'collapseBtn32')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup32"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

/**
 * Controller method options
 */
export interface ControllerMethodOptions {
  requireAuth?: boolean;
  requiredRole?: string;
  validateInput?: boolean;
  logRequest?: boolean;
}

/**
 * Request context interface
 */
export interface RequestContext {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
  requestId: string;
  timestamp: Date;
  ip: string;
  userAgent: string;
}

/**
 * Service dependencies interface
 */
export interface FraudDetectionServiceDependencies</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\types\FraudDetectionControllerTypes.ts (Line 251:7 - Line 303:4), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 283:7 - Line 335:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn33" onclick="toggleCodeBlock('cloneGroup33', 'expandBtn33', 'collapseBtn33')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn33" onclick="toggleCodeBlock('cloneGroup33', 'expandBtn33', 'collapseBtn33')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup33"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * Error response format
 */
export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    type: string;
    details?: any;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Success response format
 */
export interface SuccessResponse&lt;T = any&gt; {
  success: true;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Controller method result
 */
export type ControllerResult&lt;T = any&gt; = Promise&lt;SuccessResponse&lt;T&gt; | ErrorResponse&gt;;

/**
 * Middleware function type
 */
export type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) =&gt; void | Promise&lt;void&gt;;

/**
 * Controller method type
 */
export type ControllerMethod&lt;T = any&gt; = (req: AuthenticatedRequest, res: Response) =&gt; ControllerResult&lt;T&gt;;

/**
 * Authorization context
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\types\FraudDetectionControllerTypes.ts (Line 301:1 - Line 323:2), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 379:1 - Line 401:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn34" onclick="toggleCodeBlock('cloneGroup34', 'expandBtn34', 'collapseBtn34')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn34" onclick="toggleCodeBlock('cloneGroup34', 'expandBtn34', 'collapseBtn34')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup34"><code class="language-typescript text-sm text-gray-800">/**
 * Authorization context
 */
export interface AuthorizationContext {
  user: {
    id: string;
    role: string;
    merchantId?: string;
  };
  resource: string;
  action: string;
  resourceId?: string;
}

/**
 * Permission check result
 */
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 16:14 - Line 55:18), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 20:6 - Line 59:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn35" onclick="toggleCodeBlock('cloneGroup35', 'expandBtn35', 'collapseBtn35')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn35" onclick="toggleCodeBlock('cloneGroup35', 'expandBtn35', 'collapseBtn35')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup35"><code class="language-typescript text-sm text-gray-800">];

  /**
   * Check if user is authorized for the given action
   */
  async checkPermission(context: AuthorizationContext): Promise&lt;PermissionResult&gt; {
    const { user, resource, action } = context;

    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated',
      };
    }

    // Check role-based permissions
    const rolePermission = this.checkRolePermission(user.role, resource, action);
    if (!rolePermission.allowed) {
      return rolePermission;
    }

    // Check resource-specific permissions
    const resourcePermission = await this.checkResourcePermission(context);
    if (!resourcePermission.allowed) {
      return resourcePermission;
    }

    return { allowed: true };
  }

  /**
   * Check role-based permissions
   */
  private checkRolePermission(
    userRole: string,
    resource: string,
    action: string
  ): PermissionResult {
    switch (resource) {
      case 'risk_assessment'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 207:9 - Line 220:2), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 177:9 - Line 190:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn36" onclick="toggleCodeBlock('cloneGroup36', 'expandBtn36', 'collapseBtn36')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn36" onclick="toggleCodeBlock('cloneGroup36', 'expandBtn36', 'collapseBtn36')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup36"><code class="language-typescript text-sm text-gray-800">)) {
      throw new AppError({
        message: 'Admin role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require merchant role or higher
   */
  requireMerchant(userRole?: string): void {
    if (!userRole || !this.merchantRoles.includes(userRole)</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 220:9 - Line 233:2), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 190:9 - Line 203:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn37" onclick="toggleCodeBlock('cloneGroup37', 'expandBtn37', 'collapseBtn37')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn37" onclick="toggleCodeBlock('cloneGroup37', 'expandBtn37', 'collapseBtn37')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup37"><code class="language-typescript text-sm text-gray-800">)) {
      throw new AppError({
        message: 'Merchant role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require authenticated user
   */
  requireAuthenticated(userRole?: string): void {
    if (!userRole || !this.userRoles.includes(userRole)</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 233:9 - Line 247:5), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 203:9 - Line 217:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn38" onclick="toggleCodeBlock('cloneGroup38', 'expandBtn38', 'collapseBtn38')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn38" onclick="toggleCodeBlock('cloneGroup38', 'expandBtn38', 'collapseBtn38')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup38"><code class="language-typescript text-sm text-gray-800">)) {
      throw new AppError({
        message: 'Authentication required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy: Record&lt;string, number&gt; = {
      USER</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 250:2 - Line 265:3), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 219:2 - Line 234:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn39" onclick="toggleCodeBlock('cloneGroup39', 'expandBtn39', 'collapseBtn39')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn39" onclick="toggleCodeBlock('cloneGroup39', 'expandBtn39', 'collapseBtn39')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup39"><code class="language-typescript text-sm text-gray-800">,
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel &gt;= requiredLevel;
  }

  /**
   * Get user permissions for a resource
   */
  getUserPermissions(userRole: string, resource: string): string[] {
    const permissions: string[] = [];

    if</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 284:5 - Line 358:6), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 253:5 - Line 327:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn40" onclick="toggleCodeBlock('cloneGroup40', 'expandBtn40', 'collapseBtn40')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn40" onclick="toggleCodeBlock('cloneGroup40', 'expandBtn40', 'collapseBtn40')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup40"><code class="language-typescript text-sm text-gray-800">}

    return permissions;
  }

  /**
   * Validate authorization context
   */
  validateAuthorizationContext(context: AuthorizationContext): void {
    if (!context.user) {
      throw new AppError({
        message: 'User context is required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }

    if (!context.resource) {
      throw new AppError({
        message: 'Resource is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!context.action) {
      throw new AppError({
        message: 'Action is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }
  }

  /**
   * Create authorization context from request
   */
  createAuthorizationContext(
    user: any,
    resource: string,
    action: string,
    resourceId?: string
  ): AuthorizationContext {
    return {
      user: {
        id: user?.id,
        role: user?.role,
        merchantId: user?.merchantId,
      },
      resource,
      action,
      resourceId,
    };
  }

  /**
   * Handle authorization error
   */
  handleAuthorizationError(result: PermissionResult): never {
    const message = result.reason ?? 'Access denied';

    throw new AppError({
      message,
      type: ErrorType.AUTHENTICATION,
      code: ErrorCode.INVALID_CREDENTIALS,
      details: {
        requiredRole: result.requiredRole,
        requiredPermissions: result.requiredPermissions,
      },
    });
  }

  /**
   * Extract merchant context from request
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\mappers\FraudDetectionResponseMapper.ts (Line 21:2 - Line 73:3), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 21:2 - Line 73:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn41" onclick="toggleCodeBlock('cloneGroup41', 'expandBtn41', 'collapseBtn41')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn41" onclick="toggleCodeBlock('cloneGroup41', 'expandBtn41', 'collapseBtn41')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup41"><code class="language-typescript text-sm text-gray-800">{
  /**
   * Send success response
   */
  static sendSuccess&lt;T&gt;(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse&lt;T&gt; = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId ?? 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? error.statusCode ?? 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message ||</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\mappers\FraudDetectionResponseMapper.ts (Line 73:2 - Line 89:6), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 73:2 - Line 89:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn42" onclick="toggleCodeBlock('cloneGroup42', 'expandBtn42', 'collapseBtn42')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn42" onclick="toggleCodeBlock('cloneGroup42', 'expandBtn42', 'collapseBtn42')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup42"><code class="language-typescript text-sm text-gray-800">'Internal server error',
          code: 'INTERNAL_SERVER_ERROR',
          type: 'INTERNAL',
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? 500;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send risk assessment response
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\mappers\FraudDetectionResponseMapper.ts (Line 157:52 - Line 224:6), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 204:36 - Line 271:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn43" onclick="toggleCodeBlock('cloneGroup43', 'expandBtn43', 'collapseBtn43')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn43" onclick="toggleCodeBlock('cloneGroup43', 'expandBtn43', 'collapseBtn43')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup43"><code class="language-typescript text-sm text-gray-800">);
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: any[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: 'VALIDATION' as any,
      code: 'INVALID_INPUT' as any,
      details: { errors },
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: 'AUTHENTICATION' as any,
      code: 'INVALID_CREDENTIALS' as any,
      details: { requiredRole },
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: 'NOT_FOUND' as any,
      code: 'RESOURCE_NOT_FOUND' as any,
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      type: 'INTERNAL' as any,
      code: 'INTERNAL_SERVER_ERROR' as any,
    });

    this.sendError(res, error, 500);
  }

  /**
   * Handle async controller method
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\mappers\FraudDetectionResponseMapper.ts (Line 219:2 - Line 242:6), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 293:2 - Line 316:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn44" onclick="toggleCodeBlock('cloneGroup44', 'expandBtn44', 'collapseBtn44')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn44" onclick="toggleCodeBlock('cloneGroup44', 'expandBtn44', 'collapseBtn44')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup44"><code class="language-typescript text-sm text-gray-800">;
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: any, res: Response, next: Function) =&gt; {
      Promise.resolve(fn(req, res, next)).catch((error) =&gt; next(error));
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res: Response): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Response-Time', Date.now());
  }

  /**
   * Format risk level for display
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\types\AlertAggregationTypes.ts (Line 109:5 - Line 134:4), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 176:2 - Line 201:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn45" onclick="toggleCodeBlock('cloneGroup45', 'expandBtn45', 'collapseBtn45')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn45" onclick="toggleCodeBlock('cloneGroup45', 'expandBtn45', 'collapseBtn45')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup45"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * API response wrapper
 */
export interface ApiResponse&lt;T = any&gt; {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Filter parameters for aggregation rules
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\types\AlertAggregationTypes.ts (Line 146:8 - Line 187:36), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 209:5 - Line 250:40)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn46" onclick="toggleCodeBlock('cloneGroup46', 'expandBtn46', 'collapseBtn46')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn46" onclick="toggleCodeBlock('cloneGroup46', 'expandBtn46', 'collapseBtn46')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup46"><code class="language-typescript text-sm text-gray-800">;
  search?: string;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

/**
 * Controller method options
 */
export interface ControllerMethodOptions {
  requireAuth?: boolean;
  requiredRole?: string;
  validateInput?: boolean;
  logRequest?: boolean;
}

/**
 * Request context interface
 */
export interface RequestContext {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
  requestId: string;
  timestamp: Date;
  ip: string;
  userAgent: string;
}

/**
 * Service dependencies interface
 */
export interface AlertAggregationServiceDependencies</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\types\AlertAggregationTypes.ts (Line 239:8 - Line 291:4), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 283:7 - Line 335:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn47" onclick="toggleCodeBlock('cloneGroup47', 'expandBtn47', 'collapseBtn47')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn47" onclick="toggleCodeBlock('cloneGroup47', 'expandBtn47', 'collapseBtn47')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup47"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * Error response format
 */
export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    type: string;
    details?: any;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Success response format
 */
export interface SuccessResponse&lt;T = any&gt; {
  success: true;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Controller method result
 */
export type ControllerResult&lt;T = any&gt; = Promise&lt;SuccessResponse&lt;T&gt; | ErrorResponse&gt;;

/**
 * Middleware function type
 */
export type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) =&gt; void | Promise&lt;void&gt;;

/**
 * Controller method type
 */
export type ControllerMethod&lt;T = any&gt; = (req: AuthenticatedRequest, res: Response) =&gt; ControllerResult&lt;T&gt;;

/**
 * Validation schema interface
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\types\AlertAggregationTypes.ts (Line 304:1 - Line 328:2), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 377:1 - Line 401:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn48" onclick="toggleCodeBlock('cloneGroup48', 'expandBtn48', 'collapseBtn48')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn48" onclick="toggleCodeBlock('cloneGroup48', 'expandBtn48', 'collapseBtn48')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup48"><code class="language-typescript text-sm text-gray-800">}

/**
 * Authorization context
 */
export interface AuthorizationContext {
  user: {
    id: string;
    role: string;
    merchantId?: string;
  };
  resource: string;
  action: string;
  resourceId?: string;
}

/**
 * Permission check result
 */
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\ValidationService.ts (Line 72:2 - Line 83:6), src\controllers\alert-aggregation\services\ValidationService.ts (Line 34:5 - Line 45:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn49" onclick="toggleCodeBlock('cloneGroup49', 'expandBtn49', 'collapseBtn49')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn49" onclick="toggleCodeBlock('cloneGroup49', 'expandBtn49', 'collapseBtn49')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup49"><code class="language-typescript text-sm text-gray-800">this.validateEnabledField(data.enabled, errors);

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    const</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\ValidationService.ts (Line 156:9 - Line 172:7), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 441:2 - Line 457:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn50" onclick="toggleCodeBlock('cloneGroup50', 'expandBtn50', 'collapseBtn50')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn50" onclick="toggleCodeBlock('cloneGroup50', 'expandBtn50', 'collapseBtn50')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup50"><code class="language-typescript text-sm text-gray-800">,
    };
  }

  /**
   * Validate ID parameter
   */
  validateId(id: any, fieldName: string = 'id'): string {
    if (!id) {
      throw new AppError({
        message: `${fieldName} is required`,
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (typeof</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\ValidationService.ts (Line 190:2 - Line 224:7), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 465:3 - Line 499:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn51" onclick="toggleCodeBlock('cloneGroup51', 'expandBtn51', 'collapseBtn51')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn51" onclick="toggleCodeBlock('cloneGroup51', 'expandBtn51', 'collapseBtn51')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup51"><code class="language-typescript text-sm text-gray-800">;
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: any): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    const page = query.page ? parseInt(query.page, 10) : 1;
    const limit = query.limit ? parseInt(query.limit, 10) : 10;

    if (isNaN(page) || page &lt; 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    if (isNaN(limit) || limit &lt; 1 || limit &gt; 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    const result: any = { page, limit };

    if (query.sortBy) {
      const validSortFields = ['name'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\ValidationService.ts (Line 224:10 - Line 251:6), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 499:7 - Line 526:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn52" onclick="toggleCodeBlock('cloneGroup52', 'expandBtn52', 'collapseBtn52')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn52" onclick="toggleCodeBlock('cloneGroup52', 'expandBtn52', 'collapseBtn52')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup52"><code class="language-typescript text-sm text-gray-800">];
      if (!validSortFields.includes(query.sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }
      result.sortBy = query.sortBy;
    }

    if (query.sortOrder) {
      if (!['asc', 'desc'].includes(query.sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either &quot;asc&quot; or &quot;desc&quot;',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }
      result.sortOrder = query.sortOrder;
    }

    return result;
  }

  /**
   * Helper validation methods
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 16:10 - Line 55:20), src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 16:11 - Line 59:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn53" onclick="toggleCodeBlock('cloneGroup53', 'expandBtn53', 'collapseBtn53')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn53" onclick="toggleCodeBlock('cloneGroup53', 'expandBtn53', 'collapseBtn53')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup53"><code class="language-typescript text-sm text-gray-800">, 'ADMIN', 'SUPER_ADMIN'];

  /**
   * Check if user is authorized for the given action
   */
  async checkPermission(context: AuthorizationContext): Promise&lt;PermissionResult&gt; {
    const { user, resource, action } = context;

    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated',
      };
    }

    // Check role-based permissions
    const rolePermission = this.checkRolePermission(user.role, resource, action);
    if (!rolePermission.allowed) {
      return rolePermission;
    }

    // Check resource-specific permissions
    const resourcePermission = await this.checkResourcePermission(context);
    if (!resourcePermission.allowed) {
      return resourcePermission;
    }

    return { allowed: true };
  }

  /**
   * Check role-based permissions
   */
  private checkRolePermission(
    userRole: string,
    resource: string,
    action: string
  ): PermissionResult {
    switch (resource) {
      case 'aggregation-rules'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 115:2 - Line 133:2), src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 76:6 - Line 95:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn54" onclick="toggleCodeBlock('cloneGroup54', 'expandBtn54', 'collapseBtn54')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn54" onclick="toggleCodeBlock('cloneGroup54', 'expandBtn54', 'collapseBtn54')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup54"><code class="language-typescript text-sm text-gray-800">;
      case 'create':
      case 'update':
      case 'delete':
        if (this.adminRoles.includes(userRole)) {
          return { allowed: true };
        }
        return {
          allowed: false,
          reason: 'Admin role required for write operations',
          requiredRole: 'ADMIN',
        };
      default:
        return {
          allowed: false,
          reason: `Unknown action: ${action}`,
        };
    }
  }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 147:7 - Line 168:6), src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 197:7 - Line 218:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn55" onclick="toggleCodeBlock('cloneGroup55', 'expandBtn55', 'collapseBtn55')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn55" onclick="toggleCodeBlock('cloneGroup55', 'expandBtn55', 'collapseBtn55')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup55"><code class="language-typescript text-sm text-gray-800">return { allowed: true };
    }

    return { allowed: true };
  }

  /**
   * Require admin role
   */
  requireAdmin(userRole?: string): void {
    if (!userRole || !this.adminRoles.includes(userRole)) {
      throw new AppError({
        message: 'Admin role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require manager role or higher
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 172:24 - Line 198:8), src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 222:25 - Line 248:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn56" onclick="toggleCodeBlock('cloneGroup56', 'expandBtn56', 'collapseBtn56')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn56" onclick="toggleCodeBlock('cloneGroup56', 'expandBtn56', 'collapseBtn56')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup56"><code class="language-typescript text-sm text-gray-800">,
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require authenticated user
   */
  requireAuthenticated(userRole?: string): void {
    if (!userRole || !this.userRoles.includes(userRole)) {
      throw new AppError({
        message: 'Authentication required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Check if user has specific role
   */
  hasRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy: Record&lt;string, number&gt; = {
      USER: 1,
      MANAGER</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 198:8 - Line 216:20), src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 248:9 - Line 235:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn57" onclick="toggleCodeBlock('cloneGroup57', 'expandBtn57', 'collapseBtn57')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn57" onclick="toggleCodeBlock('cloneGroup57', 'expandBtn57', 'collapseBtn57')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup57"><code class="language-typescript text-sm text-gray-800">: 2,
      ADMIN: 3,
      SUPER_ADMIN: 4,
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel &gt;= requiredLevel;
  }

  /**
   * Get user permissions for a resource
   */
  getUserPermissions(userRole: string, resource: string): string[] {
    const permissions: string[] = [];

    switch (resource) {
      case 'aggregation-rules'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 229:2 - Line 303:2), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 250:2 - Line 327:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn58" onclick="toggleCodeBlock('cloneGroup58', 'expandBtn58', 'collapseBtn58')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn58" onclick="toggleCodeBlock('cloneGroup58', 'expandBtn58', 'collapseBtn58')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup58"><code class="language-typescript text-sm text-gray-800">'create', 'update', 'delete');
        }
        break;
    }

    return permissions;
  }

  /**
   * Validate authorization context
   */
  validateAuthorizationContext(context: AuthorizationContext): void {
    if (!context.user) {
      throw new AppError({
        message: 'User context is required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }

    if (!context.resource) {
      throw new AppError({
        message: 'Resource is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!context.action) {
      throw new AppError({
        message: 'Action is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }
  }

  /**
   * Create authorization context from request
   */
  createAuthorizationContext(
    user: any,
    resource: string,
    action: string,
    resourceId?: string
  ): AuthorizationContext {
    return {
      user: {
        id: user?.id,
        role: user?.role,
        merchantId: user?.merchantId,
      },
      resource,
      action,
      resourceId,
    };
  }

  /**
   * Handle authorization error
   */
  handleAuthorizationError(result: PermissionResult): never {
    const message = result.reason ?? 'Access denied';

    throw new AppError({
      message,
      type: ErrorType.AUTHENTICATION,
      code: ErrorCode.INVALID_CREDENTIALS,
      details: {
        requiredRole: result.requiredRole,
        requiredPermissions: result.requiredPermissions,
      },
    });
  }
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AlertAggregationBusinessService.ts (Line 59:6 - Line 70:3), src\controllers\fraud-detection\services\FraudDetectionBusinessService.ts (Line 278:2 - Line 289:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn59" onclick="toggleCodeBlock('cloneGroup59', 'expandBtn59', 'collapseBtn59')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn59" onclick="toggleCodeBlock('cloneGroup59', 'expandBtn59', 'collapseBtn59')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup59"><code class="language-typescript text-sm text-gray-800">,
        orderBy: { createdAt: 'desc' },
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder ||</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AlertAggregationBusinessService.ts (Line 249:5 - Line 264:6), src\controllers\alert-aggregation\services\AlertAggregationBusinessService.ts (Line 181:24 - Line 196:53)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn60" onclick="toggleCodeBlock('cloneGroup60', 'expandBtn60', 'collapseBtn60')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn60" onclick="toggleCodeBlock('cloneGroup60', 'expandBtn60', 'collapseBtn60')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup60"><code class="language-typescript text-sm text-gray-800">&gt; {
    try {
      // Check if rule exists
      const existingRule = await this.prisma.alertAggregationRule.findUnique({
        where: { id },
      });

      if (!existingRule) {
        throw new AppError({
          message: 'Aggregation rule not found',
          type: ErrorType.NOT_FOUND,
          code: ErrorCode.RESOURCE_NOT_FOUND,
        });
      }

      await</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\services\AlertAggregationBusinessService.ts (Line 291:7 - Line 321:21), src\controllers\alert-aggregation\services\AlertAggregationBusinessService.ts (Line 46:7 - Line 76:21)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn61" onclick="toggleCodeBlock('cloneGroup61', 'expandBtn61', 'collapseBtn61')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn61" onclick="toggleCodeBlock('cloneGroup61', 'expandBtn61', 'collapseBtn61')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup61"><code class="language-typescript text-sm text-gray-800">if (filters?.enabled !== undefined) {
        where.enabled = filters.enabled;
      }

      if (filters?.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
        ];
      }

      // Build query options
      const queryOptions: any = {
        where,
        orderBy: { createdAt: 'desc' },
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder || 'desc' };
        }
      }

      // Execute queries
      const [rules, total] = await Promise.all([
        this.prisma.alertCorrelationRule</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\mappers\ResponseMapper.ts (Line 20:2 - Line 88:6), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 21:2 - Line 89:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn62" onclick="toggleCodeBlock('cloneGroup62', 'expandBtn62', 'collapseBtn62')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn62" onclick="toggleCodeBlock('cloneGroup62', 'expandBtn62', 'collapseBtn62')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup62"><code class="language-typescript text-sm text-gray-800">{
  /**
   * Send success response
   */
  static sendSuccess&lt;T&gt;(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse&lt;T&gt; = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId ?? 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? error.statusCode ?? 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message || 'Internal server error',
          code: 'INTERNAL_SERVER_ERROR',
          type: 'INTERNAL',
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? 500;
    }

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Send aggregation rules list response
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\mappers\ResponseMapper.ts (Line 179:40 - Line 273:6), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 204:36 - Line 298:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn63" onclick="toggleCodeBlock('cloneGroup63', 'expandBtn63', 'collapseBtn63')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn63" onclick="toggleCodeBlock('cloneGroup63', 'expandBtn63', 'collapseBtn63')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup63"><code class="language-typescript text-sm text-gray-800">);
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: any[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: 'VALIDATION' as any,
      code: 'INVALID_INPUT' as any,
      details: { errors },
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: 'AUTHENTICATION' as any,
      code: 'INVALID_CREDENTIALS' as any,
      details: { requiredRole },
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: 'NOT_FOUND' as any,
      code: 'RESOURCE_NOT_FOUND' as any,
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      type: 'INTERNAL' as any,
      code: 'INTERNAL_SERVER_ERROR' as any,
    });

    this.sendError(res, error, 500);
  }

  /**
   * Format pagination metadata
   */
  static formatPagination(
    page: number,
    limit: number,
    total: number
  ): {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } {
    const totalPages = Math.ceil(total / limit);

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page &lt; totalPages,
      hasPrev: page &gt; 1,
    };
  }

  /**
   * Create API response wrapper
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\mappers\ResponseMapper.ts (Line 284:6 - Line 312:2), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 292:2 - Line 323:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn64" onclick="toggleCodeBlock('cloneGroup64', 'expandBtn64', 'collapseBtn64')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn64" onclick="toggleCodeBlock('cloneGroup64', 'expandBtn64', 'collapseBtn64')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup64"><code class="language-typescript text-sm text-gray-800">,
    };
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: any, res: Response, next: Function) =&gt; {
      Promise.resolve(fn(req, res, next)).catch((error) =&gt; next(error));
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res: Response): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Response-Time', Date.now());
  }

  /**
   * Log response for debugging
   */
  static logResponse(method: string, url: string, statusCode: number, responseTime: number): void {
    console.log(`${method} ${url} - ${statusCode} - ${responseTime}ms`);
  }
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\types\AdminControllerTypes.ts (Line 134:9 - Line 161:4), src\controllers\alert-aggregation\types\AlertAggregationTypes.ts (Line 107:8 - Line 201:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn65" onclick="toggleCodeBlock('cloneGroup65', 'expandBtn65', 'collapseBtn65')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn65" onclick="toggleCodeBlock('cloneGroup65', 'expandBtn65', 'collapseBtn65')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup65"><code class="language-typescript text-sm text-gray-800">: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * API response wrapper
 */
export interface ApiResponse&lt;T = any&gt; {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Admin user filters
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\types\AdminControllerTypes.ts (Line 185:9 - Line 226:25), src\controllers\alert-aggregation\types\AlertAggregationTypes.ts (Line 146:8 - Line 250:40)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn66" onclick="toggleCodeBlock('cloneGroup66', 'expandBtn66', 'collapseBtn66')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn66" onclick="toggleCodeBlock('cloneGroup66', 'expandBtn66', 'collapseBtn66')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup66"><code class="language-typescript text-sm text-gray-800">?: boolean;
  search?: string;
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

/**
 * Controller method options
 */
export interface ControllerMethodOptions {
  requireAuth?: boolean;
  requiredRole?: string;
  validateInput?: boolean;
  logRequest?: boolean;
}

/**
 * Request context interface
 */
export interface RequestContext {
  user?: {
    id: string;
    role: string;
    merchantId?: string;
  };
  requestId: string;
  timestamp: Date;
  ip: string;
  userAgent: string;
}

/**
 * Service dependencies interface
 */
export interface AdminServiceDependencies</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\types\AdminControllerTypes.ts (Line 261:4 - Line 313:4), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 283:7 - Line 335:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn67" onclick="toggleCodeBlock('cloneGroup67', 'expandBtn67', 'collapseBtn67')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn67" onclick="toggleCodeBlock('cloneGroup67', 'expandBtn67', 'collapseBtn67')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup67"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * Error response format
 */
export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code: string;
    type: string;
    details?: any;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Success response format
 */
export interface SuccessResponse&lt;T = any&gt; {
  success: true;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  timestamp: Date;
  requestId: string;
}

/**
 * Controller method result
 */
export type ControllerResult&lt;T = any&gt; = Promise&lt;SuccessResponse&lt;T&gt; | ErrorResponse&gt;;

/**
 * Middleware function type
 */
export type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) =&gt; void | Promise&lt;void&gt;;

/**
 * Controller method type
 */
export type ControllerMethod&lt;T = any&gt; = (req: AuthenticatedRequest, res: Response) =&gt; ControllerResult&lt;T&gt;;

/**
 * Admin user status enum
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\types\AdminControllerTypes.ts (Line 351:1 - Line 379:4), src\controllers\identity-verification\types\IdentityVerificationControllerTypes.ts (Line 377:1 - Line 327:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn68" onclick="toggleCodeBlock('cloneGroup68', 'expandBtn68', 'collapseBtn68')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn68" onclick="toggleCodeBlock('cloneGroup68', 'expandBtn68', 'collapseBtn68')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup68"><code class="language-typescript text-sm text-gray-800">}

/**
 * Authorization context
 */
export interface AuthorizationContext {
  user: {
    id: string;
    role: string;
    merchantId?: string;
  };
  resource: string;
  action: string;
  resourceId?: string;
}

/**
 * Permission check result
 */
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: string;
  requiredPermissions?: string[];
}

/**
 * Dashboard statistics
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 92:7 - Line 97:2), src\controllers\admin\services\AdminValidationService.ts (Line 38:2 - Line 44:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn69" onclick="toggleCodeBlock('cloneGroup69', 'expandBtn69', 'collapseBtn69')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn69" onclick="toggleCodeBlock('cloneGroup69', 'expandBtn69', 'collapseBtn69')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup69"><code class="language-typescript text-sm text-gray-800">if (typeof data.name !== 'string' || data.name.trim().length === 0) {
        errors.push({ field: 'name', message: 'Name must be a non-empty string' });
      } else if (data.name.length &gt; 100) {
        errors.push({ field: 'name', message: 'Name must be less than 100 characters' });
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 180:5 - Line 191:5), src\controllers\alert-aggregation\services\ValidationService.ts (Line 36:5 - Line 47:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn70" onclick="toggleCodeBlock('cloneGroup70', 'expandBtn70', 'collapseBtn70')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn70" onclick="toggleCodeBlock('cloneGroup70', 'expandBtn70', 'collapseBtn70')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup70"><code class="language-typescript text-sm text-gray-800">if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      name: data.name.trim(),
      type</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 204:7 - Line 209:2), src\controllers\admin\services\AdminValidationService.ts (Line 135:2 - Line 141:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn71" onclick="toggleCodeBlock('cloneGroup71', 'expandBtn71', 'collapseBtn71')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn71" onclick="toggleCodeBlock('cloneGroup71', 'expandBtn71', 'collapseBtn71')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup71"><code class="language-typescript text-sm text-gray-800">if (typeof data.name !== 'string' || data.name.trim().length === 0) {
        errors.push({ field: 'name', message: 'Name must be a non-empty string' });
      } else if (data.name.length &gt; 50) {
        errors.push({ field: 'name', message: 'Name must be less than 50 characters' });
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 212:7 - Line 220:2), src\controllers\admin\services\AdminValidationService.ts (Line 153:2 - Line 162:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn72" onclick="toggleCodeBlock('cloneGroup72', 'expandBtn72', 'collapseBtn72')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn72" onclick="toggleCodeBlock('cloneGroup72', 'expandBtn72', 'collapseBtn72')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup72"><code class="language-typescript text-sm text-gray-800">if (typeof data.description !== 'string' || data.description.trim().length === 0) {
        errors.push({ field: 'description', message: 'Description must be a non-empty string' });
      } else if (data.description.length &gt; 500) {
        errors.push({
          field: 'description',
          message: 'Description must be less than 500 characters',
        });
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 224:2 - Line 236:2), src\controllers\admin\services\AdminValidationService.ts (Line 167:2 - Line 180:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn73" onclick="toggleCodeBlock('cloneGroup73', 'expandBtn73', 'collapseBtn73')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn73" onclick="toggleCodeBlock('cloneGroup73', 'expandBtn73', 'collapseBtn73')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup73"><code class="language-typescript text-sm text-gray-800">});
      } else {
        data.permissions.forEach((permissionId: any, index: number) =&gt; {
          if (!this.isValidUUID(permissionId)) {
            errors.push({
              field: `permissions[${index}]`,
              message: 'Invalid permission ID format',
              value: permissionId,
            });
          }
        });
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 234:9 - Line 251:18), src\controllers\admin\services\AdminValidationService.ts (Line 101:2 - Line 118:23)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn74" onclick="toggleCodeBlock('cloneGroup74', 'expandBtn74', 'collapseBtn74')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn74" onclick="toggleCodeBlock('cloneGroup74', 'expandBtn74', 'collapseBtn74')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup74"><code class="language-typescript text-sm text-gray-800">});
      }
    }

    if (data.isActive !== undefined &amp;&amp; typeof data.isActive !== 'boolean') {
      errors.push({ field: 'isActive', message: 'isActive must be a boolean' });
    }

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    const result: UpdateRoleRequest</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 263:2 - Line 274:12), src\controllers\admin\services\AdminValidationService.ts (Line 130:2 - Line 141:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn75" onclick="toggleCodeBlock('cloneGroup75', 'expandBtn75', 'collapseBtn75')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn75" onclick="toggleCodeBlock('cloneGroup75', 'expandBtn75', 'collapseBtn75')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup75"><code class="language-typescript text-sm text-gray-800">{
    const errors: ValidationError[] = [];

    if (!data.name) {
      errors.push({ field: 'name', message: 'Name is required' });
    } else if (typeof data.name !== 'string' || data.name.trim().length === 0) {
      errors.push({ field: 'name', message: 'Name must be a non-empty string' });
    } else if (data.name.length &gt; 50) {
      errors.push({ field: 'name', message: 'Name must be less than 50 characters' });
    }

    if (!data.description</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 271:2 - Line 278:4), src\controllers\admin\services\AdminValidationService.ts (Line 148:7 - Line 155:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn76" onclick="toggleCodeBlock('cloneGroup76', 'expandBtn76', 'collapseBtn76')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn76" onclick="toggleCodeBlock('cloneGroup76', 'expandBtn76', 'collapseBtn76')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup76"><code class="language-typescript text-sm text-gray-800">});
    }

    if (!data.description) {
      errors.push({ field: 'description', message: 'Description is required' });
    } else if (typeof data.description !== 'string' || data.description.trim().length === 0) {
      errors.push({ field: 'description', message: 'Description must be a non-empty string' });
    } else if (data.description.length &gt; 200</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 304:7 - Line 319:9), src\controllers\admin\services\AdminValidationService.ts (Line 177:7 - Line 48:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn77" onclick="toggleCodeBlock('cloneGroup77', 'expandBtn77', 'collapseBtn77')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn77" onclick="toggleCodeBlock('cloneGroup77', 'expandBtn77', 'collapseBtn77')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup77"><code class="language-typescript text-sm text-gray-800">});
    }

    if (errors.length &gt; 0) {
      throw new AppError({
        message: 'Validation failed',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
        details: { errors },
      });
    }

    return {
      name: data.name.trim(),
      description: data.description.trim(),
      resource</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 320:7 - Line 378:8), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 441:2 - Line 224:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn78" onclick="toggleCodeBlock('cloneGroup78', 'expandBtn78', 'collapseBtn78')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn78" onclick="toggleCodeBlock('cloneGroup78', 'expandBtn78', 'collapseBtn78')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup78"><code class="language-typescript text-sm text-gray-800">,
    };
  }

  /**
   * Validate ID parameter
   */
  validateId(id: any, fieldName: string = 'id'): string {
    if (!id) {
      throw new AppError({
        message: `${fieldName} is required`,
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!this.isValidUUID(id)) {
      throw new AppError({
        message: `${fieldName} must be a valid UUID`,
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    return id;
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(query: any): {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } {
    const page = query.page ? parseInt(query.page, 10) : 1;
    const limit = query.limit ? parseInt(query.limit, 10) : 10;

    if (isNaN(page) || page &lt; 1) {
      throw new AppError({
        message: 'Page must be a positive integer',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    if (isNaN(limit) || limit &lt; 1 || limit &gt; 100) {
      throw new AppError({
        message: 'Limit must be between 1 and 100',
        type: ErrorType.VALIDATION,
        code: ErrorCode.INVALID_INPUT,
      });
    }

    const result: any = { page, limit };

    if (query.sortBy) {
      const validSortFields = ['name', 'email'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminValidationService.ts (Line 378:9 - Line 405:6), src\controllers\identity-verification\services\IdentityVerificationValidationService.ts (Line 499:7 - Line 526:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn79" onclick="toggleCodeBlock('cloneGroup79', 'expandBtn79', 'collapseBtn79')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn79" onclick="toggleCodeBlock('cloneGroup79', 'expandBtn79', 'collapseBtn79')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup79"><code class="language-typescript text-sm text-gray-800">];
      if (!validSortFields.includes(query.sortBy)) {
        throw new AppError({
          message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }
      result.sortBy = query.sortBy;
    }

    if (query.sortOrder) {
      if (!['asc', 'desc'].includes(query.sortOrder)) {
        throw new AppError({
          message: 'Sort order must be either &quot;asc&quot; or &quot;desc&quot;',
          type: ErrorType.VALIDATION,
          code: ErrorCode.INVALID_INPUT,
        });
      }
      result.sortOrder = query.sortOrder;
    }

    return result;
  }

  /**
   * Check if string is a valid email
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminBusinessService.ts (Line 184:12 - Line 204:6), src\controllers\fraud-detection\services\FraudDetectionBusinessService.ts (Line 274:5 - Line 75:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn80" onclick="toggleCodeBlock('cloneGroup80', 'expandBtn80', 'collapseBtn80')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn80" onclick="toggleCodeBlock('cloneGroup80', 'expandBtn80', 'collapseBtn80')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup80"><code class="language-typescript text-sm text-gray-800">: true },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      };

      // Apply pagination
      if (pagination) {
        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;
        queryOptions.skip = (page - 1) * limit;
        queryOptions.take = limit;

        if (sortBy) {
          queryOptions.orderBy = { [sortBy]: sortOrder || 'desc' };
        }
      }

      // Execute queries
      const [users</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminBusinessService.ts (Line 380:11 - Line 395:30), src\controllers\admin\services\AdminBusinessService.ts (Line 300:11 - Line 315:30)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn81" onclick="toggleCodeBlock('cloneGroup81', 'expandBtn81', 'collapseBtn81')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn81" onclick="toggleCodeBlock('cloneGroup81', 'expandBtn81', 'collapseBtn81')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup81"><code class="language-typescript text-sm text-gray-800">},
          include: {
            user: true,
          },
        });

        return admin;
      });

      return this.mapAdminUserToResponse(result);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError({
        message: 'Failed to update admin user'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminAuthorizationService.ts (Line 16:2 - Line 55:12), src\controllers\alert-aggregation\services\AuthorizationService.ts (Line 16:2 - Line 59:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn82" onclick="toggleCodeBlock('cloneGroup82', 'expandBtn82', 'collapseBtn82')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn82" onclick="toggleCodeBlock('cloneGroup82', 'expandBtn82', 'collapseBtn82')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup82"><code class="language-typescript text-sm text-gray-800">'MANAGER', 'ADMIN', 'SUPER_ADMIN'];

  /**
   * Check if user is authorized for the given action
   */
  async checkPermission(context: AuthorizationContext): Promise&lt;PermissionResult&gt; {
    const { user, resource, action } = context;

    if (!user) {
      return {
        allowed: false,
        reason: 'User not authenticated',
      };
    }

    // Check role-based permissions
    const rolePermission = this.checkRolePermission(user.role, resource, action);
    if (!rolePermission.allowed) {
      return rolePermission;
    }

    // Check resource-specific permissions
    const resourcePermission = await this.checkResourcePermission(context);
    if (!resourcePermission.allowed) {
      return resourcePermission;
    }

    return { allowed: true };
  }

  /**
   * Check role-based permissions
   */
  private checkRolePermission(
    userRole: string,
    resource: string,
    action: string
  ): PermissionResult {
    switch (resource) {
      case 'dashboard'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminAuthorizationService.ts (Line 247:3 - Line 277:6), src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 201:3 - Line 181:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn83" onclick="toggleCodeBlock('cloneGroup83', 'expandBtn83', 'collapseBtn83')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn83" onclick="toggleCodeBlock('cloneGroup83', 'expandBtn83', 'collapseBtn83')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup83"><code class="language-typescript text-sm text-gray-800">}

  /**
   * Require admin role
   */
  requireAdmin(userRole?: string): void {
    if (!userRole || !this.adminRoles.includes(userRole)) {
      throw new AppError({
        message: 'Admin role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Require manager role or higher
   */
  requireManager(userRole?: string): void {
    if (!userRole || !this.managerRoles.includes(userRole)) {
      throw new AppError({
        message: 'Manager role required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }
  }

  /**
   * Check if user has specific role
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminAuthorizationService.ts (Line 282:12 - Line 297:12), src\controllers\identity-verification\services\IdentityVerificationAuthService.ts (Line 219:2 - Line 265:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn84" onclick="toggleCodeBlock('cloneGroup84', 'expandBtn84', 'collapseBtn84')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn84" onclick="toggleCodeBlock('cloneGroup84', 'expandBtn84', 'collapseBtn84')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup84"><code class="language-typescript text-sm text-gray-800">: 3,
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel &gt;= requiredLevel;
  }

  /**
   * Get user permissions for a resource
   */
  getUserPermissions(userRole: string, resource: string): string[] {
    const permissions: string[] = [];

    if (resource === 'dashboard'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminAuthorizationService.ts (Line 308:14 - Line 315:9), src\controllers\admin\services\AdminAuthorizationService.ts (Line 301:14 - Line 308:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn85" onclick="toggleCodeBlock('cloneGroup85', 'expandBtn85', 'collapseBtn85')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn85" onclick="toggleCodeBlock('cloneGroup85', 'expandBtn85', 'collapseBtn85')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup85"><code class="language-typescript text-sm text-gray-800">) {
      if (this.adminRoles.includes(userRole)) {
        permissions.push('read');
      }
      if (this.superAdminRoles.includes(userRole)) {
        permissions.push('create', 'update', 'delete');
      }
    } else if (resource === 'system'</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\services\AdminAuthorizationService.ts (Line 317:9 - Line 390:2), src\controllers\fraud-detection\services\FraudDetectionAuthService.ts (Line 282:18 - Line 327:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn86" onclick="toggleCodeBlock('cloneGroup86', 'expandBtn86', 'collapseBtn86')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn86" onclick="toggleCodeBlock('cloneGroup86', 'expandBtn86', 'collapseBtn86')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup86"><code class="language-typescript text-sm text-gray-800">);
      }
    }

    return permissions;
  }

  /**
   * Validate authorization context
   */
  validateAuthorizationContext(context: AuthorizationContext): void {
    if (!context.user) {
      throw new AppError({
        message: 'User context is required',
        type: ErrorType.AUTHENTICATION,
        code: ErrorCode.INVALID_CREDENTIALS,
      });
    }

    if (!context.resource) {
      throw new AppError({
        message: 'Resource is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }

    if (!context.action) {
      throw new AppError({
        message: 'Action is required',
        type: ErrorType.VALIDATION,
        code: ErrorCode.MISSING_REQUIRED_FIELD,
      });
    }
  }

  /**
   * Create authorization context from request
   */
  createAuthorizationContext(
    user: any,
    resource: string,
    action: string,
    resourceId?: string
  ): AuthorizationContext {
    return {
      user: {
        id: user?.id,
        role: user?.role,
        merchantId: user?.merchantId,
      },
      resource,
      action,
      resourceId,
    };
  }

  /**
   * Handle authorization error
   */
  handleAuthorizationError(result: PermissionResult): never {
    const message = result.reason ?? 'Access denied';

    throw new AppError({
      message,
      type: ErrorType.AUTHENTICATION,
      code: ErrorCode.INVALID_CREDENTIALS,
      details: {
        requiredRole: result.requiredRole,
        requiredPermissions: result.requiredPermissions,
      },
    });
  }
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\mappers\AdminResponseMapper.ts (Line 23:2 - Line 45:3), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 21:2 - Line 43:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn87" onclick="toggleCodeBlock('cloneGroup87', 'expandBtn87', 'collapseBtn87')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn87" onclick="toggleCodeBlock('cloneGroup87', 'expandBtn87', 'collapseBtn87')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup87"><code class="language-typescript text-sm text-gray-800">{
  /**
   * Send success response
   */
  static sendSuccess&lt;T&gt;(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200,
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }
  ): void {
    const response: SuccessResponse&lt;T&gt; = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId ||</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\mappers\AdminResponseMapper.ts (Line 45:2 - Line 67:3), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 43:2 - Line 65:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn88" onclick="toggleCodeBlock('cloneGroup88', 'expandBtn88', 'collapseBtn88')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn88" onclick="toggleCodeBlock('cloneGroup88', 'expandBtn88', 'collapseBtn88')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup88"><code class="language-typescript text-sm text-gray-800">'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {
    let errorResponse: ErrorResponse;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ||</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\mappers\AdminResponseMapper.ts (Line 253:11 - Line 335:2), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 204:36 - Line 316:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn89" onclick="toggleCodeBlock('cloneGroup89', 'expandBtn89', 'collapseBtn89')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn89" onclick="toggleCodeBlock('cloneGroup89', 'expandBtn89', 'collapseBtn89')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup89"><code class="language-typescript text-sm text-gray-800">);
  }

  /**
   * Send validation error response
   */
  static sendValidationError(
    res: Response,
    errors: any[],
    message: string = 'Validation failed'
  ): void {
    const error = new AppError({
      message,
      type: 'VALIDATION' as any,
      code: 'INVALID_INPUT' as any,
      details: { errors },
    });

    this.sendError(res, error, 400);
  }

  /**
   * Send authorization error response
   */
  static sendAuthorizationError(
    res: Response,
    message: string = 'Access denied',
    requiredRole?: string
  ): void {
    const error = new AppError({
      message,
      type: 'AUTHENTICATION' as any,
      code: 'INVALID_CREDENTIALS' as any,
      details: { requiredRole },
    });

    this.sendError(res, error, 403);
  }

  /**
   * Send not found error response
   */
  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {
    const error = new AppError({
      message: `${resource} not found`,
      type: 'NOT_FOUND' as any,
      code: 'RESOURCE_NOT_FOUND' as any,
    });

    this.sendError(res, error, 404);
  }

  /**
   * Send internal server error response
   */
  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {
    const error = new AppError({
      message,
      type: 'INTERNAL' as any,
      code: 'INTERNAL_SERVER_ERROR' as any,
    });

    this.sendError(res, error, 500);
  }

  /**
   * Handle async controller method
   */
  static asyncHandler(fn: Function) {
    return (req: any, res: Response, next: Function) =&gt; {
      Promise.resolve(fn(req, res, next)).catch((error) =&gt; next(error));
    };
  }

  /**
   * Set response headers for API
   */
  static setApiHeaders(res: Response): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('X-API-Version', '1.0');
    res.setHeader('X-Response-Time', Date.now());
  }
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\utils\errors\ErrorFactory.ts (Line 351:22 - Line 364:24), src\utils\errors\ErrorFactory.ts (Line 293:16 - Line 306:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn90" onclick="toggleCodeBlock('cloneGroup90', 'expandBtn90', 'collapseBtn90')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn90" onclick="toggleCodeBlock('cloneGroup90', 'expandBtn90', 'collapseBtn90')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup90"><code class="language-typescript text-sm text-gray-800">, originalError?: Error): AppError {
    const errorId = uuidv4();

    const error: any = new AppError({
      message,
      type: ErrorType.NETWORK,
      code: ErrorCode.NETWORK_ERROR,
      statusCode: 503,
      originalError,
      requestId: errorId
    });

    if (originalError) {
      logger.error(`[${errorId}] Service unavailable: </code></pre></div><div class="py-4"><p class="text-gray-600">src\utils\errors\AppError.ts (Line 233:2 - Line 244:15), src\utils\errors\AppError.ts (Line 206:2 - Line 217:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn91" onclick="toggleCodeBlock('cloneGroup91', 'expandBtn91', 'collapseBtn91')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn91" onclick="toggleCodeBlock('cloneGroup91', 'expandBtn91', 'collapseBtn91')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup91"><code class="language-typescript text-sm text-gray-800">extends AppError {
  constructor(options: {
    message: string;
    code?: ErrorCode;
    details?: any;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: options.message,
      type: ErrorType.AUTHENTICATION</code></pre></div><div class="py-4"><p class="text-gray-600">src\utils\errors\AppError.ts (Line 260:2 - Line 271:14), src\utils\errors\AppError.ts (Line 206:2 - Line 217:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn92" onclick="toggleCodeBlock('cloneGroup92', 'expandBtn92', 'collapseBtn92')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn92" onclick="toggleCodeBlock('cloneGroup92', 'expandBtn92', 'collapseBtn92')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup92"><code class="language-typescript text-sm text-gray-800">extends AppError {
  constructor(options: {
    message: string;
    code?: ErrorCode;
    details?: any;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: options.message,
      type: ErrorType.AUTHORIZATION</code></pre></div><div class="py-4"><p class="text-gray-600">src\utils\errors\AppError.ts (Line 287:2 - Line 298:10), src\utils\errors\AppError.ts (Line 206:2 - Line 217:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn93" onclick="toggleCodeBlock('cloneGroup93', 'expandBtn93', 'collapseBtn93')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn93" onclick="toggleCodeBlock('cloneGroup93', 'expandBtn93', 'collapseBtn93')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup93"><code class="language-typescript text-sm text-gray-800">extends AppError {
  constructor(options: {
    message: string;
    code?: ErrorCode;
    details?: any;
    path?: string;
    requestId?: string;
    originalError?: Error;
  }) {
    super({
      message: options.message,
      type: ErrorType.NOT_FOUND</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 135:15 - Line 145:2), src\tests\utils\TestUtility.ts (Line 117:17 - Line 127:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn94" onclick="toggleCodeBlock('cloneGroup94', 'expandBtn94', 'collapseBtn94')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn94" onclick="toggleCodeBlock('cloneGroup94', 'expandBtn94', 'collapseBtn94')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup94"><code class="language-typescript text-sm text-gray-800">?: any;
  expectedError?: any;
  description?: string;
  setup?: () =&gt; void;
  cleanup?: () =&gt; void;
  beforeEach?: () =&gt; void;
  afterEach?: () =&gt; void;
  timeout?: number;
  skip?: boolean;
  only?: boolean;
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 173:2 - Line 178:7), src\tests\utils\factories\MockFactories.ts (Line 91:2 - Line 96:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn95" onclick="toggleCodeBlock('cloneGroup95', 'expandBtn95', 'collapseBtn95')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn95" onclick="toggleCodeBlock('cloneGroup95', 'expandBtn95', 'collapseBtn95')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup95"><code class="language-typescript text-sm text-gray-800">= {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    locals</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 385:11 - Line 407:4), src\tests\utils\TestUtility.ts (Line 327:8 - Line 350:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn96" onclick="toggleCodeBlock('cloneGroup96', 'expandBtn96', 'collapseBtn96')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn96" onclick="toggleCodeBlock('cloneGroup96', 'expandBtn96', 'collapseBtn96')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup96"><code class="language-typescript text-sm text-gray-800">);
    }

    if (options.afterEach) {
      options.afterEach();
    }

    return result;
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }
}

/**
 * Create a test suite for a controller
 * @param name Test suite name
 * @param controllerClass Controller class
 * @param tests Test definitions
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 511:2 - Line 522:4), src\tests\utils\TestUtility.ts (Line 247:2 - Line 258:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn97" onclick="toggleCodeBlock('cloneGroup97', 'expandBtn97', 'collapseBtn97')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn97" onclick="toggleCodeBlock('cloneGroup97', 'expandBtn97', 'collapseBtn97')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup97"><code class="language-typescript text-sm text-gray-800">= {}
): Promise&lt;{ req: MockRequest; res: MockResponse; next: jest.Mock }&gt; {
  const req = options.req || createMockRequest();
  const res: any =options.res || createMockResponse();
  const next: any =options.next || createMockNext();

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup(req</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 526:11 - Line 537:4), src\tests\utils\TestUtility.ts (Line 262:2 - Line 273:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn98" onclick="toggleCodeBlock('cloneGroup98', 'expandBtn98', 'collapseBtn98')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn98" onclick="toggleCodeBlock('cloneGroup98', 'expandBtn98', 'collapseBtn98')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup98"><code class="language-typescript text-sm text-gray-800">(req, res, next);

    if (options.expectedStatus) {
      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
    }

    if (options.expectedResponse) {
      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
    }

    if (options.cleanup) {
      options.cleanup(req</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 537:5 - Line 559:4), src\tests\utils\TestUtility.ts (Line 273:11 - Line 296:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn99" onclick="toggleCodeBlock('cloneGroup99', 'expandBtn99', 'collapseBtn99')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn99" onclick="toggleCodeBlock('cloneGroup99', 'expandBtn99', 'collapseBtn99')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup99"><code class="language-typescript text-sm text-gray-800">);
    }

    if (options.afterEach) {
      options.afterEach();
    }
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }

  return { req, res, next };
}

/**
 * Test validator
 * @param validator Validator function
 * @param options Test options
 * @returns Test result
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 565:2 - Line 574:2), src\tests\utils\TestUtility.ts (Line 249:2 - Line 258:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn100" onclick="toggleCodeBlock('cloneGroup100', 'expandBtn100', 'collapseBtn100')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn100" onclick="toggleCodeBlock('cloneGroup100', 'expandBtn100', 'collapseBtn100')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup100"><code class="language-typescript text-sm text-gray-800">=options.req || createMockRequest();
  const res: any =options.res || createMockResponse();
  const next: any =options.next || createMockNext();

  if (options.beforeEach) {
    options.beforeEach();
  }

  if (options.setup) {
    options.setup()</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 578:5 - Line 589:2), src\tests\utils\TestUtility.ts (Line 262:5 - Line 273:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn101" onclick="toggleCodeBlock('cloneGroup101', 'expandBtn101', 'collapseBtn101')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn101" onclick="toggleCodeBlock('cloneGroup101', 'expandBtn101', 'collapseBtn101')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup101"><code class="language-typescript text-sm text-gray-800">);

    if (options.expectedStatus) {
      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);
    }

    if (options.expectedResponse) {
      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);
    }

    if (options.cleanup) {
      options.cleanup()</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 589:2 - Line 611:4), src\tests\utils\TestUtility.ts (Line 327:8 - Line 350:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn102" onclick="toggleCodeBlock('cloneGroup102', 'expandBtn102', 'collapseBtn102')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn102" onclick="toggleCodeBlock('cloneGroup102', 'expandBtn102', 'collapseBtn102')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup102"><code class="language-typescript text-sm text-gray-800">);
    }

    if (options.afterEach) {
      options.afterEach();
    }

    return result;
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }
}

/**
 * Test utility function
 * @param utility Utility function
 * @param options Test options
 * @returns Test result
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 630:15 - Line 656:4), src\tests\utils\TestUtility.ts (Line 585:17 - Line 350:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn103" onclick="toggleCodeBlock('cloneGroup103', 'expandBtn103', 'collapseBtn103')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn103" onclick="toggleCodeBlock('cloneGroup103', 'expandBtn103', 'collapseBtn103')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup103"><code class="language-typescript text-sm text-gray-800">);
    }

    if (options.cleanup) {
      options.cleanup();
    }

    if (options.afterEach) {
      options.afterEach();
    }

    return result;
  } catch (error) {
    if (options.expectedError) {
      expect(error).toEqual(options.expectedError);
    } else {
      throw error;
    }
  }
}

/**
 * Create a test suite for middleware
 * @param name Test suite name
 * @param middleware Middleware function
 * @param tests Test definitions
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 698:3 - Line 709:14), src\tests\utils\TestUtility.ts (Line 662:3 - Line 673:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn104" onclick="toggleCodeBlock('cloneGroup104', 'expandBtn104', 'collapseBtn104')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn104" onclick="toggleCodeBlock('cloneGroup104', 'expandBtn104', 'collapseBtn104')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup104"><code class="language-typescript text-sm text-gray-800">}
): void {
  describe(name, () =&gt; {
    Object.entries(tests).forEach(([testName, test]) =&gt; {
      const testFn: any =test.skip ? it.skip : test.only ? it.only : it;

      testFn(test.description || testName, async () =&gt; {
        if (test.beforeEach) {
          test.beforeEach();
        }

        await testValidator</code></pre></div><div class="py-4"><p class="text-gray-600">src\tests\utils\TestUtility.ts (Line 734:3 - Line 745:12), src\tests\utils\TestUtility.ts (Line 662:3 - Line 673:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn105" onclick="toggleCodeBlock('cloneGroup105', 'expandBtn105', 'collapseBtn105')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn105" onclick="toggleCodeBlock('cloneGroup105', 'expandBtn105', 'collapseBtn105')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup105"><code class="language-typescript text-sm text-gray-800">}
): void {
  describe(name, () =&gt; {
    Object.entries(tests).forEach(([testName, test]) =&gt; {
      const testFn: any =test.skip ? it.skip : test.only ? it.only : it;

      testFn(test.description || testName, async () =&gt; {
        if (test.beforeEach) {
          test.beforeEach();
        }

        await testUtility</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\verification\VerificationService.ts (Line 18:11 - Line 33:4), src\services\verification\VerificationService.ts (Line 8:17 - Line 19:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn106" onclick="toggleCodeBlock('cloneGroup106', 'expandBtn106', 'collapseBtn106')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn106" onclick="toggleCodeBlock('cloneGroup106', 'expandBtn106', 'collapseBtn106')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup106"><code class="language-typescript text-sm text-gray-800">;
import { IVerificationStrategy, VerificationRequest, VerificationResult } from &quot;../../interfaces/verification/IVerificationStrategy&quot;;
import { VerificationStrategyFactory } from &quot;../../factories/verification/VerificationStrategyFactory&quot;;
import { logger } from &quot;../../lib/logger&quot;;
import { VerificationPreProcessor } from &quot;./processors/VerificationPreProcessor&quot;;
import { VerificationPostProcessor } from &quot;./processors/VerificationPostProcessor&quot;;
import { VerificationChain, VerificationChainResult } from &quot;./VerificationChain&quot;;
import { VerificationPolicy } from &quot;./policy/VerificationPolicy&quot;;
import { verificationPolicyManager } from &quot;./policy/VerificationPolicyManager&quot;;
import { eventBus } from &quot;../../lib/EventBus&quot;;
import { VerificationResult } from '../types';


/**
 * Verification service
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\verification\binance-verification.service.ts (Line 173:39 - Line 209:45), src\services\verification\binance-verification.service.ts (Line 79:30 - Line 115:43)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn107" onclick="toggleCodeBlock('cloneGroup107', 'expandBtn107', 'collapseBtn107')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn107" onclick="toggleCodeBlock('cloneGroup107', 'expandBtn107', 'collapseBtn107')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup107"><code class="language-typescript text-sm text-gray-800">,
                    status: BinanceVerificationStatus.FAILED
                };
            }

            // Check transaction status
            if (transaction.status === BinanceTransactionStatus.COMPLETED) {
                return {
                    success: true,
                    transaction,
                    message: &quot;Transaction verified&quot;,
                    status: BinanceVerificationStatus.VERIFIED
                };
            } else if (transaction.status === BinanceTransactionStatus.PENDING) {
                return {
                    success: false,
                    transaction,
                    message: &quot;Transaction is still pending&quot;,
                    status: BinanceVerificationStatus.PENDING
                };
            } else if (transaction.status === BinanceTransactionStatus.EXPIRED) {
                return {
                    success: false,
                    transaction,
                    message: &quot;Transaction has expired&quot;,
                    status: BinanceVerificationStatus.EXPIRED
                };
            } else {
                return {
                    success: false,
                    transaction,
                    message: `Transaction failed with status: ${transaction.status}`,
                    status: BinanceVerificationStatus.FAILED
                };
            }
        } catch (error) {
            logger.error(`Error verifying Binance TRC20 transaction: </code></pre></div><div class="py-4"><p class="text-gray-600">src\services\system\SystemInitializer.ts (Line 22:27 - Line 40:4), src\services\system\SystemInitializer.ts (Line 8:17 - Line 23:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn108" onclick="toggleCodeBlock('cloneGroup108', 'expandBtn108', 'collapseBtn108')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn108" onclick="toggleCodeBlock('cloneGroup108', 'expandBtn108', 'collapseBtn108')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup108"><code class="language-typescript text-sm text-gray-800">;
import { logger } from &quot;../../lib/logger&quot;;
import { container } from &quot;../../lib/DIContainer&quot;;
import { moduleRegistry } from &quot;../../lib/ModuleRegistry&quot;;
import { eventBus } from &quot;../../lib/EventBus&quot;;
import { RBACInitializer } from &quot;../rbac/RBACInitializer&quot;;
import { VerificationService } from &quot;../verification/VerificationService&quot;;
import { PREDEFINED_VERIFICATION_POLICIES } from &quot;../../config/verification/PredefinedVerificationPolicies&quot;;
import { EnhancedPaymentService } from &quot;../payment/EnhancedPaymentService&quot;;
import { EnhancedSubscriptionService } from &quot;../enhanced-subscription.service&quot;;
import { FeeCalculator } from &quot;../payment/fees/FeeCalculator&quot;;
import { PercentageFeeStrategy, TieredFeeStrategy, FixedFeeStrategy } from &quot;../payment/fees/strategies/CommonFeeStrategies&quot;;
import { PaymentRouter } from &quot;../payment/routing/PaymentRouter&quot;;
import { CountryBasedRule, AmountBasedRule, SuccessRateRule } from &quot;../payment/routing/rules/CommonRoutingRules&quot;;
import { OperationalModeService, OperationalMode } from &quot;./OperationalModeService&quot;;

/**
 * System initializer service
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\payment\EnhancedPaymentService.ts (Line 16:11 - Line 29:4), src\services\payment\EnhancedPaymentService.ts (Line 8:17 - Line 17:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn109" onclick="toggleCodeBlock('cloneGroup109', 'expandBtn109', 'collapseBtn109')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn109" onclick="toggleCodeBlock('cloneGroup109', 'expandBtn109', 'collapseBtn109')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup109"><code class="language-typescript text-sm text-gray-800">;
import { IPaymentMethod, PaymentRequest, PaymentResult } from &quot;../../interfaces/payment/IPaymentMethod&quot;;
import { PaymentMethodFactory } from &quot;../../factories/payment/PaymentMethodFactory&quot;;
import { PaymentGatewayFactory } from &quot;../../factories/payment/PaymentGatewayFactory&quot;;
import { PaymentMethodType } from &quot;../../types/payment-method.types&quot;;
import { logger } from &quot;../../lib/logger&quot;;
import { SubscriptionService } from &quot;../subscription.service&quot;;
import { v4 as uuidv4 } from &quot;uuid&quot;;
import { Merchant, PaymentMethodType } from '../types';


/**
 * Payment pre-processor interface
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\monitoring\verification-alert.service.ts (Line 338:5 - Line 345:9), src\services\optimization\verification-optimization.service.ts (Line 182:5 - Line 189:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn110" onclick="toggleCodeBlock('cloneGroup110', 'expandBtn110', 'collapseBtn110')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn110" onclick="toggleCodeBlock('cloneGroup110', 'expandBtn110', 'collapseBtn110')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup110"><code class="language-typescript text-sm text-gray-800">);

          Object.entries(methodDistribution).forEach((([method, data])) =&gt; {
            const methodData: any = data as Record&lt;string, number&gt;;

            if (!methodMetrics[method]) {
              methodMetrics[method] = {
                attempts</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\blockchain\binance-api.service.ts (Line 485:2 - Line 502:5), src\services\blockchain\binance-api.service.ts (Line 347:6 - Line 364:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn111" onclick="toggleCodeBlock('cloneGroup111', 'expandBtn111', 'collapseBtn111')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn111" onclick="toggleCodeBlock('cloneGroup111', 'expandBtn111', 'collapseBtn111')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup111"><code class="language-typescript text-sm text-gray-800">,
        timestamp: Date.now(),
        recvWindow: 5000,
      };

      // Generate signature
      const queryString: any = this.buildQueryString(params);
      const signature: any = crypto
        .createHmac('sha256', merchantSecretKey)
        .update(queryString)
        .digest('hex');

      // Prepare headers
      const headers: any = {
        'X-MBX-APIKEY': merchantApiKey,
      };

      const response: any = await this</code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\payment\payment.module.ts (Line 8:2 - Line 29:4), src\modules\webhook\webhook.module.ts (Line 8:2 - Line 29:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn112" onclick="toggleCodeBlock('cloneGroup112', 'expandBtn112', 'collapseBtn112')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn112" onclick="toggleCodeBlock('cloneGroup112', 'expandBtn112', 'collapseBtn112')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup112"><code class="language-typescript text-sm text-gray-800">} from '@prisma/client';
import { Prisma } from '@prisma/client';
import { logger, ErrorFactory } from &quot;../../utils&quot;;
import { authMiddleware } from '../../middlewares/auth.middleware';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Payment Module
 * This module provides payment functionality with zero duplication
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\payment\payment.module.ts (Line 47:5 - Line 58:16), src\modules\webhook\webhook.module.ts (Line 47:5 - Line 58:24)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn113" onclick="toggleCodeBlock('cloneGroup113', 'expandBtn113', 'collapseBtn113')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn113" onclick="toggleCodeBlock('cloneGroup113', 'expandBtn113', 'collapseBtn113')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup113"><code class="language-typescript text-sm text-gray-800">);

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('get', '/:id', controller.getById)
      .addRoute('post', '/', controller.create)
      .addRoute('put', '/:id', controller.update)
      .addRoute('delete', '/:id', controller.delete)
      .addRoute('get', '/merchant/:merchantId', controller.getByMerchantId</code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\payment\payment.module.ts (Line 59:14 - Line 69:40), src\modules\webhook\webhook.module.ts (Line 59:15 - Line 69:40)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn114" onclick="toggleCodeBlock('cloneGroup114', 'expandBtn114', 'collapseBtn114')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn114" onclick="toggleCodeBlock('cloneGroup114', 'expandBtn114', 'collapseBtn114')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup114"><code class="language-typescript text-sm text-gray-800">)
      .addMiddleware(authMiddleware);

    // Add custom repository methods
    this.moduleFactory.addRepositoryMethod(
      'findByMerchantId',
      async (merchantId: string, options: { limit?: number; offset?: number } = {}) =&gt; {
        try {
          return await repository.findByFieldWithPagination('merchantId', merchantId, options);
        } catch (error) {
          logger.error(`Error finding payments by merchant ID </code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\payment\payment.module.ts (Line 129:52 - Line 137:16), src\modules\webhook\webhook.module.ts (Line 124:52 - Line 132:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn115" onclick="toggleCodeBlock('cloneGroup115', 'expandBtn115', 'collapseBtn115')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn115" onclick="toggleCodeBlock('cloneGroup115', 'expandBtn115', 'collapseBtn115')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup115"><code class="language-typescript text-sm text-gray-800">);
          }

          // Parse pagination parameters
          const limit: any =parseInt(req.query.limit as string) || 10;
          const page: any =parseInt(req.query.page as string) || 1;
          const offset: any =(page - 1) * limit;

          // Get payments</code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\merchant\merchant.module.ts (Line 311:11 - Line 316:25), src\modules\webhook\webhook.module.ts (Line 127:11 - Line 132:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn116" onclick="toggleCodeBlock('cloneGroup116', 'expandBtn116', 'collapseBtn116')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn116" onclick="toggleCodeBlock('cloneGroup116', 'expandBtn116', 'collapseBtn116')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup116"><code class="language-typescript text-sm text-gray-800">// Parse pagination parameters
          const limit: any =parseInt(req.query.limit as string) || 10;
          const page: any =parseInt(req.query.page as string) || 1;
          const offset: any =(page - 1) * limit;

          // Get merchant payments</code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\example\example.module.ts (Line 34:5 - Line 45:17), src\modules\webhook\webhook.module.ts (Line 47:5 - Line 58:24)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn117" onclick="toggleCodeBlock('cloneGroup117', 'expandBtn117', 'collapseBtn117')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn117" onclick="toggleCodeBlock('cloneGroup117', 'expandBtn117', 'collapseBtn117')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup117"><code class="language-typescript text-sm text-gray-800">);

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('get', '/:id', controller.getById)
      .addRoute('post', '/', controller.create)
      .addRoute('put', '/:id', controller.update)
      .addRoute('delete', '/:id', controller.delete)
      .addRoute('get', '/search/:query'</code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\example\example.module.ts (Line 332:2 - Line 341:28), src\modules\example\example.module.ts (Line 300:2 - Line 309:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn118" onclick="toggleCodeBlock('cloneGroup118', 'expandBtn118', 'collapseBtn118')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn118" onclick="toggleCodeBlock('cloneGroup118', 'expandBtn118', 'collapseBtn118')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup118"><code class="language-typescript text-sm text-gray-800">} = req.params;
          const { limit, offset } = req.query;

          // Parse pagination options
          const options: any = {
            limit: limit ? parseInt(limit as string, 10) : undefined,
            offset: offset ? parseInt(offset as string, 10) : undefined
          };

          // Get examples by category</code></pre></div><div class="py-4"><p class="text-gray-600">src\modules\example\example.module.ts (Line 364:2 - Line 373:26), src\modules\example\example.module.ts (Line 300:2 - Line 309:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn119" onclick="toggleCodeBlock('cloneGroup119', 'expandBtn119', 'collapseBtn119')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn119" onclick="toggleCodeBlock('cloneGroup119', 'expandBtn119', 'collapseBtn119')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup119"><code class="language-typescript text-sm text-gray-800">} = req.params;
          const { limit, offset } = req.query;

          // Parse pagination options
          const options: any = {
            limit: limit ? parseInt(limit as string, 10) : undefined,
            offset: offset ? parseInt(offset as string, 10) : undefined
          };

          // Get examples by status</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\shared\BaseResponseMapper.ts (Line 56:12 - Line 71:6), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 37:16 - Line 52:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn120" onclick="toggleCodeBlock('cloneGroup120', 'expandBtn120', 'collapseBtn120')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn120" onclick="toggleCodeBlock('cloneGroup120', 'expandBtn120', 'collapseBtn120')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup120"><code class="language-typescript text-sm text-gray-800">&lt;T&gt; = {
      success: true,
      data,
      message,
      pagination,
      timestamp: new Date(),
      requestId: res.locals.requestId ?? 'unknown',
    };

    res.status(statusCode).json(response);
  }

  /**
   * Send error response
   */
  static sendError(res: Response, error: Error</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\shared\BaseResponseMapper.ts (Line 72:17 - Line 92:2), src\controllers\identity-verification\mappers\IdentityVerificationResponseMapper.ts (Line 53:14 - Line 73:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn121" onclick="toggleCodeBlock('cloneGroup121', 'expandBtn121', 'collapseBtn121')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn121" onclick="toggleCodeBlock('cloneGroup121', 'expandBtn121', 'collapseBtn121')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup121"><code class="language-typescript text-sm text-gray-800">;

    if (error instanceof AppError) {
      errorResponse = {
        success: false,
        error: {
          message: error.message,
          code: error.code,
          type: error.type,
          details: error.details,
        },
        timestamp: new Date(),
        requestId: res.locals.requestId ?? 'unknown',
      };

      statusCode = statusCode ?? error.statusCode ?? 400;
    } else {
      errorResponse = {
        success: false,
        error: {
          message: error.message,</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\monitoring\verification-monitoring.controller.ts (Line 88:29 - Line 115:33), src\controllers\monitoring\verification-monitoring.controller.ts (Line 29:23 - Line 56:21)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn122" onclick="toggleCodeBlock('cloneGroup122', 'expandBtn122', 'collapseBtn122')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn122" onclick="toggleCodeBlock('cloneGroup122', 'expandBtn122', 'collapseBtn122')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup122"><code class="language-typescript text-sm text-gray-800">(req: Request, res: Response) {
    try {
      const { startDate, endDate, period = 'day' } = req.query;

      // Parse dates
      const parsedStartDate: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const parsedEndDate: any = endDate ? new Date(endDate as string) : new Date();

      // Validate dates
      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format',
        });
      }

      // Get metrics from database
      const metrics: any = await prisma.verificationMetrics.findMany({
        where: { timestamp: {
            gte: parsedStartDate,
            lte: parsedEndDate,
          },
        },
        orderBy: { timestamp: 'asc',
        },
      });

      // Aggregate method distribution</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\merchant\MerchantController.ts (Line 9:11 - Line 21:4), src\controllers\merchant\MerchantController.ts (Line 2:10 - Line 10:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn123" onclick="toggleCodeBlock('cloneGroup123', 'expandBtn123', 'collapseBtn123')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn123" onclick="toggleCodeBlock('cloneGroup123', 'expandBtn123', 'collapseBtn123')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup123"><code class="language-typescript text-sm text-gray-800">;
import { asyncHandler } from &quot;../../middleware/asyncHandler&quot;;
import { CrudController } from &quot;../base/CrudController&quot;;
import { MerchantService } from &quot;../../services/refactored/merchant.service&quot;;
import { Merchant, Prisma } from &quot;@prisma/client&quot;;
import { AppError } from &quot;../../utils/appError&quot;;
import { ServiceFactory } from &quot;../../factories/ServiceFactory&quot;;
import { Merchant } from '../types';


/**
 * Merchant controller
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\IdentityVerificationController.ts (Line 73:2 - Line 87:24), src\controllers\identity-verification\IdentityVerificationController.ts (Line 37:2 - Line 51:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn124" onclick="toggleCodeBlock('cloneGroup124', 'expandBtn124', 'collapseBtn124')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn124" onclick="toggleCodeBlock('cloneGroup124', 'expandBtn124', 'collapseBtn124')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup124"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: AuthenticatedRequest, res: Response) =&gt; {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateERC1484Identity</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\IdentityVerificationController.ts (Line 108:2 - Line 122:23), src\controllers\identity-verification\IdentityVerificationController.ts (Line 37:2 - Line 51:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn125" onclick="toggleCodeBlock('cloneGroup125', 'expandBtn125', 'collapseBtn125')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn125" onclick="toggleCodeBlock('cloneGroup125', 'expandBtn125', 'collapseBtn125')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup125"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: AuthenticatedRequest, res: Response) =&gt; {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateERC725Identity</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\IdentityVerificationController.ts (Line 143:2 - Line 157:24), src\controllers\identity-verification\IdentityVerificationController.ts (Line 37:2 - Line 51:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn126" onclick="toggleCodeBlock('cloneGroup126', 'expandBtn126', 'collapseBtn126')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn126" onclick="toggleCodeBlock('cloneGroup126', 'expandBtn126', 'collapseBtn126')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup126"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: AuthenticatedRequest, res: Response) =&gt; {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateENSVerification</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\IdentityVerificationController.ts (Line 178:2 - Line 192:18), src\controllers\identity-verification\IdentityVerificationController.ts (Line 37:2 - Line 51:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn127" onclick="toggleCodeBlock('cloneGroup127', 'expandBtn127', 'collapseBtn127')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn127" onclick="toggleCodeBlock('cloneGroup127', 'expandBtn127', 'collapseBtn127')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup127"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: AuthenticatedRequest, res: Response) =&gt; {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validatePolygonID</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\IdentityVerificationController.ts (Line 213:2 - Line 227:18), src\controllers\identity-verification\IdentityVerificationController.ts (Line 37:2 - Line 51:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn128" onclick="toggleCodeBlock('cloneGroup128', 'expandBtn128', 'collapseBtn128')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn128" onclick="toggleCodeBlock('cloneGroup128', 'expandBtn128', 'collapseBtn128')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup128"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: AuthenticatedRequest, res: Response) =&gt; {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateWorldcoin</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\identity-verification\IdentityVerificationController.ts (Line 248:2 - Line 262:27), src\controllers\identity-verification\IdentityVerificationController.ts (Line 37:2 - Line 51:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn129" onclick="toggleCodeBlock('cloneGroup129', 'expandBtn129', 'collapseBtn129')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn129" onclick="toggleCodeBlock('cloneGroup129', 'expandBtn129', 'collapseBtn129')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup129"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: AuthenticatedRequest, res: Response) =&gt; {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'verification',
        'create'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      this.validationService.validateUnstoppableDomains</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fraud-detection\FraudDetectionController.ts (Line 132:16 - Line 142:6), src\controllers\fraud-detection\FraudDetectionController.ts (Line 102:14 - Line 113:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn130" onclick="toggleCodeBlock('cloneGroup130', 'expandBtn130', 'collapseBtn130')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn130" onclick="toggleCodeBlock('cloneGroup130', 'expandBtn130', 'collapseBtn130')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup130"><code class="language-typescript text-sm text-gray-800">,
        req.params.merchantId
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const merchantId = this.validationService.validateMerchantId(req.params.merchantId);
      const</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\AlertAggregationController.ts (Line 148:9 - Line 158:6), src\controllers\alert-aggregation\AlertAggregationController.ts (Line 89:7 - Line 100:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn131" onclick="toggleCodeBlock('cloneGroup131', 'expandBtn131', 'collapseBtn131')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn131" onclick="toggleCodeBlock('cloneGroup131', 'expandBtn131', 'collapseBtn131')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup131"><code class="language-typescript text-sm text-gray-800">,
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');
      const</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\AlertAggregationController.ts (Line 179:9 - Line 191:6), src\controllers\alert-aggregation\AlertAggregationController.ts (Line 89:7 - Line 101:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn132" onclick="toggleCodeBlock('cloneGroup132', 'expandBtn132', 'collapseBtn132')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn132" onclick="toggleCodeBlock('cloneGroup132', 'expandBtn132', 'collapseBtn132')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup132"><code class="language-typescript text-sm text-gray-800">,
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');

      // Business logic
      await</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\AlertAggregationController.ts (Line 208:20 - Line 220:23), src\controllers\alert-aggregation\AlertAggregationController.ts (Line 46:20 - Line 58:23)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn133" onclick="toggleCodeBlock('cloneGroup133', 'expandBtn133', 'collapseBtn133')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn133" onclick="toggleCodeBlock('cloneGroup133', 'expandBtn133', 'collapseBtn133')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup133"><code class="language-typescript text-sm text-gray-800">,
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const pagination = this.validationService.validatePaginationParams(req.query);

      // Build filters
      const filters: CorrelationRuleFilters</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\AlertAggregationController.ts (Line 220:2 - Line 225:20), src\controllers\alert-aggregation\AlertAggregationController.ts (Line 60:4 - Line 65:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn134" onclick="toggleCodeBlock('cloneGroup134', 'expandBtn134', 'collapseBtn134')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn134" onclick="toggleCodeBlock('cloneGroup134', 'expandBtn134', 'collapseBtn134')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup134"><code class="language-typescript text-sm text-gray-800">;
      if (req.query.enabled !== undefined) filters.enabled = req.query.enabled === 'true';
      if (req.query.search) filters.search = req.query.search as string;

      // Business logic
      const result = await this.businessService.getCorrelationRules</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\alert-aggregation\AlertAggregationController.ts (Line 248:20 - Line 261:19), src\controllers\alert-aggregation\AlertAggregationController.ts (Line 88:20 - Line 101:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn135" onclick="toggleCodeBlock('cloneGroup135', 'expandBtn135', 'collapseBtn135')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn135" onclick="toggleCodeBlock('cloneGroup135', 'expandBtn135', 'collapseBtn135')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup135"><code class="language-typescript text-sm text-gray-800">,
        'read',
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');

      // Business logic
      const rule = await this.businessService.getCorrelationRule</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\AdminController.ts (Line 68:2 - Line 82:11), src\controllers\admin\AdminController.ts (Line 42:2 - Line 56:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn136" onclick="toggleCodeBlock('cloneGroup136', 'expandBtn136', 'collapseBtn136')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn136" onclick="toggleCodeBlock('cloneGroup136', 'expandBtn136', 'collapseBtn136')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup136"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: AuthenticatedRequest, res: Response) =&gt; {
    try {
      // Authorization
      const authContext = this.authService.createAuthorizationContext(
        req.user,
        'dashboard',
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Business logic
      const statistics</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\AdminController.ts (Line 99:14 - Line 111:17), src\controllers\alert-aggregation\AlertAggregationController.ts (Line 46:20 - Line 58:23)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn137" onclick="toggleCodeBlock('cloneGroup137', 'expandBtn137', 'collapseBtn137')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn137" onclick="toggleCodeBlock('cloneGroup137', 'expandBtn137', 'collapseBtn137')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup137"><code class="language-typescript text-sm text-gray-800">,
        'read'
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const pagination = this.validationService.validatePaginationParams(req.query);

      // Build filters
      const filters: AdminUserFilters</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\AdminController.ts (Line 202:9 - Line 212:6), src\controllers\admin\AdminController.ts (Line 143:7 - Line 154:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn138" onclick="toggleCodeBlock('cloneGroup138', 'expandBtn138', 'collapseBtn138')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn138" onclick="toggleCodeBlock('cloneGroup138', 'expandBtn138', 'collapseBtn138')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup138"><code class="language-typescript text-sm text-gray-800">,
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const userId = this.validationService.validateId(req.params.id, 'User ID');
      const</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\admin\AdminController.ts (Line 233:9 - Line 245:6), src\controllers\admin\AdminController.ts (Line 143:7 - Line 155:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn139" onclick="toggleCodeBlock('cloneGroup139', 'expandBtn139', 'collapseBtn139')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn139" onclick="toggleCodeBlock('cloneGroup139', 'expandBtn139', 'collapseBtn139')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup139"><code class="language-typescript text-sm text-gray-800">,
        req.params.id
      );
      const permission = await this.authService.checkPermission(authContext);
      if (!permission.allowed) {
        this.authService.handleAuthorizationError(permission);
      }

      // Validation
      const userId = this.validationService.validateId(req.params.id, 'User ID');

      // Business logic
      await</code></pre></div><div class="py-4"><p class="text-gray-600">tests\setup\jest.setup.ts (Line 116:9 - Line 124:12), tests\setup\jest.setup.ts (Line 108:5 - Line 116:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn140" onclick="toggleCodeBlock('cloneGroup140', 'expandBtn140', 'collapseBtn140')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn140" onclick="toggleCodeBlock('cloneGroup140', 'expandBtn140', 'collapseBtn140')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup140"><code class="language-typescript text-sm text-gray-800">: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    transaction</code></pre></div><div class="py-4"><p class="text-gray-600">tests\setup\jest.setup.ts (Line 124:12 - Line 132:21), tests\setup\jest.setup.ts (Line 108:5 - Line 116:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn141" onclick="toggleCodeBlock('cloneGroup141', 'expandBtn141', 'collapseBtn141')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn141" onclick="toggleCodeBlock('cloneGroup141', 'expandBtn141', 'collapseBtn141')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup141"><code class="language-typescript text-sm text-gray-800">: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    identityVerification</code></pre></div><div class="py-4"><p class="text-gray-600">tests\setup\jest.setup.ts (Line 132:21 - Line 140:15), tests\setup\jest.setup.ts (Line 108:5 - Line 116:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn142" onclick="toggleCodeBlock('cloneGroup142', 'expandBtn142', 'collapseBtn142')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn142" onclick="toggleCodeBlock('cloneGroup142', 'expandBtn142', 'collapseBtn142')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup142"><code class="language-typescript text-sm text-gray-800">: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    riskAssessment</code></pre></div><div class="py-4"><p class="text-gray-600">tests\setup\jest.setup.ts (Line 140:15 - Line 147:10), tests\setup\jest.setup.ts (Line 108:5 - Line 115:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn143" onclick="toggleCodeBlock('cloneGroup143', 'expandBtn143', 'collapseBtn143')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn143" onclick="toggleCodeBlock('cloneGroup143', 'expandBtn143', 'collapseBtn143')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup143"><code class="language-typescript text-sm text-gray-800">: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      aggregate</code></pre></div><div class="py-4"><p class="text-gray-600">src\utils\test-connection.ts (Line 48:5 - Line 56:2), src\utils\test-db-connection.ts (Line 26:5 - Line 34:24)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn144" onclick="toggleCodeBlock('cloneGroup144', 'expandBtn144', 'collapseBtn144')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn144" onclick="toggleCodeBlock('cloneGroup144', 'expandBtn144', 'collapseBtn144')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup144"><code class="language-typescript text-sm text-gray-800">// Print database connection info
    console.log('\n📊 Database connection info:');
    console.log(`🔗 URL: ${process.env.DATABASE_URL?.replace(/:[^:]*@/, ':****@')}`);
    console.log(`🏠 Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`🔢 Port: ${process.env.DB_PORT || '5432'}`);
    console.log(`👤 Username: ${process.env.DB_USERNAME || 'postgres'}`);
    console.log(`📚 Database: ${process.env.DB_NAME || 'amazingpay'}`);

  }</code></pre></div><div class="py-4"><p class="text-gray-600">src\utils\test-connection.ts (Line 56:3 - Line 72:15), src\utils\test-db-connection.ts (Line 45:3 - Line 61:23)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn145" onclick="toggleCodeBlock('cloneGroup145', 'expandBtn145', 'collapseBtn145')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn145" onclick="toggleCodeBlock('cloneGroup145', 'expandBtn145', 'collapseBtn145')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup145"><code class="language-typescript text-sm text-gray-800">} catch (error) {
    console.error('❌ Database connection failed:', error);
    console.log('\n⚠️ Please check your database configuration in the .env file.');
    console.log('⚠️ Make sure PostgreSQL is running and accessible.');
    console.log('⚠️ You may need to create the database: CREATE DATABASE amazingpay;');
  } finally {
    try {
      await prisma.$disconnect();
      console.log('🔌 Database connection closed.');
    } catch (disconnectError) {
      console.error('❌ Error disconnecting from database:', disconnectError);
    }
  }
}

// Run the test
testConnection</code></pre></div><div class="py-4"><p class="text-gray-600">src\utils\error-handler.ts (Line 128:24 - Line 163:14), src\utils\error-handler.ts (Line 47:20 - Line 82:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn146" onclick="toggleCodeBlock('cloneGroup146', 'expandBtn146', 'collapseBtn146')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn146" onclick="toggleCodeBlock('cloneGroup146', 'expandBtn146', 'collapseBtn146')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup146"><code class="language-typescript text-sm text-gray-800">, {
    path: req.path,
    method: req.method,
    error: err.message,
    stack: err.stack,
    requestId: req.id
  });

  // Create error response
  const errorResponse: ErrorResponse = {
    status: 'error',
    statusCode: err instanceof AppError ? err.statusCode : 500,
    message: err.message || 'Internal server error',
    timestamp: new Date().toISOString(),
    path: req.originalUrl || req.url
  };

  // Add request ID if available
  if (req.id) {
    errorResponse.requestId = req.id;
  }

  // Add error code if available
  if (err instanceof AppError &amp;&amp; err.code) {
    errorResponse.code = err.code;
  }

  // Add error details if available
  if (err instanceof AppError &amp;&amp; err.details) {
    errorResponse.details = err.details;
  }

  // Add stack trace in development
  if (!isProduction()) {
    errorResponse.stack = err.stack;
    errorResponse</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\verification-method.service.ts (Line 186:5 - Line 198:30), src\services\verification-method.service.ts (Line 134:5 - Line 146:22)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn147" onclick="toggleCodeBlock('cloneGroup147', 'expandBtn147', 'collapseBtn147')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn147" onclick="toggleCodeBlock('cloneGroup147', 'expandBtn147', 'collapseBtn147')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup147"><code class="language-typescript text-sm text-gray-800">const verificationMethod: any = await prisma.verificationMethod.findUnique({
      where: { id },
    });

    if (!verificationMethod) {
      throw new AppError({
        message: 'Verification method not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    // Delete verification method</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\verification-method.service.ts (Line 262:2 - Line 274:58), src\controllers\fraud-detection\services\FraudDetectionBusinessService.ts (Line 41:2 - Line 53:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn148" onclick="toggleCodeBlock('cloneGroup148', 'expandBtn148', 'collapseBtn148')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn148" onclick="toggleCodeBlock('cloneGroup148', 'expandBtn148', 'collapseBtn148')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup148"><code class="language-typescript text-sm text-gray-800">prisma.transaction.findUnique({
      where: { id: data.transactionId },
    });

    if (!transaction) {
      throw new AppError({
        message: 'Transaction not found',
        type: ErrorType.NOT_FOUND,
        code: ErrorCode.RESOURCE_NOT_FOUND,
      });
    }

    // Check if transaction is for the correct payment method</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\two-factor-auth.service.ts (Line 190:6 - Line 202:2), src\services\two-factor-auth.service.ts (Line 142:7 - Line 155:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn149" onclick="toggleCodeBlock('cloneGroup149', 'expandBtn149', 'collapseBtn149')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn149" onclick="toggleCodeBlock('cloneGroup149', 'expandBtn149', 'collapseBtn149')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup149"><code class="language-typescript text-sm text-gray-800">: string): Promise&lt;{ success: boolean }&gt; {
    try {
      // Get user
      const user: any = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },
      });

      if (!user) {
        throw createNotFoundError('User not found');
      }

      if (!</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\report-optimization.service.ts (Line 107:2 - Line 118:35), src\services\report-optimization.service.ts (Line 48:2 - Line 59:37)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn150" onclick="toggleCodeBlock('cloneGroup150', 'expandBtn150', 'collapseBtn150')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn150" onclick="toggleCodeBlock('cloneGroup150', 'expandBtn150', 'collapseBtn150')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup150"><code class="language-typescript text-sm text-gray-800">= true;

    while (hasMoreData) {
      const batch = await this.getDataBatch(reportType, parameters, offset, this.BATCH_SIZE);
      
      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      if (isFirstBatch &amp;&amp; batch.length &gt; 0) {
        // Add headers for the first batch</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\report-optimization.service.ts (Line 158:4 - Line 172:4), src\services\report-optimization.service.ts (Line 103:9 - Line 117:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn151" onclick="toggleCodeBlock('cloneGroup151', 'expandBtn151', 'collapseBtn151')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn151" onclick="toggleCodeBlock('cloneGroup151', 'expandBtn151', 'collapseBtn151')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup151"><code class="language-typescript text-sm text-gray-800">);
    
    let offset = 0;
    let hasMoreData = true;
    let isFirstBatch = true;

    while (hasMoreData) {
      const batch = await this.getDataBatch(reportType, parameters, offset, this.BATCH_SIZE);
      
      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      for</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\report-optimization.service.ts (Line 354:13 - Line 360:8), src\services\report-optimization.service.ts (Line 277:9 - Line 283:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn152" onclick="toggleCodeBlock('cloneGroup152', 'expandBtn152', 'collapseBtn152')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn152" onclick="toggleCodeBlock('cloneGroup152', 'expandBtn152', 'collapseBtn152')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup152"><code class="language-typescript text-sm text-gray-800">.findMany({
      where: {
        ...(startDate &amp;&amp; { createdAt: { gte: new Date(startDate) } }),
        ...(endDate &amp;&amp; { createdAt: { lte: new Date(endDate) } }),
        ...(status &amp;&amp; { status })
      },
      include</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\report-optimization.service.ts (Line 436:6 - Line 443:2), src\services\report-optimization.service.ts (Line 233:9 - Line 239:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn153" onclick="toggleCodeBlock('cloneGroup153', 'expandBtn153', 'collapseBtn153')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn153" onclick="toggleCodeBlock('cloneGroup153', 'expandBtn153', 'collapseBtn153')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup153"><code class="language-typescript text-sm text-gray-800">({
      where: {
        ...(startDate &amp;&amp; { createdAt: { gte: new Date(startDate) } }),
        ...(endDate &amp;&amp; { createdAt: { lte: new Date(endDate) } }),
        ...(merchantId &amp;&amp; { merchantId }),
        ...(status &amp;&amp; { status })
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\report-optimization.service.ts (Line 449:6 - Line 455:2), src\services\report-optimization.service.ts (Line 277:9 - Line 282:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn154" onclick="toggleCodeBlock('cloneGroup154', 'expandBtn154', 'collapseBtn154')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn154" onclick="toggleCodeBlock('cloneGroup154', 'expandBtn154', 'collapseBtn154')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup154"><code class="language-typescript text-sm text-gray-800">({
      where: {
        ...(startDate &amp;&amp; { createdAt: { gte: new Date(startDate) } }),
        ...(endDate &amp;&amp; { createdAt: { lte: new Date(endDate) } }),
        ...(status &amp;&amp; { status })
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\report-optimization.service.ts (Line 461:6 - Line 467:2), src\services\report-optimization.service.ts (Line 311:9 - Line 316:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn155" onclick="toggleCodeBlock('cloneGroup155', 'expandBtn155', 'collapseBtn155')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn155" onclick="toggleCodeBlock('cloneGroup155', 'expandBtn155', 'collapseBtn155')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup155"><code class="language-typescript text-sm text-gray-800">({
      where: {
        ...(startDate &amp;&amp; { createdAt: { gte: new Date(startDate) } }),
        ...(endDate &amp;&amp; { createdAt: { lte: new Date(endDate) } }),
        ...(type &amp;&amp; { type })
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\report-optimization.service.ts (Line 473:13 - Line 484:6), src\services\report-optimization.service.ts (Line 449:9 - Line 458:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn156" onclick="toggleCodeBlock('cloneGroup156', 'expandBtn156', 'collapseBtn156')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn156" onclick="toggleCodeBlock('cloneGroup156', 'expandBtn156', 'collapseBtn156')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup156"><code class="language-typescript text-sm text-gray-800">.count({
      where: {
        ...(startDate &amp;&amp; { createdAt: { gte: new Date(startDate) } }),
        ...(endDate &amp;&amp; { createdAt: { lte: new Date(endDate) } }),
        ...(status &amp;&amp; { status })
      }
    });
  }

  /**
   * Get average record size in bytes for different report types
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\rbac.service.ts (Line 182:2 - Line 195:2), src\services\rbac.service.ts (Line 137:8 - Line 150:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn157" onclick="toggleCodeBlock('cloneGroup157', 'expandBtn157', 'collapseBtn157')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn157" onclick="toggleCodeBlock('cloneGroup157', 'expandBtn157', 'collapseBtn157')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup157"><code class="language-typescript text-sm text-gray-800">&gt; {
    try {
      // Get user with roles and permissions
      const user: any = await this.prisma.user.findUnique({
        where: { id: userId },
        include: {
          roles: {
            include: { permissions: true },
          },
        },
      });

      if (!user || !user.roles || user.roles.length === 0) {
        return [</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\push-notification.service.ts (Line 188:7 - Line 213:38), src\services\push-notification.service.ts (Line 117:7 - Line 142:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn158" onclick="toggleCodeBlock('cloneGroup158', 'expandBtn158', 'collapseBtn158')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn158" onclick="toggleCodeBlock('cloneGroup158', 'expandBtn158', 'collapseBtn158')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup158"><code class="language-typescript text-sm text-gray-800">let successCount: number = 0;

      for (const sub of subscriptions) {
        try {
          const subscription: any = JSON.parse(sub.subscription);
          const success: any = await this.sendNotification(
            subscription,
            title,
            body,
            icon,
            data,
            url
          );

          if (success) {
            successCount++;
          }
        } catch (error) {
          logger.error('Error sending notification to subscription', {
            error,
            subscriptionId: sub.id,
          });
        }
      }

      logger.info('Push notifications sent to merchant'</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\payment-verification.service.ts (Line 169:5 - Line 178:76), src\services\payment-verification.service.ts (Line 123:14 - Line 132:76)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn159" onclick="toggleCodeBlock('cloneGroup159', 'expandBtn159', 'collapseBtn159')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn159" onclick="toggleCodeBlock('cloneGroup159', 'expandBtn159', 'collapseBtn159')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup159"><code class="language-typescript text-sm text-gray-800">: string,
        amount: string,
        currency: string,
        merchantApiKey?: string,
        merchantSecretKey?: string
    ): Promise&lt;PaymentVerificationResult&gt; {
        return this.executeDbOperation(
            async () =&gt; {
                if (!merchantApiKey || !merchantSecretKey) {
                    throw this.paymentError(&quot;Merchant API key and secret key are required for Binance C2C verification&quot;</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\payment-page.service.ts (Line 178:17 - Line 195:38), src\services\payment-page.service.ts (Line 148:2 - Line 165:36)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn160" onclick="toggleCodeBlock('cloneGroup160', 'expandBtn160', 'collapseBtn160')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn160" onclick="toggleCodeBlock('cloneGroup160', 'expandBtn160', 'collapseBtn160')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup160"><code class="language-typescript text-sm text-gray-800">},
                include: { merchant: {
                        select: { id: true,
                            name: true,
                            email: true,
                            businessName: true
                        }
                    }
                }
            });

            if (!paymentPage) {
                return null;
            }

            return paymentPage;
        } catch (error) {
            console.error(&quot;Error getting payment page by slug:&quot;</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\payment-page.service.ts (Line 230:13 - Line 239:12), src\services\payment-page.service.ts (Line 86:13 - Line 95:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn161" onclick="toggleCodeBlock('cloneGroup161', 'expandBtn161', 'collapseBtn161')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn161" onclick="toggleCodeBlock('cloneGroup161', 'expandBtn161', 'collapseBtn161')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup161"><code class="language-typescript text-sm text-gray-800">for (const paymentMethodId of data.paymentMethodIds) {
                const paymentMethod: any = await prisma.paymentMethod.findUnique({
                    where: { id: paymentMethodId }
                });

                if (!paymentMethod) {
                    throw new AppError(`Payment method with ID ${paymentMethodId} not found`, 404);
                }

                if (paymentMethod.merchantId !== paymentPage</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\payment-page.service.ts (Line 295:9 - Line 307:63), src\services\payment-page.service.ts (Line 202:9 - Line 214:54)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn162" onclick="toggleCodeBlock('cloneGroup162', 'expandBtn162', 'collapseBtn162')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn162" onclick="toggleCodeBlock('cloneGroup162', 'expandBtn162', 'collapseBtn162')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup162"><code class="language-typescript text-sm text-gray-800">const paymentPage: any = await prisma.paymentPage.findUnique({
            where: { id }
        });

        if (!paymentPage) {
            throw new AppError({
            message: &quot;Payment page not found&quot;,
            type: ErrorType.NOT_FOUND,
            code: ErrorCode.RESOURCE_NOT_FOUND
        });
        }

        // Check if there are any transactions using this payment page</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\merchant-self-service.service.ts (Line 317:7 - Line 328:6), src\services\merchant-self-service.service.ts (Line 267:5 - Line 278:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn163" onclick="toggleCodeBlock('cloneGroup163', 'expandBtn163', 'collapseBtn163')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn163" onclick="toggleCodeBlock('cloneGroup163', 'expandBtn163', 'collapseBtn163')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup163"><code class="language-typescript text-sm text-gray-800">): Promise&lt;MerchantWebhook&gt; {
        try {
            // Check if webhook exists
            const webhook: any = await prisma.merchantWebhook.findUnique({
                where: { id: webhookId }
            });

            if (!webhook) {
                throw this.genericError(&quot;Webhook not found&quot;, 404, ApiErrorCode.NOT_FOUND);
            }

            const</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\merchant-relationship.service.ts (Line 239:26 - Line 250:17), src\services\merchant-segmentation.service.ts (Line 182:9 - Line 193:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn164" onclick="toggleCodeBlock('cloneGroup164', 'expandBtn164', 'collapseBtn164')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn164" onclick="toggleCodeBlock('cloneGroup164', 'expandBtn164', 'collapseBtn164')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup164"><code class="language-typescript text-sm text-gray-800">&gt; {
        try {
            // Check if merchant exists
            const merchant: any = await this.prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError(&quot;Merchant not found&quot;, 404, ApiErrorCode.NOT_FOUND);
            }

            // Create ticket</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\merchant-relationship.service.ts (Line 372:22 - Line 383:17), src\services\merchant-relationship.service.ts (Line 321:23 - Line 332:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn165" onclick="toggleCodeBlock('cloneGroup165', 'expandBtn165', 'collapseBtn165')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn165" onclick="toggleCodeBlock('cloneGroup165', 'expandBtn165', 'collapseBtn165')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup165"><code class="language-typescript text-sm text-gray-800">&gt; {
        try {
            // Check if ticket exists
            const ticket: any = await this.prisma.merchantSupportTicket.findUnique({
                where: { id: ticketId }
            });

            if (!ticket) {
                throw this.genericError(&quot;Support ticket not found&quot;, 404, ApiErrorCode.NOT_FOUND);
            }

            // Update ticket</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\merchant-relationship.service.ts (Line 419:20 - Line 430:38), src\services\merchant-segmentation.service.ts (Line 182:9 - Line 193:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn166" onclick="toggleCodeBlock('cloneGroup166', 'expandBtn166', 'collapseBtn166')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn166" onclick="toggleCodeBlock('cloneGroup166', 'expandBtn166', 'collapseBtn166')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup166"><code class="language-typescript text-sm text-gray-800">&gt; {
        try {
            // Check if merchant exists
            const merchant: any = await this.prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                throw this.genericError(&quot;Merchant not found&quot;, 404, ApiErrorCode.NOT_FOUND);
            }

            // Check if onboarding already exists</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\fraud-detection.service.ts (Line 6:11 - Line 36:4), src\services\fraud-detection\core\FraudDetectionTypes.ts (Line 7:17 - Line 36:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn167" onclick="toggleCodeBlock('cloneGroup167', 'expandBtn167', 'collapseBtn167')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn167" onclick="toggleCodeBlock('cloneGroup167', 'expandBtn167', 'collapseBtn167')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup167"><code class="language-typescript text-sm text-gray-800">;


/**
 * Risk level enum
 */
export enum RiskLevel {
  LOW = &quot;LOW&quot;,
  MEDIUM = &quot;MEDIUM&quot;,
  HIGH = &quot;HIGH&quot;,
  CRITICAL = &quot;CRITICAL&quot;,
}

/**
 * Risk factor enum
 */
export enum RiskFactor {
  AMOUNT = &quot;AMOUNT&quot;,
  LOCATION = &quot;LOCATION&quot;,
  FREQUENCY = &quot;FREQUENCY&quot;,
  TIME = &quot;TIME&quot;,
  IP = &quot;IP&quot;,
  DEVICE = &quot;DEVICE&quot;,
  PAYMENT_METHOD = &quot;PAYMENT_METHOD&quot;,
  BEHAVIOR = &quot;BEHAVIOR&quot;,
  HISTORY = &quot;HISTORY&quot;,
}

/**
 * Risk score interface
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\fraud-detection.service.ts (Line 94:7 - Line 147:2), src\services\fraud-detection\core\FraudDetectionTypes.ts (Line 112:7 - Line 168:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn168" onclick="toggleCodeBlock('cloneGroup168', 'expandBtn168', 'collapseBtn168')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn168" onclick="toggleCodeBlock('cloneGroup168', 'expandBtn168', 'collapseBtn168')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup168"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * Fraud detection configuration
 */
export interface FraudDetectionConfig {
  /**
   * Threshold for flagging transactions (0-100)
   */
  flagThreshold: number;

  /**
   * Threshold for blocking transactions (0-100)
   */
  blockThreshold: number;

  /**
   * Whether to automatically block high-risk transactions
   */
  autoBlock: boolean;

  /**
   * Factor weights (0-1)
   */
  factorWeights: {
    [key in RiskFactor]?: number;
  };

  /**
   * High-risk countries
   */
  highRiskCountries: string[];

  /**
   * High-risk IP ranges
   */
  highRiskIpRanges: string[];

  /**
   * Maximum transaction amount before flagging
   */
  maxTransactionAmount: number;

  /**
   * Maximum transactions per hour before flagging
   */
  maxTransactionsPerHour: number;

  /**
   * Maximum transactions per day before flagging
   */
  maxTransactionsPerDay: number;
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\enhanced-subscription.service.ts (Line 121:23 - Line 142:6), src\services\enhanced-subscription.service.ts (Line 83:18 - Line 104:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn169" onclick="toggleCodeBlock('cloneGroup169', 'expandBtn169', 'collapseBtn169')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn169" onclick="toggleCodeBlock('cloneGroup169', 'expandBtn169', 'collapseBtn169')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup169"><code class="language-typescript text-sm text-gray-800">[]&gt; {
        try {
            // Get merchant with subscription
            const merchant: any = await prisma.merchant.findUnique({
                where: { id: merchantId },
                include: { subscription: true }
            });

            if (!merchant || !merchant.subscription) {
                return [];
            }

            // Get subscription plan
            const plan: any = await prisma.subscriptionPlan.findUnique({
                where: { id: merchant.subscription.planId }
            });

            if (!plan) {
                return [];
            }

            const</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\alert-aggregation.service.ts (Line 398:6 - Line 413:5), src\services\alert-aggregation.service.ts (Line 350:21 - Line 365:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn170" onclick="toggleCodeBlock('cloneGroup170', 'expandBtn170', 'collapseBtn170')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn170" onclick="toggleCodeBlock('cloneGroup170', 'expandBtn170', 'collapseBtn170')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup170"><code class="language-typescript text-sm text-gray-800">,
    startTime: Date,
    endTime: Date
  ): Promise&lt;any[]&gt; {
    try {
      // Build where clause
      const where: any = {
        createdAt: {
          gte: startTime,
          lte: endTime,
        },
        status: AlertStatus.ACTIVE,
      };

      // Add type filter if not ANY
      if (type</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\alert-aggregation.service.ts (Line 458:2 - Line 472:49), src\services\alert-aggregation.service.ts (Line 414:5 - Line 428:39)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn171" onclick="toggleCodeBlock('cloneGroup171', 'expandBtn171', 'collapseBtn171')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn171" onclick="toggleCodeBlock('cloneGroup171', 'expandBtn171', 'collapseBtn171')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup171"><code class="language-typescript text-sm text-gray-800">;
      }

      // Exclude aggregated and correlated alerts
      where.source = { notIn: ['aggregation', 'correlation'] };

      // Get alerts
      const alerts: any = await prisma.alert.findMany({
        where,
        orderBy: { createdAt: 'desc' },
      });

      return alerts;
    } catch (error) {
      logger.error('Error getting secondary alerts for correlation'</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\advanced-report.service.ts (Line 290:2 - Line 298:14), src\services\report-optimization.service.ts (Line 254:2 - Line 262:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn172" onclick="toggleCodeBlock('cloneGroup172', 'expandBtn172', 'collapseBtn172')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn172" onclick="toggleCodeBlock('cloneGroup172', 'expandBtn172', 'collapseBtn172')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup172"><code class="language-typescript text-sm text-gray-800">=&gt; ({
      id: transaction.id,
      reference: transaction.reference,
      amount: transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      paymentMethod: transaction.paymentMethod,
      merchantName: transaction.merchant?.businessName || 'Unknown',
      merchantEmail</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\advanced-report.service.ts (Line 296:14 - Line 302:2), src\services\reporting\generators\TransactionReportGenerator.ts (Line 224:3 - Line 230:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn173" onclick="toggleCodeBlock('cloneGroup173', 'expandBtn173', 'collapseBtn173')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn173" onclick="toggleCodeBlock('cloneGroup173', 'expandBtn173', 'collapseBtn173')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup173"><code class="language-typescript text-sm text-gray-800">,
      merchantName: transaction.merchant?.businessName || 'Unknown',
      merchantEmail: transaction.merchant?.contactEmail || 'Unknown',
      description: transaction.description || '',
      createdAt: dayjs(transaction.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs(transaction.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
    }</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\advanced-report.service.ts (Line 367:5 - Line 387:23), src\services\advanced-report.service.ts (Line 309:7 - Line 329:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn174" onclick="toggleCodeBlock('cloneGroup174', 'expandBtn174', 'collapseBtn174')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn174" onclick="toggleCodeBlock('cloneGroup174', 'expandBtn174', 'collapseBtn174')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup174"><code class="language-typescript text-sm text-gray-800">, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere: any = {};

    if (merchantId) {
      merchantWhere.id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await prisma.merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      merchantWhere.id = merchant.id;
    }

    // Get payment methods</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\advanced-report.service.ts (Line 388:14 - Line 393:5), src\services\advanced-report.service.ts (Line 330:9 - Line 335:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn175" onclick="toggleCodeBlock('cloneGroup175', 'expandBtn175', 'collapseBtn175')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn175" onclick="toggleCodeBlock('cloneGroup175', 'expandBtn175', 'collapseBtn175')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup175"><code class="language-typescript text-sm text-gray-800">.findMany({
      where: {
        merchantId: merchantWhere.id,
        ...(startDate &amp;&amp; { createdAt: { gte: new Date(startDate) } }),
        ...(endDate &amp;&amp; { createdAt: { lte: new Date(endDate) } }),
        ...(type</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\advanced-report.service.ts (Line 436:26 - Line 457:21), src\services\advanced-report.service.ts (Line 308:22 - Line 329:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn176" onclick="toggleCodeBlock('cloneGroup176', 'expandBtn176', 'collapseBtn176')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn176" onclick="toggleCodeBlock('cloneGroup176', 'expandBtn176', 'collapseBtn176')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup176"><code class="language-typescript text-sm text-gray-800">(parameters: any): Promise&lt;any[]&gt; {
    const { startDate, endDate, merchantId, status, userId, userRole } = parameters;

    // Build where clause for merchant
    const merchantWhere: any = {};

    if (merchantId) {
      merchantWhere.id = merchantId;
    } else if (userRole !== 'ADMIN') {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await prisma.merchant.findFirst({
        where: { userId },
      });

      if (!merchant) {
        throw new Error('Merchant profile not found');
      }

      merchantWhere.id = merchant.id;
    }

    // Get subscriptions</code></pre></div><div class="py-4"><p class="text-gray-600">src\services\advanced-report.service.ts (Line 458:13 - Line 471:9), src\services\advanced-report.service.ts (Line 330:9 - Line 343:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn177" onclick="toggleCodeBlock('cloneGroup177', 'expandBtn177', 'collapseBtn177')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn177" onclick="toggleCodeBlock('cloneGroup177', 'expandBtn177', 'collapseBtn177')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup177"><code class="language-typescript text-sm text-gray-800">.findMany({
      where: {
        merchantId: merchantWhere.id,
        ...(startDate &amp;&amp; { createdAt: { gte: new Date(startDate) } }),
        ...(endDate &amp;&amp; { createdAt: { lte: new Date(endDate) } }),
        ...(status &amp;&amp; { status }),
      },
      include: {
        merchant: {
          select: {
            businessName: true,
          },
        },
        customer</code></pre></div><div class="py-4"><p class="text-gray-600">src\security\security-audit.ts (Line 399:11 - Line 408:9), src\security\security-audit.ts (Line 388:15 - Line 397:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn178" onclick="toggleCodeBlock('cloneGroup178', 'expandBtn178', 'collapseBtn178')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn178" onclick="toggleCodeBlock('cloneGroup178', 'expandBtn178', 'collapseBtn178')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup178"><code class="language-typescript text-sm text-gray-800">.forEach((issue) =&gt; {
        report += `- **${issue.test}**: ${issue.message}\n`;
        if (issue.recommendation) {
          report += `  - Recommendation: ${issue.recommendation}\n`;
        }
      });
      report += '\n';
    }

    if (warnings</code></pre></div><div class="py-4"><p class="text-gray-600">src\routes\subscription.routes.ts (Line 5:41 - Line 14:45), src\routes\system.routes.ts (Line 5:35 - Line 14:41)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn179" onclick="toggleCodeBlock('cloneGroup179', 'expandBtn179', 'collapseBtn179')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn179" onclick="toggleCodeBlock('cloneGroup179', 'expandBtn179', 'collapseBtn179')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup179"><code class="language-typescript text-sm text-gray-800">;
import { authenticate, authorize } from &quot;../middlewares/auth.middleware&quot;;
import { validate } from &quot;../middlewares/validation.middleware&quot;;
import { body, param } from &quot;express-validator&quot;;
import { authenticate, authorize } from &quot;../middlewares/auth.middleware&quot;;
import { validate } from &quot;../middlewares/validation.middleware&quot;;

const router: any =Router();

// Get all subscription plans - public route</code></pre></div><div class="py-4"><p class="text-gray-600">src\routes\merchant-relationship.routes.ts (Line 9:50 - Line 19:31), src\routes\merchant-segmentation.routes.ts (Line 9:50 - Line 19:31)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn180" onclick="toggleCodeBlock('cloneGroup180', 'expandBtn180', 'collapseBtn180')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn180" onclick="toggleCodeBlock('cloneGroup180', 'expandBtn180', 'collapseBtn180')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup180"><code class="language-typescript text-sm text-gray-800">;
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';


const router: any =express.Router();
const merchantRelationshipController</code></pre></div><div class="py-4"><p class="text-gray-600">src\routes\enhanced-risk-engine.routes.ts (Line 9:49 - Line 19:29), src\routes\merchant-segmentation.routes.ts (Line 9:50 - Line 19:31)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn181" onclick="toggleCodeBlock('cloneGroup181', 'expandBtn181', 'collapseBtn181')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn181" onclick="toggleCodeBlock('cloneGroup181', 'expandBtn181', 'collapseBtn181')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup181"><code class="language-typescript text-sm text-gray-800">;
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';
import { authMiddleware } from '../middlewares/authMiddleware';
import { roleMiddleware } from '../middlewares/roleMiddleware';
import { Merchant } from '../types';


const router: any =express.Router();
const enhancedRiskEngineController</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\validation.middleware.ts (Line 140:2 - Line 157:3), src\middlewares\validation.middleware.ts (Line 95:7 - Line 112:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn182" onclick="toggleCodeBlock('cloneGroup182', 'expandBtn182', 'collapseBtn182')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn182" onclick="toggleCodeBlock('cloneGroup182', 'expandBtn182', 'collapseBtn182')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup182"><code class="language-typescript text-sm text-gray-800">;
  }&gt;
): Middleware =&gt; {
  return (req: Request, res: Response, next: NextFunction) =&gt; {
    for (const param of params) {
      const source: any = param.source || 'query';
      const value: any =
        source === 'query'
          ? (req.query[param.name] as string)
          : source === 'body'
          ? req.body[param.name]
          : req.params[param.name];

      if (param.required &amp;&amp; (value === undefined || value === null || value === '')) {
        return next(new AppError(`${param.name} is required`, 400));
      }

      if (value !== undefined &amp;&amp; value !== null &amp;&amp; value !== '' &amp;&amp;</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\rbac.middleware.ts (Line 80:2 - Line 91:4), src\middlewares\rbac.middleware.ts (Line 43:7 - Line 54:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn183" onclick="toggleCodeBlock('cloneGroup183', 'expandBtn183', 'collapseBtn183')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn183" onclick="toggleCodeBlock('cloneGroup183', 'expandBtn183', 'collapseBtn183')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup183"><code class="language-typescript text-sm text-gray-800">) =&gt; {
    return async (req: Request, res: Response, next: NextFunction) =&gt; {
        try {
            if (!req.user) {
                return next(new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
            }

            for</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\rbac.middleware.ts (Line 118:2 - Line 129:5), src\middlewares\rbac.middleware.ts (Line 43:7 - Line 54:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn184" onclick="toggleCodeBlock('cloneGroup184', 'expandBtn184', 'collapseBtn184')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn184" onclick="toggleCodeBlock('cloneGroup184', 'expandBtn184', 'collapseBtn184')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup184"><code class="language-typescript text-sm text-gray-800">) =&gt; {
    return async (req: Request, res: Response, next: NextFunction) =&gt; {
        try {
            if (!req.user) {
                return next(new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
            }

            const user</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\rbac.middleware.ts (Line 147:31 - Line 167:4), src\middlewares\rbac.middleware.ts (Line 59:38 - Line 79:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn185" onclick="toggleCodeBlock('cloneGroup185', 'expandBtn185', 'collapseBtn185')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn185" onclick="toggleCodeBlock('cloneGroup185', 'expandBtn185', 'collapseBtn185')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup185"><code class="language-typescript text-sm text-gray-800">,
            type: ErrorType.AUTHORIZATION,
            code: ErrorCode.FORBIDDEN
        }));
            }

            next();
        } catch (error) {
            logger.error(&quot;Error in RBAC middleware:&quot;, error);
            next(new AppError({
            message: &quot;Internal server error&quot;,
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        }));
        }
    };
};

/**
 * Middleware to attach user permissions to request
 */</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\rate-limit.middleware.ts (Line 115:45 - Line 121:47), src\middlewares\rate-limit.middleware.ts (Line 83:45 - Line 89:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn186" onclick="toggleCodeBlock('cloneGroup186', 'expandBtn186', 'collapseBtn186')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn186" onclick="toggleCodeBlock('cloneGroup186', 'expandBtn186', 'collapseBtn186')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup186"><code class="language-typescript text-sm text-gray-800">${req.ip}`, {
      ip: req.ip,
      path: req.path,
      method: req.method,
      requestId: req.requestId,
      userAgent: req.headers['user-agent'],
      email: req.body?.email, // Log the email being used for password reset</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\enhanced-auth.middleware.ts (Line 16:19 - Line 25:60), src\middlewares\enhanced-auth.middleware.ts (Line 9:15 - Line 17:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn187" onclick="toggleCodeBlock('cloneGroup187', 'expandBtn187', 'collapseBtn187')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn187" onclick="toggleCodeBlock('cloneGroup187', 'expandBtn187', 'collapseBtn187')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup187"><code class="language-typescript text-sm text-gray-800">;
import { PrismaClient } from &quot;@prisma/client&quot;;
import { logger } from &quot;../utils/logger&quot;;
import { AppError } from &quot;../utils/appError&quot;;
import { RBACService } from &quot;../services/rbac.service&quot;;
import { AuditService } from &quot;../services/audit.service&quot;;
import { verifyToken } from &quot;../utils/jwt.utils&quot;;
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\enhanced-auth.middleware.ts (Line 22:21 - Line 40:6), src\middlewares\rbac.middleware.ts (Line 19:11 - Line 42:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn188" onclick="toggleCodeBlock('cloneGroup188', 'expandBtn188', 'collapseBtn188')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn188" onclick="toggleCodeBlock('cloneGroup188', 'expandBtn188', 'collapseBtn188')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup188"><code class="language-typescript text-sm text-gray-800">;
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

const prisma: any = new PrismaClient();
const rbacService: any = new RBACService(prisma);
const</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\authorization.middleware.ts (Line 95:2 - Line 104:6), src\middlewares\authorization.middleware.ts (Line 25:2 - Line 33:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn189" onclick="toggleCodeBlock('cloneGroup189', 'expandBtn189', 'collapseBtn189')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn189" onclick="toggleCodeBlock('cloneGroup189', 'expandBtn189', 'collapseBtn189')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup189"><code class="language-typescript text-sm text-gray-800">(req: Request, res: Response, next: NextFunction) =&gt; {
    if (!req.user) {
      return next(new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        }));
    }

    const</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\auth.ts (Line 114:18 - Line 119:11), src\middlewares\auth.ts (Line 98:8 - Line 103:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn190" onclick="toggleCodeBlock('cloneGroup190', 'expandBtn190', 'collapseBtn190')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn190" onclick="toggleCodeBlock('cloneGroup190', 'expandBtn190', 'collapseBtn190')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup190"><code class="language-typescript text-sm text-gray-800">: any =(req: Request, res: Response, next: NextFunction) =&gt; {
    if (!req.user) {
        return res.status(401).json({ message: &quot;Authentication required. Please log in.&quot; });
    }

    if (req.user.role !== &quot;MERCHANT&quot;</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\auth.middleware.ts (Line 115:47 - Line 124:28), src\middlewares\auth.middleware.ts (Line 91:44 - Line 100:25)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn191" onclick="toggleCodeBlock('cloneGroup191', 'expandBtn191', 'collapseBtn191')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn191" onclick="toggleCodeBlock('cloneGroup191', 'expandBtn191', 'collapseBtn191')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup191"><code class="language-typescript text-sm text-gray-800">${req.user.id}`, {
      // Fixed: using id instead of userId
      userId: req.user.id, // Fixed: using id instead of userId
      role: req.user.role,
      requestId: req.requestId,
      path: req.path,
      method: req.method,
    });

    return next(new AppError('Merchant access required.'</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\auth.middleware.ts (Line 133:18 - Line 139:3), src\middlewares\auth.middleware.ts (Line 109:11 - Line 114:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn192" onclick="toggleCodeBlock('cloneGroup192', 'expandBtn192', 'collapseBtn192')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn192" onclick="toggleCodeBlock('cloneGroup192', 'expandBtn192', 'collapseBtn192')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup192"><code class="language-typescript text-sm text-gray-800">: any = (req: Request, res: Response, next: NextFunction) =&gt; {
  if (!req.user) {
    return next(new AppError('Authentication required. Please log in.', 401, true));
  }

  if (
    req.user.role !== 'MERCHANT' &amp;&amp;</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\auth.middleware.ts (Line 143:53 - Line 152:37), src\middlewares\auth.middleware.ts (Line 91:44 - Line 100:25)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn193" onclick="toggleCodeBlock('cloneGroup193', 'expandBtn193', 'collapseBtn193')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn193" onclick="toggleCodeBlock('cloneGroup193', 'expandBtn193', 'collapseBtn193')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup193"><code class="language-typescript text-sm text-gray-800">${req.user.id}`, {
      // Fixed: using id instead of userId
      userId: req.user.id, // Fixed: using id instead of userId
      role: req.user.role,
      requestId: req.requestId,
      path: req.path,
      method: req.method,
    });

    return next(new AppError('Merchant or admin access required.'</code></pre></div><div class="py-4"><p class="text-gray-600">src\middlewares\audit.middleware.ts (Line 15:16 - Line 33:13), src\middlewares\rbac.middleware.ts (Line 19:11 - Line 38:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn194" onclick="toggleCodeBlock('cloneGroup194', 'expandBtn194', 'collapseBtn194')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn194" onclick="toggleCodeBlock('cloneGroup194', 'expandBtn194', 'collapseBtn194')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup194"><code class="language-typescript text-sm text-gray-800">;
import { Middleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}


const prisma: any = new PrismaClient();
const auditService</code></pre></div><div class="py-4"><p class="text-gray-600">src\lib\prisma.ts (Line 92:13 - Line 109:7), src\lib\prisma.ts (Line 38:2 - Line 55:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn195" onclick="toggleCodeBlock('cloneGroup195', 'expandBtn195', 'collapseBtn195')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn195" onclick="toggleCodeBlock('cloneGroup195', 'expandBtn195', 'collapseBtn195')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup195"><code class="language-typescript text-sm text-gray-800">;

    // Configure logging based on environment
    const prismaOptions: Prisma.PrismaClientOptions = {
      datasources: {
        db: {
          url: databaseUrl,
        },
      },
      log: isProd ? [] : [
        { level: 'query', emit: 'event' } as Prisma.LogDefinition,
        { level: 'info', emit: 'event' } as Prisma.LogDefinition,
        { level: 'warn', emit: 'event' } as Prisma.LogDefinition,
        { level: 'error', emit: 'event' } as Prisma.LogDefinition
      ]
    };

    global</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\telegram-webhook.controller.ts (Line 120:2 - Line 134:18), src\controllers\telegram-webhook.controller.ts (Line 62:2 - Line 76:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn196" onclick="toggleCodeBlock('cloneGroup196', 'expandBtn196', 'collapseBtn196')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn196" onclick="toggleCodeBlock('cloneGroup196', 'expandBtn196', 'collapseBtn196')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup196"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: Request, res: Response) =&gt; {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== &quot;ADMIN&quot;) {
            throw new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Delete webhook</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\telegram-webhook.controller.ts (Line 166:2 - Line 180:20), src\controllers\telegram-webhook.controller.ts (Line 62:2 - Line 76:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn197" onclick="toggleCodeBlock('cloneGroup197', 'expandBtn197', 'collapseBtn197')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn197" onclick="toggleCodeBlock('cloneGroup197', 'expandBtn197', 'collapseBtn197')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup197"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: Request, res: Response) =&gt; {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== &quot;ADMIN&quot;) {
            throw new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get webhook info</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\telegram-webhook.controller.ts (Line 212:2 - Line 226:16), src\controllers\telegram-webhook.controller.ts (Line 62:2 - Line 76:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn198" onclick="toggleCodeBlock('cloneGroup198', 'expandBtn198', 'collapseBtn198')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn198" onclick="toggleCodeBlock('cloneGroup198', 'expandBtn198', 'collapseBtn198')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup198"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: Request, res: Response) =&gt; {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== &quot;ADMIN&quot;) {
            throw new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get bot info</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\subscription-history.controller.ts (Line 81:17 - Line 95:18), src\controllers\subscription-history.controller.ts (Line 39:17 - Line 53:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn199" onclick="toggleCodeBlock('cloneGroup199', 'expandBtn199', 'collapseBtn199')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn199" onclick="toggleCodeBlock('cloneGroup199', 'expandBtn199', 'collapseBtn199')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup199"><code class="language-typescript text-sm text-gray-800">});
            }

            // Parse dates if provided
            const parsedStartDate: any =startDate ? new Date(startDate as string) : undefined;
            const parsedEndDate: any =endDate ? new Date(endDate as string) : undefined;

            // Get subscription history
            const history: any =await subscriptionService.getSubscriptionHistory(
                merchantId,
                parsedStartDate,
                parsedEndDate
            );

            // Convert to CSV</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\sms.controller.ts (Line 37:2 - Line 51:30), src\controllers\telegram-webhook.controller.ts (Line 62:2 - Line 76:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn200" onclick="toggleCodeBlock('cloneGroup200', 'expandBtn200', 'collapseBtn200')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn200" onclick="toggleCodeBlock('cloneGroup200', 'expandBtn200', 'collapseBtn200')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup200"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: Request, res: Response) =&gt; {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== &quot;ADMIN&quot;) {
            throw new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get phone number from body</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\sms.controller.ts (Line 96:2 - Line 110:26), src\controllers\telegram-webhook.controller.ts (Line 62:2 - Line 76:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn201" onclick="toggleCodeBlock('cloneGroup201', 'expandBtn201', 'collapseBtn201')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn201" onclick="toggleCodeBlock('cloneGroup201', 'expandBtn201', 'collapseBtn201')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup201"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: Request, res: Response) =&gt; {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== &quot;ADMIN&quot;) {
            throw new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Get SMS data from body</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\sms.controller.ts (Line 155:2 - Line 169:22), src\controllers\telegram-webhook.controller.ts (Line 62:2 - Line 76:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn202" onclick="toggleCodeBlock('cloneGroup202', 'expandBtn202', 'collapseBtn202')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn202" onclick="toggleCodeBlock('cloneGroup202', 'expandBtn202', 'collapseBtn202')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup202"><code class="language-typescript text-sm text-gray-800">= asyncHandler(async (req: Request, res: Response) =&gt; {
    try {
        // Get user role
        const userRole: any =req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== &quot;ADMIN&quot;) {
            throw new AppError({
            message: &quot;Unauthorized&quot;,
            type: ErrorType.AUTHENTICATION,
            code: ErrorCode.INVALID_CREDENTIALS
        });
        }

        // Create SMS service</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\payment.controller.ts (Line 60:11 - Line 69:25), src\controllers\payment.controller.ts (Line 12:2 - Line 21:30)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn203" onclick="toggleCodeBlock('cloneGroup203', 'expandBtn203', 'collapseBtn203')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn203" onclick="toggleCodeBlock('cloneGroup203', 'expandBtn203', 'collapseBtn203')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup203"><code class="language-typescript text-sm text-gray-800">);

            return res.status(200).json({
                status: &quot;success&quot;,
                data: payments
            });
        } catch (error) {
            return res.status(500).json({
                status: &quot;error&quot;,
                message: error instanceof Error ? (error as Error).message : &quot;Failed to get payments&quot;</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\payment-verification.controller.ts (Line 155:19 - Line 165:12), src\controllers\payment-verification.controller.ts (Line 113:29 - Line 123:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn204" onclick="toggleCodeBlock('cloneGroup204', 'expandBtn204', 'collapseBtn204')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn204" onclick="toggleCodeBlock('cloneGroup204', 'expandBtn204', 'collapseBtn204')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup204"><code class="language-typescript text-sm text-gray-800">] : [],
                    amount: !amount ? [&quot;Amount is required&quot;] : [],
                    currency: !currency ? [&quot;Currency is required&quot;] : [],
                    merchantApiKey: !merchantApiKey ? [&quot;Merchant API key is required&quot;] : [],
                    merchantSecretKey: !merchantSecretKey ? [&quot;Merchant secret key is required&quot;] : []
                });
            }

            // Verify payment
            const result: any =await this.paymentVerificationService.verifyPayment(
                PaymentMethod.BINANCE_C2C</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\payment-verification.controller.ts (Line 238:17 - Line 244:2), src\controllers\payment-verification.controller.ts (Line 196:18 - Line 201:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn205" onclick="toggleCodeBlock('cloneGroup205', 'expandBtn205', 'collapseBtn205')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn205" onclick="toggleCodeBlock('cloneGroup205', 'expandBtn205', 'collapseBtn205')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup205"><code class="language-typescript text-sm text-gray-800">) {
                return this.sendValidationError(res, {
                    txHash: !txHash ? [&quot;Transaction hash is required&quot;] : [],
                    amount: !amount ? [&quot;Amount is required&quot;] : [],
                    currency: !currency ? [&quot;Currency is required&quot;] : [],
                    recipientAddress: !recipientAddress ? [&quot;Recipient address is required&quot;] : []
                }</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\payment-recommendation.controller.ts (Line 98:13 - Line 112:6), src\controllers\payment-recommendation.controller.ts (Line 53:13 - Line 67:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn206" onclick="toggleCodeBlock('cloneGroup206', 'expandBtn206', 'collapseBtn206')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn206" onclick="toggleCodeBlock('cloneGroup206', 'expandBtn206', 'collapseBtn206')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup206"><code class="language-typescript text-sm text-gray-800">);

            // Send success response
            this.sendSuccess(res, recommendations, &quot;Payment method recommendations retrieved&quot;);
        } catch (error) {
            this.sendError(res, error instanceof ServiceError ? error : new ServiceError(
                (error as Error).message || &quot;Failed to get payment method recommendations&quot;,
                500
            ));
        }
    });

    /**
   * Update merchant recommendation weights
   */</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\multi-factor-auth.controller.ts (Line 94:11 - Line 106:15), src\controllers\multi-factor-auth.controller.ts (Line 31:27 - Line 43:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn207" onclick="toggleCodeBlock('cloneGroup207', 'expandBtn207', 'collapseBtn207')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn207" onclick="toggleCodeBlock('cloneGroup207', 'expandBtn207', 'collapseBtn207')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup207"><code class="language-typescript text-sm text-gray-800">: any =asyncHandler(async (req: Request, res: Response) =&gt; {
    // Get user ID from authenticated user
    const userId: any =req.user?.id // Fixed: using id instead of userId;

    if (!userId) {
        throw new AppError({
            message: &quot;User ID is required&quot;,
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }

    // Disable MFA</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\merchant-subscription.controller.ts (Line 62:13 - Line 69:19), src\controllers\merchant-subscription.controller.ts (Line 30:13 - Line 37:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn208" onclick="toggleCodeBlock('cloneGroup208', 'expandBtn208', 'collapseBtn208')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn208" onclick="toggleCodeBlock('cloneGroup208', 'expandBtn208', 'collapseBtn208')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup208"><code class="language-typescript text-sm text-gray-800">if (req.user?.role !== &quot;admin&quot; &amp;&amp; req.user?.merchantId !== merchantId) {
                return res.status(403).json({
                    status: &quot;error&quot;,
                    message: &quot;You are not authorized to manage this merchant subscription&quot;
                });
            }

            const result: any =await subscriptionService.cancelSubscription</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fee-management.controller.ts (Line 194:2 - Line 204:6), src\controllers\fee-management.controller.ts (Line 128:2 - Line 138:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn209" onclick="toggleCodeBlock('cloneGroup209', 'expandBtn209', 'collapseBtn209')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn209" onclick="toggleCodeBlock('cloneGroup209', 'expandBtn209', 'collapseBtn209')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup209"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { tierId } = req.params;

      // Validate required parameters
      if (!tierId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      await</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\fee-management.controller.ts (Line 321:2 - Line 331:7), src\controllers\payment-routing.controller.ts (Line 142:2 - Line 152:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn210" onclick="toggleCodeBlock('cloneGroup210', 'expandBtn210', 'collapseBtn210')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn210" onclick="toggleCodeBlock('cloneGroup210', 'expandBtn210', 'collapseBtn210')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup210"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { merchantId } = req.params;

      // Validate required parameters
      if (!merchantId) {
        ApiResponse.error(res, 'Missing required parameters', 400);
        return;
      }

      const result: any = await prisma</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\enhanced-risk-engine.controller.ts (Line 210:2 - Line 216:24), src\controllers\monitoring\verification-monitoring.controller.ts (Line 31:2 - Line 37:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn211" onclick="toggleCodeBlock('cloneGroup211', 'expandBtn211', 'collapseBtn211')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn211" onclick="toggleCodeBlock('cloneGroup211', 'expandBtn211', 'collapseBtn211')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup211"><code class="language-typescript text-sm text-gray-800">} = req.query;

            // Parse dates
            const parsedStartDate: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const parsedEndDate: any = endDate ? new Date(endDate as string) : new Date();

            // Get risk assessments</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard.controller.ts (Line 96:2 - Line 105:2), src\controllers\dashboard.controller.ts (Line 11:2 - Line 20:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn212" onclick="toggleCodeBlock('cloneGroup212', 'expandBtn212', 'collapseBtn212')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn212" onclick="toggleCodeBlock('cloneGroup212', 'expandBtn212', 'collapseBtn212')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup212"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      const {</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard.controller.ts (Line 133:2 - Line 143:49), src\controllers\dashboard.controller.ts (Line 48:2 - Line 58:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn213" onclick="toggleCodeBlock('cloneGroup213', 'expandBtn213', 'collapseBtn213')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn213" onclick="toggleCodeBlock('cloneGroup213', 'expandBtn213', 'collapseBtn213')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup213"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if dashboard exists and belongs to user</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard.controller.ts (Line 186:2 - Line 211:28), src\controllers\dashboard.controller.ts (Line 48:2 - Line 158:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn214" onclick="toggleCodeBlock('cloneGroup214', 'expandBtn214', 'collapseBtn214')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn214" onclick="toggleCodeBlock('cloneGroup214', 'expandBtn214', 'collapseBtn214')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup214"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if dashboard exists and belongs to user
      const existingDashboard = await prisma.dashboard.findUnique({
        where: { id }
      });
      
      if (!existingDashboard) {
        res.status(404).json({ success: false, message: 'Dashboard not found' });
        return;
      }
      
      if (existingDashboard.createdById !== userId) {
        res.status(403).json({ success: false, message: 'Access denied' });
        return;
      }
      
      // Delete all widgets first</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard-widget.controller.ts (Line 56:2 - Line 66:7), src\controllers\dashboard.controller.ts (Line 48:2 - Line 58:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn215" onclick="toggleCodeBlock('cloneGroup215', 'expandBtn215', 'collapseBtn215')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn215" onclick="toggleCodeBlock('cloneGroup215', 'expandBtn215', 'collapseBtn215')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup215"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      const widget</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard-widget.controller.ts (Line 98:2 - Line 108:49), src\controllers\dashboard-widget.controller.ts (Line 10:2 - Line 20:49)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn216" onclick="toggleCodeBlock('cloneGroup216', 'expandBtn216', 'collapseBtn216')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn216" onclick="toggleCodeBlock('cloneGroup216', 'expandBtn216', 'collapseBtn216')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup216"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { dashboardId } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if dashboard exists and belongs to user</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard-widget.controller.ts (Line 109:7 - Line 118:10), src\controllers\dashboard-widget.controller.ts (Line 21:7 - Line 30:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn217" onclick="toggleCodeBlock('cloneGroup217', 'expandBtn217', 'collapseBtn217')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn217" onclick="toggleCodeBlock('cloneGroup217', 'expandBtn217', 'collapseBtn217')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup217"><code class="language-typescript text-sm text-gray-800">const dashboard = await prisma.dashboard.findUnique({
        where: { id: dashboardId }
      });
      
      if (!dashboard) {
        res.status(404).json({ success: false, message: 'Dashboard not found' });
        return;
      }
      
      if (dashboard</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard-widget.controller.ts (Line 162:2 - Line 172:46), src\controllers\dashboard.controller.ts (Line 48:2 - Line 58:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn218" onclick="toggleCodeBlock('cloneGroup218', 'expandBtn218', 'collapseBtn218')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn218" onclick="toggleCodeBlock('cloneGroup218', 'expandBtn218', 'collapseBtn218')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup218"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if widget exists and user has access</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard-widget.controller.ts (Line 173:7 - Line 183:3), src\controllers\dashboard-widget.controller.ts (Line 66:7 - Line 76:45)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn219" onclick="toggleCodeBlock('cloneGroup219', 'expandBtn219', 'collapseBtn219')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn219" onclick="toggleCodeBlock('cloneGroup219', 'expandBtn219', 'collapseBtn219')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup219"><code class="language-typescript text-sm text-gray-800">const widget = await prisma.dashboardWidget.findUnique({
        where: { id },
        include: { dashboard: true }
      });
      
      if (!widget) {
        res.status(404).json({ success: false, message: 'Widget not found' });
        return;
      }
      
      if</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard-widget.controller.ts (Line 218:2 - Line 244:6), src\controllers\dashboard.controller.ts (Line 48:2 - Line 188:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn220" onclick="toggleCodeBlock('cloneGroup220', 'expandBtn220', 'collapseBtn220')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn220" onclick="toggleCodeBlock('cloneGroup220', 'expandBtn220', 'collapseBtn220')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup220"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if widget exists and user has access
      const widget = await prisma.dashboardWidget.findUnique({
        where: { id },
        include: { dashboard: true }
      });
      
      if (!widget) {
        res.status(404).json({ success: false, message: 'Widget not found' });
        return;
      }
      
      if (widget.dashboard.createdById !== userId) {
        res.status(403).json({ success: false, message: 'Access denied' });
        return;
      }
      
      await</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\dashboard-widget.controller.ts (Line 264:2 - Line 289:8), src\controllers\dashboard-widget.controller.ts (Line 10:2 - Line 123:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn221" onclick="toggleCodeBlock('cloneGroup221', 'expandBtn221', 'collapseBtn221')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn221" onclick="toggleCodeBlock('cloneGroup221', 'expandBtn221', 'collapseBtn221')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup221"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { dashboardId } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }
      
      // Check if dashboard exists and belongs to user
      const dashboard = await prisma.dashboard.findUnique({
        where: { id: dashboardId }
      });
      
      if (!dashboard) {
        res.status(404).json({ success: false, message: 'Dashboard not found' });
        return;
      }
      
      if (dashboard.createdById !== userId) {
        res.status(403).json({ success: false, message: 'Access denied' });
        return;
      }
      
      const { widgets</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\blockchain-verification.controller.ts (Line 11:11 - Line 19:2), src\controllers\blockchain-verification.controller.ts (Line 2:10 - Line 10:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn222" onclick="toggleCodeBlock('cloneGroup222', 'expandBtn222', 'collapseBtn222')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn222" onclick="toggleCodeBlock('cloneGroup222', 'expandBtn222', 'collapseBtn222')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup222"><code class="language-typescript text-sm text-gray-800">;
import { BaseController } from './base/BaseController';
import { logger } from &quot;../utils/logger&quot;;
import { BlockchainNetwork } from &quot;../types/blockchain&quot;;
import { VerificationStatus } from &quot;../types/verification&quot;;
import { BlockchainApiService } from &quot;../services/blockchain/blockchain-api.service&quot;;
import { BinanceApiService } from &quot;../services/blockchain/binance-api.service&quot;;
import { verificationEvents } from &quot;../services/websocket/verification-websocket.service&quot;;
import {</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\blockchain-verification.controller.ts (Line 203:17 - Line 235:6), src\controllers\blockchain-verification.controller.ts (Line 61:17 - Line 93:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn223" onclick="toggleCodeBlock('cloneGroup223', 'expandBtn223', 'collapseBtn223')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn223" onclick="toggleCodeBlock('cloneGroup223', 'expandBtn223', 'collapseBtn223')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup223"><code class="language-typescript text-sm text-gray-800">});
            }

            // Get merchant
            const merchant: any = await prisma.merchant.findUnique({
                where: { id: merchantId }
            });

            if (!merchant) {
                return res.status(404).json({
                    success: false,
                    message: &quot;Merchant not found&quot;
                });
            }

            // Get transaction
            const transaction: any = await prisma.transaction.findUnique({
                where: { id: paymentId }
            });

            if (!transaction) {
                return res.status(404).json({
                    success: false,
                    message: &quot;Transaction not found&quot;
                });
            }

            // Get payment method
            const paymentMethod: any = paymentMethodId ? await prisma.paymentMethod.findUnique({
                where: { id: paymentMethodId }
            }) : null;

            const</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\blockchain-verification.controller.ts (Line 281:10 - Line 290:27), src\controllers\blockchain-verification.controller.ts (Line 263:10 - Line 272:30)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn224" onclick="toggleCodeBlock('cloneGroup224', 'expandBtn224', 'collapseBtn224')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn224" onclick="toggleCodeBlock('cloneGroup224', 'expandBtn224', 'collapseBtn224')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup224"><code class="language-typescript text-sm text-gray-800">: verificationResult.toAddress || toAddress || paymentMethod?.address,
                        amount: verificationResult.amount || parseFloat(amount.toString()),
                        currency,
                        timestamp: verificationResult.timestamp || Date.now(),
                        status: verificationResult.success ? &quot;success&quot; : &quot;failed&quot;
                    })
                }
            });

            // Emit verification event</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\blockchain-verification.controller.ts (Line 295:16 - Line 308:37), src\controllers\blockchain-verification.controller.ts (Line 158:2 - Line 171:40)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn225" onclick="toggleCodeBlock('cloneGroup225', 'expandBtn225', 'collapseBtn225')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn225" onclick="toggleCodeBlock('cloneGroup225', 'expandBtn225', 'collapseBtn225')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup225"><code class="language-typescript text-sm text-gray-800">,
                timestamp: new Date()
            });

            return res.status(200).json({
                success: true,
                message: verificationResult.success ? &quot;Transaction verified successfully&quot; : &quot;Transaction verification failed&quot;,
                data: { transaction: updatedTransaction,
                    verification,
                    verificationResult
                }
            });
        } catch (error) {
            logger.error(`Error in verifyBinanceTransaction: </code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\blockchain-verification.controller.ts (Line 308:37 - Line 316:2), src\controllers\blockchain-verification.controller.ts (Line 171:40 - Line 182:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn226" onclick="toggleCodeBlock('cloneGroup226', 'expandBtn226', 'collapseBtn226')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn226" onclick="toggleCodeBlock('cloneGroup226', 'expandBtn226', 'collapseBtn226')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup226"><code class="language-typescript text-sm text-gray-800">${error instanceof Error ? (error as Error).message : 'Unknown error'}`);
            return res.status(500).json({
                success: false,
                message: &quot;Internal server error&quot;,
                error: error instanceof Error ? (error as Error).message : &quot;Unknown error&quot;
            });
        }
    }
}</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\binance.controller.ts (Line 38:15 - Line 50:27), src\controllers\binance.controller.ts (Line 9:15 - Line 21:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn227" onclick="toggleCodeBlock('cloneGroup227', 'expandBtn227', 'collapseBtn227')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn227" onclick="toggleCodeBlock('cloneGroup227', 'expandBtn227', 'collapseBtn227')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup227"><code class="language-typescript text-sm text-gray-800">: any =asyncHandler(async (req: Request, res: Response) =&gt; {
    const { apiKey, apiSecret } = req.body;
  
    // Validate required fields
    if (!apiKey || !apiSecret) {
        throw new AppError({
            message: &quot;API key and secret are required&quot;,
            type: ErrorType.VALIDATION,
            code: ErrorCode.MISSING_REQUIRED_FIELD
        });
    }
  
    // Get account information</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\analytics.controller.ts (Line 75:13 - Line 96:26), src\controllers\analytics.controller.ts (Line 35:13 - Line 56:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn228" onclick="toggleCodeBlock('cloneGroup228', 'expandBtn228', 'collapseBtn228')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn228" onclick="toggleCodeBlock('cloneGroup228', 'expandBtn228', 'collapseBtn228')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup228"><code class="language-typescript text-sm text-gray-800">// Parse filter parameters
            const filter: AnalyticsFilter = {};

            if (req.query.merchantId) {
                filter.merchantId = req.query.merchantId as string;
            }

            if (req.query.startDate) {
                filter.startDate = new Date(req.query.startDate as string);
            }

            if (req.query.endDate) {
                filter.endDate = new Date(req.query.endDate as string);
            }

            if (req.query.period) {
                filter.period = req.query.period as AnalyticsPeriod;
            } else {
                filter.period = AnalyticsPeriod.MONTH; // Default to month
            }

            const analytics: any =await paymentAnalyticsService.getPaymentMethodAnalytics</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\analytics.controller.ts (Line 124:13 - Line 138:21), src\controllers\analytics.controller.ts (Line 42:13 - Line 56:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn229" onclick="toggleCodeBlock('cloneGroup229', 'expandBtn229', 'collapseBtn229')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn229" onclick="toggleCodeBlock('cloneGroup229', 'expandBtn229', 'collapseBtn229')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup229"><code class="language-typescript text-sm text-gray-800">if (req.query.startDate) {
                filter.startDate = new Date(req.query.startDate as string);
            }

            if (req.query.endDate) {
                filter.endDate = new Date(req.query.endDate as string);
            }

            if (req.query.period) {
                filter.period = req.query.period as AnalyticsPeriod;
            } else {
                filter.period = AnalyticsPeriod.MONTH; // Default to month
            }

            const analytics: any =await paymentAnalyticsService.getMerchantAnalytics</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\analytics.controller.ts (Line 155:2 - Line 171:36), src\controllers\analytics.controller.ts (Line 122:13 - Line 56:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn230" onclick="toggleCodeBlock('cloneGroup230', 'expandBtn230', 'collapseBtn230')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn230" onclick="toggleCodeBlock('cloneGroup230', 'expandBtn230', 'collapseBtn230')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup230"><code class="language-typescript text-sm text-gray-800">};

            if (req.query.startDate) {
                filter.startDate = new Date(req.query.startDate as string);
            }

            if (req.query.endDate) {
                filter.endDate = new Date(req.query.endDate as string);
            }

            if (req.query.period) {
                filter.period = req.query.period as AnalyticsPeriod;
            } else {
                filter.period = AnalyticsPeriod.MONTH; // Default to month
            }

            // Get analytics based on user role</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\advanced-report.controller.ts (Line 51:2 - Line 60:14), src\controllers\dashboard.controller.ts (Line 11:2 - Line 20:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn231" onclick="toggleCodeBlock('cloneGroup231', 'expandBtn231', 'collapseBtn231')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn231" onclick="toggleCodeBlock('cloneGroup231', 'expandBtn231', 'collapseBtn231')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup231"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const includeSystem</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\advanced-report.controller.ts (Line 107:2 - Line 116:5), src\controllers\dashboard.controller.ts (Line 11:2 - Line 20:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn232" onclick="toggleCodeBlock('cloneGroup232', 'expandBtn232', 'collapseBtn232')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn232" onclick="toggleCodeBlock('cloneGroup232', 'expandBtn232', 'collapseBtn232')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup232"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const data</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\advanced-report.controller.ts (Line 183:2 - Line 192:8), src\controllers\dashboard.controller.ts (Line 11:2 - Line 20:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn233" onclick="toggleCodeBlock('cloneGroup233', 'expandBtn233', 'collapseBtn233')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn233" onclick="toggleCodeBlock('cloneGroup233', 'expandBtn233', 'collapseBtn233')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup233"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const reports</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\advanced-report.controller.ts (Line 237:2 - Line 251:7), src\controllers\dashboard.controller.ts (Line 11:2 - Line 121:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn234" onclick="toggleCodeBlock('cloneGroup234', 'expandBtn234', 'collapseBtn234')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn234" onclick="toggleCodeBlock('cloneGroup234', 'expandBtn234', 'collapseBtn234')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup234"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const data = {
        ...req.body,
        createdById: userId,
      };

      const report</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\advanced-report.controller.ts (Line 335:2 - Line 344:16), src\controllers\dashboard.controller.ts (Line 11:2 - Line 192:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn235" onclick="toggleCodeBlock('cloneGroup235', 'expandBtn235', 'collapseBtn235')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn235" onclick="toggleCodeBlock('cloneGroup235', 'expandBtn235', 'collapseBtn235')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup235"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      const reports = await this.reportService.getSavedReports</code></pre></div><div class="py-4"><p class="text-gray-600">src\controllers\advanced-report.controller.ts (Line 411:2 - Line 421:24), src\controllers\dashboard.controller.ts (Line 48:2 - Line 58:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn236" onclick="toggleCodeBlock('cloneGroup236', 'expandBtn236', 'collapseBtn236')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn236" onclick="toggleCodeBlock('cloneGroup236', 'expandBtn236', 'collapseBtn236')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup236"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response): Promise&lt;void&gt; =&gt; {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ success: false, message: 'Unauthorized' });
        return;
      }

      // Get the saved report</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="javascript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">javascript</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">scripts\test-duplication.js (Line 43:20 - Line 53:22), scripts\test-new-code-duplication.js (Line 72:12 - Line 82:35)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn237" onclick="toggleCodeBlock('cloneGroup237', 'expandBtn237', 'collapseBtn237')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn237" onclick="toggleCodeBlock('cloneGroup237', 'expandBtn237', 'collapseBtn237')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup237"><code class="language-javascript text-sm text-gray-800">);
    if (fs.existsSync(reportPath)) {
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      
      console.log('\nDuplication Statistics:');
      console.log(`- Duplication Percentage: ${report.statistics.total.percentage}%`);
      console.log(`- Duplicated Lines: ${report.statistics.total.duplicatedLines} of ${report.statistics.total.lines}`);
      console.log(`- Clones Found: ${report.duplicates.length}`);
      
      if (report.duplicates.length &gt; 0) {
        console.log('\nTop Duplications:'</code></pre></div><div class="py-4"><p class="text-gray-600">scripts\test-duplication.js (Line 56:2 - Line 61:2), scripts\test-new-code-duplication.js (Line 84:11 - Line 90:41)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn238" onclick="toggleCodeBlock('cloneGroup238', 'expandBtn238', 'collapseBtn238')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn238" onclick="toggleCodeBlock('cloneGroup238', 'expandBtn238', 'collapseBtn238')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup238"><code class="language-javascript text-sm text-gray-800">.forEach((dup, index) =&gt; {
          console.log(`\n${index + 1}. Between ${dup.firstFile.name} and ${dup.secondFile.name}`);
          console.log(`   First file: Lines ${dup.firstFile.start}-${dup.firstFile.end}`);
          console.log(`   Second file: Lines ${dup.secondFile.start}-${dup.secondFile.end}`);
          console.log(`   Size: ${dup.lines} lines, ${dup.tokens} tokens`);
        }</code></pre></div><div class="py-4"><p class="text-gray-600">scripts\run-comprehensive-tests.js (Line 234:7 - Line 240:12), scripts\run-comprehensive-tests.js (Line 187:7 - Line 193:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn239" onclick="toggleCodeBlock('cloneGroup239', 'expandBtn239', 'collapseBtn239')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn239" onclick="toggleCodeBlock('cloneGroup239', 'expandBtn239', 'collapseBtn239')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup239"><code class="language-javascript text-sm text-gray-800">const lines = result.split('\n');
      const summaryLine = lines.find((line) =&gt; line.includes('Tests:'));

      if (summaryLine) {
        const matches = summaryLine.match(/(\d+) passed.*?(\d+) total/);
        if (matches) {
          this.results.integration</code></pre></div><div class="py-4"><p class="text-gray-600">scripts\run-comprehensive-tests.js (Line 278:9 - Line 288:12), scripts\run-comprehensive-tests.js (Line 230:9 - Line 193:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn240" onclick="toggleCodeBlock('cloneGroup240', 'expandBtn240', 'collapseBtn240')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn240" onclick="toggleCodeBlock('cloneGroup240', 'expandBtn240', 'collapseBtn240')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup240"><code class="language-javascript text-sm text-gray-800">encoding: 'utf8',
      });

      // Parse results
      const lines = result.split('\n');
      const summaryLine = lines.find((line) =&gt; line.includes('Tests:'));

      if (summaryLine) {
        const matches = summaryLine.match(/(\d+) passed.*?(\d+) total/);
        if (matches) {
          this.results.performance</code></pre></div><div class="py-4"><p class="text-gray-600">scripts\fix-duplication.js (Line 44:2 - Line 51:6), scripts\fix-duplication.js (Line 32:2 - Line 40:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn241" onclick="toggleCodeBlock('cloneGroup241', 'expandBtn241', 'collapseBtn241')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn241" onclick="toggleCodeBlock('cloneGroup241', 'expandBtn241', 'collapseBtn241')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup241"><code class="language-javascript text-sm text-gray-800">{
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      duplications = report.duplicates || [];
      duplicationFound = duplications.length &gt; 0;
      
      // Clean up temporary report
      fs.rmSync('.jscpd-temp', { recursive: true, force: true });
    } catch</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div></section><!-- Add more sections for other formats and clone groups as needed--></main><footer class="bg-white shadow mt-8 py-4"><div class="container mx-auto px-4 text-center"><p class="text-sm text-gray-600">This report is generated by jscpd, an open-source copy/paste detector.</p><p class="text-sm text-gray-600">jscpd is licensed under the MIT License.</p><a class="text-blue-500 text-sm" href="https://github.com/kucherenko/jscpd" target="_blank" rel="noopener noreferrer">View jscpd on GitHub</a></div></footer><script src="js/prism.js"></script><script>function toggleCodeBlock(codeBlockId, expandBtnId, collapseBtnId) {
  const codeBlock = document.getElementById(codeBlockId);
  const expandBtn = document.getElementById(expandBtnId);
  const collapseBtn = document.getElementById(collapseBtnId);

  codeBlock.classList.toggle('hidden');
  expandBtn.classList.toggle('hidden');
  collapseBtn.classList.toggle('hidden');
}</script></body></html>