#!/bin/bash

# AmazingPay Flow - Deployment Manager
# Comprehensive deployment management with rollback capabilities

set -e

# Configuration
VPS_HOST="${VPS_HOST:-}"
VPS_USER="${VPS_USER:-root}"
VPS_PORT="${VPS_PORT:-22}"
WEBHOOK_SECRET="${WEBHOOK_SECRET:-}"
DOMAIN_NAME="${DOMAIN_NAME:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Load configuration
load_config() {
    if [ -f ".deployment-config" ]; then
        source .deployment-config
        log "Configuration loaded from .deployment-config"
    else
        warning "No configuration file found. Run 'setup' first."
    fi
}

# Save configuration
save_config() {
    cat > .deployment-config << EOF
VPS_HOST="$VPS_HOST"
VPS_USER="$VPS_USER"
VPS_PORT="$VPS_PORT"
WEBHOOK_SECRET="$WEBHOOK_SECRET"
DOMAIN_NAME="$DOMAIN_NAME"
EOF
    success "Configuration saved to .deployment-config"
}

# Setup deployment configuration
setup_deployment() {
    echo "🚀 AmazingPay Flow - Deployment Setup"
    echo "====================================="
    echo ""
    
    read -p "Enter your VPS IP address: " VPS_HOST
    read -p "Enter VPS username (default: root): " input_user
    VPS_USER="${input_user:-root}"
    read -p "Enter VPS SSH port (default: 22): " input_port
    VPS_PORT="${input_port:-22}"
    read -p "Enter your domain name: " DOMAIN_NAME
    read -p "Enter webhook secret (or press Enter to generate): " WEBHOOK_SECRET
    
    if [ -z "$WEBHOOK_SECRET" ]; then
        WEBHOOK_SECRET=$(openssl rand -hex 32)
        log "Generated webhook secret: $WEBHOOK_SECRET"
    fi
    
    save_config
    
    echo ""
    success "Setup completed!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Run: $0 install-webhook"
    echo "2. Configure GitHub webhook"
    echo "3. Run: $0 test-connection"
}

# Test VPS connection
test_connection() {
    load_config
    
    if [ -z "$VPS_HOST" ]; then
        error "VPS_HOST not configured. Run setup first."
        exit 1
    fi
    
    log "Testing connection to $VPS_USER@$VPS_HOST:$VPS_PORT..."
    
    if ssh -p "$VPS_PORT" -o ConnectTimeout=10 "$VPS_USER@$VPS_HOST" "echo 'Connection successful'"; then
        success "VPS connection successful"
        
        # Test application status
        log "Checking application status..."
        ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" "cd /www/wwwroot/amazingpay-flow && pm2 status amazingpay-flow" || warning "Application not running"
        
    else
        error "Failed to connect to VPS"
        exit 1
    fi
}

# Install webhook handler on VPS
install_webhook() {
    load_config
    
    if [ -z "$VPS_HOST" ]; then
        error "VPS_HOST not configured. Run setup first."
        exit 1
    fi
    
    log "Installing webhook handler on VPS..."
    
    # Copy webhook files to VPS
    scp -P "$VPS_PORT" scripts/setup-automated-deployment.sh "$VPS_USER@$VPS_HOST:/tmp/"
    
    # Run installation on VPS
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << EOF
cd /tmp
chmod +x setup-automated-deployment.sh

# Extract webhook handler from setup script
sed -n '/^cat > webhook-handler.js/,/^EOF$/p' setup-automated-deployment.sh | sed '1d;$d' > /www/wwwroot/amazingpay-flow/webhook-handler.js

# Extract systemd service
sed -n '/^cat > webhook-handler.service/,/^EOF$/p' setup-automated-deployment.sh | sed '1d;$d' > /etc/systemd/system/webhook-handler.service

# Extract restore script
sed -n '/^cat > restore-backup.sh/,/^EOF$/p' setup-automated-deployment.sh | sed '1d;$d' > /www/wwwroot/amazingpay-flow/restore-backup.sh

# Set permissions
chmod +x /www/wwwroot/amazingpay-flow/webhook-handler.js
chmod +x /www/wwwroot/amazingpay-flow/restore-backup.sh

# Configure systemd service
sed -i "s/WEBHOOK_SECRET=.*/WEBHOOK_SECRET=$WEBHOOK_SECRET/" /etc/systemd/system/webhook-handler.service

# Start webhook service
systemctl daemon-reload
systemctl enable webhook-handler
systemctl start webhook-handler

echo "✅ Webhook handler installed and started"
EOF
    
    success "Webhook handler installation completed"
    
    echo ""
    echo "🌐 GitHub Webhook Configuration:"
    echo "URL: https://$DOMAIN_NAME/webhook"
    echo "Content type: application/json"
    echo "Secret: $WEBHOOK_SECRET"
    echo "Events: Just the push event"
}

# Deploy to VPS
deploy() {
    load_config
    
    local branch="${1:-main}"
    
    log "Deploying branch '$branch' to VPS..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << EOF
cd /www/wwwroot/amazingpay-flow

# Create backup
echo "🔄 Creating backup..."
./backup-vps.sh "manual-deploy-\$(date +%Y%m%d-%H%M%S)"

# Pull latest changes
echo "📥 Pulling latest changes..."
git pull origin $branch

# Install dependencies
echo "📦 Installing dependencies..."
npm ci --production

# Run migrations
echo "🗄️ Running database migrations..."
npx prisma migrate deploy

# Build application
echo "🔨 Building application..."
npm run build

# Restart application
echo "🚀 Restarting application..."
pm2 restart amazingpay-flow

# Health check
echo "🏥 Performing health check..."
sleep 10
curl -f http://localhost:3002/api/health || exit 1

echo "✅ Deployment completed successfully"
EOF
    
    success "Deployment to VPS completed"
}

# List available backups
list_backups() {
    load_config
    
    log "Available backups on VPS:"
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" "ls -la /var/backups/amazingpay/ | grep -E '^d' | awk '{print \$9, \$6, \$7, \$8}' | grep -v '^\.$\|^\.\.$'"
}

# Rollback to specific backup
rollback() {
    load_config
    
    local backup_name="$1"
    
    if [ -z "$backup_name" ]; then
        error "Backup name required. Use: $0 list-backups to see available backups"
        exit 1
    fi
    
    log "Rolling back to backup: $backup_name"
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << EOF
cd /www/wwwroot/amazingpay-flow

echo "🔄 Rolling back to backup: $backup_name"
./restore-backup.sh "$backup_name"

echo "✅ Rollback completed"
EOF
    
    success "Rollback completed successfully"
}

# Get application status
status() {
    load_config
    
    log "Getting application status from VPS..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << 'EOF'
echo "📊 Application Status:"
echo "====================="

# PM2 status
echo ""
echo "🔧 PM2 Processes:"
pm2 status

# System resources
echo ""
echo "💻 System Resources:"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory Usage: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
echo "Disk Usage: $(df -h / | awk 'NR==2{printf "%s", $5}')"

# Application health
echo ""
echo "🏥 Application Health:"
if curl -s http://localhost:3002/api/health > /dev/null; then
    echo "✅ Application is healthy"
else
    echo "❌ Application health check failed"
fi

# Webhook handler status
echo ""
echo "🔗 Webhook Handler:"
if systemctl is-active --quiet webhook-handler; then
    echo "✅ Webhook handler is running"
else
    echo "❌ Webhook handler is not running"
fi

# Recent logs
echo ""
echo "📝 Recent Application Logs:"
pm2 logs amazingpay-flow --lines 5 --nostream
EOF
}

# Show logs
logs() {
    load_config
    
    local lines="${1:-50}"
    
    log "Showing last $lines lines of application logs..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" "pm2 logs amazingpay-flow --lines $lines --nostream"
}

# Manual trigger webhook
trigger_webhook() {
    load_config
    
    log "Manually triggering webhook deployment..."
    
    curl -X POST "https://$DOMAIN_NAME/webhook/deploy" \
        -H "Content-Type: application/json" \
        -d "{\"branch\":\"main\",\"secret\":\"$WEBHOOK_SECRET\"}"
    
    success "Webhook deployment triggered"
}

# Show help
show_help() {
    echo "AmazingPay Flow - Deployment Manager"
    echo "===================================="
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup              - Initial deployment setup"
    echo "  test-connection    - Test VPS connection"
    echo "  install-webhook    - Install webhook handler on VPS"
    echo "  deploy [branch]    - Deploy to VPS (default: main)"
    echo "  status             - Show application status"
    echo "  logs [lines]       - Show application logs (default: 50)"
    echo "  list-backups       - List available backups"
    echo "  rollback <backup>  - Rollback to specific backup"
    echo "  trigger-webhook    - Manually trigger webhook deployment"
    echo "  help               - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 deploy main"
    echo "  $0 rollback backup-20231225-120000"
    echo "  $0 logs 100"
}

# Main command handler
case "${1:-help}" in
    setup)
        setup_deployment
        ;;
    test-connection)
        test_connection
        ;;
    install-webhook)
        install_webhook
        ;;
    deploy)
        deploy "$2"
        ;;
    status)
        status
        ;;
    logs)
        logs "$2"
        ;;
    list-backups)
        list_backups
        ;;
    rollback)
        rollback "$2"
        ;;
    trigger-webhook)
        trigger_webhook
        ;;
    help|*)
        show_help
        ;;
esac
