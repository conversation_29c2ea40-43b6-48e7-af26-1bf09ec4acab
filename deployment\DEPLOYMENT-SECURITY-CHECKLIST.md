# 🔒 DEPLOYMENT SECURITY CHECKLIST
## Critical Financial Application - Production Deployment

### ✅ **PRE-DEPLOYMENT SECURITY VALIDATION**

This comprehensive checklist ensures your critical financial application is completely secure against unauthorized file access and meets enterprise security standards.

---

## 🎯 **CRITICAL SECURITY OBJECTIVES**

- [ ] ✅ **Zero sensitive file exposure** via web browser
- [ ] ✅ **Complete directory listing protection**
- [ ] ✅ **Proper file and folder permissions**
- [ ] ✅ **Secure web server configuration**
- [ ] ✅ **Environment variable protection**
- [ ] ✅ **Comprehensive security testing**

---

## 📁 **1. PROJECT STRUCTURE VALIDATION**

### **Directory Structure Check**
- [ ] ✅ Web root (`/var/www/html`) contains **ONLY** public files
- [ ] ✅ Application code is **OUTSIDE** web root (`/var/www/app`)
- [ ] ✅ Configuration files are **OUTSIDE** web root (`/var/www/config`)
- [ ] ✅ Environment files are **OUTSIDE** web root (`/var/www/.env`)
- [ ] ✅ Source code is **OUTSIDE** web root (`/var/www/src`)
- [ ] ✅ Node modules are **OUTSIDE** web root (`/var/www/node_modules`)
- [ ] ✅ Log files are **OUTSIDE** web root (`/var/www/storage/logs`)

### **Public Directory Contents**
```bash
# Verify web root contains ONLY these files:
ls -la /var/www/html/
# Expected: index.html, assets/, api/ (public endpoints only)
```

### **Sensitive Files Location**
```bash
# Verify these are OUTSIDE web root:
ls -la /var/www/.env*
ls -la /var/www/config/
ls -la /var/www/src/
ls -la /var/www/package.json
```

---

## 🔧 **2. WEB SERVER CONFIGURATION**

### **Nginx Configuration**
- [ ] ✅ Copy `deployment/nginx-security.conf` to `/etc/nginx/sites-available/amazingpay`
- [ ] ✅ Enable site: `sudo ln -s /etc/nginx/sites-available/amazingpay /etc/nginx/sites-enabled/`
- [ ] ✅ Test configuration: `sudo nginx -t`
- [ ] ✅ Reload Nginx: `sudo systemctl reload nginx`

### **Apache Configuration**
- [ ] ✅ Copy `deployment/apache-security.htaccess` to `/var/www/html/.htaccess`
- [ ] ✅ Ensure mod_rewrite is enabled: `sudo a2enmod rewrite`
- [ ] ✅ Ensure mod_headers is enabled: `sudo a2enmod headers`
- [ ] ✅ Test configuration: `sudo apache2ctl configtest`
- [ ] ✅ Reload Apache: `sudo systemctl reload apache2`

### **SSL/TLS Configuration**
- [ ] ✅ Valid SSL certificate installed
- [ ] ✅ TLS 1.2+ only (disable older versions)
- [ ] ✅ Strong cipher suites configured
- [ ] ✅ HSTS header enabled
- [ ] ✅ OCSP stapling enabled (Nginx)

---

## 🔐 **3. FILE PERMISSIONS SETUP**

### **Critical Permission Commands**
```bash
# Web root permissions (public files only)
sudo chmod 755 /var/www/html
sudo chmod 644 /var/www/html/*.html
sudo chmod -R 644 /var/www/html/assets/

# Application directories (outside web root)
sudo chmod 750 /var/www/app
sudo chmod 750 /var/www/config
sudo chmod 750 /var/www/storage

# CRITICAL: Environment files (most restrictive)
sudo chmod 600 /var/www/.env*
sudo chown www-data:www-data /var/www/.env*

# Configuration files
sudo chmod 640 /var/www/config/*.ts
sudo chmod 640 /var/www/config/*.js
sudo chown www-data:www-data /var/www/config/*

# Log files
sudo chmod 750 /var/www/storage/logs
sudo chmod 640 /var/www/storage/logs/*.log
sudo chown www-data:www-data /var/www/storage/logs/

# Source code
sudo chmod -R 640 /var/www/src/
sudo chown -R www-data:www-data /var/www/src/
```

### **Permission Verification**
- [ ] ✅ Environment files: `600` (owner read/write only)
- [ ] ✅ Configuration files: `640` (owner read/write, group read)
- [ ] ✅ Source code: `640` (owner read/write, group read)
- [ ] ✅ Public files: `644` (world readable)
- [ ] ✅ Directories: `750` (owner full, group read/execute)

---

## 🚫 **4. DIRECTORY LISTING PROTECTION**

### **Nginx Verification**
```bash
# Check autoindex is off
grep -r "autoindex off" /etc/nginx/sites-available/amazingpay
```

### **Apache Verification**
```bash
# Check Options -Indexes is set
grep -r "Options -Indexes" /var/www/html/.htaccess
```

### **Additional Protection**
```bash
# Create empty index.html files in sensitive directories
sudo touch /var/www/app/index.html
sudo touch /var/www/config/index.html
sudo touch /var/www/storage/index.html
echo "Access Denied" | sudo tee /var/www/app/index.html
```

---

## 🧪 **5. SECURITY TESTING AND VALIDATION**

### **Automated Security Testing**
```bash
# Run comprehensive file access test
./scripts/security-file-test.sh https://yourdomain.com

# Run full security audit
./scripts/comprehensive-security-audit.sh https://yourdomain.com
```

### **Manual Testing Checklist**
- [ ] ✅ Test `.env` file access: `curl -I https://yourdomain.com/.env` → 403/404
- [ ] ✅ Test config access: `curl -I https://yourdomain.com/config/` → 403/404
- [ ] ✅ Test source code: `curl -I https://yourdomain.com/src/app.ts` → 403/404
- [ ] ✅ Test package.json: `curl -I https://yourdomain.com/package.json` → 403/404
- [ ] ✅ Test .git access: `curl -I https://yourdomain.com/.git/config` → 403/404
- [ ] ✅ Test directory listing: `curl https://yourdomain.com/config/` → 403/404
- [ ] ✅ Test node_modules: `curl -I https://yourdomain.com/node_modules/` → 403/404
- [ ] ✅ Test backup files: `curl -I https://yourdomain.com/.env.bak` → 403/404

### **Security Headers Validation**
```bash
# Check security headers
curl -I https://yourdomain.com

# Expected headers:
# X-Frame-Options: SAMEORIGIN
# X-Content-Type-Options: nosniff
# X-XSS-Protection: 1; mode=block
# Strict-Transport-Security: max-age=31536000
# Content-Security-Policy: default-src 'self'
```

---

## 🔒 **6. ENVIRONMENT VARIABLE SECURITY**

### **Environment File Protection**
- [ ] ✅ All `.env*` files are outside web root
- [ ] ✅ Environment files have `600` permissions
- [ ] ✅ Environment files owned by web server user
- [ ] ✅ No environment variables in source code
- [ ] ✅ Production uses secure secret management

### **Runtime Environment Security**
```bash
# Verify environment variables are not exposed
# Check application doesn't log environment variables
grep -r "process.env" /var/www/src/ | grep -i "console\|log"
# Should return no results
```

### **Secret Management Best Practices**
- [ ] ✅ Use dedicated secrets management (HashiCorp Vault, AWS Secrets Manager)
- [ ] ✅ Environment variables injected at runtime
- [ ] ✅ No secrets in Docker images or containers
- [ ] ✅ Regular secret rotation implemented
- [ ] ✅ Secrets encrypted at rest

---

## 🛠️ **7. SECURITY AUDIT TOOLS**

### **Install Security Tools**
```bash
# Install security scanning tools
sudo apt-get update
sudo apt-get install -y nikto dirb gobuster sslscan

# Install additional tools
sudo apt-get install -y nmap curl wget
```

### **Automated Security Scanning**
```bash
# Web vulnerability scan
nikto -h https://yourdomain.com -C all -Format txt

# Directory brute force
dirb https://yourdomain.com /usr/share/dirb/wordlists/common.txt

# SSL/TLS security scan
sslscan https://yourdomain.com

# Port scan
nmap -sS -O yourdomain.com
```

---

## 📊 **8. MONITORING AND ALERTING**

### **Security Event Monitoring**
```bash
# Monitor for sensitive file access attempts
sudo tail -f /var/log/nginx/access.log | grep -E "\.(env|config|json|ts)"

# Monitor for directory traversal attempts
sudo tail -f /var/log/nginx/access.log | grep -E "\.\./|\.\.\\|/etc/passwd"
```

### **Fail2Ban Configuration**
```bash
# Install and configure fail2ban
sudo apt-get install fail2ban

# Create custom jail for file access attempts
sudo tee /etc/fail2ban/jail.local << EOF
[nginx-sensitive-files]
enabled = true
port = http,https
filter = nginx-sensitive-files
logpath = /var/log/nginx/access.log
maxretry = 3
bantime = 3600
EOF
```

---

## ✅ **9. FINAL DEPLOYMENT VALIDATION**

### **Pre-Go-Live Checklist**
- [ ] ✅ All security tests pass with zero vulnerabilities
- [ ] ✅ Web server configuration blocks all sensitive files
- [ ] ✅ Directory listing is completely disabled
- [ ] ✅ File permissions are properly restrictive
- [ ] ✅ SSL/TLS configuration is secure
- [ ] ✅ Security headers are properly configured
- [ ] ✅ Environment variables are secured
- [ ] ✅ Monitoring and alerting is active
- [ ] ✅ Backup and recovery procedures tested
- [ ] ✅ Incident response plan is ready

### **Post-Deployment Monitoring**
- [ ] ✅ Monitor security logs for access attempts
- [ ] ✅ Regular security scans scheduled
- [ ] ✅ Security patches applied promptly
- [ ] ✅ Access logs reviewed regularly
- [ ] ✅ Security metrics tracked and reported

---

## 🚨 **10. EMERGENCY RESPONSE**

### **Security Incident Response**
```bash
# If sensitive file exposure is detected:
1. Immediately block access via web server config
2. Review access logs for compromise indicators
3. Rotate all potentially exposed secrets
4. Notify security team and stakeholders
5. Document incident and lessons learned
```

### **Emergency Contacts**
- **Security Team**: [<EMAIL>]
- **DevOps Team**: [<EMAIL>]
- **Management**: [<EMAIL>]

---

## 📋 **VALIDATION COMMANDS SUMMARY**

```bash
# Quick security validation
./scripts/security-file-test.sh https://yourdomain.com

# Comprehensive security audit
./scripts/comprehensive-security-audit.sh https://yourdomain.com

# Manual file access tests
curl -I https://yourdomain.com/.env
curl -I https://yourdomain.com/config/
curl -I https://yourdomain.com/package.json
curl -I https://yourdomain.com/.git/config

# Security headers check
curl -I https://yourdomain.com | grep -E "X-Frame-Options|X-Content-Type-Options|Strict-Transport-Security"

# SSL security check
sslscan https://yourdomain.com
```

---

## 🏆 **SUCCESS CRITERIA**

### **Deployment is SECURE when:**
- ✅ **All security tests pass** with zero vulnerabilities
- ✅ **No sensitive files accessible** via web browser
- ✅ **Directory listing completely disabled**
- ✅ **Security headers properly configured**
- ✅ **SSL/TLS configuration secure**
- ✅ **File permissions properly restrictive**
- ✅ **Environment variables protected**
- ✅ **Monitoring and alerting active**

---

**🔒 SECURITY CLASSIFICATION**: Critical - Implementation Required  
**📅 LAST UPDATED**: [Current Date]  
**🔄 REVIEW CYCLE**: Before each deployment  
**✅ APPROVAL**: Security Team Required

**🏆 YOUR CRITICAL FINANCIAL APPLICATION WILL BE COMPLETELY SECURE WHEN ALL ITEMS ARE CHECKED!**
