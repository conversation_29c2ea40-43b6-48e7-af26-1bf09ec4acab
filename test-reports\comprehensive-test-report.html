
<!DOCTYPE html>
<html>
<head>
    <title>AmazingPay Flow - Comprehensive Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2196F3; color: white; padding: 20px; border-radius: 5px; }
        .summary { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .test-category { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .passed { color: #4CAF50; }
        .failed { color: #f44336; }
        .progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #4CAF50; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 AmazingPay Flow - Comprehensive Test Report</h1>
        <p>Generated on: 2025-05-25T01:48:46.291Z</p>
        <p>Total Duration: 134.68 seconds</p>
    </div>
    
    <div class="summary">
        <h2>📊 Overall Summary</h2>
        <p><strong>Success Rate:</strong> 71.4%</p>
        <p><strong>Total Tests:</strong> 14</p>
        <p><strong>Passed:</strong> <span class="passed">10</span></p>
        <p><strong>Failed:</strong> <span class="failed">4</span></p>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 71.4%"></div>
        </div>
    </div>
    
    
    <div class="test-category">
        <h3>📋 Unit Tests</h3>
        <p><strong>Passed:</strong> <span class="passed">0</span></p>
        <p><strong>Failed:</strong> <span class="failed">1</span></p>
        <p><strong>Total:</strong> 1</p>
        <p><strong>Duration:</strong> 57.47s</p>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 0%"></div>
        </div>
    </div>
    
    <div class="test-category">
        <h3>📋 Integration Tests</h3>
        <p><strong>Passed:</strong> <span class="passed">0</span></p>
        <p><strong>Failed:</strong> <span class="failed">1</span></p>
        <p><strong>Total:</strong> 1</p>
        <p><strong>Duration:</strong> 23.25s</p>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 0%"></div>
        </div>
    </div>
    
    <div class="test-category">
        <h3>📋 Performance Tests</h3>
        <p><strong>Passed:</strong> <span class="passed">0</span></p>
        <p><strong>Failed:</strong> <span class="failed">1</span></p>
        <p><strong>Total:</strong> 1</p>
        <p><strong>Duration:</strong> 16.32s</p>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 0%"></div>
        </div>
    </div>
    
    <div class="test-category">
        <h3>📋 Security Tests</h3>
        <p><strong>Passed:</strong> <span class="passed">2</span></p>
        <p><strong>Failed:</strong> <span class="failed">1</span></p>
        <p><strong>Total:</strong> 3</p>
        <p><strong>Duration:</strong> 3.03s</p>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 66.66666666666666%"></div>
        </div>
    </div>
    
    <div class="test-category">
        <h3>📋 Compilation Tests</h3>
        <p><strong>Passed:</strong> <span class="passed">8</span></p>
        <p><strong>Failed:</strong> <span class="failed">0</span></p>
        <p><strong>Total:</strong> 8</p>
        <p><strong>Duration:</strong> 34.60s</p>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 100%"></div>
        </div>
    </div>
    
</body>
</html>