# 🤖 AUTOMATED SECURITY RESPONSE SCRIPT (PowerShell)
# This script automates most security actions that can be done programmatically

Write-Host "🚨 STARTING AUTOMATED SECURITY RESPONSE..." -ForegroundColor Red
Write-Host "==================================================" -ForegroundColor Yellow

# Function to generate random string
function Get-RandomString {
    param([int]$Length = 32)
    $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    $random = 1..$Length | ForEach-Object { Get-Random -Maximum $chars.length }
    return ($random | ForEach-Object { $chars[$_] }) -join ''
}

# Function to generate base64 string
function Get-RandomBase64 {
    param([int]$Bytes = 32)
    $randomBytes = New-Object byte[] $Bytes
    [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($randomBytes)
    return [Convert]::ToBase64String($randomBytes)
}

# Step 1: Generate new secure credentials
Write-Host ""
Write-Host "🔑 STEP 1: GENERATING NEW SECURE CREDENTIALS..." -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Yellow

$JWT_SECRET = Get-RandomBase64 -Bytes 64
Write-Host "✅ Generated new JWT secret (256-bit)" -ForegroundColor Green

$JWT_REFRESH_SECRET = Get-RandomBase64 -Bytes 64
Write-Host "✅ Generated new JWT refresh secret" -ForegroundColor Green

$CSRF_SECRET = Get-RandomString -Length 64
Write-Host "✅ Generated new CSRF secret" -ForegroundColor Green

$SESSION_SECRET = Get-RandomBase64 -Bytes 48
Write-Host "✅ Generated new session secret" -ForegroundColor Green

$ENCRYPTION_KEY = Get-RandomString -Length 64
Write-Host "✅ Generated new encryption key" -ForegroundColor Green

$API_SECRET_KEY = Get-RandomBase64 -Bytes 32
Write-Host "✅ Generated new API secret key" -ForegroundColor Green

$DB_PASSWORD = Get-RandomString -Length 24
Write-Host "✅ Generated new database password" -ForegroundColor Green

# Step 2: Create secure .env file
Write-Host ""
Write-Host "📝 STEP 2: CREATING SECURE .ENV FILE..." -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Yellow

$currentDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$nextRotationDate = (Get-Date).AddMonths(3).ToString("yyyy-MM-dd")

$envContent = @"
# 🔒 AMAZINGPAY SECURE ENVIRONMENT - AUTO-GENERATED $currentDate
# ⚠️ NEVER COMMIT THIS FILE TO VERSION CONTROL!

# Server Configuration
PORT=3002
HOST=localhost
NODE_ENV=development
API_PREFIX=/api

# Frontend Configuration
FRONTEND_URL=http://localhost:5173
API_URL=http://localhost:3002/api
DOMAIN=amazingpayme.com

# 🔒 DATABASE CONFIGURATION - NEW SECURE CREDENTIALS
DATABASE_URL=postgresql://amazingpay_app:$DB_PASSWORD@localhost:5432/amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=amazingpay_app
DB_PASSWORD=$DB_PASSWORD
DB_NAME=amazingpay
DB_SSL=false
DB_CONNECTION_POOL_MIN=5
DB_CONNECTION_POOL_MAX=20
DB_STATEMENT_TIMEOUT=30000

# 🔑 JWT CONFIGURATION - NEW SECURE SECRETS
JWT_SECRET=$JWT_SECRET
JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=amazingpayme.com
JWT_AUDIENCE=amazingpayme.com

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_PREFLIGHT_CONTINUE=false
CORS_OPTIONS_SUCCESS_STATUS=204
CORS_MAX_AGE=86400

# 🛡️ SECURITY CONFIGURATION - NEW SECURE SECRETS
BCRYPT_SALT_ROUNDS=12
CSRF_ENABLED=true
CSRF_SECRET=$CSRF_SECRET
CSRF_TOKEN_EXPIRATION=3600000
XSS_PROTECTION=true
CONTENT_SECURITY_POLICY=true
HSTS=true
HSTS_MAX_AGE=31536000
FRAME_GUARD=true
NO_SNIFF=true
REFERRER_POLICY=strict-origin-when-cross-origin

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
AUTH_RATE_LIMIT_WINDOW_MS=3600000
AUTH_RATE_LIMIT_MAX=5
PASSWORD_RESET_RATE_LIMIT_WINDOW_MS=3600000
PASSWORD_RESET_RATE_LIMIT_MAX=3

# 📧 EMAIL CONFIGURATION - UPDATE THESE MANUALLY
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USER=MANUAL_UPDATE_REQUIRED
EMAIL_PASSWORD=MANUAL_UPDATE_REQUIRED
EMAIL_FROM=<EMAIL>
EMAIL_SECURE=true

# 📱 SMS CONFIGURATION - UPDATE THESE MANUALLY
TWILIO_ACCOUNT_SID=MANUAL_UPDATE_REQUIRED
TWILIO_AUTH_TOKEN=MANUAL_UPDATE_REQUIRED
TWILIO_PHONE_NUMBER=+**********

# 💰 BINANCE API CONFIGURATION - UPDATE THESE MANUALLY
BINANCE_API_URL=https://api.binance.com
BINANCE_API_KEY=MANUAL_UPDATE_REQUIRED
BINANCE_API_SECRET=MANUAL_UPDATE_REQUIRED

# ⛓️ BLOCKCHAIN API CONFIGURATION - UPDATE THESE MANUALLY
TRONSCAN_API_URL=https://apilist.tronscan.org/api
ETHERSCAN_API_URL=https://api.etherscan.io/api
ETHERSCAN_API_KEY=MANUAL_UPDATE_REQUIRED

# 📝 LOGGING CONFIGURATION
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7

# 📊 ADVANCED REPORTING CONFIGURATION
REPORTS_DIR=./reports
MAX_MEMORY_USAGE=*********
BATCH_SIZE=1000
STREAMING_THRESHOLD=*********

# 📧 SMTP CONFIGURATION FOR REPORTS
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=MANUAL_UPDATE_REQUIRED
SMTP_PASSWORD=MANUAL_UPDATE_REQUIRED

# 🔐 ADDITIONAL SECURITY SECRETS - AUTO-GENERATED
SESSION_SECRET=$SESSION_SECRET
ENCRYPTION_KEY=$ENCRYPTION_KEY
API_SECRET_KEY=$API_SECRET_KEY

# 🚨 SECURITY INCIDENT TRACKING
SECURITY_INCIDENT_DATE=$(Get-Date -Format "yyyy-MM-dd")
CREDENTIAL_ROTATION_DATE=$(Get-Date -Format "yyyy-MM-dd")
NEXT_ROTATION_DUE=$nextRotationDate
"@

$envContent | Out-File -FilePath ".env" -Encoding UTF8
Write-Host "✅ Created secure .env file with auto-generated secrets" -ForegroundColor Green

# Step 3: Create database setup script
Write-Host ""
Write-Host "🗄️ STEP 3: CREATING DATABASE SETUP SCRIPT..." -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Yellow

$dbScript = @"
-- 🔒 SECURE DATABASE SETUP SCRIPT
-- Auto-generated on $currentDate

-- Create new secure application user
CREATE USER amazingpay_app WITH PASSWORD '$DB_PASSWORD';

-- Grant necessary privileges
GRANT ALL PRIVILEGES ON DATABASE amazingpay TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO amazingpay_app;

-- Change postgres password (uncomment and set new password)
-- ALTER USER postgres PASSWORD 'NEW_POSTGRES_PASSWORD_HERE';

-- Verify users
\du

-- Show current database
SELECT current_database();
"@

$dbScript | Out-File -FilePath "setup-secure-database.sql" -Encoding UTF8
Write-Host "✅ Created database setup script" -ForegroundColor Green

# Step 4: Create manual actions checklist
Write-Host ""
Write-Host "📋 STEP 4: CREATING MANUAL ACTION CHECKLIST..." -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Yellow

$manualActions = @"
# 📋 MANUAL ACTIONS REQUIRED

## ✅ AUTOMATED ACTIONS COMPLETED
- [x] Generated new secure secrets (JWT, CSRF, Session, etc.)
- [x] Created secure .env file with auto-generated credentials
- [x] Created database setup script

## 🔴 MANUAL ACTIONS STILL REQUIRED

### 1. MAKE REPOSITORY PRIVATE (2 minutes) - CRITICAL!
- [ ] Go to: https://github.com/Amazingteam-eg/Amazingpayflow/settings
- [ ] Scroll to "Danger Zone"
- [ ] Click "Change repository visibility" → "Make private"

### 2. UPDATE DATABASE (5 minutes)
``````sql
-- Run the generated database script
psql -U postgres -d amazingpay -f setup-secure-database.sql
``````

### 3. UPDATE API KEYS (10 minutes)
Update these in your .env file:
- [ ] TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN
- [ ] BINANCE_API_KEY and BINANCE_API_SECRET  
- [ ] ETHERSCAN_API_KEY
- [ ] EMAIL_USER and EMAIL_PASSWORD

### 4. TEST APPLICATION (5 minutes)
``````bash
npm start
``````

### 5. CLEAN GIT HISTORY (10 minutes)
``````bash
# Run the comprehensive cleanup
./security-cleanup.sh
git push origin --force --all
``````

## 🎯 TOTAL TIME: ~32 minutes
## 🚨 PRIORITY: Make repository private FIRST!

## 🔑 GENERATED CREDENTIALS SUMMARY
- Database Password: $DB_PASSWORD
- JWT Secret: [64-character secure string]
- CSRF Secret: [64-character secure string]
- Session Secret: [48-character secure string]
- Encryption Key: [64-character secure string]
- API Secret: [32-character secure string]
"@

$manualActions | Out-File -FilePath "MANUAL_ACTIONS_REQUIRED.md" -Encoding UTF8
Write-Host "✅ Created manual action checklist" -ForegroundColor Green

# Summary
Write-Host ""
Write-Host "🎉 AUTOMATED SECURITY RESPONSE COMPLETE!" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Yellow
Write-Host "✅ Generated new secure credentials" -ForegroundColor Green
Write-Host "✅ Created secure .env file" -ForegroundColor Green
Write-Host "✅ Created database setup script" -ForegroundColor Green
Write-Host "✅ Created manual action checklist" -ForegroundColor Green

Write-Host ""
Write-Host "⚠️  CRITICAL: Repository is still PUBLIC!" -ForegroundColor Red
Write-Host "⚠️  NEXT STEP: Make repository private immediately" -ForegroundColor Yellow
Write-Host "ℹ️  Follow instructions in: MANUAL_ACTIONS_REQUIRED.md" -ForegroundColor Blue

Write-Host ""
Write-Host "📊 SECURITY STATUS:" -ForegroundColor Cyan
Write-Host "   🤖 Automated: 70% complete" -ForegroundColor Green
Write-Host "   👤 Manual:    30% remaining" -ForegroundColor Yellow
Write-Host "   ⏰ Time:      ~32 minutes total" -ForegroundColor Blue
Write-Host ""
Write-Host "ℹ️  Run: Get-Content MANUAL_ACTIONS_REQUIRED.md" -ForegroundColor Blue
