/**
 * 🎨 AMAZINGPAY PAYMENT GATEWAY ANIMATIONS
 * Professional animations that reflect payment processing functionality
 */

class PaymentAnimations {
  constructor() {
    this.isInitialized = false;
    this.animationElements = new Map();
    this.init();
  }

  init() {
    if (this.isInitialized) return;
    
    // Register GSAP plugins
    if (typeof gsap !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
      this.isInitialized = true;
      this.setupAnimations();
    } else {
      console.warn('GSAP not loaded. Payment animations disabled.');
    }
  }

  setupAnimations() {
    this.createPaymentFlowAnimation();
    this.createSecurityShieldAnimation();
    this.createDataFlowAnimation();
    this.createTransactionCounterAnimation();
    this.createCurrencyExchangeAnimation();
    this.createGlobalNetworkAnimation();
  }

  // 💳 Payment Flow Animation
  createPaymentFlowAnimation() {
    const container = document.querySelector('.payment-flow-animation');
    if (!container) return;

    const timeline = gsap.timeline({ repeat: -1, repeatDelay: 2 });
    
    // Card insertion animation
    timeline
      .from('.payment-card', { x: -100, opacity: 0, duration: 1, ease: 'power2.out' })
      .to('.payment-card', { scale: 1.05, duration: 0.3, yoyo: true, repeat: 1 })
      .to('.processing-dots', { opacity: 1, duration: 0.5 })
      .to('.processing-dots .dot', { 
        scale: 1.2, 
        duration: 0.3, 
        stagger: 0.1, 
        yoyo: true, 
        repeat: 3 
      })
      .to('.success-checkmark', { 
        opacity: 1, 
        scale: 1, 
        duration: 0.5, 
        ease: 'back.out(1.7)' 
      })
      .to('.payment-complete', { opacity: 1, y: 0, duration: 0.5 })
      .to('.payment-flow-animation', { opacity: 0.7, duration: 0.5 }, '+=1')
      .set('.payment-flow-animation *', { opacity: 0, scale: 0.8, x: 0, y: 20 });
  }

  // 🛡️ Security Shield Animation
  createSecurityShieldAnimation() {
    const shields = document.querySelectorAll('.security-shield');
    
    shields.forEach(shield => {
      const tl = gsap.timeline({ 
        scrollTrigger: {
          trigger: shield,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });

      tl.from(shield, { 
        scale: 0, 
        rotation: -180, 
        duration: 1, 
        ease: 'elastic.out(1, 0.5)' 
      })
      .to(shield.querySelector('.shield-glow'), { 
        opacity: 0.8, 
        scale: 1.2, 
        duration: 2, 
        repeat: -1, 
        yoyo: true 
      }, '-=0.5');
    });
  }

  // 📊 Data Flow Animation
  createDataFlowAnimation() {
    const dataStreams = document.querySelectorAll('.data-stream');
    
    dataStreams.forEach(stream => {
      const particles = stream.querySelectorAll('.data-particle');
      
      gsap.set(particles, { x: -50, opacity: 0 });
      
      gsap.to(particles, {
        x: '100vw',
        opacity: 1,
        duration: 3,
        stagger: 0.2,
        repeat: -1,
        ease: 'none',
        modifiers: {
          opacity: (value, target, index) => {
            const progress = parseFloat(value);
            return progress < 0.5 ? progress * 2 : (1 - progress) * 2;
          }
        }
      });
    });
  }

  // 🔢 Transaction Counter Animation
  createTransactionCounterAnimation() {
    const counters = document.querySelectorAll('.transaction-counter');
    
    counters.forEach(counter => {
      const target = parseInt(counter.getAttribute('data-target'));
      const duration = parseFloat(counter.getAttribute('data-duration')) || 2;
      
      ScrollTrigger.create({
        trigger: counter,
        start: 'top 80%',
        onEnter: () => {
          gsap.to(counter, {
            innerHTML: target,
            duration: duration,
            ease: 'power2.out',
            snap: { innerHTML: 1 },
            onUpdate: function() {
              counter.innerHTML = Math.ceil(this.targets()[0].innerHTML).toLocaleString();
            }
          });
        }
      });
    });
  }

  // 💱 Currency Exchange Animation
  createCurrencyExchangeAnimation() {
    const exchangeElements = document.querySelectorAll('.currency-exchange');
    
    exchangeElements.forEach(element => {
      const currencies = element.querySelectorAll('.currency');
      const arrows = element.querySelectorAll('.exchange-arrow');
      
      const tl = gsap.timeline({ repeat: -1, repeatDelay: 1 });
      
      tl.from(currencies[0], { scale: 0, duration: 0.5, ease: 'back.out(1.7)' })
        .to(arrows, { x: 20, duration: 0.5, ease: 'power2.inOut' })
        .from(currencies[1], { scale: 0, duration: 0.5, ease: 'back.out(1.7)' }, '-=0.3')
        .to(arrows, { rotation: 180, duration: 0.3 }, '+=0.5')
        .to(currencies, { scale: 0.8, duration: 0.3 })
        .to(currencies, { scale: 1, duration: 0.3 });
    });
  }

  // 🌐 Global Network Animation
  createGlobalNetworkAnimation() {
    const networkContainer = document.querySelector('.global-network');
    if (!networkContainer) return;

    const nodes = networkContainer.querySelectorAll('.network-node');
    const connections = networkContainer.querySelectorAll('.network-connection');
    
    // Animate nodes
    gsap.from(nodes, {
      scale: 0,
      duration: 1,
      stagger: 0.1,
      ease: 'elastic.out(1, 0.5)',
      scrollTrigger: {
        trigger: networkContainer,
        start: 'top 80%'
      }
    });

    // Animate connections
    gsap.from(connections, {
      scaleX: 0,
      duration: 1.5,
      stagger: 0.2,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: networkContainer,
        start: 'top 80%'
      }
    });

    // Pulse animation for active nodes
    gsap.to('.network-node.active', {
      scale: 1.2,
      duration: 1,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut',
      stagger: 0.3
    });
  }

  // 📱 Mobile Payment Animation
  createMobilePaymentAnimation() {
    const mobileElements = document.querySelectorAll('.mobile-payment');
    
    mobileElements.forEach(element => {
      const phone = element.querySelector('.phone');
      const nfcWaves = element.querySelectorAll('.nfc-wave');
      const terminal = element.querySelector('.payment-terminal');
      
      const tl = gsap.timeline({ 
        repeat: -1, 
        repeatDelay: 2,
        scrollTrigger: {
          trigger: element,
          start: 'top 80%'
        }
      });
      
      tl.from(phone, { y: 50, opacity: 0, duration: 0.8 })
        .to(phone, { x: 30, duration: 1, ease: 'power2.inOut' })
        .to(nfcWaves, { 
          scale: 2, 
          opacity: 0, 
          duration: 1, 
          stagger: 0.2, 
          ease: 'power2.out' 
        }, '-=0.5')
        .to(terminal.querySelector('.terminal-light'), { 
          backgroundColor: '#10b981', 
          duration: 0.3 
        })
        .to(phone, { x: 0, duration: 1, ease: 'power2.inOut' }, '+=0.5');
    });
  }

  // 🔐 Encryption Animation
  createEncryptionAnimation() {
    const encryptionElements = document.querySelectorAll('.encryption-demo');
    
    encryptionElements.forEach(element => {
      const plainText = element.querySelector('.plain-text');
      const encryptedText = element.querySelector('.encrypted-text');
      const lockIcon = element.querySelector('.lock-icon');
      
      const tl = gsap.timeline({ 
        repeat: -1, 
        repeatDelay: 3,
        scrollTrigger: {
          trigger: element,
          start: 'top 80%'
        }
      });
      
      tl.from(plainText, { opacity: 0, y: 20, duration: 0.5 })
        .to(lockIcon, { rotation: 360, scale: 1.2, duration: 0.8, ease: 'power2.inOut' })
        .to(plainText, { 
          opacity: 0, 
          filter: 'blur(5px)', 
          duration: 0.5 
        }, '-=0.3')
        .from(encryptedText, { 
          opacity: 0, 
          scale: 0.8, 
          duration: 0.5, 
          ease: 'back.out(1.7)' 
        })
        .to(encryptedText, { 
          color: '#10b981', 
          duration: 0.3 
        });
    });
  }

  // 🎯 Performance Metrics Animation
  createPerformanceMetricsAnimation() {
    const metricsElements = document.querySelectorAll('.performance-metric');
    
    metricsElements.forEach(metric => {
      const progressBar = metric.querySelector('.progress-bar');
      const percentage = metric.querySelector('.percentage');
      const targetValue = parseInt(percentage.getAttribute('data-target'));
      
      ScrollTrigger.create({
        trigger: metric,
        start: 'top 80%',
        onEnter: () => {
          gsap.to(progressBar, {
            width: `${targetValue}%`,
            duration: 2,
            ease: 'power2.out'
          });
          
          gsap.to(percentage, {
            innerHTML: targetValue,
            duration: 2,
            ease: 'power2.out',
            snap: { innerHTML: 1 },
            onUpdate: function() {
              percentage.innerHTML = Math.ceil(this.targets()[0].innerHTML) + '%';
            }
          });
        }
      });
    });
  }

  // 🌊 Liquid Loading Animation
  createLiquidLoadingAnimation() {
    const liquidElements = document.querySelectorAll('.liquid-loading');
    
    liquidElements.forEach(element => {
      const liquid = element.querySelector('.liquid');
      const percentage = element.querySelector('.loading-percentage');
      
      gsap.to(liquid, {
        height: '100%',
        duration: 3,
        ease: 'power2.inOut',
        onUpdate: function() {
          const progress = Math.ceil(this.progress() * 100);
          if (percentage) {
            percentage.innerHTML = progress + '%';
          }
        }
      });
    });
  }

  // 🎨 Particle System for Background
  createParticleSystem(container) {
    if (!container) return;
    
    const particleCount = 50;
    const particles = [];
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.cssText = `
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(59, 130, 246, 0.6);
        border-radius: 50%;
        pointer-events: none;
      `;
      
      container.appendChild(particle);
      particles.push(particle);
      
      // Random initial position
      gsap.set(particle, {
        x: Math.random() * container.offsetWidth,
        y: Math.random() * container.offsetHeight,
        scale: Math.random() * 0.5 + 0.5
      });
      
      // Floating animation
      gsap.to(particle, {
        x: `+=${Math.random() * 200 - 100}`,
        y: `+=${Math.random() * 200 - 100}`,
        duration: Math.random() * 10 + 5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut'
      });
      
      // Opacity animation
      gsap.to(particle, {
        opacity: Math.random() * 0.5 + 0.3,
        duration: Math.random() * 3 + 1,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut'
      });
    }
  }

  // 🎭 Initialize all animations on page load
  static init() {
    document.addEventListener('DOMContentLoaded', () => {
      new PaymentAnimations();
    });
  }
}

// Auto-initialize when script loads
PaymentAnimations.init();

// Export for manual initialization
window.PaymentAnimations = PaymentAnimations;
