# AmazingPay Deployment Guide

This guide provides instructions for deploying the AmazingPay application to various environments.

## Prerequisites

Before deploying the application, ensure you have the following:

- Node.js (v14 or higher)
- PostgreSQL database
- npm or yarn
- Git

## Local Development Deployment

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/amazingpay.git
cd amazingpay
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set Up Environment Variables

Copy the example environment file and update it with your configuration:

```bash
cp .env.example .env
```

Edit the `.env` file to set your database connection string, JWT secret, and other configuration options.

### 4. Set Up the Database

```bash
# Generate Prisma client
npm run prisma:generate

# Run database migrations
npm run prisma:migrate

# Seed the database with initial data
npm run prisma:seed
```

Alternatively, you can run all database setup commands at once:

```bash
npm run db:setup
```

### 5. Build and Start the Application

```bash
# Build the application
npm run build

# Start the application
npm start
```

For development with hot reloading:

```bash
npm run dev
```

## Production Deployment

### 1. Server Requirements

- Node.js (v14 or higher)
- PostgreSQL database
- Nginx or another reverse proxy (recommended)
- PM2 or another process manager (recommended)

### 2. Clone and Set Up the Application

Follow steps 1-4 from the Local Development Deployment section.

### 3. Configure for Production

Update the `.env` file with production settings:

```
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
DATABASE_URL=************************************************/amazingpay
JWT_SECRET=your-secure-jwt-secret
```

### 4. Build the Application

```bash
npm run build
```

### 5. Set Up Process Management with PM2

Install PM2 globally:

```bash
npm install -g pm2
```

Create a PM2 ecosystem file (`ecosystem.config.js`):

```javascript
module.exports = {
  apps: [{
    name: 'amazingpay',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
    },
  }],
};
```

Start the application with PM2:

```bash
pm2 start ecosystem.config.js
```

### 6. Set Up Nginx as a Reverse Proxy

Install Nginx:

```bash
sudo apt update
sudo apt install nginx
```

Create an Nginx configuration file:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the configuration and restart Nginx:

```bash
sudo ln -s /etc/nginx/sites-available/amazingpay /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 7. Set Up SSL with Let's Encrypt

Install Certbot:

```bash
sudo apt install certbot python3-certbot-nginx
```

Obtain and configure SSL certificates:

```bash
sudo certbot --nginx -d your-domain.com
```

## Docker Deployment

### 1. Create a Dockerfile

Create a `Dockerfile` in the root of your project:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3002

CMD ["node", "dist/index.js"]
```

### 2. Create a Docker Compose File

Create a `docker-compose.yml` file:

```yaml
version: '3'

services:
  app:
    build: .
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/amazingpay
      - JWT_SECRET=your-secure-jwt-secret
    depends_on:
      - db
    restart: always

  db:
    image: postgres:14
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=amazingpay
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### 3. Build and Run with Docker Compose

```bash
docker-compose up -d
```

### 4. Initialize the Database

```bash
docker-compose exec app npm run db:setup
```

## Continuous Integration/Continuous Deployment (CI/CD)

For CI/CD, you can use GitHub Actions, GitLab CI, or Jenkins to automate the deployment process.

### GitHub Actions Example

Create a `.github/workflows/deploy.yml` file:

```yaml
name: Deploy

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Build
        run: npm run build
        
      - name: Deploy to server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /path/to/amazingpay
            git pull
            npm ci
            npm run build
            pm2 restart amazingpay
```

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues:

1. Verify that your PostgreSQL server is running
2. Check that the `DATABASE_URL` in your `.env` file is correct
3. Ensure that the database user has the necessary permissions
4. Check for network connectivity issues between your application and database

### Application Startup Issues

If the application fails to start:

1. Check the logs for error messages
2. Verify that all required environment variables are set
3. Ensure that the build process completed successfully
4. Check that the port is not already in use by another application

## Maintenance

### Database Backups

Set up regular database backups:

```bash
# Create a backup
pg_dump -U postgres -d amazingpay > backup.sql

# Restore from a backup
psql -U postgres -d amazingpay < backup.sql
```

### Updating the Application

To update the application:

1. Pull the latest changes from the repository
2. Install dependencies
3. Run database migrations
4. Rebuild the application
5. Restart the application

```bash
git pull
npm install
npm run prisma:migrate
npm run build
pm2 restart amazingpay
```
