import { PrismaClient } from '@prisma/client';
// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { AppError as ImportedAppError } from "../utils/appError";
import { logger as Importedlogger } from "../lib/logger";
import { ErrorFactory, ErrorType } from "../utils/errors/ErrorFactory";
import { AppError as ImportedAppError } from "../utils/appError";
import { logger as Importedlogger } from "../lib/logger";
import { ErrorFactory, ErrorType } from "../utils/errors/ErrorFactory";

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Error handler middleware
 * This middleware handles all errors in the application
 */
export const errorHandler = (
  err: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Convert error to AppError
  const error: Error = err instanceof AppError ? err : (ErrorFactory).handle(err);
  
  // Log error
  if (error.statusCode >= 500) {
    logger.error(`[${error.type || 'ERROR'}] ${error.message}`, {
      error,
      request: { method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userId: req.user?.id
      }
    });
  } else {
    logger.warn(`[${error.type || 'ERROR'}] ${error.message}`, {
      error,
      request: { method: req.method,
        url: req.originalUrl
      }
    });
  }
  
  // Prepare response
  const response = {
    success: false,
    message: error.message,
    type: error.type,
    ...(error.details && { details: error.details }),
    ...(process.env.NODE_ENV === 'development' && {
      stack: error.stack,
      originalError: error.originalError
    })
  };
  
  // Send response
  res.status(error.statusCode).json(response);
};

/**
 * Not found middleware
 * This middleware handles 404 errors
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error: Error = (ErrorFactory).notFound('Route', req.originalUrl);
  next(error);
};

/**
 * Validation error handler
 * This function creates a validation error from validation errors
 * @param errors Validation errors
 * @returns AppError
 */
export const validationErrorHandler = (errors: Record<string, string[]>): AppError  =>  {
  const message = 'Validation failed';
  return (ErrorFactory).validation(message, errors);
};

/**
 * Database error handler
 * This function handles database errors
 * @param error Database error
 * @returns AppError
 */
export const databaseErrorHandler = (error): AppError  =>  {
  // Handle specific database errors
  if (error.name === 'PrismaClientKnownRequestError') {
    // @ts-ignore - PrismaClientKnownRequestError has a code property
    const code = error.code;
    
    // Handle unique constraint violations
    if (code === 'P2002') {
      // @ts-ignore - PrismaClientKnownRequestError has a meta property
      const fields = error.meta?.target as string[];
      const fieldNames = (fields).join(', ');
      
      return (ErrorFactory).conflict(`Unique constraint violation: ${fieldNames} already exists`);
    }
    
    // Handle foreign key constraint violations
    if (code === 'P2003') {
      // @ts-ignore - PrismaClientKnownRequestError has a meta property
      const field = error.meta?.field_name as string;
      
      return (ErrorFactory).validation(`Foreign key constraint violation: ${field} does not exist`);
    }
    
    // Handle record not found
    if (code === 'P2001') {
      // @ts-ignore - PrismaClientKnownRequestError has a meta property
      const model = error.meta?.model as string;
      
      return (ErrorFactory).notFound(model);
    }
  }
  
  // Default database error
  return (ErrorFactory).database('Database operation failed', error);
};

/**
 * Authentication error handler
 * This function handles authentication errors
 * @param message Error message
 * @returns AppError
 */
export const authenticationErrorHandler = (message = 'Authentication failed'): AppError  =>  {
  return (ErrorFactory).authentication(message);
};

/**
 * Authorization error handler
 * This function handles authorization errors
 * @param message Error message
 * @returns AppError
 */
export const authorizationErrorHandler = (message = 'Authorization failed'): AppError  =>  {
  return (ErrorFactory).authorization(message);
};
