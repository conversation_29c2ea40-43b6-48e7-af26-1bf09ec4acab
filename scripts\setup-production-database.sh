#!/bin/bash

# AmazingPay Flow - VPS Production Database Setup Script
# VPS: ************ | Domain: amazingpayme.com | Database: Amazingpay

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🗄️  AmazingPay Flow - Production Database Setup${NC}"
    echo -e "${BLUE}===============================================\n${NC}"
}

print_status() {
    echo -e "${CYAN}📦 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to check if PostgreSQL is available
check_postgres() {
    print_status "Checking PostgreSQL availability..."
    
    if command -v psql &> /dev/null; then
        print_success "PostgreSQL client found"
        return 0
    else
        print_warning "PostgreSQL client not found. Please install PostgreSQL"
        return 1
    fi
}

# Function to create production database
create_production_database() {
    print_status "Setting up production database..."
    
    # VPS Database configuration
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-5432}
    DB_NAME=${DB_NAME:-Amazingpay}
    DB_USER=${DB_USER:-postgres}

    # Set VPS database password
    if [ -z "$DB_PASSWORD" ]; then
        DB_PASSWORD="CepWrkdzE5TL"
        print_info "Using VPS database password"
    fi
    
    # Create database URL
    DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
    
    print_info "Database configuration:"
    print_info "Host: $DB_HOST"
    print_info "Port: $DB_PORT"
    print_info "Database: $DB_NAME"
    print_info "User: $DB_USER"
    
    # Update .env.production with database URL
    if [ -f ".env.production" ]; then
        # Backup existing file
        cp .env.production .env.production.backup
        
        # Update DATABASE_URL
        sed -i.bak "s|DATABASE_URL=.*|DATABASE_URL=\"$DATABASE_URL\"|g" .env.production
        sed -i.bak "s|DB_HOST=.*|DB_HOST=$DB_HOST|g" .env.production
        sed -i.bak "s|DB_PORT=.*|DB_PORT=$DB_PORT|g" .env.production
        sed -i.bak "s|DB_USERNAME=.*|DB_USERNAME=$DB_USER|g" .env.production
        sed -i.bak "s|DB_PASSWORD=.*|DB_PASSWORD=$DB_PASSWORD|g" .env.production
        sed -i.bak "s|DB_NAME=.*|DB_NAME=$DB_NAME|g" .env.production
        
        print_success "Updated .env.production with database configuration"
    else
        print_error ".env.production file not found"
        return 1
    fi
    
    # Export DATABASE_URL for Prisma
    export DATABASE_URL="$DATABASE_URL"
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Generate Prisma client
    print_info "Generating Prisma client..."
    npx prisma generate
    
    # Run migrations
    print_info "Applying database migrations..."
    npx prisma migrate deploy
    
    print_success "Database migrations completed"
}

# Function to seed database with initial data
seed_database() {
    print_status "Seeding database with initial data..."
    
    if [ -f "prisma/seed.ts" ]; then
        npx prisma db seed
        print_success "Database seeded successfully"
    else
        print_warning "No seed file found. Creating basic seed data..."
        create_basic_seed_data
    fi
}

# Function to create basic seed data
create_basic_seed_data() {
    print_info "Creating basic system data..."
    
    # Create a basic seed script
    cat > temp_seed.js << 'EOF'
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding production database...');
  
  // Create system admin user
  const hashedPassword = await bcrypt.hash('admin123!@#', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'ADMIN',
      isActive: true,
      admin: {
        create: {
          department: 'System',
          permissions: ['ALL']
        }
      }
    }
  });
  
  // Create system roles
  const roles = [
    { name: 'SUPER_ADMIN', description: 'Super Administrator', permissions: ['ALL'] },
    { name: 'ADMIN', description: 'Administrator', permissions: ['USER_MANAGEMENT', 'SYSTEM_SETTINGS'] },
    { name: 'MERCHANT', description: 'Merchant User', permissions: ['TRANSACTION_VIEW', 'PAYMENT_METHODS'] },
    { name: 'USER', description: 'Regular User', permissions: ['PROFILE_VIEW'] }
  ];
  
  for (const role of roles) {
    await prisma.role.upsert({
      where: { name: role.name },
      update: {},
      create: role
    });
  }
  
  // Create system settings
  const settings = [
    { key: 'SYSTEM_NAME', value: 'AmazingPay Flow' },
    { key: 'SYSTEM_VERSION', value: '1.0.0' },
    { key: 'MAINTENANCE_MODE', value: 'false' },
    { key: 'MAX_TRANSACTION_AMOUNT', value: '100000' },
    { key: 'DEFAULT_CURRENCY', value: 'USD' }
  ];
  
  for (const setting of settings) {
    await prisma.systemSetting.upsert({
      where: { key: setting.key },
      update: { value: setting.value, updatedById: adminUser.id },
      create: { ...setting, updatedById: adminUser.id }
    });
  }
  
  console.log('✅ Database seeded successfully');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
EOF
    
    # Run the seed script
    node temp_seed.js
    
    # Clean up
    rm temp_seed.js
    
    print_success "Basic seed data created"
}

# Function to verify database setup
verify_database() {
    print_status "Verifying database setup..."
    
    # Test database connection
    if npx prisma db pull --preview-feature > /dev/null 2>&1; then
        print_success "Database connection verified"
    else
        print_error "Database connection failed"
        return 1
    fi
    
    # Check if tables exist
    print_info "Checking database tables..."
    
    # Create a simple verification script
    cat > temp_verify.js << 'EOF'
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verify() {
  try {
    const userCount = await prisma.user.count();
    const roleCount = await prisma.role.count();
    const settingCount = await prisma.systemSetting.count();
    
    console.log(`📊 Database Statistics:`);
    console.log(`   Users: ${userCount}`);
    console.log(`   Roles: ${roleCount}`);
    console.log(`   Settings: ${settingCount}`);
    
    console.log('✅ Database verification completed');
  } catch (error) {
    console.error('❌ Database verification failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

verify();
EOF
    
    node temp_verify.js
    rm temp_verify.js
    
    print_success "Database verification completed"
}

# Function to create database backup
create_backup() {
    print_status "Creating database backup..."
    
    BACKUP_DIR="backups/database"
    mkdir -p $BACKUP_DIR
    
    BACKUP_FILE="$BACKUP_DIR/amazingpay_$(date +%Y%m%d_%H%M%S).sql"
    
    if command -v pg_dump &> /dev/null; then
        pg_dump $DATABASE_URL > $BACKUP_FILE
        print_success "Database backup created: $BACKUP_FILE"
    else
        print_warning "pg_dump not found. Backup skipped."
    fi
}

# Main execution
main() {
    print_header
    
    # Check prerequisites
    if ! check_postgres; then
        print_error "PostgreSQL is required. Please install it first."
        exit 1
    fi
    
    # Setup database
    create_production_database
    
    # Run migrations
    run_migrations
    
    # Seed database
    seed_database
    
    # Verify setup
    verify_database
    
    # Create backup
    create_backup
    
    echo ""
    print_success "🎉 Production database setup completed successfully!"
    echo ""
    print_info "📋 Next steps:"
    print_info "1. Verify your application can connect to the database"
    print_info "2. Test the admin login: <EMAIL> / admin123!@#"
    print_info "3. Change the default admin password"
    print_info "4. Configure regular database backups"
    echo ""
    print_info "🔗 Database URL: $DATABASE_URL"
}

# Run main function
main "$@"
