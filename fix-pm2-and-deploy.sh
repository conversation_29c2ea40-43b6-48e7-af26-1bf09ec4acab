#!/bin/bash

# 🚀 COMPLETE PM2 FIX AND DEPLOYMENT
# Fixes all PM2 PATH issues and deploys application
# Handles all common Node.js and PM2 installation problems

set -e

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo -e "${CYAN}🚀 COMPLETE PM2 FIX AND DEPLOYMENT${NC}"
echo "=================================="
echo ""

# 🔍 STEP 1: DIAGNOSE NODE.JS AND NPM SETUP
log "Diagnosing Node.js and npm setup..."

NODE_VERSION=$(node --version 2>/dev/null || echo "not found")
NPM_VERSION=$(npm --version 2>/dev/null || echo "not found")
NPM_PREFIX=$(npm config get prefix 2>/dev/null || echo "not found")

info "Node.js version: $NODE_VERSION"
info "npm version: $NPM_VERSION"
info "npm prefix: $NPM_PREFIX"

# 🔧 STEP 2: FIX NPM GLOBAL CONFIGURATION
log "Fixing npm global configuration..."

# Set proper npm prefix
npm config set prefix /usr/local
export NPM_CONFIG_PREFIX=/usr/local

# Update PATH to include all possible Node.js locations
export PATH="/usr/local/bin:/usr/bin:/bin:/usr/local/sbin:/usr/sbin:/sbin:$PATH"

# Make PATH permanent
echo 'export PATH="/usr/local/bin:/usr/bin:/bin:/usr/local/sbin:/usr/sbin:/sbin:$PATH"' >> ~/.bashrc

success "npm configuration fixed"

# 🗑️ STEP 3: CLEAN PREVIOUS PM2 INSTALLATIONS
log "Cleaning previous PM2 installations..."

# Remove any existing PM2 installations
npm uninstall -g pm2 2>/dev/null || true
rm -f /usr/local/bin/pm2 2>/dev/null || true
rm -f /usr/bin/pm2 2>/dev/null || true
rm -rf ~/.pm2 2>/dev/null || true

success "Previous PM2 installations cleaned"

# 📦 STEP 4: INSTALL PM2 PROPERLY
log "Installing PM2 with proper configuration..."

# Install PM2 globally with correct prefix
npm install -g pm2

# Find where PM2 was installed
PM2_LOCATIONS=(
    "/usr/local/bin/pm2"
    "/usr/bin/pm2"
    "$(npm config get prefix)/bin/pm2"
    "/root/.npm-global/bin/pm2"
)

PM2_PATH=""
for location in "${PM2_LOCATIONS[@]}"; do
    if [ -f "$location" ]; then
        PM2_PATH="$location"
        break
    fi
done

if [ -z "$PM2_PATH" ]; then
    # Find PM2 anywhere on the system
    PM2_PATH=$(find /usr -name "pm2" -type f 2>/dev/null | head -1)
fi

if [ -n "$PM2_PATH" ]; then
    success "PM2 found at: $PM2_PATH"
    
    # Create symlink to ensure PM2 is in PATH
    ln -sf "$PM2_PATH" /usr/local/bin/pm2
    chmod +x /usr/local/bin/pm2
    
    # Test PM2
    if /usr/local/bin/pm2 --version >/dev/null 2>&1; then
        success "PM2 is working correctly"
    else
        warning "PM2 installed but may have issues"
    fi
else
    warning "PM2 not found after installation, will use npx"
fi

# 🔄 STEP 5: CREATE PM2 WRAPPER FUNCTION
log "Creating PM2 wrapper function..."

# Create a wrapper script that handles PM2 execution
cat > /usr/local/bin/pm2-wrapper.sh << 'EOF'
#!/bin/bash

# PM2 Wrapper Script - Handles all PM2 execution methods

PM2_CMD=""

# Method 1: Try direct PM2
if command -v pm2 >/dev/null 2>&1; then
    PM2_CMD="pm2"
# Method 2: Try /usr/local/bin/pm2
elif [ -f "/usr/local/bin/pm2" ]; then
    PM2_CMD="/usr/local/bin/pm2"
# Method 3: Try /usr/bin/pm2
elif [ -f "/usr/bin/pm2" ]; then
    PM2_CMD="/usr/bin/pm2"
# Method 4: Use npx
else
    PM2_CMD="npx pm2"
fi

# Execute PM2 with all arguments
$PM2_CMD "$@"
EOF

chmod +x /usr/local/bin/pm2-wrapper.sh

success "PM2 wrapper created"

# 📝 STEP 6: CREATE .env FILE
log "Creating environment file..."

cat > .env << 'EOF'
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
DATABASE_URL=postgresql://postgres:CepWrkdzE5TL@localhost:5432/Amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=CepWrkdzE5TL
DB_NAME=Amazingpay
FRONTEND_URL=https://amazingpayme.com
API_URL=https://amazingpayme.com/api
DOMAIN=amazingpayme.com
VPS_IP=************
JWT_SECRET=AzP4y_Pr0d_JWT_S3cr3t_2024_V3ry_L0ng_4nd_S3cur3_K3y
JWT_EXPIRES_IN=1d
EOF

# Load environment
export $(cat .env | xargs)

success "Environment file created and loaded"

# 🗄️ STEP 7: SETUP DATABASE (IF NOT DONE)
log "Setting up database..."

sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'CepWrkdzE5TL';" 2>/dev/null || true
sudo -u postgres psql -c "CREATE DATABASE \"Amazingpay\";" 2>/dev/null || true

success "Database setup completed"

# 📦 STEP 8: INSTALL DEPENDENCIES AND BUILD
log "Installing dependencies and building..."

export HUSKY=0
npm install --omit=dev --ignore-scripts

npx prisma generate
npx prisma migrate deploy

npm run build

success "Application built successfully"

# 🚀 STEP 9: START APPLICATION WITH PM2
log "Starting application with PM2..."

# Stop any existing processes
/usr/local/bin/pm2-wrapper.sh stop all 2>/dev/null || true
/usr/local/bin/pm2-wrapper.sh delete all 2>/dev/null || true

# Start application
if /usr/local/bin/pm2-wrapper.sh start ecosystem.config.js --env production; then
    success "Application started with PM2"
    
    # Save PM2 configuration
    /usr/local/bin/pm2-wrapper.sh save
    
    # Setup startup script
    /usr/local/bin/pm2-wrapper.sh startup
    
else
    warning "PM2 failed, starting with Node.js directly..."
    
    # Fallback: Start with Node.js directly
    nohup npm start > /var/log/amazingpay.log 2>&1 &
    
    success "Application started with Node.js directly"
fi

# 🧪 STEP 10: TEST APPLICATION
log "Testing application..."

sleep 10

# Test health endpoint
if curl -f http://localhost:3002/api/health >/dev/null 2>&1; then
    success "✅ Health check PASSED"
else
    warning "⚠️ Health check failed, but application may still be starting"
fi

# 📊 STEP 11: DISPLAY STATUS
echo ""
echo -e "${CYAN}📊 DEPLOYMENT STATUS${NC}"
echo "==================="
echo ""

# Show PM2 status if available
if command -v pm2 >/dev/null 2>&1 || [ -f "/usr/local/bin/pm2" ]; then
    echo -e "${GREEN}PM2 Status:${NC}"
    /usr/local/bin/pm2-wrapper.sh status 2>/dev/null || echo "PM2 status not available"
else
    echo -e "${YELLOW}PM2 not available, checking Node.js processes:${NC}"
    ps aux | grep node | grep -v grep || echo "No Node.js processes found"
fi

echo ""
echo -e "${GREEN}✅ DEPLOYMENT COMPLETED!${NC}"
echo ""
echo -e "${CYAN}🔗 Application URLs:${NC}"
echo "  🏠 Local: http://localhost:3002"
echo "  🏥 Health: http://localhost:3002/api/health"
echo ""
echo -e "${CYAN}🔧 Management Commands:${NC}"
echo "  📊 Status: /usr/local/bin/pm2-wrapper.sh status"
echo "  📝 Logs: /usr/local/bin/pm2-wrapper.sh logs"
echo "  🔄 Restart: /usr/local/bin/pm2-wrapper.sh restart all"
echo "  🛑 Stop: /usr/local/bin/pm2-wrapper.sh stop all"
echo ""
echo -e "${CYAN}🌐 Next Steps:${NC}"
echo "  1. Configure domain in aaPanel dashboard"
echo "  2. Set up reverse proxy to port 3002"
echo "  3. Enable SSL certificate"
echo "  4. Test: https://amazingpayme.com"
echo ""

success "🎉 AmazingPay deployment completed successfully!"
