import { Server as HttpServer } from "http";
import { Server as WebSocketServer } from "socket.io";
import { VerificationStatus } from '../types';
export declare const verificationEvents: any;
export interface PaymentVerificationMessage {
    paymentId: string;
    merchantId: string;
    status: VerificationStatus;
    timestamp: string;
    verificationMethod?: string;
    message?: string;
    transactionDetails?: Record<string, unknown>;
}
/**
 * WebSocket service for payment verification
 */
export declare class VerificationWebSocketService {
    private static instance;
    private io;
    private connectedClients;
    /**
   * Get singleton instance
   */
    static getInstance(): VerificationWebSocketService;
    /**
   * Initialize WebSocket server
   * @param httpServer HTTP server
   */
    initialize(httpServer: HttpServer): WebSocketServer;
    /**
   * Handle WebSocket connection
   * @param socket Socket
   */
    private handleConnection;
}
//# sourceMappingURL=verificationWebSocketService.d.ts.map