{"errors": {"count": 64, "issues": [{"config": "tsconfig.zero-errors.json", "count": 64, "sample": "src/controllers/admin/index.ts(2,33): error TS2307: Cannot find module './ AdminController' or its corresponding type declarations.\r\nsrc/controllers/admin/index.ts(4,43): error TS2307: Cannot find module './ services / AdminAuthorizationService' or its corresponding type declarations.\r\nsrc/controllers/admin/index.ts(5,40): error TS2307: Cannot find module './ services / AdminValidationService' or its corresponding type declarations.\r\nsrc/controllers/admin/index.ts(6,38): error TS2307: Cannot find module './ services / AdminBusinessService' or its corresponding type declarations.\r\nsrc/controllers/admin/index.ts(8,37): error TS2307: Cannot find module './ mappers / AdminResponseMapper' or its corresponding type declarations.\r"}]}, "duplication": {"count": 0, "issues": []}, "unsafeTypes": {"count": 0, "issues": []}, "unknownTypes": {"count": 0, "issues": []}, "redundantTypes": {"count": 0, "issues": []}, "codeQuality": {"count": 0, "issues": []}, "security": {"count": 0, "issues": []}}