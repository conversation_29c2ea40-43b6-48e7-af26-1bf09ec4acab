// jscpd:ignore-file
import axios from "axios";
import { BaseService as ImportedBaseService } from "../base/BaseService";
import { logger as Importedlogger } from "../../utils/logger";
import { config as Importedconfig } from "../../config";
import { getEnvironment as ImportedgetEnvironment } from "../../config/environment";
import { logger as Importedlogger } from "../../utils/logger";
import { config as Importedconfig } from "../../config";
import { getEnvironment as ImportedgetEnvironment } from "../../config/environment";
import {
    getApiKey,
    getApiUrl,
    logServiceConfig
} from "../../utils/service-config";
import { ServiceError as ImportedServiceError } from "../../utils/errors/ServiceError";
import { Transaction as ImportedTransaction } from '../types';
import {
    getApiKey,
    getApiUrl,
    logServiceConfig
} from "../../utils/service-config";
import { ServiceError as ImportedServiceError } from "../../utils/errors/ServiceError";
import { Transaction as ImportedTransaction } from '../types';


/**
 * Blockchain network types
 */
export enum BlockchainNetwork {
  TRC20 = "trc20",
  ERC20 = "erc20",
  BEP20 = "bep20",
  POLYGON = "polygon",
}

/**
 * Blockchain transaction status
 */
export enum BlockchainTransactionStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  FAILED = "failed",
  NOT_FOUND = "not_found",
}

/**
 * Blockchain transaction
 */
export interface BlockchainTransaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  valueInUSD?: number;
  timestamp: number;
  blockNumber?: number;
  confirmations?: number;
  status: BlockchainTransactionStatus;
  network: BlockchainNetwork;
  tokenSymbol?: string;
  tokenDecimals?: number;
  fee?: string;
  gasPrice?: string;
  gasUsed?: string;
}

/**
 * Blockchain API response
 */
export interface BlockchainApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Network configuration
 */
interface NetworkConfig {
  apiKey: string;
  apiUrl: string;
  explorerUrl: string;
}

/**
 * Service for interacting with blockchain APIs
 */
export class BlockchainApiService extends BaseService {
    private networkConfigs: Map<BlockchainNetwork, NetworkConfig>;
    private defaultRetryConfig: RetryConfig;

    /**
     * Create a new BlockchainApiService instance
     */
    constructor() {
        super();
        
        // Initialize network configurations
        this.networkConfigs = new Map();
        this.initializeNetworkConfigs();
        
        // Set default retry configuration
        this.defaultRetryConfig = {
            maxRetries: 3,
            initialDelayMs: 1000,
            maxDelayMs: 10000,
            retryStatusCodes: [408, 429, 500, 502, 503, 504],
            retryErrorCodes: ["ECONNABORTED", "ETIMEDOUT", "ECONNRESET", "ECONNREFUSED"]
        };
        
        logger.debug(`Initialized Blockchain API service for ${getEnvironment(} environment`);
    }
    
    /**
     * Initialize network configurations
     */
    private initializeNetworkConfigs(): void {
        // TRC20 (TRON)
        this.networkConfigs.set((BlockchainNetwork).TRC20, {
            apiKey: getApiKey("tron", config.blockchain.tronApiKey ?? ""),
            apiUrl: getApiUrl("tron", "https://(api).trongrid.io"),
            explorerUrl: "https://(tronscan).org"
        });
        
        // ERC20 (Ethereum)
        this.networkConfigs.set((BlockchainNetwork).ERC20, {
            apiKey: getApiKey("etherscan", config.blockchain.etherscanApiKey ?? ""),
            apiUrl: getApiUrl("etherscan", "https://(api).etherscan.io/api"),
            explorerUrl: "https://(etherscan).io"
        });
        
        // BEP20 (Binance Smart Chain)
        this.networkConfigs.set((BlockchainNetwork).BEP20, {
            apiKey: getApiKey("bscscan", config.blockchain.bscscanApiKey ?? ""),
            apiUrl: getApiUrl("bscscan", "https://(api).bscscan.com/api"),
            explorerUrl: "https://(bscscan).com"
        });
        
        // POLYGON
        this.networkConfigs.set((BlockchainNetwork).POLYGON, {
            apiKey: getApiKey("polygonscan", config.blockchain.polygonscanApiKey ?? ""),
            apiUrl: getApiUrl("polygonscan", "https://(api).polygonscan.com/api"),
            explorerUrl: "https://(polygonscan).com"
        });
        
        // Log service configuration (masked)
        logServiceConfig("tron");
        logServiceConfig("etherscan");
        logServiceConfig("bscscan");
        logServiceConfig("polygonscan");
    }
    
    /**
     * Get network configuration
     * @param network Blockchain network
     * @returns Network configuration
     */
    private getNetworkConfig(network: BlockchainNetwork): NetworkConfig {
        const config: Record<string, unknown> =this.networkConfigs.get(network);
        if (!config) {
            throw this.createError(`Unsupported blockchain network: ${network}`, 400, "BLOCKCHAIN_ERROR");
        }
        return config;
    }
    
    /**
     * Make a request with retry capability
     * @param config Request configuration
     * @param retryConfig Retry configuration
     * @returns Response data
     */
    private async makeRequestWithRetry<T>(
        config: Record<string, unknown>,
        retryConfig = this.defaultRetryConfig
    ): Promise<T> {
        const { maxRetries, initialDelayMs, maxDelayMs, retryStatusCodes, retryErrorCodes } = retryConfig;
        
        let lastError;
        let attempt: number = 0;
        
        while (attempt < maxRetries) {
            try {
                const response = await axios(config);
                return response.data;
            } catch (error) {
                lastError = error;
                
                // Check if we should retry based on status code or error code
                const shouldRetry =(error.response && (retryStatusCodes).includes(error.response.status) ||
                    (error.code && (retryErrorCodes).includes(error.code);
                
                if (!shouldRetry || attempt >= maxRetries - 1) {
                    break;
                }
                
                // Calculate exponential backoff delay
                const delay = Math.min(
                    initialDelayMs * Math.pow(2, attempt),
                    maxDelayMs
                );
                
                logger.debug(`Retrying request (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);
                await new Promise(resolve  =>  setTimeout(resolve, delay);
                
                attempt++;
            }
        }
        
        throw lastError;
    }
    
    /**
     * Verify a blockchain transaction
     * @param txHash Transaction hash
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address (for ERC20/BEP20/POLYGON)
     * @returns Verification result
     */
    async verifyTransaction(
        txHash: string,
        network: BlockchainNetwork,
        toAddress: string,
        amount: string,
        tokenAddress?: string
    ): Promise<BlockchainApiResponse<BlockchainTransaction>> {
        try {
            logger.info(`Verifying ${network} transaction: ${txHash}`);
            
            // Validate inputs
            if (!txHash || !network || !toAddress) {
                return {
                    success: false,
                    message: "Missing required parameters"
                };
            }
            
            // Get transaction details based on network
            switch (network) {
                case (BlockchainNetwork).TRC20:
                    return this.verifyTronTransaction(txHash, toAddress, amount);
                case (BlockchainNetwork).ERC20:
                case (BlockchainNetwork).BEP20:
                case (BlockchainNetwork).POLYGON:
                    return this.verifyEVMTransaction(txHash, network, toAddress, amount, tokenAddress);
                default:
                    return {
                        success: false,
                        message: `Unsupported blockchain network: ${network}`
                    };
            }
        } catch (error) {
            logger.error(`Error verifying transaction: ${error}`);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    
    /**
     * Verify a TRON transaction
     * @param txHash Transaction hash
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @returns Verification result
     */
    private async verifyTronTransaction(
        txHash: string,
        toAddress: string,
        amount: string
    ): Promise<BlockchainApiResponse<BlockchainTransaction>> {
        try {
            const networkConfig = this.getNetworkConfig((BlockchainNetwork).TRC20);
            const apiUrl =`${networkConfig).apiUrl}/v1/transactions/${txHash}`;
            
            const response = await this.makeRequestWithRetry({
                method: 'GET',
                url: apiUrl,
                headers: {
                    'TRON-PRO-API-KEY': (networkConfig).apiKey
                }
            });
            
            // Process and return the transaction details
            return this.processTronTransaction(response, toAddress, amount);
        } catch (error) {
            logger.error(`Error verifying TRON transaction: ${error}`);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    
    /**
     * Process TRON transaction response
     * @param response API response
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @returns Processed transaction
     */
    private processTronTransaction(
        response: any,
        toAddress: string,
        amount: string
    ): BlockchainApiResponse<BlockchainTransaction> {
        // Implementation details for processing TRON transaction
        // This would be specific to the TRON API response format
        
        // For now, return a placeholder
        return {
            success: true,
            data: { hash: (response).txID ?? '',
                from: (response).from ?? '',
                to: (response).to ?? '',
                value: (response).amount || '0',
                timestamp: (response).timestamp ?? 0,
                status: (BlockchainTransactionStatus).CONFIRMED,
                network: (BlockchainNetwork).TRC20
            }
        };
    }
    
    /**
     * Verify an EVM transaction (Ethereum, BSC, Polygon)
     * @param txHash Transaction hash
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address
     * @returns Verification result
     */
    private async verifyEVMTransaction(
        txHash: string,
        network: BlockchainNetwork,
        toAddress: string,
        amount: string,
        tokenAddress?: string
    ): Promise<BlockchainApiResponse<BlockchainTransaction>> {
        try {
            const networkConfig = this.getNetworkConfig(network);
            
            // Different API endpoint based on whether it's a token transfer or native currency
            const apiUrl =tokenAddress
                ? `${networkConfig).apiUrl}?module=account&action=tokentx&txhash=${txHash}&apikey=${networkConfig).apiKey}`
                : `${networkConfig).apiUrl}?module=proxy&action=eth_getTransactionReceipt&txhash=${txHash}&apikey=${networkConfig).apiKey}`;
            
            const response = await this.makeRequestWithRetry({
                method: 'GET',
                url: apiUrl
            });
            
            // Process and return the transaction details
            return this.processEVMTransaction(response, network, toAddress, amount, tokenAddress);
        } catch (error) {
            logger.error(`Error verifying ${network} transaction: ${error}`);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    
    /**
     * Process EVM transaction response
     * @param response API response
     * @param network Blockchain network
     * @param toAddress Recipient address
     * @param amount Expected amount
     * @param tokenAddress Token contract address
     * @returns Processed transaction
     */
    private processEVMTransaction(
        response: any,
        network: BlockchainNetwork,
        toAddress: string,
        amount: string,
        tokenAddress?: string
    ): BlockchainApiResponse<BlockchainTransaction> {
        // Implementation details for processing EVM transaction
        // This would be specific to the EVM API response format
        
        // For now, return a placeholder
        return {
            success: true,
            data: { hash: (response).hash ?? '',
                from: (response).from ?? '',
                to: (response).to ?? '',
                value: (response).value || '0',
                timestamp: (response).timeStamp ?? 0,
                status: (BlockchainTransactionStatus).CONFIRMED,
                network: network
            }
        };
    }
}

/**
 * Retry configuration
 */
interface RetryConfig {
    maxRetries: number;
    initialDelayMs: number;
    maxDelayMs: number;
    retryStatusCodes: number[];
    retryErrorCodes: string[];
}
