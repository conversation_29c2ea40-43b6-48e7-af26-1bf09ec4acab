{"summary": {"startTime": "2025-05-27T23:31:11.670Z", "duration": "5133ms", "initialErrors": 11935, "finalErrors": 11781, "errorsFixed": 154, "successRate": "1.3", "filesProcessed": 477, "filesWithFixes": 251, "totalFixes": 370}, "files": [{"file": "src\\index.ts", "fixes": 1}, {"file": "src\\config\\index.ts", "fixes": 1}, {"file": "src\\lib\\logger.ts", "fixes": 1}, {"file": "src\\lib\\prisma.ts", "fixes": 2}, {"file": "src\\config\\api\\production.api.config.ts", "fixes": 1}, {"file": "src\\config\\auth.ts", "fixes": 1}, {"file": "src\\config\\database\\production.config.ts", "fixes": 1}, {"file": "src\\config\\database.config.ts", "fixes": 1}, {"file": "src\\config\\env.config.ts", "fixes": 1}, {"file": "src\\config\\environment\\production.config.ts", "fixes": 2}, {"file": "src\\config\\index.ts", "fixes": 1}, {"file": "src\\config\\security\\production.security.config.ts", "fixes": 1}, {"file": "src\\config\\verification\\PredefinedVerificationPolicies.ts", "fixes": 1}, {"file": "src\\controllers\\admin\\mappers\\AdminResponseMapper.ts", "fixes": 1}, {"file": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "fixes": 2}, {"file": "src\\controllers\\admin\\services\\AdminBusinessService.ts", "fixes": 2}, {"file": "src\\controllers\\admin\\services\\AdminValidationService.ts", "fixes": 2}, {"file": "src\\controllers\\advanced-report.controller.ts", "fixes": 2}, {"file": "src\\controllers\\alert-aggregation\\mappers\\ResponseMapper.ts", "fixes": 1}, {"file": "src\\controllers\\alert-aggregation\\services\\AlertAggregationBusinessService.ts", "fixes": 2}, {"file": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "fixes": 2}, {"file": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "fixes": 2}, {"file": "src\\controllers\\alert-analytics.controller.ts", "fixes": 2}, {"file": "src\\controllers\\alert.controller.ts", "fixes": 2}, {"file": "src\\controllers\\AlertAggregationController.ts", "fixes": 2}, {"file": "src\\controllers\\AlertController.ts", "fixes": 2}, {"file": "src\\controllers\\auth.controller.ts", "fixes": 1}, {"file": "src\\controllers\\base.controller.ts", "fixes": 2}, {"file": "src\\controllers\\binance-pay-webhook.controller.ts", "fixes": 1}, {"file": "src\\controllers\\binance-verification.controller.ts", "fixes": 1}, {"file": "src\\controllers\\binance.controller.ts", "fixes": 1}, {"file": "src\\controllers\\blockchain-verification.controller.ts", "fixes": 1}, {"file": "src\\controllers\\dashboard-widget.controller.ts", "fixes": 2}, {"file": "src\\controllers\\dashboard.controller.ts", "fixes": 2}, {"file": "src\\controllers\\email.controller.ts", "fixes": 1}, {"file": "src\\controllers\\enhanced-payment.controller.ts", "fixes": 1}, {"file": "src\\controllers\\enhanced-risk-engine.controller.ts", "fixes": 2}, {"file": "src\\controllers\\enhanced-verification.controller.ts", "fixes": 1}, {"file": "src\\controllers\\fee-management.controller.ts", "fixes": 1}, {"file": "src\\controllers\\fraud-detection\\mappers\\FraudDetectionResponseMapper.ts", "fixes": 1}, {"file": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "fixes": 2}, {"file": "src\\controllers\\fraud-detection\\services\\FraudDetectionBusinessService.ts", "fixes": 1}, {"file": "src\\controllers\\fraud-detection\\validators\\BaseValidator.ts", "fixes": 1}, {"file": "src\\controllers\\fraud-detection\\validators\\FraudConfigValidator.ts", "fixes": 2}, {"file": "src\\controllers\\fraud-detection\\validators\\TransactionRiskValidator.ts", "fixes": 2}, {"file": "src\\controllers\\health.controller.ts", "fixes": 1}, {"file": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "fixes": 2}, {"file": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "fixes": 1}, {"file": "src\\controllers\\log.controller.ts", "fixes": 2}, {"file": "src\\controllers\\merchant\\MerchantController.ts", "fixes": 1}, {"file": "src\\controllers\\merchant-relationship.controller.ts", "fixes": 2}, {"file": "src\\controllers\\merchant-segmentation.controller.ts", "fixes": 1}, {"file": "src\\controllers\\merchant-self-service.controller.ts", "fixes": 1}, {"file": "src\\controllers\\merchant-subscription.controller.ts", "fixes": 2}, {"file": "src\\controllers\\merchant.controller.ts", "fixes": 2}, {"file": "src\\controllers\\monitoring\\verification-monitoring.controller.ts", "fixes": 1}, {"file": "src\\controllers\\monitoring.controller.ts", "fixes": 1}, {"file": "src\\controllers\\multi-factor-auth.controller.ts", "fixes": 1}, {"file": "src\\controllers\\notification.controller.ts", "fixes": 1}, {"file": "src\\controllers\\operational-mode.controller.ts", "fixes": 1}, {"file": "src\\controllers\\optimization\\verification-optimization.controller.ts", "fixes": 1}, {"file": "src\\controllers\\payment-method.controller.ts", "fixes": 2}, {"file": "src\\controllers\\payment-page.controller.ts", "fixes": 1}, {"file": "src\\controllers\\payment-recommendation.controller.ts", "fixes": 3}, {"file": "src\\controllers\\payment-routing.controller.ts", "fixes": 2}, {"file": "src\\controllers\\payment-verification.controller.ts", "fixes": 2}, {"file": "src\\controllers\\payment.controller.ts", "fixes": 2}, {"file": "src\\controllers\\paymentVerificationController.ts", "fixes": 1}, {"file": "src\\controllers\\push-notification.controller.ts", "fixes": 2}, {"file": "src\\controllers\\sms.controller.ts", "fixes": 1}, {"file": "src\\controllers\\subscription-history.controller.ts", "fixes": 2}, {"file": "src\\controllers\\subscription-plans.controller.ts", "fixes": 1}, {"file": "src\\controllers\\subscription.controller.ts", "fixes": 1}, {"file": "src\\controllers\\system.controller.ts", "fixes": 1}, {"file": "src\\controllers\\telegram-webhook.controller.ts", "fixes": 1}, {"file": "src\\controllers\\transaction-analytics.controller.ts", "fixes": 1}, {"file": "src\\controllers\\transaction.controller.ts", "fixes": 2}, {"file": "src\\controllers\\user.controller.ts", "fixes": 2}, {"file": "src\\controllers\\verification-method.controller.ts", "fixes": 1}, {"file": "src\\controllers\\verification-policy.controller.ts", "fixes": 1}, {"file": "src\\controllers\\verification.controller.ts", "fixes": 1}, {"file": "src\\controllers\\version.controller.ts", "fixes": 1}, {"file": "src\\controllers\\webhook.controller.ts", "fixes": 1}, {"file": "src\\docs\\api-documentation.ts", "fixes": 1}, {"file": "src\\index.ts", "fixes": 1}, {"file": "src\\lib\\DIContainer.ts", "fixes": 1}, {"file": "src\\lib\\logger.ts", "fixes": 1}, {"file": "src\\lib\\mode-specific-db.ts", "fixes": 1}, {"file": "src\\lib\\ModuleRegistry.ts", "fixes": 1}, {"file": "src\\lib\\prisma-client.ts", "fixes": 2}, {"file": "src\\lib\\prisma.ts", "fixes": 2}, {"file": "src\\lib\\redis-manager.ts", "fixes": 2}, {"file": "src\\middlewares\\apiResponseMiddleware.ts", "fixes": 1}, {"file": "src\\middlewares\\audit.middleware.ts", "fixes": 2}, {"file": "src\\middlewares\\auth.middleware.ts", "fixes": 2}, {"file": "src\\middlewares\\auth.ts", "fixes": 2}, {"file": "src\\middlewares\\authorization.middleware.ts", "fixes": 2}, {"file": "src\\middlewares\\enhanced-auth.middleware.ts", "fixes": 1}, {"file": "src\\middlewares\\enhanced-error.middleware.ts", "fixes": 2}, {"file": "src\\middlewares\\enhanced-rate-limit.middleware.ts", "fixes": 2}, {"file": "src\\middlewares\\environment.middleware.ts", "fixes": 1}, {"file": "src\\middlewares\\error.middleware.ts", "fixes": 1}, {"file": "src\\middlewares\\errorHandler.middleware.ts", "fixes": 1}, {"file": "src\\middlewares\\errorHandler.ts", "fixes": 2}, {"file": "src\\middlewares\\performance-monitor.middleware.ts", "fixes": 1}, {"file": "src\\middlewares\\rate-limit.middleware.ts", "fixes": 1}, {"file": "src\\middlewares\\request-id.middleware.ts", "fixes": 1}, {"file": "src\\middlewares\\security.middleware.ts", "fixes": 2}, {"file": "src\\middlewares\\validation.middleware.ts", "fixes": 2}, {"file": "src\\middlewares\\versionMiddleware.ts", "fixes": 1}, {"file": "src\\modules\\example\\example.module.ts", "fixes": 1}, {"file": "src\\modules\\merchant\\merchant.module.ts", "fixes": 2}, {"file": "src\\modules\\payment\\payment.module.ts", "fixes": 2}, {"file": "src\\modules\\user\\user.module.ts", "fixes": 1}, {"file": "src\\modules\\webhook\\webhook.module.ts", "fixes": 2}, {"file": "src\\routes\\alerting.routes.ts", "fixes": 2}, {"file": "src\\routes\\health.routes.ts", "fixes": 1}, {"file": "src\\routes\\performance.routes.ts", "fixes": 1}, {"file": "src\\routes\\webhook.routes.ts", "fixes": 1}, {"file": "src\\security\\security-audit.ts", "fixes": 1}, {"file": "src\\services\\advanced-report.service.ts", "fixes": 2}, {"file": "src\\services\\alert-aggregation.service.ts", "fixes": 2}, {"file": "src\\services\\alert.service.ts", "fixes": 2}, {"file": "src\\services\\analytics\\ApiAnalyticsService.ts", "fixes": 2}, {"file": "src\\services\\analytics\\payment-analytics.service.ts", "fixes": 1}, {"file": "src\\services\\audit.service.ts", "fixes": 1}, {"file": "src\\services\\auth.service.ts", "fixes": 1}, {"file": "src\\services\\base.service.ts", "fixes": 2}, {"file": "src\\services\\binance-api.service.ts", "fixes": 2}, {"file": "src\\services\\binance-pay.service.ts", "fixes": 1}, {"file": "src\\services\\binance.service.ts", "fixes": 1}, {"file": "src\\services\\binanceApiService.ts", "fixes": 2}, {"file": "src\\services\\blockchain\\binance-api.service.ts", "fixes": 2}, {"file": "src\\services\\blockchain\\blockchain-api.service.ts", "fixes": 1}, {"file": "src\\services\\blockchain-api.service.ts", "fixes": 2}, {"file": "src\\services\\blockchain-verification.service.ts", "fixes": 2}, {"file": "src\\services\\cache\\analytics-cache.service.ts", "fixes": 2}, {"file": "src\\services\\email.service.ts", "fixes": 2}, {"file": "src\\services\\enhanced-risk-engine.service.implementation.ts", "fixes": 2}, {"file": "src\\services\\enhanced-risk-engine.service.ts", "fixes": 2}, {"file": "src\\services\\enhanced-subscription.service.ts", "fixes": 2}, {"file": "src\\services\\example.service.ts", "fixes": 2}, {"file": "src\\services\\fee-management.service.ts", "fixes": 2}, {"file": "src\\services\\fraud-detection\\core\\FraudDetectionService.ts", "fixes": 2}, {"file": "src\\services\\fraud-detection\\detectors\\AmountRiskDetector.ts", "fixes": 2}, {"file": "src\\services\\fraud-detection\\detectors\\VelocityRiskDetector.ts", "fixes": 2}, {"file": "src\\services\\fraud-detection\\rules\\RiskRuleEngine.ts", "fixes": 2}, {"file": "src\\services\\fraud-detection.service.ts", "fixes": 2}, {"file": "src\\services\\identity-verification\\blockchain-identity.service.ts", "fixes": 1}, {"file": "src\\services\\identity-verification\\core\\IdentityVerificationService.ts", "fixes": 1}, {"file": "src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts", "fixes": 2}, {"file": "src\\services\\identity-verification\\utils\\BlockchainUtils.ts", "fixes": 1}, {"file": "src\\services\\identity-verification.service.ts", "fixes": 2}, {"file": "src\\services\\merchant-relationship.service.ts", "fixes": 1}, {"file": "src\\services\\merchant-segmentation.service.ts", "fixes": 1}, {"file": "src\\services\\merchant-self-service.service.ts", "fixes": 1}, {"file": "src\\services\\merchant.service.ts", "fixes": 2}, {"file": "src\\services\\monitoring\\payment-monitoring.service.ts", "fixes": 2}, {"file": "src\\services\\monitoring\\verification-alert.service.ts", "fixes": 1}, {"file": "src\\services\\monitoring\\verification-monitoring.service.ts", "fixes": 1}, {"file": "src\\services\\monitoring.service.ts", "fixes": 2}, {"file": "src\\services\\multi-factor-auth.service.ts", "fixes": 1}, {"file": "src\\services\\notification\\alert-notification.service.ts", "fixes": 1}, {"file": "src\\services\\notification.service.ts", "fixes": 2}, {"file": "src\\services\\optimization\\verification-optimization.service.ts", "fixes": 1}, {"file": "src\\services\\payment\\fees\\strategies\\CommonFeeStrategies.ts", "fixes": 2}, {"file": "src\\services\\payment\\gateways\\BinanceGateway.ts", "fixes": 2}, {"file": "src\\services\\payment\\methods\\BinanceTRC20PaymentMethod.ts", "fixes": 2}, {"file": "src\\services\\payment\\routing\\rules\\CommonRoutingRules.ts", "fixes": 1}, {"file": "src\\services\\payment-method.service.ts", "fixes": 1}, {"file": "src\\services\\payment-page.service.ts", "fixes": 2}, {"file": "src\\services\\payment-recommendation.service.ts", "fixes": 2}, {"file": "src\\services\\payment-routing.service.ts", "fixes": 2}, {"file": "src\\services\\payment-verification.service.ts", "fixes": 1}, {"file": "src\\services\\payment.service.ts", "fixes": 1}, {"file": "src\\services\\paymentVerificationService.ts", "fixes": 1}, {"file": "src\\services\\push-notification.service.ts", "fixes": 2}, {"file": "src\\services\\rbac\\RBACInitializer.ts", "fixes": 2}, {"file": "src\\services\\rbac.service.ts", "fixes": 2}, {"file": "src\\services\\report-optimization.service.ts", "fixes": 2}, {"file": "src\\services\\reporting\\core\\ReportService.ts", "fixes": 1}, {"file": "src\\services\\reporting\\export\\CSVExporter.ts", "fixes": 2}, {"file": "src\\services\\reporting\\generators\\TransactionReportGenerator.ts", "fixes": 2}, {"file": "src\\services\\reporting\\scheduling\\ReportScheduler.ts", "fixes": 1}, {"file": "src\\services\\sms.service.ts", "fixes": 1}, {"file": "src\\services\\subscription.service.ts", "fixes": 1}, {"file": "src\\services\\system\\OperationalModeService.ts", "fixes": 1}, {"file": "src\\services\\telegram.service.ts", "fixes": 2}, {"file": "src\\services\\transaction.service.ts", "fixes": 1}, {"file": "src\\services\\two-factor-auth.service.ts", "fixes": 1}, {"file": "src\\services\\user.service.ts", "fixes": 2}, {"file": "src\\services\\validation\\paymentMethodValidator.ts", "fixes": 2}, {"file": "src\\services\\verification\\binance-verification.service.ts", "fixes": 2}, {"file": "src\\services\\verification\\blockchain-verification.service.ts", "fixes": 1}, {"file": "src\\services\\verification\\policy\\VerificationPolicy.ts", "fixes": 1}, {"file": "src\\services\\verification\\strategies\\BinanceTRC20VerificationStrategy.ts", "fixes": 2}, {"file": "src\\services\\verification\\unified-verification.service.ts", "fixes": 1}, {"file": "src\\services\\verification\\VerificationChain.ts", "fixes": 1}, {"file": "src\\services\\verification-method.service.ts", "fixes": 2}, {"file": "src\\services\\verification.service.ts", "fixes": 1}, {"file": "src\\services\\webhook.service.ts", "fixes": 1}, {"file": "src\\services\\websocket\\verification-realtime.service.ts", "fixes": 1}, {"file": "src\\shared\\modules\\controllers\\BaseController.ts", "fixes": 1}, {"file": "src\\shared\\modules\\utils\\responseUtils.ts", "fixes": 1}, {"file": "src\\tests\\e2e\\advanced-reporting.e2e.test.ts", "fixes": 1}, {"file": "src\\tests\\integration\\advanced-reporting-integration.test.ts", "fixes": 1}, {"file": "src\\tests\\integration\\shared-modules.test.ts", "fixes": 1}, {"file": "src\\tests\\load-test.ts", "fixes": 2}, {"file": "src\\tests\\performance\\performance.test.ts", "fixes": 1}, {"file": "src\\tests\\security-test.ts", "fixes": 1}, {"file": "src\\tests\\setup.ts", "fixes": 2}, {"file": "src\\tests\\utils\\assertions\\CustomAssertions.ts", "fixes": 3}, {"file": "src\\tests\\utils\\factories\\MockFactories.ts", "fixes": 2}, {"file": "src\\tests\\utils\\runners\\TestRunners.ts", "fixes": 1}, {"file": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "fixes": 2}, {"file": "src\\tests\\utils\\TestUtility.ts", "fixes": 2}, {"file": "src\\utils\\cache-manager.ts", "fixes": 1}, {"file": "src\\utils\\cache.ts", "fixes": 2}, {"file": "src\\utils\\common.ts", "fixes": 2}, {"file": "src\\utils\\controller-utils.ts", "fixes": 2}, {"file": "src\\utils\\controller.utils.ts", "fixes": 2}, {"file": "src\\utils\\controllerUtils.ts", "fixes": 1}, {"file": "src\\utils\\CryptoUtils.ts", "fixes": 1}, {"file": "src\\utils\\csrf.ts", "fixes": 2}, {"file": "src\\utils\\database-initializer.ts", "fixes": 2}, {"file": "src\\utils\\database-verifier.ts", "fixes": 1}, {"file": "src\\utils\\encryption.ts", "fixes": 1}, {"file": "src\\utils\\error-handler.ts", "fixes": 2}, {"file": "src\\utils\\errorHandling.ts", "fixes": 1}, {"file": "src\\utils\\errors\\AppError.ts", "fixes": 1}, {"file": "src\\utils\\errors\\ServiceError.ts", "fixes": 1}, {"file": "src\\utils\\formatting.ts", "fixes": 1}, {"file": "src\\utils\\health-monitor.ts", "fixes": 2}, {"file": "src\\utils\\jwt.ts", "fixes": 1}, {"file": "src\\utils\\jwt.utils.ts", "fixes": 1}, {"file": "src\\utils\\log-rotation.ts", "fixes": 1}, {"file": "src\\utils\\memory-store.ts", "fixes": 2}, {"file": "src\\utils\\migration-manager.ts", "fixes": 1}, {"file": "src\\utils\\monitoring.ts", "fixes": 1}, {"file": "src\\utils\\object.ts", "fixes": 2}, {"file": "src\\utils\\repositoryUtils.ts", "fixes": 2}, {"file": "src\\utils\\response\\ResponseFactory.ts", "fixes": 1}, {"file": "src\\utils\\secrets-manager.ts", "fixes": 1}, {"file": "src\\utils\\service-config.ts", "fixes": 2}, {"file": "src\\utils\\test-connection.ts", "fixes": 1}, {"file": "src\\utils\\test-db-connection.ts", "fixes": 1}, {"file": "src\\utils\\validation\\RequestValidator.ts", "fixes": 1}, {"file": "src\\utils\\validation.ts", "fixes": 2}, {"file": "src\\utils\\verification-error-handler.ts", "fixes": 2}, {"file": "src\\utils\\verification-utils.ts", "fixes": 2}, {"file": "src\\utils\\websocket-monitor.ts", "fixes": 1}], "errors": []}