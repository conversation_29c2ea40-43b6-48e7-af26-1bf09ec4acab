#!/bin/bash

# AmazingPay Flow - Comprehensive Status Check
# This script provides a complete overview of your system status

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🚀 AmazingPay Flow - System Status Dashboard${NC}"
    echo -e "${BLUE}==========================================\n${NC}"
}

print_section() {
    echo -e "${CYAN}📦 $1${NC}"
    echo "----------------------------------------"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to check application status
check_application() {
    print_section "APPLICATION STATUS"
    
    # Check if PM2 is running
    if command -v pm2 &> /dev/null; then
        if pm2 list | grep -q "amazingpay-flow"; then
            print_success "PM2 processes running"
            pm2 list | grep amazingpay-flow | head -4
        else
            print_warning "PM2 processes not found"
        fi
    else
        print_warning "PM2 not installed"
    fi
    
    # Check health endpoint
    if curl -f http://localhost:3002/api/health > /dev/null 2>&1; then
        print_success "Health endpoint responding"
        print_info "URL: http://localhost:3002/api/health"
    else
        print_error "Health endpoint not responding"
    fi
    
    # Check API endpoint
    if curl -f http://localhost:3002/api > /dev/null 2>&1; then
        print_success "API endpoint responding"
        print_info "URL: http://localhost:3002/api"
    else
        print_error "API endpoint not responding"
    fi
    
    echo ""
}

# Function to check database status
check_database() {
    print_section "DATABASE STATUS"
    
    if [ -f ".env.production" ]; then
        source .env.production
        
        if [ -n "$DATABASE_URL" ]; then
            print_success "Database URL configured"
            
            # Test database connection
            if command -v psql &> /dev/null; then
                if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
                    print_success "Database connection successful"
                    
                    # Get database stats
                    USER_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM \"User\";" 2>/dev/null | xargs)
                    SETTING_COUNT=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM \"SystemSetting\";" 2>/dev/null | xargs)
                    
                    print_info "Users: $USER_COUNT"
                    print_info "Settings: $SETTING_COUNT"
                else
                    print_error "Database connection failed"
                fi
            else
                print_warning "psql not available for testing"
            fi
        else
            print_error "DATABASE_URL not configured"
        fi
    else
        print_error ".env.production file not found"
    fi
    
    echo ""
}

# Function to check files and configuration
check_configuration() {
    print_section "CONFIGURATION STATUS"
    
    # Check essential files
    if [ -f "package.json" ]; then
        print_success "package.json found"
    else
        print_error "package.json missing"
    fi
    
    if [ -f "tsconfig.json" ]; then
        print_success "tsconfig.json found"
    else
        print_error "tsconfig.json missing"
    fi
    
    if [ -f "prisma/schema.prisma" ]; then
        print_success "Prisma schema found"
    else
        print_error "Prisma schema missing"
    fi
    
    if [ -f ".env.production" ]; then
        print_success ".env.production found"
    else
        print_error ".env.production missing"
    fi
    
    if [ -f "ecosystem.config.js" ]; then
        print_success "PM2 config found"
    else
        print_warning "PM2 config missing"
    fi
    
    if [ -f "docker-compose.yml" ]; then
        print_success "Docker Compose config found"
    else
        print_warning "Docker Compose config missing"
    fi
    
    echo ""
}

# Function to check available scripts
check_scripts() {
    print_section "AVAILABLE SCRIPTS"
    
    SCRIPT_DIR="scripts"
    if [ -d "$SCRIPT_DIR" ]; then
        print_success "Scripts directory found"
        
        # List key scripts
        KEY_SCRIPTS=(
            "production-manager.sh"
            "setup-external-services.sh"
            "setup-monitoring.sh"
            "setup-domain-ssl.sh"
            "backup-system.sh"
            "health-check.sh"
        )
        
        for script in "${KEY_SCRIPTS[@]}"; do
            if [ -f "$SCRIPT_DIR/$script" ]; then
                if [ -x "$SCRIPT_DIR/$script" ]; then
                    print_success "$script (executable)"
                else
                    print_warning "$script (not executable)"
                fi
            else
                print_error "$script (missing)"
            fi
        done
    else
        print_error "Scripts directory not found"
    fi
    
    echo ""
}

# Function to check system resources
check_resources() {
    print_section "SYSTEM RESOURCES"
    
    # Check disk space
    DISK_USAGE=$(df . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $DISK_USAGE -lt 80 ]; then
        print_success "Disk space: $DISK_USAGE% used"
    elif [ $DISK_USAGE -lt 90 ]; then
        print_warning "Disk space: $DISK_USAGE% used"
    else
        print_error "Disk space: $DISK_USAGE% used (critical)"
    fi
    
    # Check memory (if available)
    if command -v free &> /dev/null; then
        MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        if [ $MEMORY_USAGE -lt 80 ]; then
            print_success "Memory usage: $MEMORY_USAGE%"
        elif [ $MEMORY_USAGE -lt 90 ]; then
            print_warning "Memory usage: $MEMORY_USAGE%"
        else
            print_error "Memory usage: $MEMORY_USAGE% (critical)"
        fi
    fi
    
    # Check if ports are in use
    if command -v lsof &> /dev/null; then
        if lsof -i :3002 > /dev/null 2>&1; then
            print_success "Port 3002 in use"
        else
            print_warning "Port 3002 not in use"
        fi
    fi
    
    echo ""
}

# Function to show next steps
show_next_steps() {
    print_section "RECOMMENDED NEXT STEPS"
    
    echo "Choose your path:"
    echo ""
    echo "🏠 PATH 1: Complete Local Setup"
    echo "   bash scripts/setup-external-services.sh"
    echo "   bash scripts/setup-monitoring.sh"
    echo ""
    echo "🌐 PATH 2: Domain & SSL"
    echo "   bash scripts/setup-domain-ssl.sh"
    echo "   docker-compose up -d nginx"
    echo ""
    echo "☁️  PATH 3: Cloud Deployment"
    echo "   Deploy to Heroku/DigitalOcean/AWS"
    echo ""
    echo "🐳 PATH 4: Docker Full Stack"
    echo "   docker-compose --profile monitoring up -d"
    echo ""
    echo "📖 For detailed guide: cat NEXT_STEPS_GUIDE.md"
    echo ""
}

# Function to show quick commands
show_quick_commands() {
    print_section "QUICK COMMANDS"
    
    echo "Application Management:"
    echo "  bash scripts/production-manager.sh status"
    echo "  bash scripts/production-manager.sh logs"
    echo "  bash scripts/production-manager.sh restart"
    echo ""
    echo "Health & Monitoring:"
    echo "  bash scripts/health-check.sh"
    echo "  bash scripts/backup-system.sh"
    echo ""
    echo "Configuration:"
    echo "  bash scripts/setup-external-services.sh"
    echo "  bash scripts/setup-monitoring.sh"
    echo ""
}

# Main execution
main() {
    print_header
    
    check_application
    check_database
    check_configuration
    check_scripts
    check_resources
    show_next_steps
    show_quick_commands
    
    echo -e "${GREEN}🎯 Status check completed! Your AmazingPay Flow is ready for the next step! 🚀${NC}"
}

# Run status check
main "$@"
