#!/bin/bash

# 🔄 AMAZINGPAY VPS UPDATE SCRIPT
# Handles automated updates from GitHub with rollback capability
# VPS: ************ | Domain: amazingpayme.com

set -e

# 🎯 CONFIGURATION
APP_DIR="/www/wwwroot/amazingpayme.com"
BACKUP_DIR="/var/backups/amazingpay"
LOG_DIR="/var/log/amazingpay"
REPO_URL="https://github.com/your-username/amazingpay-flow.git"
BRANCH="${1:-main}"

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_DIR/deployment.log"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_DIR/deployment.log"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_DIR/deployment.log"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_DIR/deployment.log"
    exit 1
}

# 🔍 PRE-DEPLOYMENT CHECKS
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if application is running
    if ! pm2 list | grep -q "amazingpay"; then
        warning "Application not running, will start after deployment"
    fi
    
    # Check disk space
    DISK_USAGE=$(df /www | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 85 ]; then
        error "Disk usage is ${DISK_USAGE}%. Please free up space before deployment."
    fi
    
    # Check database connection
    if ! sudo -u postgres psql -d Amazingpay -c "SELECT 1;" &> /dev/null; then
        error "Database connection failed"
    fi
    
    success "Pre-deployment checks passed"
}

# 💾 CREATE BACKUP
create_backup() {
    local backup_name="$1"
    log "Creating backup: $backup_name"
    
    mkdir -p "$BACKUP_DIR/$backup_name"
    
    # Backup application files
    log "Backing up application files..."
    rsync -av --exclude=node_modules --exclude=.git "$APP_DIR/" "$BACKUP_DIR/$backup_name/app/"
    
    # Backup database
    log "Backing up database..."
    sudo -u postgres pg_dump Amazingpay > "$BACKUP_DIR/$backup_name/database.sql"
    
    # Backup environment
    cp "$APP_DIR/.env.production" "$BACKUP_DIR/$backup_name/.env.production" 2>/dev/null || true
    
    # Create backup metadata
    cat > "$BACKUP_DIR/$backup_name/metadata.json" << EOF
{
  "timestamp": "$(date -Iseconds)",
  "commit": "$(cd $APP_DIR && git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "branch": "$BRANCH",
  "backup_type": "pre-deployment"
}
EOF
    
    success "Backup created: $backup_name"
}

# 📥 PULL LATEST CHANGES
pull_changes() {
    log "Pulling latest changes from GitHub..."
    
    cd "$APP_DIR"
    
    # Stash any local changes
    git stash push -m "Auto-stash before deployment $(date)"
    
    # Fetch latest changes
    git fetch origin
    
    # Get current commit for rollback reference
    PREVIOUS_COMMIT=$(git rev-parse HEAD)
    echo "$PREVIOUS_COMMIT" > /tmp/previous_commit
    
    # Checkout latest version
    git checkout "$BRANCH"
    git pull origin "$BRANCH"
    
    # Get new commit
    NEW_COMMIT=$(git rev-parse HEAD)
    
    log "Updated from $PREVIOUS_COMMIT to $NEW_COMMIT"
    success "Code updated successfully"
}

# 📦 INSTALL DEPENDENCIES
install_dependencies() {
    log "Installing dependencies..."
    
    cd "$APP_DIR"
    
    # Clean install for production
    npm ci --production --silent
    
    success "Dependencies installed"
}

# 🗄️ DATABASE MIGRATIONS
run_migrations() {
    log "Running database migrations..."
    
    cd "$APP_DIR"
    
    # Generate Prisma client
    npx prisma generate
    
    # Run migrations
    npx prisma migrate deploy
    
    success "Database migrations completed"
}

# 🔨 BUILD APPLICATION
build_application() {
    log "Building application..."
    
    cd "$APP_DIR"
    
    # Build the application
    npm run build
    
    success "Application built successfully"
}

# 🚀 RESTART APPLICATION
restart_application() {
    log "Restarting application..."
    
    cd "$APP_DIR"
    
    # Reload PM2 configuration
    pm2 reload ecosystem.config.js --env production
    
    # Wait for application to start
    sleep 10
    
    success "Application restarted"
}

# 🏥 HEALTH CHECK
health_check() {
    log "Performing health check..."
    
    local max_attempts=6
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:3002/api/health > /dev/null; then
            success "Health check passed"
            return 0
        fi
        
        warning "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 10
        ((attempt++))
    done
    
    error "Health check failed after $max_attempts attempts"
}

# 🔄 ROLLBACK FUNCTION
rollback() {
    local backup_name="$1"
    error "Deployment failed, initiating rollback to: $backup_name"
    
    log "Stopping application..."
    pm2 stop amazingpay || true
    
    log "Restoring application files..."
    rsync -av --delete "$BACKUP_DIR/$backup_name/app/" "$APP_DIR/"
    
    log "Restoring database..."
    if [ -f "$BACKUP_DIR/$backup_name/database.sql" ]; then
        sudo -u postgres psql -d Amazingpay < "$BACKUP_DIR/$backup_name/database.sql"
    fi
    
    log "Restoring environment..."
    if [ -f "$BACKUP_DIR/$backup_name/.env.production" ]; then
        cp "$BACKUP_DIR/$backup_name/.env.production" "$APP_DIR/.env.production"
    fi
    
    log "Restarting application..."
    cd "$APP_DIR"
    pm2 start ecosystem.config.js --env production
    
    success "Rollback completed"
}

# 📊 DEPLOYMENT SUMMARY
deployment_summary() {
    log "Deployment Summary"
    echo "===================="
    echo "🕐 Timestamp: $(date)"
    echo "🌿 Branch: $BRANCH"
    echo "📝 Commit: $(cd $APP_DIR && git rev-parse HEAD)"
    echo "🔗 URL: https://amazingpayme.com"
    echo "🏥 Health: $(curl -s https://amazingpayme.com/api/health | jq -r '.status' 2>/dev/null || echo 'Unknown')"
    echo "===================="
}

# 🎯 MAIN DEPLOYMENT FUNCTION
main() {
    local backup_name="deploy-$(date +%Y%m%d-%H%M%S)"
    
    log "🚀 Starting AmazingPay VPS Update"
    log "Branch: $BRANCH"
    log "Backup: $backup_name"
    
    # Set trap for rollback on failure
    trap "rollback $backup_name" ERR
    
    # Execute deployment steps
    pre_deployment_checks
    create_backup "$backup_name"
    pull_changes
    install_dependencies
    run_migrations
    build_application
    restart_application
    health_check
    
    # Clear trap on success
    trap - ERR
    
    success "🎉 Deployment completed successfully!"
    deployment_summary
    
    # Clean up old backups (keep last 10)
    find "$BACKUP_DIR" -maxdepth 1 -type d -name "deploy-*" | sort | head -n -10 | xargs rm -rf 2>/dev/null || true
}

# 🔧 SCRIPT EXECUTION
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Ensure log directory exists
    mkdir -p "$LOG_DIR"
    
    # Run main function
    main "$@"
fi
