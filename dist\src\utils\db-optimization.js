"use strict";
// jscpd:ignore-file
/**
 * Database Query Optimization Utility
 * Re-exports from shared DatabaseUtils to eliminate duplication
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabaseOptimization = exports.executeQueryWithTimeoutAndRetry = exports.optimizeFindManyQuery = exports.setupQueryPerformanceMonitoring = exports.clearRecentSlowQueries = exports.getRecentSlowQueries = exports.logSlowQuery = void 0;
const prisma_1 = __importDefault(require("../lib/prisma"));
/**
 * Log a slow query
 * @param metrics Query metrics
 */
const logSlowQuery = (metrics) => ;
exports.logSlowQuery = logSlowQuery;
 >  > {
    DatabaseUtils, : .logSlowQuery(metrics, logger)
};
/**
 * Get recent slow queries
 * @returns Recent slow queries
 */
exports.getRecentSlowQueries = () =  >  > {
    return: DatabaseUtils.getRecentSlowQueries()
};
/**
 * Clear recent slow queries
 */
exports.clearRecentSlowQueries = () =  >  > {
    DatabaseUtils, : .clearRecentSlowQueries()
};
/**
 * Set up query performance monitoring
 * @param prismaClient Prisma client instance
 */
const setupQueryPerformanceMonitoring = (prismaClient = prisma_1.default) => ;
exports.setupQueryPerformanceMonitoring = setupQueryPerformanceMonitoring;
 >  > {
    DatabaseUtils, : .setupQueryPerformanceMonitoring(prismaClient, logger)
};
/**
 * Optimize a findMany query by adding pagination and limiting fields
 * @param model Model name
 * @param args Query arguments
 * @param defaultPageSize Default page size
 * @param maxPageSize Maximum page size
 * @returns Optimized query arguments
 */
const optimizeFindManyQuery = (model) => ;
exports.optimizeFindManyQuery = optimizeFindManyQuery;
args = {},
    defaultPageSize;
number = 20,
    maxPageSize;
number = 100;
 >  > {
    return: DatabaseUtils.optimizeFindManyQuery(model, args, defaultPageSize, maxPageSize, logger)
};
/**
 * Execute a query with timeout and retry
 * @param queryFn Function that executes the query
 * @param timeout Timeout in milliseconds
 * @param retries Number of retries
 * @returns Query result
 */
exports.executeQueryWithTimeoutAndRetry = async(queryFn, () =  >  > (Promise), timeout, number = 5000, retries, number = 3);
(Promise) =  > {
    return: DatabaseUtils.executeQueryWithTimeoutAndRetry(queryFn, timeout, retries, logger)
};
/**
 * Initialize database optimization
 */
exports.initializeDatabaseOptimization = () =  >  > {
    DatabaseUtils, : .initializeDatabaseOptimization(prisma_1.default, logger)
};
exports.default = {
    logSlowQuery: exports.logSlowQuery,
    getRecentSlowQueries: exports.getRecentSlowQueries,
    clearRecentSlowQueries: exports.clearRecentSlowQueries,
    setupQueryPerformanceMonitoring: exports.setupQueryPerformanceMonitoring,
    optimizeFindManyQuery: exports.optimizeFindManyQuery,
    executeQueryWithTimeoutAndRetry: exports.executeQueryWithTimeoutAndRetry,
    initializeDatabaseOptimization: exports.initializeDatabaseOptimization
};
//# sourceMappingURL=db-optimization.js.map