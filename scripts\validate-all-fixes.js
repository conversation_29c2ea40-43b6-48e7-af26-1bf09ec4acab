#!/usr/bin/env node

/**
 * VALIDATE ALL FIXES SCRIPT
 * Comprehensive validation of all code quality fixes
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 VALIDATING ALL CODE QUALITY FIXES');
console.log('====================================');
console.log('📋 Checking: SonarLint issues, TypeScript errors, Code quality');
console.log('');

const validationResults = {
    typescript: { perfect: false, errors: 0 },
    sonarLint: { perfect: false, issues: [] },
    codeQuality: { perfect: false, score: 0 },
    spelling: { perfect: false, issues: 0 },
    security: { perfect: false, vulnerabilities: 0 }
};

function validateTypeScriptErrors() {
    console.log('🔧 1. TypeScript Error Validation');
    console.log('==================================');
    
    const configs = ['tsconfig.json', 'tsconfig.ultimate-zero.json', 'tsconfig.zero-errors.json'];
    let totalErrors = 0;
    
    for (const config of configs) {
        if (fs.existsSync(config)) {
            try {
                const output = execSync(`npx tsc --project ${config} --noEmit --skipLibCheck 2>&1`, { 
                    encoding: 'utf8',
                    timeout: 60000
                });
                
                const errorMatches = output.match(/error TS/g) || [];
                const errorCount = errorMatches.length;
                totalErrors += errorCount;
                
                if (errorCount === 0) {
                    console.log(`✅ ${config}: ZERO errors`);
                } else {
                    console.log(`❌ ${config}: ${errorCount} errors`);
                }
            } catch (error) {
                const errorOutput = error.stdout || error.stderr || '';
                const errorMatches = errorOutput.match(/error TS/g) || [];
                totalErrors += errorMatches.length;
            }
        }
    }
    
    validationResults.typescript.errors = totalErrors;
    validationResults.typescript.perfect = totalErrors === 0;
    
    console.log(`📊 Total TypeScript errors: ${totalErrors}`);
    return totalErrors === 0;
}

function validateSonarLintFixes() {
    console.log('\n🔧 2. SonarLint Issue Validation');
    console.log('=================================');
    
    const filesToCheck = [
        'scripts/final-comprehensive-audit.js',
        'src/config/database.config.ts'
    ];
    
    let issuesFound = [];
    
    for (const filePath of filesToCheck) {
        if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath, 'utf8');
            
            // Check for proper exception handling
            const emptycatches = content.match(/catch\s*\([^)]*\)\s*{\s*\/\/[^}]*}\s*}/g) || [];
            if (emptycatches.length > 0) {
                issuesFound.push({
                    file: filePath,
                    issue: 'Empty catch blocks found',
                    count: emptycatches.length
                });
            }
            
            // Check for nullish coalescing usage
            const logicalOr = content.match(/process\.env\.[A-Z_]+\s*\|\|\s*['"]/g) || [];
            if (logicalOr.length > 0) {
                issuesFound.push({
                    file: filePath,
                    issue: 'Logical OR instead of nullish coalescing',
                    count: logicalOr.length
                });
            }
        }
    }
    
    validationResults.sonarLint.issues = issuesFound;
    validationResults.sonarLint.perfect = issuesFound.length === 0;
    
    if (issuesFound.length === 0) {
        console.log('✅ All SonarLint issues resolved');
    } else {
        console.log(`❌ ${issuesFound.length} SonarLint issues remaining:`);
        issuesFound.forEach(issue => {
            console.log(`   ${issue.file}: ${issue.issue} (${issue.count})`);
        });
    }
    
    return issuesFound.length === 0;
}

function validateSpellingFixes() {
    console.log('\n🔧 3. Spelling Issue Validation');
    console.log('================================');
    
    // Check if cspell.json exists and contains Amazingpay
    if (fs.existsSync('cspell.json')) {
        try {
            const cspellConfig = JSON.parse(fs.readFileSync('cspell.json', 'utf8'));
            const hasAmazingpay = cspellConfig.words && cspellConfig.words.includes('Amazingpay');
            
            if (hasAmazingpay) {
                console.log('✅ Spelling dictionary updated with "Amazingpay"');
                validationResults.spelling.perfect = true;
                validationResults.spelling.issues = 0;
            } else {
                console.log('❌ "Amazingpay" not found in spelling dictionary');
                validationResults.spelling.issues = 1;
            }
        } catch (error) {
            console.log('❌ Error reading cspell.json');
            validationResults.spelling.issues = 1;
        }
    } else {
        console.log('❌ cspell.json not found');
        validationResults.spelling.issues = 1;
    }
    
    return validationResults.spelling.perfect;
}

function validateCodeQuality() {
    console.log('\n🔧 4. Code Quality Validation');
    console.log('==============================');
    
    let qualityScore = 0;
    const maxScore = 5;
    
    // Check for proper type usage
    const authFile = 'src/config/auth.ts';
    if (fs.existsSync(authFile)) {
        const content = fs.readFileSync(authFile, 'utf8');
        
        // Check for proper JWT type definitions
        if (content.includes('JwtSignFunction') && !content.includes('as any')) {
            qualityScore++;
            console.log('✅ Proper JWT type definitions used');
        } else {
            console.log('❌ Unsafe type usage detected');
        }
        
        // Check for proper error handling
        if (content.includes('AppError') && content.includes('throw new')) {
            qualityScore++;
            console.log('✅ Proper error handling implemented');
        } else {
            console.log('❌ Improper error handling');
        }
    }
    
    // Check for nullish coalescing usage
    const dbFile = 'src/config/database.config.ts';
    if (fs.existsSync(dbFile)) {
        const content = fs.readFileSync(dbFile, 'utf8');
        
        const nullishCount = (content.match(/\?\?/g) || []).length;
        const logicalOrCount = (content.match(/process\.env\.[A-Z_]+\s*\|\|/g) || []).length;
        
        if (nullishCount > 0 && logicalOrCount === 0) {
            qualityScore++;
            console.log('✅ Proper nullish coalescing operators used');
        } else {
            console.log('❌ Inconsistent operator usage');
        }
    }
    
    // Check for proper exception handling in audit script
    const auditFile = 'scripts/final-comprehensive-audit.js';
    if (fs.existsSync(auditFile)) {
        const content = fs.readFileSync(auditFile, 'utf8');
        
        const properCatches = content.match(/catch\s*\([^)]*\)\s*{\s*console\.(warn|log|error)/g) || [];
        if (properCatches.length >= 4) {
            qualityScore++;
            console.log('✅ Proper exception handling in audit script');
        } else {
            console.log('❌ Improper exception handling');
        }
    }
    
    // Check for cSpell configuration
    if (fs.existsSync('cspell.json')) {
        qualityScore++;
        console.log('✅ Spelling configuration present');
    } else {
        console.log('❌ Missing spelling configuration');
    }
    
    validationResults.codeQuality.score = qualityScore;
    validationResults.codeQuality.perfect = qualityScore === maxScore;
    
    console.log(`📊 Code quality score: ${qualityScore}/${maxScore}`);
    return qualityScore === maxScore;
}

function generateValidationReport() {
    console.log('\n🏆 VALIDATION REPORT');
    console.log('====================');
    
    const allPerfect = validationResults.typescript.perfect &&
                      validationResults.sonarLint.perfect &&
                      validationResults.spelling.perfect &&
                      validationResults.codeQuality.perfect;
    
    console.log(`\n🎯 OVERALL STATUS: ${allPerfect ? '🏆 ALL FIXES VALIDATED' : '⚠️  SOME ISSUES REMAIN'}`);
    console.log('================================================');
    
    console.log('\n📋 DETAILED VALIDATION RESULTS:');
    console.log(`   🔴 TypeScript Errors: ${validationResults.typescript.perfect ? '✅ FIXED' : '❌ REMAINING'} (${validationResults.typescript.errors})`);
    console.log(`   🟠 SonarLint Issues: ${validationResults.sonarLint.perfect ? '✅ FIXED' : '❌ REMAINING'} (${validationResults.sonarLint.issues.length})`);
    console.log(`   🟣 Spelling Issues: ${validationResults.spelling.perfect ? '✅ FIXED' : '❌ REMAINING'} (${validationResults.spelling.issues})`);
    console.log(`   🟢 Code Quality: ${validationResults.codeQuality.perfect ? '✅ PERFECT' : '❌ NEEDS WORK'} (${validationResults.codeQuality.score}/5)`);
    
    if (allPerfect) {
        console.log('\n🎉 ALL CODE QUALITY ISSUES RESOLVED!');
        console.log('====================================');
        console.log('✅ Zero TypeScript compilation errors');
        console.log('✅ All SonarLint issues fixed');
        console.log('✅ Spelling dictionary updated');
        console.log('✅ Perfect code quality achieved');
        console.log('✅ Enterprise-grade standards met');
        console.log('');
        console.log('🚀 CODEBASE STATUS: PRODUCTION READY WITH PERFECT QUALITY');
    } else {
        console.log('\n⚠️  REMAINING ISSUES TO ADDRESS:');
        console.log('=================================');
        
        if (!validationResults.typescript.perfect) {
            console.log('🔴 Fix remaining TypeScript errors');
        }
        if (!validationResults.sonarLint.perfect) {
            console.log('🟠 Address remaining SonarLint issues');
        }
        if (!validationResults.spelling.perfect) {
            console.log('🟣 Update spelling configuration');
        }
        if (!validationResults.codeQuality.perfect) {
            console.log('🟢 Improve code quality standards');
        }
    }
    
    // Save validation report
    const reportPath = 'validation-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(validationResults, null, 2), 'utf8');
    console.log(`\n📄 Validation report saved to: ${reportPath}`);
    
    return allPerfect;
}

async function main() {
    console.log('🚀 Starting comprehensive validation...\n');
    
    // Run all validations
    const tsValid = validateTypeScriptErrors();
    const sonarValid = validateSonarLintFixes();
    const spellingValid = validateSpellingFixes();
    const qualityValid = validateCodeQuality();
    
    // Generate final report
    const allValid = generateValidationReport();
    
    console.log('\n🎊 VALIDATION COMPLETE!');
    console.log('=======================');
    
    if (allValid) {
        console.log('🏆 ALL FIXES VALIDATED - PERFECT QUALITY ACHIEVED!');
        process.exit(0);
    } else {
        console.log('⚠️  Some issues remain - review recommendations above');
        process.exit(1);
    }
}

main().catch(console.error);
