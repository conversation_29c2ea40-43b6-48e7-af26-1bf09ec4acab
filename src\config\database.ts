// jscpd:ignore-file;
import { logger } from '../lib/logger';
import { secretsManager } from '../utils/secrets-manager';
import prisma from '../lib/prisma'; /**; * Test database connection; * Uses the connection string from secrets manager; */;
export const testConnection = async (): Promise<boolean> => { try { // Initialize secrets manager if not already initialized; await secretsManager.initialize(); // Connect to database; await prisma.$connect(); logger.info('Database connection established successfully'); return true; } catch (error) { logger.error(`Database connection failed: ${error}`); return false; } }; /**; * Get database connection URL; * This is used by Prisma client; */;
export const getDatabaseUrl = (): string => { return secretsManager.getDatabaseUrl(); }; export default prisma;