# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables (CRITICAL SECURITY)
.env
.env.local
.env.production
.env.staging
.env.development
*.env
.env.*

# Secrets and sensitive configuration
secrets/
config/secrets.json
config/production.json
*.key
*.pem
*.p12
*.pfx

# Build outputs
dist/
build/
coverage/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Security audit reports
security-audit-report.json
audit-report.json
