# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables (CRITICAL SECURITY)
.env
.env.local
.env.production
.env.staging
.env.development
*.env
.env.*

# Secrets and sensitive configuration
secrets/
config/secrets.json
config/production.json
*.key
*.pem
*.p12
*.pfx

# Build outputs
dist/
build/
coverage/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Security audit reports
security-audit-report.json
audit-report.json

# 🚨 FINANCIAL APPLICATION SECURITY - NEVER COMMIT THESE
# Deployment and infrastructure
ecosystem.config.js
ecosystem.*.config.js
docker-compose.override.yml
docker-compose.production.yml
deploy-*.sh
vps-setup.sh
backup-*.sh
upload-to-github.*

# Database files
*.db
*.sqlite
*.sqlite3
prisma/dev.db
database.json

# SSL/TLS certificates
*.crt
*.cert
*.ca-bundle
ssl/

# Payment gateway configurations
payment-config.json
stripe-config.json
paypal-config.json

# Banking and financial data
bank-config.json
financial-reports/
transaction-logs/
audit-trails/

# Server configurations
nginx.conf
apache.conf
.htaccess
server-config/

# Backup files
*.backup
*.bak
backups/

# Temporary files
tmp/
temp/
.tmp/
