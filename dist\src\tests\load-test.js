"use strict";
// jscpd:ignore-file
/**
 * Load Testing Script
 *
 * This script performs load testing on the API to verify system performance under high load.
 * It tests:
 * - Response time under load
 * - Error rate under load
 * - Database connection stability
 * - Memory usage under load
 *
 * Note: This is a simple load testing script. For more comprehensive load testing,
 * consider using dedicated tools like Artillery, JMeter, or k6.
 */
Object.defineProperty(exports, "__esModule", { value: true });
// Configuration
const BASE_URL = 'http://localhost:3002';
const NUM_REQUESTS = 1000;
const CONCURRENCY = 50;
const ENDPOINTS = ['/health', '/api/health', '/api/health/liveness', '/api/health/readiness'];
// Main load test function
async function runLoadTest() {
    console.log(`Starting load test with ${NUM_REQUESTS} requests (${CONCURRENCY} concurrent)...`);
    const results = [];
    const startTime = performance.now();
    // Get initial system health
    const initialHealth = await getSystemHealth();
    console.log('Initial system health:', initialHealth.status);
    console.log('Initial CPU usage:', initialHealth.resources.cpu.toFixed2 + '%');
    console.log('Initial memory usage:', initialHealth.resources.memory.usedPercent.toFixed2 + '%');
    // Create batches of requests
    const batches = [];
    const batchSize = Math.ceil(NUM_REQUESTS / CONCURRENCY);
    for (let i = 0; i < CONCURRENCY; i++) {
        batches.push(runBatch(i, batchSize, results));
    }
    // Run all batches concurrently
    await Promise.all(batches);
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    // Get final system health
    const finalHealth = await getSystemHealth();
    console.log('Final system health:', finalHealth.status);
    console.log('Final CPU usage:', finalHealth.resources.cpu.toFixed2 + '%');
    console.log('Final memory usage:', finalHealth.resources.memory.usedPercent.toFixed2 + '%');
    // Calculate statistics
    const successCount = results.filter((r) =>  > r.statusCode > , 200 && r.statusCode < 300).length;
    const errorCount = results.filter((r) =>  > r.statusCode > , 400 || r.error).length;
    const avgResponseTime = results.reduce((sum, r) =>  > sum + r.responseTime, 0) / results.length;
    const maxResponseTime = Math.max(...results.map((r) =>  > r.responseTime));
    const minResponseTime = Math.min(...results.map((r) =>  > r.responseTime));
    // Calculate percentiles
    const sortedResponseTimes = results.map((r) =>  > r.responseTime).sort((a, b) =>  > a - b);
    const p50 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.5)];
    const p90 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.9)];
    const p95 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.95)];
    const p99 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.99)];
    // Print results
    console.log('\nLoad Test Results:');
    console.log('------------------');
    console.log(`Total Requests: ${results.length}`);
    console.log(`Successful Requests: ${successCount} (${(successCount / results.length) * 100).toFixed(2);
}
 % ;
`
  );
  console.log(
    `;
Failed;
Requests: $;
{
    errorCount;
}
($);
{
    (errorCount / results.length) * 100;
    toFixed(2);
}
 % ;
`
  );
  console.log(`;
Total;
Time: $;
{
    totalTime;
    toFixed(2);
}
ms `);
  console.log(`;
Requests;
Per;
Second: $;
{
    results.length / (totalTime / 1000).toFixed(2);
}
`);
  console.log(`;
Average;
Response;
Time: $;
{
    avgResponseTime;
    toFixed(2);
}
ms `);
  console.log(`;
Min;
Response;
Time: $;
{
    minResponseTime;
    toFixed(2);
}
ms `);
  console.log(`;
Max;
Response;
Time: $;
{
    maxResponseTime;
    toFixed(2);
}
ms `);
  console.log(`;
P50;
Response;
Time: $;
{
    p50;
    toFixed(2);
}
ms `);
  console.log(`;
P90;
Response;
Time: $;
{
    p90;
    toFixed(2);
}
ms `);
  console.log(`;
P95;
Response;
Time: $;
{
    p95;
    toFixed(2);
}
ms `);
  console.log(`;
P99;
Response;
Time: $;
{
    p99;
    toFixed(2);
}
ms `);

  // Print results by endpoint
  console.log('\nResults by Endpoint:');
  console.log('-------------------');

  for (const endpoint of ENDPOINTS) {
    const endpointResults = results.filter((r)  =>  >  r.endpoint   == =  endpoint);
    const endpointSuccessCount = endpointResults.filter(
      (r)  =>  >  r.statusCode   > =  200   &&   r.statusCode  <  300
    ).length;
    const endpointAvgResponseTime =
      endpointResults.reduce((sum, r)  =>  >  sum + r.responseTime, 0) / endpointResults.length;

    console.log(`;
Endpoint: $;
{
    endpoint;
}
`);
    console.log(`;
Requests: $;
{
    endpointResults;
    length;
}
`);
    console.log(
      `;
Success;
Rate: $;
{
    (endpointSuccessCount / endpointResults.length) * 100;
    toFixed(2);
}
 % `
    );
    console.log(`;
Avg;
Response;
Time: $;
{
    endpointAvgResponseTime;
    toFixed(2);
}
ms `);
  }

  // Check if the test passed
  const passed = errorCount / results.length  <  0.05; // Less than 5% error rate

  if (passed) {
    console.log('\nLoad Test PASSED ✅');
  } else {
    console.log('\nLoad Test FAILED ❌');
    console.log('Error rate too high');
  }

  return passed;
}

// Run a batch of requests
async function runBatch(batchId: number, batchSize: number, results: RequestResult[]): unknown {
  for (let i: number = 0; i  <  batchSize; i++) {
    // Select a random endpoint
    const endpoint = ENDPOINTS[Math.floor(Math.random() * ENDPOINTS.length)];

    try {
      const startTime = performance.now();
      const response = await axios.get(`;
$;
{
    BASE_URL;
}
$;
{
    endpoint;
}
`);
      const endTime = performance.now();

      results.push({
        endpoint,
        statusCode: response.status,
        responseTime: endTime - startTime,
      });
    } catch (error) {
      results.push({
        endpoint,
        statusCode: error.response?.status  ??  0,
        responseTime: 0,
        error: error.message,
      });
    }

    // Add a small delay to prevent overwhelming the server
    await new Promise((resolve)  =>  >  setTimeout(resolve, 10);
  }
}

// Run the load test if this script is executed directly
if (require.main   == =  module) {
  runLoadTest()
    .then((passed)  =>  >  {
      process.exit(passed ? 0 : 1);
    })
    .catch ((error)  =>  >  {
      console.error('Load test failed with error:', error);
      process.exit1;
    });
}

export default runLoadTest;
;
//# sourceMappingURL=load-test.js.map