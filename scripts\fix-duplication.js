#!/usr/bin/env node

/**
 * Fix Duplication Script
 * 
 * This script helps fix duplication issues by providing guidance and automated fixes
 * for common duplication patterns.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import readline from 'readline';

// Create readline interface for user interaction
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Run jscpd to find duplications
console.log('Running duplication check...');
let duplications = [];
let duplicationFound = false;

try {
  // Run jscpd with JSON reporter to get structured output
  execSync('npx jscpd . --config .jscpd.json --reporters json --output .jscpd-temp', { stdio: 'inherit' });
  
  // Read the JSON report
  const reportPath = path.resolve('.jscpd-temp', 'jscpd-report.json');
  if (fs.existsSync(reportPath)) {
    const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    duplications = report.duplicates || [];
    duplicationFound = duplications.length > 0;
    
    // Clean up temporary report
    fs.rmSync('.jscpd-temp', { recursive: true, force: true });
  }
} catch (error) {
  // If jscpd fails due to high duplication, try to read the report anyway
  const reportPath = path.resolve('.jscpd-temp', 'jscpd-report.json');
  if (fs.existsSync(reportPath)) {
    try {
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      duplications = report.duplicates || [];
      duplicationFound = duplications.length > 0;
      
      // Clean up temporary report
      fs.rmSync('.jscpd-temp', { recursive: true, force: true });
    } catch (readError) {
      console.error('Error reading duplication report:', readError.message);
    }
  } else {
    console.error('Error running duplication check:', error.message);
  }
}

if (!duplicationFound) {
  console.log('No duplication found! Your codebase is clean.');
  process.exit(0);
}

console.log(`Found ${duplications.length} duplications. Let's fix them!`);

// Group duplications by file
const duplicationsByFile = {};
for (const duplication of duplications) {
  const sourceFile = duplication.firstFile.name;
  const targetFile = duplication.secondFile.name;
  
  if (!duplicationsByFile[sourceFile]) {
    duplicationsByFile[sourceFile] = [];
  }
  
  if (!duplicationsByFile[targetFile]) {
    duplicationsByFile[targetFile] = [];
  }
  
  duplicationsByFile[sourceFile].push({
    otherFile: targetFile,
    fragment: duplication.fragment,
    sourceLines: {
      start: duplication.firstFile.start,
      end: duplication.firstFile.end
    }
  });
  
  duplicationsByFile[targetFile].push({
    otherFile: sourceFile,
    fragment: duplication.fragment,
    sourceLines: {
      start: duplication.secondFile.start,
      end: duplication.secondFile.end
    }
  });
}

// Sort files by number of duplications
const sortedFiles = Object.keys(duplicationsByFile).sort((a, b) => {
  return duplicationsByFile[b].length - duplicationsByFile[a].length;
});

// Function to suggest fixes for a file
function suggestFixes(file, duplications) {
  console.log(`\nAnalyzing duplications in ${file}...`);
  
  // Check if it's a controller
  if (file.includes('/controllers/') && !file.includes('/shared/')) {
    console.log('This appears to be a controller file. Consider these fixes:');
    console.log('1. Extend the BaseController or CrudController from shared modules');
    console.log('2. Replace duplicated methods with calls to the base class methods');
    console.log('3. Example:');
    console.log('   Before: async getAllItems(req, res) { ... }');
    console.log('   After:  async getAllItems(req, res) { return this.getAll(req, res, this.itemService); }');
    return true;
  }
  
  // Check if it's a service
  if (file.includes('/services/') && !file.includes('/shared/')) {
    console.log('This appears to be a service file. Consider these fixes:');
    console.log('1. Extend the BaseService from shared modules');
    console.log('2. Replace duplicated methods with calls to the base class methods');
    console.log('3. Extract common functionality into shared utility functions');
    return true;
  }
  
  // Check if it's a utility file
  if (file.includes('/utils/') && !file.includes('/shared/')) {
    console.log('This appears to be a utility file. Consider these fixes:');
    console.log('1. Move duplicated utility functions to shared/modules/utils');
    console.log('2. Import the shared utility functions instead of duplicating them');
    return true;
  }
  
  // Generic suggestions
  console.log('Consider these general fixes:');
  console.log('1. Extract duplicated code into a shared function or class');
  console.log('2. Use composition or inheritance to reuse code');
  console.log('3. Consider if the duplication is necessary - sometimes minimal duplication is acceptable for clarity');
  
  return false;
}

// Function to process each file
async function processFile(file) {
  return new Promise((resolve) => {
    console.log(`\n=============================================`);
    console.log(`File: ${file}`);
    console.log(`Found ${duplicationsByFile[file].length} duplications with other files`);
    
    // List duplications
    duplicationsByFile[file].forEach((dup, index) => {
      console.log(`\n${index + 1}. Duplication with ${dup.otherFile}`);
      console.log(`   Lines ${dup.sourceLines.start}-${dup.sourceLines.end}`);
      console.log(`   Fragment preview: ${dup.fragment.slice(0, 100)}...`);
    });
    
    // Suggest fixes
    const hasSuggestions = suggestFixes(file, duplicationsByFile[file]);
    
    // Ask if user wants to fix this file
    rl.question('\nDo you want to fix this file? (y/n): ', (answer) => {
      if (answer.toLowerCase() === 'y') {
        console.log('\nPlease make the suggested changes to fix the duplications.');
        console.log('You can use the following commands:');
        console.log('1. Open the file in your editor');
        console.log('2. Apply the suggested fixes');
        console.log('3. Run "npm run check:duplication" to verify the fixes');
      }
      resolve();
    });
  });
}

// Main function to process all files
async function main() {
  console.log('\n=== Duplication Fix Assistant ===\n');
  console.log('This tool will help you fix duplication issues in your codebase.');
  console.log('For each file with duplications, suggestions will be provided.');
  
  // Process top 5 files with most duplications
  const filesToProcess = sortedFiles.slice(0, 5);
  for (const file of filesToProcess) {
    await processFile(file);
  }
  
  console.log('\n=== Summary ===');
  console.log(`Total files with duplications: ${sortedFiles.length}`);
  console.log(`Files analyzed: ${filesToProcess.length}`);
  console.log(`Remaining files to fix: ${sortedFiles.length - filesToProcess.length}`);
  
  console.log('\nTo fix remaining duplications:');
  console.log('1. Run "npm run check:duplication" to check current status');
  console.log('2. Run "npm run check:duplication:fix" to run this tool again');
  console.log('3. Consider using shared modules for common functionality');
  
  rl.close();
}

// Run the main function
main().catch(error => {
  console.error('Error:', error);
  rl.close();
  process.exit(1);
});
