/**
 * Service Configuration Utility
 *
 * This utility provides functions for managing environment-specific third-party service configurations
 * to ensure complete isolation between production and demo environments.
 */
/**
 * Get environment-specific service configuration
 * @param serviceName Name of the service
 * @param configKey Configuration key
 * @param defaultValue Default value if not found
 * @returns Environment-specific configuration value
 */
export declare const getServiceConfig: T, string: any, configKey: string, defaultValue: any, T: any;
//# sourceMappingURL=service-config.d.ts.map