# 🚀 AmazingPay Production Deployment

Complete production deployment guide for VPS with aaPanel.

## 📋 Prerequisites

- Ubuntu/Debian VPS with root access
- aaPanel installed and configured
- Domain pointing to VPS IP (************)
- Node.js 16+ installed
- PostgreSQL installed

## 🎯 Automated One-Command Deployment

```bash
# SSH to your VPS
ssh root@************

# Run the automated deployment script
curl -sSL https://raw.githubusercontent.com/Amazingteam-eg/Amazingpayflow/main/deploy.sh | bash
```

**This single command will:**

- ✅ Validate environment and dependencies
- ✅ Clean any previous installations
- ✅ Clone latest code from GitHub
- ✅ Install all dependencies automatically
- ✅ Configure PostgreSQL database
- ✅ Build TypeScript application
- ✅ Start with PM2 process management
- ✅ Configure Nginx reverse proxy
- ✅ Run health checks and validation
- ✅ Provide complete status report

## 📁 Manual Deployment

```bash
# Clone the repository
git clone https://github.com/Amazingteam-eg/Amazingpayflow.git /www/wwwroot/Amazingpayflow
cd /www/wwwroot/Amazingpayflow

# Make script executable and run
chmod +x deploy.sh
./deploy.sh
```

## 🔧 What the Script Does

1. **Environment Validation** - Checks prerequisites
2. **Clean Installation** - Removes previous installations
3. **Project Setup** - Clones from GitHub and sets permissions
4. **Dependencies** - Installs Node.js packages and PM2
5. **Database Setup** - Configures PostgreSQL
6. **Environment Config** - Creates production .env file
7. **Build Application** - Compiles TypeScript and runs migrations
8. **Start Services** - Launches with PM2
9. **Nginx Configuration** - Sets up reverse proxy
10. **Validation** - Tests deployment

## 📊 Post-Deployment

### Application URLs

- **Main Application:** https://amazingpayme.com
- **Health Check:** https://amazingpayme.com/api/health
- **API Documentation:** https://amazingpayme.com/api/docs

### Management Commands

```bash
# Check application status
pm2 status

# View logs
pm2 logs

# Restart application
pm2 restart amazingpay

# Stop application
pm2 stop amazingpay
```

### File Structure

```
/www/wwwroot/Amazingpayflow/
├── src/                    # Source code
├── dist/                   # Built application
├── public/                 # Static files
├── logs/                   # Application logs
├── .env                    # Environment configuration
└── ecosystem.config.js     # PM2 configuration
```

## 🔒 Security

The deployment includes:

- Environment variable protection
- Nginx security headers
- File access restrictions
- Process isolation with PM2
- Database connection security

## 📞 Troubleshooting

### Common Issues

**Application not starting:**

```bash
pm2 logs
pm2 restart amazingpay
```

**Database connection issues:**

```bash
sudo systemctl status postgresql
sudo systemctl restart postgresql
```

**Domain not accessible:**

```bash
sudo nginx -t
sudo systemctl reload nginx
```

### Health Check

```bash
curl http://localhost:3002/api/health
```

Should return:

```json
{
  "status": "healthy",
  "timestamp": "2024-...",
  "environment": "production",
  "version": "1.0.0"
}
```

## 🎉 Success

After successful deployment:

- ✅ Application running on PM2
- ✅ Database configured and migrated
- ✅ Domain accessible via HTTPS
- ✅ Health checks passing
- ✅ Logs being generated

Your AmazingPay payment gateway is now live! 🌟
