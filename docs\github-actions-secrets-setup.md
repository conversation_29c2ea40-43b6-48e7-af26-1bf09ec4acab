# GitHub Actions Secrets Configuration Guide

## 🔐 Required Secrets for Full Workflow Functionality

Your GitHub Actions workflow is now fully fixed and validated, but requires these secrets to be configured for complete functionality.

## 📋 **REQUIRED SECRETS**

### **1. VPS Deployment Secrets**

Configure these in your GitHub repository: **Settings → Secrets and variables → Actions → Repository secrets**

#### **VPS_HOST**
- **Description**: Your VPS server hostname or IP address
- **Example**: `***********` or `amazingpay.com`
- **Required for**: SSH deployment to your production server

#### **VPS_USER**
- **Description**: SSH username for your VPS server
- **Example**: `root`, `ubuntu`, or `amazingpay`
- **Required for**: SSH authentication

#### **VPS_SSH_KEY**
- **Description**: Private SSH key for server access
- **Format**: Complete private key including headers
- **Example**:
```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAFwAAAAdzc2gtcn
...
-----END OPENSSH PRIVATE KEY-----
```

### **2. Slack Notifications (Optional)**

#### **SLACK_WEBHOOK**
- **Description**: Slack webhook URL for deployment notifications
- **Example**: `*****************************************************************************`
- **Required for**: Slack notifications on deployment status

## 🚀 **SETUP INSTRUCTIONS**

### **Step 1: Configure VPS SSH Access**

1. **Generate SSH Key Pair** (if you don't have one):
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

2. **Copy Public Key to VPS**:
```bash
ssh-copy-id -i ~/.ssh/id_rsa.pub user@your-vps-ip
```

3. **Test SSH Connection**:
```bash
ssh -i ~/.ssh/id_rsa user@your-vps-ip
```

### **Step 2: Add Secrets to GitHub**

1. Go to your repository on GitHub
2. Click **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**
4. Add each secret:

#### **VPS_HOST**
- Name: `VPS_HOST`
- Value: Your server IP or hostname

#### **VPS_USER**
- Name: `VPS_USER`
- Value: Your SSH username

#### **VPS_SSH_KEY**
- Name: `VPS_SSH_KEY`
- Value: Your complete private SSH key (copy entire content of `~/.ssh/id_rsa`)

#### **SLACK_WEBHOOK** (Optional)
- Name: `SLACK_WEBHOOK`
- Value: Your Slack webhook URL

### **Step 3: Configure Slack Webhook (Optional)**

1. Go to your Slack workspace
2. Create a new app at https://api.slack.com/apps
3. Enable **Incoming Webhooks**
4. Create a webhook for your desired channel
5. Copy the webhook URL to GitHub secrets

## ✅ **WORKFLOW BEHAVIOR**

### **With Secrets Configured:**
- ✅ **VPS Deployment**: Automatically deploys to your server via SSH
- ✅ **Slack Notifications**: Sends deployment status to Slack channels
- ✅ **Rollback Capability**: Automatic rollback on deployment failure
- ✅ **Health Checks**: Validates deployment success

### **Without Secrets:**
- ⚠️ **VPS Deployment**: Skipped with informative message
- ⚠️ **Slack Notifications**: Skipped silently
- ✅ **Build & Test**: All other workflow steps continue normally
- ✅ **Docker Images**: Still built and pushed to registry

## 🛡️ **SECURITY BEST PRACTICES**

### **SSH Key Security:**
- ✅ Use dedicated SSH keys for GitHub Actions
- ✅ Restrict key permissions on VPS (add to `authorized_keys` only)
- ✅ Consider using SSH key with passphrase for extra security
- ✅ Regularly rotate SSH keys

### **Slack Webhook Security:**
- ✅ Use workspace-specific webhooks
- ✅ Limit webhook permissions to specific channels
- ✅ Monitor webhook usage in Slack audit logs

### **VPS Security:**
- ✅ Use non-root user for deployments when possible
- ✅ Configure firewall to allow only necessary ports
- ✅ Keep VPS system updated
- ✅ Monitor deployment logs

## 🔧 **TESTING YOUR SETUP**

### **Test VPS Connection:**
```bash
# Test SSH connection manually
ssh -i ~/.ssh/id_rsa user@your-vps-ip "echo 'Connection successful'"
```

### **Test Slack Webhook:**
```bash
# Test webhook manually
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"Test message from GitHub Actions setup"}' \
  YOUR_SLACK_WEBHOOK_URL
```

### **Trigger Workflow:**
1. Push a commit to `main` branch
2. Check **Actions** tab in GitHub
3. Monitor workflow execution
4. Verify deployment on your VPS
5. Check Slack for notifications

## 🎯 **WORKFLOW TRIGGERS**

Your workflow will run on:
- ✅ **Push to main/develop/staging**: Full CI/CD pipeline
- ✅ **Pull Requests**: Code quality checks and tests
- ✅ **Manual Dispatch**: On-demand deployment with environment selection

## 📞 **TROUBLESHOOTING**

### **Common Issues:**

#### **SSH Connection Failed:**
- Verify VPS_HOST, VPS_USER, and VPS_SSH_KEY are correct
- Check SSH key format (include headers and footers)
- Ensure public key is in VPS `~/.ssh/authorized_keys`

#### **Slack Notifications Not Working:**
- Verify SLACK_WEBHOOK URL is correct
- Check Slack app permissions
- Ensure webhook is enabled for the target channel

#### **Deployment Path Issues:**
- Update VPS paths in workflow if your setup differs
- Ensure deployment directory exists on VPS
- Check file permissions on VPS

## 🏆 **SUCCESS INDICATORS**

When properly configured, you should see:
- ✅ Green checkmarks on all workflow steps
- ✅ Successful deployment messages in logs
- ✅ Slack notifications in configured channels
- ✅ Updated application running on your VPS
- ✅ Health checks passing

Your GitHub Actions workflow is now production-ready with enterprise-grade CI/CD capabilities!
