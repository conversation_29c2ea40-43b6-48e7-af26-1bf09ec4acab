#!/usr/bin/env node

/**
 * Test Duplication Script
 * 
 * This script runs jscpd to check for duplication and fails if duplication is found.
 * It's designed to be used in CI/CD pipelines and test suites.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Configuration
const THRESHOLD = 0; // 0% duplication threshold
const CONFIG_FILE = '.jscpd.json';
const TEMP_DIR = '.jscpd-temp';

console.log('Running duplication test...');

try {
  // Ensure the temp directory exists
  if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
  }
  
  // Run jscpd with JSON reporter
  execSync(`npx jscpd . --config ${CONFIG_FILE} --threshold ${THRESHOLD} --reporters json --output ${TEMP_DIR}`, {
    stdio: 'inherit'
  });
  
  console.log('✅ Duplication test passed! No duplication found.');
  
  // Clean up
  fs.rmSync(TEMP_DIR, { recursive: true, force: true });
  
  process.exit(0);
} catch (error) {
  console.error('❌ Duplication test failed!');
  
  // Try to read the report to provide more details
  try {
    const reportPath = path.join(TEMP_DIR, 'jscpd-report.json');
    if (fs.existsSync(reportPath)) {
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      
      console.log('\nDuplication Statistics:');
      console.log(`- Duplication Percentage: ${report.statistics.total.percentage}%`);
      console.log(`- Duplicated Lines: ${report.statistics.total.duplicatedLines} of ${report.statistics.total.lines}`);
      console.log(`- Clones Found: ${report.duplicates.length}`);
      
      if (report.duplicates.length > 0) {
        console.log('\nTop Duplications:');
        
        // Show top 5 duplications
        report.duplicates.slice(0, 5).forEach((dup, index) => {
          console.log(`\n${index + 1}. Between ${dup.firstFile.name} and ${dup.secondFile.name}`);
          console.log(`   First file: Lines ${dup.firstFile.start}-${dup.firstFile.end}`);
          console.log(`   Second file: Lines ${dup.secondFile.start}-${dup.secondFile.end}`);
          console.log('   Size: [REDACTED FOR SECURITY]');
        });
      }
      
      console.log('\nTo fix duplication issues:');
      console.log('1. Run "npm run check:duplication:fix" for assistance');
      console.log('2. Use shared modules for common functionality');
      console.log('3. Extract duplicated code into reusable functions');
      console.log('4. See the duplication strategy document for more guidance');
    }
  } catch (readError) {
    console.error('Error reading duplication report:', readError.message);
  }
  
  // Clean up
  fs.rmSync(TEMP_DIR, { recursive: true, force: true });
  
  process.exit(1);
}
