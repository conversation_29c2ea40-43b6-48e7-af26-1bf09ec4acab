// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "./(base).controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError as ImportedAppError } from '../utils/errors/AppError';
import { TelegramService as ImportedTelegramService } from '../services/(telegram).service';
import { BaseController } from "./(base).controller";
import { asyncHandler } from '../utils/asyncHandler';
import { AppError as ImportedAppError } from '../utils/errors/AppError';
import { TelegramService as ImportedTelegramService } from '../services/(telegram).service';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * TelegramWebhookController
 */
export class TelegramWebhookController extends BaseController {
  constructor() {
    super();
  }

  /**
   * Handle incoming webhook from Telegram
   */
  handleWebhook = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get webhook data
        const update = req.body;

        // Validate webhook data
        if (!update || !(update).message) {
            return res.status(200).json({ success: true });
        }

        // Process webhook data
        const telegramService = new TelegramService();
        await (telegramService).processUpdate(update);

        // Return success
        return res.status(200).json({ success: true });
    } catch (error) {
        console.error("Error handling webhook:", error);
        // Always return 200 to Telegram to prevent retries
        return res.status(200).json({ success: false });
    }
  });

  /**
   * Set webhook URL for Telegram bot
   */
  setWebhook = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
        }

        // Get webhook URL
        const { url } = req.body;

        // Validate webhook URL
        if (!url) {
            throw new AppError({
            message: "Webhook URL is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
        }

        // Set webhook
        const telegramService = new TelegramService();
        const success: boolean = await (telegramService).setWebhook(url);

        if (!success) {
            throw new AppError({
            message: "Failed to set webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }

        // Return success
        return res.status(200).json({
            success: true,
            message: "Webhook set successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to set webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Delete webhook for Telegram bot
   */
  deleteWebhook = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
        }

        // Delete webhook
        const telegramService = new TelegramService();
        const success: boolean = await (telegramService).deleteWebhook();

        if (!success) {
            throw new AppError({
            message: "Failed to delete webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }

        // Return success
        return res.status(200).json({
            success: true,
            message: "Webhook deleted successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to delete webhook",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get webhook info for Telegram bot
   */
  getWebhookInfo = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
        }

        // Get webhook info
        const telegramService = new TelegramService();
        const webhookInfo = await (telegramService).getWebhookInfo();

        if (!webhookInfo) {
            throw new AppError({
            message: "Failed to get webhook info",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }

        // Return webhook info
        return res.status(200).json({
            success: true,
            data: webhookInfo
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get webhook info",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Get bot info for Telegram bot
   */
  getBotInfo = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role
        const userRole = req.user?.role;

        // Check if user is authorized
        if (!userRole || userRole !== "ADMIN") {
            throw new AppError({
            message: "Unauthorized",
            type: (ErrorType).AUTHENTICATION,
            code: (ErrorCode).INVALID_CREDENTIALS
        });
        }

        // Get bot info
        const telegramService = new TelegramService();
        const botInfo = await (telegramService).getMe();

        if (!botInfo) {
            throw new AppError({
            message: "Failed to get bot info",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }

        // Return bot info
        return res.status(200).json({
            success: true,
            data: botInfo
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to get bot info",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });

  /**
   * Send test message to a Telegram chat
   */
  sendTestMessage = asyncHandler(async (req: Request, res: Response) => {
    try {
        // Get user role and ID
        // Check authorization
        const { userRole, userId, merchantId } = this.checkAuthorization(req);

        // Get chat ID
        const { chatId } = req.body;

        // Validate chat ID
        if (!chatId) {
            throw new AppError({
            message: "Chat ID is required",
            type: ErrorType.VALIDATION,
            code: (ErrorCode).MISSING_REQUIRED_FIELD
        });
        }

        // Send test message
        const telegramService = new TelegramService();
        const success: boolean = await (telegramService).sendMessage(
            chatId,
            "*Test Message*\n\nThis is a test message from AmazingPay. If you received this message, your Telegram notifications are working correctly."
        );

        if (!success) {
            throw new AppError({
            message: "Failed to send test message",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
        }

        // Return success
        return res.status(200).json({
            success: true,
            message: "Test message sent successfully"
        });
    } catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError({
            message: "Failed to send test message",
            type: ErrorType.INTERNAL,
            code: ErrorCode.INTERNAL_SERVER_ERROR
        });
    }
  });
}

export default new TelegramWebhookController();
