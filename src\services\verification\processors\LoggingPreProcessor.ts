// jscpd:ignore-file
/**
 * Logging Pre-Processor
 *
 * Pre-processor that logs verification requests.
 */

import { VerificationRequest } from '../../../interfaces/verification/IVerificationStrategy';
import { VerificationPreProcessor } from './VerificationPreProcessor';
import { logger } from '../../../lib/logger';

/**
 * Logging pre-processor
 */
export class LoggingPreProcessor implements VerificationPreProcessor {
  /**
   * Get the name of the pre-processor
   */
  public getName(): string {
    return 'logging_pre_processor';
  }

  /**
   * Process a verification request
   */
  public async process(request: VerificationRequest): Promise<VerificationRequest> {
    logger.info(`Processing verification request for transaction: ${request.transactionId}`, {
      verificationMethod: request.verificationMethod,
      merchantId: request.merchantId,
      paymentMethodId: request.paymentMethodId,
      paymentMethodType: request.paymentMethodType,
      amount: request.amount,
      currency: request.currency,
    });

    // Return the request unchanged
    return request;
  }
}
