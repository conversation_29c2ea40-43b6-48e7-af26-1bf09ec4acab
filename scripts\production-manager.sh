#!/bin/bash

# AmazingPay Flow - Production Management Script
# This script provides easy management commands for production deployment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
APP_NAME="amazingpay-flow"
PORT=3002
HEALTH_URL="http://localhost:$PORT/api/health"
LOG_DIR="./logs"
DIST_DIR="./dist"

print_header() {
    echo -e "${BLUE}🚀 AmazingPay Flow - Production Manager${NC}"
    echo -e "${BLUE}=====================================\n${NC}"
}

print_status() {
    echo -e "${CYAN}📦 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to check if PM2 is installed
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        print_warning "PM2 not found. Installing PM2..."
        npm install -g pm2
        print_success "PM2 installed successfully"
    fi
}

# Function to start the application
start_app() {
    print_status "Starting AmazingPay Flow..."
    
    if [ -f "ecosystem.config.js" ]; then
        check_pm2
        pm2 start ecosystem.config.js
        print_success "Application started with PM2"
    else
        print_warning "PM2 config not found. Starting directly..."
        cd $DIST_DIR
        npm start &
        cd ..
        print_success "Application started directly"
    fi
    
    sleep 3
    check_health
}

# Function to stop the application
stop_app() {
    print_status "Stopping AmazingPay Flow..."
    
    if command -v pm2 &> /dev/null; then
        pm2 stop $APP_NAME 2>/dev/null || true
        print_success "Application stopped"
    else
        pkill -f "node.*index.js" 2>/dev/null || true
        print_success "Application processes killed"
    fi
}

# Function to restart the application
restart_app() {
    print_status "Restarting AmazingPay Flow..."
    stop_app
    sleep 2
    start_app
}

# Function to check application health
check_health() {
    print_status "Checking application health..."
    
    if curl -f $HEALTH_URL > /dev/null 2>&1; then
        print_success "Application is healthy"
        echo -e "${GREEN}🌐 API available at: $HEALTH_URL${NC}"
    else
        print_error "Application health check failed"
        return 1
    fi
}

# Function to show application status
show_status() {
    print_status "Application Status:"
    
    if command -v pm2 &> /dev/null; then
        pm2 list | grep $APP_NAME || print_info "No PM2 processes found"
    fi
    
    echo ""
    print_status "Port Status:"
    if lsof -i :$PORT > /dev/null 2>&1; then
        print_success "Port $PORT is in use"
        lsof -i :$PORT
    else
        print_warning "Port $PORT is not in use"
    fi
    
    echo ""
    check_health
}

# Function to show logs
show_logs() {
    print_status "Showing application logs..."
    
    if command -v pm2 &> /dev/null; then
        pm2 logs $APP_NAME --lines 50
    elif [ -f "$LOG_DIR/combined.log" ]; then
        tail -f $LOG_DIR/combined.log
    else
        print_warning "No logs found"
    fi
}

# Function to monitor the application
monitor_app() {
    print_status "Starting application monitor..."
    
    if command -v pm2 &> /dev/null; then
        pm2 monit
    else
        print_info "PM2 not available. Use 'show_logs' for log monitoring"
    fi
}

# Function to deploy updates
deploy_update() {
    print_status "Deploying application update..."
    
    # Stop application
    stop_app
    
    # Run deployment script
    if [ -f "scripts/deploy-production.sh" ]; then
        bash scripts/deploy-production.sh
    else
        print_error "Deployment script not found"
        return 1
    fi
    
    # Start application
    start_app
    
    print_success "Update deployed successfully"
}

# Function to backup application
backup_app() {
    print_status "Creating application backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # Backup application files
    cp -r $DIST_DIR $BACKUP_DIR/
    cp ecosystem.config.js $BACKUP_DIR/ 2>/dev/null || true
    cp .env.production $BACKUP_DIR/ 2>/dev/null || true
    
    # Backup logs
    if [ -d "$LOG_DIR" ]; then
        cp -r $LOG_DIR $BACKUP_DIR/
    fi
    
    print_success "Backup created: $BACKUP_DIR"
}

# Function to show help
show_help() {
    print_header
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     - Start the application"
    echo "  stop      - Stop the application"
    echo "  restart   - Restart the application"
    echo "  status    - Show application status"
    echo "  health    - Check application health"
    echo "  logs      - Show application logs"
    echo "  monitor   - Monitor application (PM2)"
    echo "  deploy    - Deploy application update"
    echo "  backup    - Create application backup"
    echo "  help      - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 status"
    echo "  $0 logs"
}

# Main script logic
case "${1:-help}" in
    start)
        print_header
        start_app
        ;;
    stop)
        print_header
        stop_app
        ;;
    restart)
        print_header
        restart_app
        ;;
    status)
        print_header
        show_status
        ;;
    health)
        print_header
        check_health
        ;;
    logs)
        print_header
        show_logs
        ;;
    monitor)
        print_header
        monitor_app
        ;;
    deploy)
        print_header
        deploy_update
        ;;
    backup)
        print_header
        backup_app
        ;;
    help|*)
        show_help
        ;;
esac
