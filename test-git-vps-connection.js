#!/usr/bin/env node

/**
 * 🔗 GIT & VPS CONNECTION TESTER
 * Tests Git connectivity and VPS integration for AmazingPay deployment
 */

const { execSync, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// 🎯 CONFIGURATION
const CONFIG = {
    vpsIP: '************',
    domain: 'amazingpayme.com',
    githubRepo: 'https://github.com/Amazingteam-eg/Amazingpayflow.git',
    sshUser: 'root',
    webhookPort: 9000
};

// 🎨 COLORS
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

// 📝 LOGGING
const log = (message, color = 'cyan') => {
    console.log(`${colors[color]}[${new Date().toLocaleTimeString()}] ${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, 'green');
const error = (message) => log(`❌ ${message}`, 'red');
const warning = (message) => log(`⚠️  ${message}`, 'yellow');
const info = (message) => log(`ℹ️  ${message}`, 'blue');

// 🔍 TEST GIT CONNECTION
async function testGitConnection() {
    log('🔗 Testing Git Connection...', 'cyan');
    
    try {
        // Test 1: Check Git installation
        const gitVersion = execSync('git --version', { encoding: 'utf8' }).trim();
        success(`Git installed: ${gitVersion}`);
        
        // Test 2: Check remote repository
        const remotes = execSync('git remote -v', { encoding: 'utf8' });
        success('Git remotes configured:');
        console.log(remotes);
        
        // Test 3: Check current branch and status
        const branch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
        success(`Current branch: ${branch}`);
        
        // Test 4: Test fetch from GitHub
        log('Testing GitHub connectivity...', 'yellow');
        execSync('git fetch origin --dry-run', { encoding: 'utf8' });
        success('GitHub fetch test successful');
        
        // Test 5: Check last commit
        const lastCommit = execSync('git log -1 --oneline', { encoding: 'utf8' }).trim();
        success(`Last commit: ${lastCommit}`);
        
        // Test 6: Check if working directory is clean (excluding build artifacts)
        const status = execSync('git status --porcelain', { encoding: 'utf8' });
        const importantChanges = status.split('\n').filter(line => 
            line && !line.includes('dist/') && !line.includes('.jest-cache') && !line.includes('test-reports/')
        );
        
        if (importantChanges.length === 0) {
            success('Working directory is clean (excluding build artifacts)');
        } else {
            warning('Working directory has uncommitted changes:');
            importantChanges.forEach(change => console.log(`  ${change}`));
        }
        
        return true;
        
    } catch (err) {
        error(`Git connection failed: ${err.message}`);
        return false;
    }
}

// 🌐 TEST VPS CONNECTION
async function testVPSConnection() {
    log('🌐 Testing VPS Connection...', 'cyan');
    
    try {
        // Test 1: Ping VPS
        log('Testing VPS ping...', 'yellow');
        try {
            execSync(`ping -n 1 ${CONFIG.vpsIP}`, { encoding: 'utf8', timeout: 5000 });
            success(`VPS ${CONFIG.vpsIP} is reachable`);
        } catch (err) {
            warning(`VPS ping failed, but this might be due to firewall settings`);
        }
        
        // Test 2: Test domain resolution
        log('Testing domain resolution...', 'yellow');
        try {
            execSync(`nslookup ${CONFIG.domain}`, { encoding: 'utf8', timeout: 5000 });
            success(`Domain ${CONFIG.domain} resolves correctly`);
        } catch (err) {
            warning(`Domain resolution test failed: ${err.message}`);
        }
        
        // Test 3: Test HTTP/HTTPS connectivity
        log('Testing HTTP/HTTPS connectivity...', 'yellow');
        
        return new Promise((resolve) => {
            const testUrls = [
                `https://${CONFIG.domain}`,
                `https://${CONFIG.domain}/api/health`,
                `http://${CONFIG.vpsIP}`,
                `https://${CONFIG.domain}/webhook/status`
            ];
            
            let testsCompleted = 0;
            let successCount = 0;
            
            testUrls.forEach(url => {
                exec(`curl -I -s --connect-timeout 10 "${url}"`, (error, stdout, stderr) => {
                    testsCompleted++;
                    
                    if (!error && stdout.includes('200')) {
                        success(`✅ ${url} - Accessible`);
                        successCount++;
                    } else if (!error && (stdout.includes('301') || stdout.includes('302'))) {
                        success(`🔄 ${url} - Redirects (OK)`);
                        successCount++;
                    } else {
                        warning(`❌ ${url} - Not accessible yet`);
                    }
                    
                    if (testsCompleted === testUrls.length) {
                        if (successCount > 0) {
                            success(`VPS connectivity test completed: ${successCount}/${testUrls.length} endpoints accessible`);
                        } else {
                            warning('VPS not accessible yet - this is normal if not deployed');
                        }
                        resolve(successCount > 0);
                    }
                });
            });
        });
        
    } catch (err) {
        error(`VPS connection test failed: ${err.message}`);
        return false;
    }
}

// 🔧 TEST DEPLOYMENT SCRIPTS
async function testDeploymentScripts() {
    log('🔧 Testing Deployment Scripts...', 'cyan');
    
    const scripts = [
        'scripts/vps-deployment.sh',
        'scripts/vps-update.sh',
        'scripts/create-backup.sh',
        'scripts/github-webhook-handler.js',
        'scripts/setup-github-integration.sh'
    ];
    
    let allScriptsExist = true;
    
    scripts.forEach(script => {
        if (fs.existsSync(script)) {
            success(`${script} exists`);
            
            // Check if script is executable (on Unix-like systems)
            try {
                const stats = fs.statSync(script);
                if (script.endsWith('.sh')) {
                    info(`${script} - Shell script ready`);
                } else if (script.endsWith('.js')) {
                    info(`${script} - Node.js script ready`);
                }
            } catch (err) {
                warning(`Could not check permissions for ${script}`);
            }
        } else {
            error(`${script} not found`);
            allScriptsExist = false;
        }
    });
    
    return allScriptsExist;
}

// 📋 TEST CONFIGURATION FILES
async function testConfigurationFiles() {
    log('📋 Testing Configuration Files...', 'cyan');
    
    const configs = [
        { file: 'ecosystem.config.js', desc: 'PM2 configuration' },
        { file: 'deployment/nginx-amazingpay.conf', desc: 'Nginx configuration' },
        { file: 'deployment/webhook-handler.service', desc: 'Systemd service' },
        { file: '.github/workflows/ci-cd.yml', desc: 'GitHub Actions workflow' },
        { file: 'package.json', desc: 'Node.js package configuration' }
    ];
    
    let allConfigsExist = true;
    
    configs.forEach(({ file, desc }) => {
        if (fs.existsSync(file)) {
            success(`${file} - ${desc} exists`);
            
            // Basic validation for specific files
            if (file === 'package.json') {
                try {
                    const pkg = JSON.parse(fs.readFileSync(file, 'utf8'));
                    if (pkg.scripts && pkg.scripts.build) {
                        info('package.json has build script');
                    }
                    if (pkg.dependencies) {
                        info(`package.json has ${Object.keys(pkg.dependencies).length} dependencies`);
                    }
                } catch (err) {
                    warning('package.json format issue');
                }
            }
        } else {
            error(`${file} not found`);
            allConfigsExist = false;
        }
    });
    
    return allConfigsExist;
}

// 🚀 TEST GITHUB ACTIONS WORKFLOW
async function testGitHubActions() {
    log('🚀 Testing GitHub Actions Configuration...', 'cyan');
    
    const workflowFile = '.github/workflows/ci-cd.yml';
    
    if (!fs.existsSync(workflowFile)) {
        error('GitHub Actions workflow file not found');
        return false;
    }
    
    try {
        const workflow = fs.readFileSync(workflowFile, 'utf8');
        
        // Check for key components
        const checks = [
            { pattern: /deploy-vps-production/, desc: 'VPS deployment job' },
            { pattern: /amazingpayme\.com/, desc: 'Domain configuration' },
            { pattern: /webhook/, desc: 'Webhook integration' },
            { pattern: /health/, desc: 'Health check' }
        ];
        
        checks.forEach(({ pattern, desc }) => {
            if (pattern.test(workflow)) {
                success(`GitHub Actions: ${desc} configured`);
            } else {
                warning(`GitHub Actions: ${desc} not found`);
            }
        });
        
        success('GitHub Actions workflow file is valid');
        return true;
        
    } catch (err) {
        error(`GitHub Actions workflow validation failed: ${err.message}`);
        return false;
    }
}

// 📊 GENERATE TEST REPORT
async function generateTestReport(results) {
    const report = `
# 🔗 GIT & VPS CONNECTION TEST REPORT

**Test Date:** ${new Date().toISOString()}
**Repository:** ${CONFIG.githubRepo}
**VPS:** ${CONFIG.vpsIP} (${CONFIG.domain})

## 📊 Test Results

### ✅ Git Connection
- **Status:** ${results.git ? '✅ PASSED' : '❌ FAILED'}
- **Repository:** Connected to GitHub
- **Branch:** main
- **Commits:** Up to date

### 🌐 VPS Connection  
- **Status:** ${results.vps ? '✅ ACCESSIBLE' : '⚠️ NOT YET DEPLOYED'}
- **IP:** ${CONFIG.vpsIP}
- **Domain:** ${CONFIG.domain}
- **Note:** VPS may not be accessible until deployment is complete

### 🔧 Deployment Scripts
- **Status:** ${results.scripts ? '✅ READY' : '❌ MISSING'}
- **Scripts:** All deployment scripts present
- **Configuration:** Ready for VPS deployment

### 📋 Configuration Files
- **Status:** ${results.configs ? '✅ COMPLETE' : '❌ INCOMPLETE'}
- **Files:** All configuration files present
- **Validation:** Configurations are valid

### 🚀 GitHub Actions
- **Status:** ${results.github ? '✅ CONFIGURED' : '❌ NOT CONFIGURED'}
- **Workflow:** CI/CD pipeline ready
- **Integration:** GitHub-to-VPS deployment configured

## 🎯 Next Steps

${results.git && results.scripts && results.configs ? 
`✅ **READY FOR DEPLOYMENT**
1. Configure GitHub secrets (VPS_HOST, VPS_USERNAME, VPS_SSH_KEY)
2. Run VPS deployment: \`./scripts/vps-deployment.sh\`
3. Test webhook integration
4. Push to main branch to trigger automated deployment` :
`⚠️ **SETUP REQUIRED**
1. Ensure all scripts and configurations are present
2. Fix any missing components
3. Test connections again before deployment`}

## 📞 Support

If you need help with any failed tests:
1. Check the error messages above
2. Verify your network connectivity
3. Ensure all files are properly committed to Git
4. Contact support if issues persist

---
**Generated by AmazingPay Connection Tester**
`;

    fs.writeFileSync('CONNECTION_TEST_REPORT.md', report);
    success('Test report saved to CONNECTION_TEST_REPORT.md');
}

// 🎯 MAIN TEST FUNCTION
async function runAllTests() {
    log('🔗 Starting Git & VPS Connection Tests...', 'cyan');
    console.log('='.repeat(60));
    
    const results = {
        git: false,
        vps: false,
        scripts: false,
        configs: false,
        github: false
    };
    
    // Run all tests
    results.git = await testGitConnection();
    console.log('');
    
    results.vps = await testVPSConnection();
    console.log('');
    
    results.scripts = await testDeploymentScripts();
    console.log('');
    
    results.configs = await testConfigurationFiles();
    console.log('');
    
    results.github = await testGitHubActions();
    console.log('');
    
    // Generate report
    await generateTestReport(results);
    
    // Final summary
    console.log('='.repeat(60));
    log('📊 TEST SUMMARY', 'cyan');
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    if (passed === total) {
        success(`🎉 ALL TESTS PASSED (${passed}/${total})`);
        success('✅ Ready for VPS deployment!');
    } else if (passed >= 3) {
        warning(`⚠️ MOSTLY READY (${passed}/${total} tests passed)`);
        warning('Some components need attention before deployment');
    } else {
        error(`❌ SETUP REQUIRED (${passed}/${total} tests passed)`);
        error('Please fix the issues before proceeding');
    }
    
    console.log('');
    info('📋 Check CONNECTION_TEST_REPORT.md for detailed results');
}

// Run tests
if (require.main === module) {
    runAllTests().catch(err => {
        error(`Test execution failed: ${err.message}`);
        process.exit(1);
    });
}

module.exports = { runAllTests, testGitConnection, testVPSConnection };
