#!/usr/bin/env node

/**
 * VALIDATE ALL FIXES SCRIPT - CLEAN VERSION
 * Comprehensive validation with reduced cognitive complexity
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 VALIDATING ALL CODE QUALITY FIXES');
console.log('====================================');
console.log('📋 Checking: SonarLint issues, TypeScript errors, Code quality');
console.log('');

const validationResults = {
    typescript: { perfect: false, errors: 0 },
    sonarLint: { perfect: false, issues: [] },
    codeQuality: { perfect: false, score: 0 },
    spelling: { perfect: false, issues: 0 },
    security: { perfect: false, vulnerabilities: 0 }
};

function validateSingleTypeScriptConfig(config) {
    try {
        const output = execSync(`npx tsc --project ${config} --noEmit --skipLibCheck 2>&1`, { 
            encoding: 'utf8',
            timeout: 60000
        });
        
        const errorMatches = output.match(/error TS/g) || [];
        const errorCount = errorMatches.length;
        
        if (errorCount === 0) {
            console.log(`✅ ${config}: ZERO errors`);
        } else {
            console.log(`❌ ${config}: ${errorCount} errors`);
        }
        
        return errorCount;
    } catch (error) {
        const errorOutput = error.stdout || error.stderr || '';
        const errorMatches = errorOutput.match(/error TS/g) || [];
        return errorMatches.length;
    }
}

function validateTypeScriptErrors() {
    console.log('🔧 1. TypeScript Error Validation');
    console.log('==================================');
    
    const configs = ['tsconfig.json', 'tsconfig.ultimate-zero.json', 'tsconfig.zero-errors.json'];
    let totalErrors = 0;
    
    for (const config of configs) {
        if (fs.existsSync(config)) {
            totalErrors += validateSingleTypeScriptConfig(config);
        }
    }
    
    validationResults.typescript.errors = totalErrors;
    validationResults.typescript.perfect = totalErrors === 0;
    
    console.log(`📊 Total TypeScript errors: ${totalErrors}`);
    return totalErrors === 0;
}

function checkFileForIssues(filePath, patterns) {
    if (!fs.existsSync(filePath)) {
        return [];
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    for (const [name, pattern] of Object.entries(patterns)) {
        const matches = content.match(pattern) || [];
        if (matches.length > 0) {
            issues.push({
                file: filePath,
                issue: name,
                count: matches.length
            });
        }
    }
    
    return issues;
}

function validateSonarLintFixes() {
    console.log('\n🔧 2. SonarLint Issue Validation');
    console.log('=================================');
    
    const filesToCheck = [
        'scripts/final-comprehensive-audit.js',
        'src/config/database.config.ts'
    ];
    
    const patterns = {
        'Empty catch blocks': /catch\s*\([^)]*\)\s*{\s*\/\/[^}]*}\s*}/g,
        'Logical OR instead of nullish coalescing': /process\.env\.[A-Z_]+\s*\|\|\s*['"]/g
    };
    
    let allIssues = [];
    
    for (const filePath of filesToCheck) {
        const fileIssues = checkFileForIssues(filePath, patterns);
        allIssues = allIssues.concat(fileIssues);
    }
    
    validationResults.sonarLint.issues = allIssues;
    validationResults.sonarLint.perfect = allIssues.length === 0;
    
    if (allIssues.length === 0) {
        console.log('✅ All SonarLint issues resolved');
    } else {
        console.log(`❌ ${allIssues.length} SonarLint issues remaining:`);
        allIssues.forEach(issue => {
            console.log(`   ${issue.file}: ${issue.issue} (${issue.count})`);
        });
    }
    
    return allIssues.length === 0;
}

function validateSpellingFixes() {
    console.log('\n🔧 3. Spelling Issue Validation');
    console.log('================================');
    
    if (!fs.existsSync('cspell.json')) {
        console.log('❌ cspell.json not found');
        validationResults.spelling.issues = 1;
        return false;
    }
    
    try {
        const cspellConfig = JSON.parse(fs.readFileSync('cspell.json', 'utf8'));
        const hasAmazingpay = cspellConfig.words && cspellConfig.words.includes('Amazingpay');
        
        if (hasAmazingpay) {
            console.log('✅ Spelling dictionary updated with "Amazingpay"');
            validationResults.spelling.perfect = true;
            validationResults.spelling.issues = 0;
            return true;
        } else {
            console.log('❌ "Amazingpay" not found in spelling dictionary');
            validationResults.spelling.issues = 1;
            return false;
        }
    } catch (error) {
        console.log(`❌ Error reading cspell.json: ${error.message}`);
        validationResults.spelling.issues = 1;
        return false;
    }
}

function checkAuthFileQuality() {
    const authFile = 'src/config/auth.ts';
    let score = 0;
    
    if (fs.existsSync(authFile)) {
        const content = fs.readFileSync(authFile, 'utf8');
        
        if (content.includes('JwtSignFunction') && !content.includes('as any')) {
            score++;
            console.log('✅ Proper JWT type definitions used');
        } else {
            console.log('❌ Unsafe type usage detected');
        }
        
        if (content.includes('AppError') && content.includes('throw new')) {
            score++;
            console.log('✅ Proper error handling implemented');
        } else {
            console.log('❌ Improper error handling');
        }
    }
    
    return score;
}

function checkDatabaseFileQuality() {
    const dbFile = 'src/config/database.config.ts';
    let score = 0;
    
    if (fs.existsSync(dbFile)) {
        const content = fs.readFileSync(dbFile, 'utf8');
        
        const nullishCount = (content.match(/\?\?/g) || []).length;
        const logicalOrCount = (content.match(/process\.env\.[A-Z_]+\s*\|\|/g) || []).length;
        
        if (nullishCount > 0 && logicalOrCount === 0) {
            score++;
            console.log('✅ Proper nullish coalescing operators used');
        } else {
            console.log('❌ Inconsistent operator usage');
        }
    }
    
    return score;
}

function checkAuditScriptQuality() {
    const auditFile = 'scripts/final-comprehensive-audit.js';
    let score = 0;
    
    if (fs.existsSync(auditFile)) {
        const content = fs.readFileSync(auditFile, 'utf8');
        
        const properCatches = content.match(/catch\s*\([^)]*\)\s*{\s*console\.(warn|log|error)/g) || [];
        if (properCatches.length >= 4) {
            score++;
            console.log('✅ Proper exception handling in audit script');
        } else {
            console.log('❌ Improper exception handling');
        }
    }
    
    return score;
}

function checkSpellingConfiguration() {
    let score = 0;
    
    if (fs.existsSync('cspell.json')) {
        score++;
        console.log('✅ Spelling configuration present');
    } else {
        console.log('❌ Missing spelling configuration');
    }
    
    return score;
}

function validateCodeQuality() {
    console.log('\n🔧 4. Code Quality Validation');
    console.log('==============================');
    
    const maxScore = 5;
    let qualityScore = 0;
    
    qualityScore += checkAuthFileQuality();
    qualityScore += checkDatabaseFileQuality();
    qualityScore += checkAuditScriptQuality();
    qualityScore += checkSpellingConfiguration();
    
    validationResults.codeQuality.score = qualityScore;
    validationResults.codeQuality.perfect = qualityScore === maxScore;
    
    console.log(`📊 Code quality score: ${qualityScore}/${maxScore}`);
    return qualityScore === maxScore;
}

function logValidationResults() {
    const allPerfect = validationResults.typescript.perfect &&
                      validationResults.sonarLint.perfect &&
                      validationResults.spelling.perfect &&
                      validationResults.codeQuality.perfect;
    
    console.log(`\n🎯 OVERALL STATUS: ${allPerfect ? '🏆 ALL FIXES VALIDATED' : '⚠️  SOME ISSUES REMAIN'}`);
    console.log('================================================');
    
    console.log('\n📋 DETAILED VALIDATION RESULTS:');
    console.log(`   🔴 TypeScript Errors: ${validationResults.typescript.perfect ? '✅ FIXED' : '❌ REMAINING'} (${validationResults.typescript.errors})`);
    console.log(`   🟠 SonarLint Issues: ${validationResults.sonarLint.perfect ? '✅ FIXED' : '❌ REMAINING'} (${validationResults.sonarLint.issues.length})`);
    console.log(`   🟣 Spelling Issues: ${validationResults.spelling.perfect ? '✅ FIXED' : '❌ REMAINING'} (${validationResults.spelling.issues})`);
    console.log(`   🟢 Code Quality: ${validationResults.codeQuality.perfect ? '✅ PERFECT' : '❌ NEEDS WORK'} (${validationResults.codeQuality.score}/5)`);
    
    return allPerfect;
}

function generateValidationReport() {
    console.log('\n🏆 VALIDATION REPORT');
    console.log('====================');
    
    const allValid = logValidationResults();
    
    if (allValid) {
        console.log('\n🎉 ALL CODE QUALITY ISSUES RESOLVED!');
        console.log('====================================');
        console.log('✅ Zero TypeScript compilation errors');
        console.log('✅ All SonarLint issues fixed');
        console.log('✅ Spelling dictionary updated');
        console.log('✅ Perfect code quality achieved');
        console.log('✅ Enterprise-grade standards met');
        console.log('');
        console.log('🚀 CODEBASE STATUS: PRODUCTION READY WITH PERFECT QUALITY');
    } else {
        console.log('\n⚠️  REMAINING ISSUES TO ADDRESS:');
        console.log('=================================');
        
        if (!validationResults.typescript.perfect) {
            console.log('🔴 Fix remaining TypeScript errors');
        }
        if (!validationResults.sonarLint.perfect) {
            console.log('🟠 Address remaining SonarLint issues');
        }
        if (!validationResults.spelling.perfect) {
            console.log('🟣 Update spelling configuration');
        }
        if (!validationResults.codeQuality.perfect) {
            console.log('🟢 Improve code quality standards');
        }
    }
    
    // Save validation report
    const reportPath = 'validation-report-clean.json';
    fs.writeFileSync(reportPath, JSON.stringify(validationResults, null, 2), 'utf8');
    console.log(`\n📄 Validation report saved to: ${reportPath}`);
    
    return allValid;
}

async function main() {
    console.log('🚀 Starting comprehensive validation...\n');
    
    // Run all validations
    validateTypeScriptErrors();
    validateSonarLintFixes();
    validateSpellingFixes();
    validateCodeQuality();
    
    // Generate final report
    const allValid = generateValidationReport();
    
    console.log('\n🎊 VALIDATION COMPLETE!');
    console.log('=======================');
    
    if (allValid) {
        console.log('🏆 ALL FIXES VALIDATED - PERFECT QUALITY ACHIEVED!');
        process.exit(0);
    } else {
        console.log('⚠️  Some issues remain - review recommendations above');
        process.exit(1);
    }
}

main().catch(console.error);
