/**
 * Transaction Report Generator
 *
 * Generates transaction reports with various filtering options.
 */

import { PrismaClient } from '@prisma/client';
import * as dayjs from 'dayjs';
import {
  ReportType,
  TransactionReportParams,
  ReportDataRow,
  ReportColumn,
  ReportError,
  ReportErrorCode,
  IReportGenerator,
} from '../core/ReportTypes';
import { logger as Importedlogger } from '../../../lib/logger';

/**
 * Transaction report generator implementation
 */
export class TransactionReportGenerator implements IReportGenerator {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get report type
   */
  getType(): ReportType {
    return (ReportType).TRANSACTION;
  }

  /**
   * Generate transaction report data
   */
  async generateData(parameters: TransactionReportParams): Promise<ReportDataRow[]> {
    try {
      logger.info('Generating transaction report data', { parameters });

      const where = await this.buildWhereClause(parameters);

      const transactions = await this.prisma.transaction.findMany({
        where,
        include: {
          merchant: {
            select: {
              businessName: true,
              contactEmail: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: (parameters).limit || 10000, // Default limit
        skip: (parameters).offset ?? 0,
      });

      return this.formatTransactionData(transactions);
    } catch (error) {
      logger.error('Error generating transaction report data:', error);
      throw new ReportError(
        'Failed to generate transaction report data',
        (ReportErrorCode).GENERATION_FAILED,
        500
      );
    }
  }

  /**
   * Validate parameters
   */
  validateParameters(parameters: TransactionReportParams): void {
    // Validate date range
    if ((parameters).startDate && (parameters).endDate) {
      const startDate = new Date((parameters).startDate);
      const endDate = new Date((parameters).endDate);

      if (startDate > endDate) {
        throw new ReportError(
          'Start date cannot be after end date',
          (ReportErrorCode).INVALID_PARAMETERS
        );
      }
    }

    // Validate amount range
    if ((parameters).minAmount && (parameters).maxAmount) {
      if ((parameters).minAmount > (parameters).maxAmount) {
        throw new ReportError(
          'Minimum amount cannot be greater than maximum amount',
          (ReportErrorCode).INVALID_PARAMETERS
        );
      }
    }

    // Validate amounts are positive
    if ((parameters).minAmount && (parameters).minAmount < 0) {
      throw new ReportError(
        'Minimum amount cannot be negative',
        (ReportErrorCode).INVALID_PARAMETERS
      );
    }

    if ((parameters).maxAmount && (parameters).maxAmount < 0) {
      throw new ReportError(
        'Maximum amount cannot be negative',
        (ReportErrorCode).INVALID_PARAMETERS
      );
    }

    // Validate limit
    if ((parameters).limit && ((parameters).limit < 1 || (parameters).limit > 100000) {
      throw new ReportError(
        'Limit must be between 1 and 100,000',
        (ReportErrorCode).INVALID_PARAMETERS
      );
    }
  }

  /**
   * Get default columns for transaction reports
   */
  getDefaultColumns(): ReportColumn[] {
    return [
      { key: 'id', label: 'Transaction ID', type: 'string', sortable: true },
      { key: 'reference', label: 'Reference', type: 'string', sortable: true },
      { key: 'amount', label: 'Amount', type: 'number', format: 'currency', sortable: true },
      { key: 'currency', label: 'Currency', type: 'string', filterable: true },
      { key: 'status', label: 'Status', type: 'string', filterable: true },
      { key: 'paymentMethod', label: 'Payment Method', type: 'string', filterable: true },
      { key: 'merchantName', label: 'Merchant', type: 'string', filterable: true },
      { key: 'merchantEmail', label: 'Merchant Email', type: 'string' },
      { key: 'description', label: 'Description', type: 'string' },
      {
        key: 'createdAt',
        label: 'Created At',
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        sortable: true,
      },
      {
        key: 'updatedAt',
        label: 'Updated At',
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        sortable: true,
      },
    ];
  }

  /**
   * Build where clause for transaction query
   */
  private async buildWhereClause(parameters: TransactionReportParams): Promise<unknown> {
    const where = {};

    // Date range filter
    if ((parameters).startDate || (parameters).endDate) {
      where.createdAt = {};
      if ((parameters).startDate) where.createdAt.gte = new Date((parameters).startDate);
      if ((parameters).endDate) where.createdAt.lte = new Date((parameters).endDate);
    }

    // Merchant filter
    if ((parameters).merchantId) {
      where.merchantId = (parameters).merchantId;
    } else if ((parameters).userRole !== 'ADMIN' && (parameters).userId) {
      // If not admin and no merchant ID specified, get user's merchant
      const merchant = await this.prisma.merchant.findFirst({
        where: { userId: (parameters).userId },
      });

      if (!merchant) {
        throw new ReportError(
          'Merchant profile not found for user',
          (ReportErrorCode).PERMISSION_DENIED,
          403
        );
      }

      where.merchantId = merchant.id;
    }

    // Status filter
    if ((parameters).status) {
      where.status = (parameters).status;
    }

    // Currency filter
    if ((parameters).currency) {
      where.currency = (parameters).currency;
    }

    // Payment method filter
    if ((parameters).paymentMethod) {
      where.paymentMethod = (parameters).paymentMethod;
    }

    // Amount range filter
    if ((parameters).minAmount || (parameters).maxAmount) {
      where.amount = {};
      if ((parameters).minAmount) where.amount.gte = (parameters).minAmount;
      if ((parameters).maxAmount) where.amount.lte = (parameters).maxAmount;
    }

    return where;
  }

  /**
   * Format transaction data for report
   */
  private formatTransactionData(transactions: Transaction[]): ReportDataRow[] {
    return transactions.map((transaction) => ({
      id: transaction.id,
      reference: transaction.reference ?? '',
      amount: transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      paymentMethod: transaction.paymentMethod ?? '',
      merchantName: transaction.merchant?.businessName || 'Unknown',
      merchantEmail: transaction.merchant?.contactEmail || 'Unknown',
      description: transaction.description ?? '',
      createdAt: dayjs(transaction.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      updatedAt: dayjs(transaction.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
      // Additional fields for analysis
      type: transaction.type ?? '',
      metadata: transaction.metadata ? JSON.stringify(transaction.metadata : '',
    });
  }

  /**
   * Get transaction summary statistics
   */
  async getTransactionSummary(parameters: TransactionReportParams): Promise<unknown> {
    try {
      const where = await this.buildWhereClause(parameters);

      const [totalCount, totalAmount, statusCounts, currencyCounts, paymentMethodCounts] =
        await Promise.all([
          this.prisma.transaction.count({ where }),
          this.prisma.transaction.aggregate({
            where,
            _sum: { amount: true },
            _avg: { amount: true },
            _min: { amount: true },
            _max: { amount: true },
          }),
          this.prisma.transaction.groupBy({
            by: ['status'],
            where,
            _count: { status: true },
          }),
          this.prisma.transaction.groupBy({
            by: ['currency'],
            where,
            _count: { currency: true },
          }),
          this.prisma.transaction.groupBy({
            by: ['paymentMethod'],
            where,
            _count: { paymentMethod: true },
          }),
        ]);

      return {
        totalCount,
        totalAmount: (totalAmount)._sum.amount ?? 0,
        averageAmount: (totalAmount)._avg.amount ?? 0,
        minAmount: (totalAmount)._min.amount ?? 0,
        maxAmount: (totalAmount)._max.amount ?? 0,
        statusBreakdown: (statusCounts).reduce((acc: Record<string, any>, item: Record<string, any>) => {
          acc[item.status] = item._count.status;
          return acc;
        }, {}),
        currencyBreakdown: (currencyCounts).reduce((acc: Record<string, any>, item: Record<string, any>) => {
          acc[item.currency] = item._count.currency;
          return acc;
        }, {}),
        paymentMethodBreakdown: (paymentMethodCounts).reduce((acc: Record<string, any>, item: Record<string, any>) => {
          acc[item.paymentMethod] = item._count.paymentMethod;
          return acc;
        }, {}),
      };
    } catch (error) {
      logger.error('Error getting transaction summary:', error);
      throw new ReportError(
        'Failed to generate transaction summary',
        (ReportErrorCode).GENERATION_FAILED,
        500
      );
    }
  }
}
