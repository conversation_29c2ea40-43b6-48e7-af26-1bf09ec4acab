{"version": 3, "file": "TestTypes.d.ts", "sourceRoot": "", "sources": ["../../../../../src/tests/utils/core/TestTypes.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAM9C,KAAK,cAAc,GAAG,OAAO,CAAC;AAC9B,KAAK,WAAW,GAAG,OAAO,CAAC;AAC3B,KAAK,cAAc,GAAG,OAAO,CAAC;AAE9B;;GAEG;AACH,MAAM,WAAW,WAAY,SAAQ,OAAO,CAAG,OAAO,CAAE;IACtD,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,GAAG,CAAC,EAAE,OAAO,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,OAAO,CAAG,QAAQ,CAAE;IACxD,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;IACnB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;IACjB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;IACjB,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;IAChB,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;IACrB,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;IACnB,WAAW,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;IACxB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB;AAED;;GAEG;AACH,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AAEjC;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,KAAK,CAAC,EAAE,MAAG,AAAD,CAAkC;IAC5C,OAAO,CAAC,EAAE,MAAG,AAAD,CAAkC;IAC9C,UAAU,CAAC,EAAE,MAAG,AAAD,CAAkC;IACjD,SAAS,CAAC,EAAE,MAAG,AAAD,CAAkC;IAChD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,qBAAsB,SAAQ,eAAe;IAC5D,GAAG,CAAC,EAAE,WAAW,CAAC;IAClB,GAAG,CAAC,EAAE,YAAY,CAAC;IACnB,IAAI,CAAC,EAAE,QAAQ,CAAC;IAChB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,eAAe,CAAC,EAAE,CAAC,UAAU,EAAE,cAAc,KAAE,AAAD,CAAkC;IAChF,iBAAiB,CAAC,EAAE,CAAC,UAAU,EAAE,cAAc,KAAE,AAAD,CAAkC;IAClF,gBAAgB,CAAC,EAAE,CAAC,GAAG,EAAE,YAAY,KAAE,AAAD,CAAkC;IACxE,eAAe,CAAC,EAAE,CAAC,GAAG,EAAE,WAAW,KAAE,AAAD,CAAkC;CACvE;AAED;;GAEG;AACH,MAAM,WAAW,kBAAmB,SAAQ,eAAe;IACzD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACb,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,KAAE,AAAD,CAAkC;IACvE,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,KAAE,AAAD,CAAkC;IACzE,gBAAgB,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;IAC/C,cAAc,CAAC,EAAE,CAAC,MAAM,CAAC,CAAkC;IAC3D,WAAW,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,IAAI,CAAC,IAAI,CAAE,CAAE;CAC7C;AAED;;GAEG;AACH,MAAM,WAAW,qBAAsB,SAAQ,kBAAkB;IAC/D,UAAU,CAAC,EAAE,YAAY,CAAC;IAC1B,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,eAAe,CAAC,EAAE,CAAC,UAAU,EAAE,cAAc,KAAE,AAAD,CAAkC;IAChF,iBAAiB,CAAC,EAAE,CAAC,UAAU,EAAE,cAAc,KAAE,AAAD,CAAkC;IAClF,WAAW,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;IAC1C,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAG,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,CAAE,KAAG,AAAD,CAAkC;CACjG;AAED;;GAEG;AACH,MAAM,WAAW,qBAAsB,SAAQ,eAAe;IAC5D,GAAG,CAAC,EAAE,WAAW,CAAC;IAClB,GAAG,CAAC,EAAE,YAAY,CAAC;IACnB,IAAI,CAAC,EAAE,QAAQ,CAAC;IAChB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,eAAe,CAAC,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,KAAE,AAAD,CAAkC;IACzG,iBAAiB,CAAC,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,KAAE,AAAD,CAAkC;IAC3G,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAChC;AAED;;GAEG;AACH,MAAM,WAAW,oBAAqB,SAAQ,eAAe;IAC3D,GAAG,CAAC,EAAE,WAAW,CAAC;IAClB,GAAG,CAAC,EAAE,YAAY,CAAC;IACnB,IAAI,CAAC,EAAE,QAAQ,CAAC;IAChB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACb,cAAc,CAAC,EAAE,MAAG,AAAD,CAAkC;IACrD,gBAAgB,CAAC,EAAE,MAAG,AAAD,CAAkC;IACvD,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,yBAAyB,CAAC,EAAE,MAAM,CAAC;CACpC;AAED;;GAEG;AACH,MAAM,WAAW,kBAAmB,SAAQ,eAAe;IACzD,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACb,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,YAAY,CAAC,EAAE,MAAG,AAAD,CAAkC;IACnD,cAAc,CAAC,EAAE,MAAG,AAAD,CAAkC;IACrD,WAAW,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;IAC1C,cAAc,CAAC,EAAE,OAAO,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,cAAe,SAAQ,eAAe;IACrD,MAAM,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC;IACrD,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,OAAO,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,MAAM,CAAE,CAAE;IACrC,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;IACpC,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,IAAI,CAAC,EAAE;QACL,IAAI,EAAE,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;QACrC,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,mBAAoB,SAAQ,eAAe;IAC1D,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC;IACjB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAkC;CACxD;AAED;;GAEG;AACH,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,UAAU,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;CACxC;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,KAAK,CAAC,EAAE,MAAG,AAAD,CAAkC;IAC5C,QAAQ,CAAC,EAAE,MAAG,AAAD,CAAkC;IAC/C,UAAU,CAAC,EAAE,MAAG,AAAD,CAAkC;IACjD,SAAS,CAAC,EAAE,MAAG,AAAD,CAAkC;IAChD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,SAAS,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;CACzC;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW,CAAC,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;CAC3C;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC,QAAQ,CAAC,EAAE;QACT,GAAG,EAAE,MAAM,CAAC;QACZ,KAAK,EAAE,OAAO,CAAC;QACf,IAAI,EAAE,OAAO,CAAC;KACf,CAAC;IACF,KAAK,CAAC,EAAE;QACN,GAAG,EAAE,MAAM,CAAC;QACZ,KAAK,EAAE,OAAO,CAAC;KAChB,CAAC;IACF,QAAQ,CAAC,EAAE;QACT,QAAQ,EAAE,OAAO,CAAC;QAClB,YAAY,EAAE,OAAO,CAAC;KACvB,CAAC;IACF,OAAO,CAAC,EAAE;QACR,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,OAAO,CAAC;KACjB,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B,aAAa,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAE,AAAD,CAAkC;IACnE,cAAc,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAE,AAAD,CAAkC;IACpE,aAAa,EAAE,CAAC,QAAQ,EAAE,GAAG,KAAE,AAAD,CAAkC;IAChE,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAE,AAAD,CAAkC;IAClE,oBAAoB,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,KAAE,AAAD,CAAkC;IACvF,kBAAkB,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,KAAE,AAAD,CAAkC;IACpF,eAAe,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAE,AAAD,CAAkC;IAC/F,6BAA6B,EAAE,CAC7B,QAAQ,EAAE,IAAI,CAAC,IAAI,EACnB,SAAS,EAAE,QAAQ,KACnB,AAAD,CAAkC;CACpC;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAG,MAAM,EAAE,OAAO,CAAE,CAAE;IACtC,KAAK,EAAE,MAAM,CAAG,MAAM,EAAE,IAAI,CAAC,IAAI,CAAE,CAAE;IACrC,OAAO,EAAE,CAAC,MAAG,AAAD,CAAA,CAAgC;IAAE,QAAG;CAClD;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,GAAG,CAAC;IACV,QAAQ,EAAE,GAAG,CAAC;IACd,WAAW,EAAE,GAAG,CAAC;IACjB,OAAO,EAAE,GAAG,CAAC;IACb,YAAY,EAAE,GAAG,CAAC;IAClB,YAAY,EAAE,GAAG,CAAC;IAClB,OAAO,EAAE,GAAG,CAAC;IACb,KAAK,EAAE,GAAG,CAAC;IACX,KAAK,EAAE,GAAG,CAAC;IACX,OAAO,EAAE,GAAG,CAAC;CACd;AAED;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,GAAG,CAAC;IACV,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IACxB,KAAK,CAAC,EAAE,MAAG,AAAD,CAAkC;IAC5C,QAAQ,CAAC,EAAE,MAAG,AAAD,CAAkC;CAChD;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,KAAK,EAAE,QAAQ,EAAE,CAAC;IAClB,KAAK,CAAC,EAAE,MAAG,AAAD,CAAkC;IAC5C,QAAQ,CAAC,EAAE,MAAG,AAAD,CAAkC;IAC/C,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,WAAW,QAAQ;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAG,AAAD,CAAkC;IAC5C,UAAU,CAAC,EAAE,MAAG,AAAD,CAAkC;IACjD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,oBAAY,aAAa;IACvB,WAAW,gBAAgB;IAC3B,eAAe,oBAAoB;IACnC,gBAAgB,qBAAqB;IACrC,aAAa,kBAAkB;IAC/B,aAAa,kBAAkB;IAC/B,eAAe,oBAAoB;CACpC;AAED;;GAEG;AACH,qBAAa,SAAU,SAAQ,KAAK;IAClC,IAAI,EAAE,aAAa,CAAC;IACpB,OAAO,CAAC,EAAE,WAAW,CAAC;IACtB,aAAa,CAAC,EAAE,KAAK,CAAC;gBAEV,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,aAAa,CAAC,EAAE,KAAK;CAO/F"}