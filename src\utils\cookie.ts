// jscpd:ignore-file
/**
 * Cookie Utility
 *
 * This utility provides functions for managing environment-specific cookies
 * to ensure complete isolation between production and demo environments.
 */

import { Request, Response, NextFunction } from 'express';
import { logger as Importedlogger } from '../lib/logger';
import { getEnvironment as ImportedgetEnvironment } from '../config/environment';
import { getSiteDomain as ImportedgetSiteDomain } from './domain';

/**
 * Cookie options interface
 */
export interface CookieOptions {
  /** Cookie max age in milliseconds */
  maxAge?: number;
  /** Cookie expiration date */
  expires?: Date;
  /** Cookie path */
  path?: string;
  /** Cookie domain */
  domain?: string;
  /** Cookie secure flag */
  secure?: boolean;
  /** Cookie HTTP only flag */
  httpOnly?: boolean;
  /** Cookie same site policy */
  sameSite?: 'strict' | 'lax' | 'none';
}

/**
 * Get environment-specific cookie name
 * @param baseName Base cookie name
 * @returns Environment-specific cookie name
 */
export const getCookieName = (baseName: string): string  =>  {
  const env = getEnvironment();

  // For production, use the base name
  if (env === 'production') {
    return baseName;
  }

  // For other environments, prefix with environment
  return `${env}_${baseName}`;
};

/**
 * Get environment-specific cookie domain
 * @returns Environment-specific cookie domain
 */
export const getCookieDomain = (): string  =>  {
  const env = getEnvironment();
  const baseDomain = getSiteDomain();

  // For production, use the base domain
  if (env === 'production') {
    return baseDomain;
  }

  // For demo, use demo subdomain
  if (env === 'demo') {
    return `demo.${baseDomain}`;
  }

  // For development, use localhost
  return '';
};

/**
 * Get environment-specific cookie options
 * @param options Additional cookie options
 * @returns Environment-specific cookie options
 */
export const getCookieOptions = (options: CookieOptions = {}): CookieOptions  =>  {
  const env = getEnvironment();
  const domain = getCookieDomain();

  // Base options
  const baseOptions: CookieOptions = {
    path: '/',
    httpOnly: true,
    secure: env !== 'development', // Secure in all environments except development
    sameSite: 'strict',
  };

  // Add domain if not empty
  if (domain) {
    (baseOptions).domain = domain;
  }

  // Merge with provided options
  return {
    ...baseOptions,
    ...options,
  };
};

/**
 * Set environment-specific cookie
 * @param res Express response
 * @param baseName Base cookie name
 * @param value Cookie value
 * @param options Additional cookie options
 */
export const setCookie = (
  res: Response,
  baseName: string,
  value: string,
  options: CookieOptions = {}
): void  =>  {
  const cookieName = getCookieName(baseName);
  const cookieOptions = getCookieOptions(options);

  res.cookie(cookieName, value, cookieOptions);

  logger.debug(`Set cookie: ${cookieName}`, {
    environment: getEnvironment(),
    domain: (cookieOptions).domain,
    secure: (cookieOptions).secure,
    sameSite: (cookieOptions).sameSite,
  });
};

/**
 * Get environment-specific cookie
 * @param req Express request
 * @param baseName Base cookie name
 * @returns Cookie value or undefined
 */
export const getCookie = (req: Request, baseName: string): string | undefined  =>  {
  const cookieName = getCookieName(baseName);
  return req.cookies[cookieName];
};

/**
 * Clear environment-specific cookie
 * @param res Express response
 * @param baseName Base cookie name
 * @param options Additional cookie options
 */
export const clearCookie = (res: Response, baseName: string, options: CookieOptions = {}): void  =>  {
  const cookieName = getCookieName(baseName);
  const cookieOptions = getCookieOptions(options);

  res.clearCookie(cookieName, cookieOptions);

  logger.debug(`Cleared cookie: ${cookieName}`, {
    environment: getEnvironment(),
    domain: (cookieOptions).domain,
  });
};

export default {
  getCookieName,
  getCookieDomain,
  getCookieOptions,
  setCookie,
  getCookie,
  clearCookie,
};
