#!/bin/bash

# 🚀 AMAZINGPAY VPS DEPLOYMENT SCRIPT
# VPS: ************ | Domain: amazingpayme.com | Database: Amazingpay

set -e

# 🎯 DEPLOYMENT CONFIGURATION
VPS_IP="************"
DOMAIN="amazingpayme.com"
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"
APP_DIR="/www/wwwroot/amazingpayme.com"
BACKUP_DIR="/var/backups/amazingpay"
LOG_DIR="/var/log/amazingpay"

# 🎨 COLORS FOR OUTPUT
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 📝 LOGGING FUNCTIONS
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 🔍 SYSTEM CHECKS
check_system() {
    log "Performing system checks..."
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
    fi
    
    # Check VPS IP
    CURRENT_IP=$(curl -s ifconfig.me)
    if [[ "$CURRENT_IP" != "$VPS_IP" ]]; then
        warning "Current IP ($CURRENT_IP) doesn't match expected VPS IP ($VPS_IP)"
    fi
    
    success "System checks completed"
}

# 📦 INSTALL DEPENDENCIES
install_dependencies() {
    log "Installing system dependencies..."
    
    # Update system
    apt update && apt upgrade -y
    
    # Install essential packages
    apt install -y curl wget git nginx postgresql postgresql-contrib \
                   nodejs npm build-essential python3-certbot-nginx \
                   ufw fail2ban htop tree zip unzip
    
    # Install PM2 globally
    npm install -g pm2
    
    success "Dependencies installed"
}

# 🗄️ SETUP DATABASE
setup_database() {
    log "Setting up PostgreSQL database..."
    
    # Start PostgreSQL service
    systemctl start postgresql
    systemctl enable postgresql
    
    # Create database and user
    sudo -u postgres psql -c "CREATE DATABASE \"$DB_NAME\";" || true
    sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';"
    
    # Configure PostgreSQL
    PG_VERSION=$(sudo -u postgres psql -t -c "SELECT version();" | grep -oP '\d+\.\d+' | head -1)
    PG_CONFIG="/etc/postgresql/$PG_VERSION/main/postgresql.conf"
    PG_HBA="/etc/postgresql/$PG_VERSION/main/pg_hba.conf"
    
    # Update PostgreSQL configuration
    sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" "$PG_CONFIG"
    sed -i "s/#port = 5432/port = 5432/" "$PG_CONFIG"
    
    # Update authentication
    sed -i "s/local   all             postgres                                peer/local   all             postgres                                md5/" "$PG_HBA"
    
    # Restart PostgreSQL
    systemctl restart postgresql
    
    success "Database setup completed"
}

# 📁 SETUP APPLICATION DIRECTORIES
setup_directories() {
    log "Setting up application directories..."
    
    # Create application directory
    mkdir -p "$APP_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "/etc/ssl/certs"
    mkdir -p "/etc/ssl/private"
    
    # Set permissions
    chown -R www-data:www-data "$APP_DIR"
    chown -R www-data:www-data "$LOG_DIR"
    chmod 755 "$APP_DIR"
    chmod 755 "$LOG_DIR"
    
    success "Directories created"
}

# 🔥 SETUP FIREWALL
setup_firewall() {
    log "Configuring firewall..."
    
    # Reset UFW
    ufw --force reset
    
    # Default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow essential ports
    ufw allow ssh
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw allow 3002/tcp
    
    # Enable firewall
    ufw --force enable
    
    success "Firewall configured"
}

# 🌐 SETUP NGINX
setup_nginx() {
    log "Configuring Nginx..."
    
    # Remove default site
    rm -f /etc/nginx/sites-enabled/default
    
    # Create Nginx configuration
    cat > "/etc/nginx/sites-available/$DOMAIN" << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN $VPS_IP;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=login:10m rate=1r/s;
    
    # API proxy
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Auth endpoints with stricter rate limiting
    location /api/auth/ {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Static files
    location / {
        root $APP_DIR/public;
        try_files \$uri \$uri/ /index.html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        proxy_pass http://localhost:3002/api/health;
        access_log off;
    }
    
    # Block sensitive files
    location ~ /\\.env {
        deny all;
        return 404;
    }
    
    location ~ /\\.(git|svn) {
        deny all;
        return 404;
    }
}
EOF
    
    # Enable site
    ln -sf "/etc/nginx/sites-available/$DOMAIN" "/etc/nginx/sites-enabled/"
    
    # Test configuration
    nginx -t
    
    # Start Nginx
    systemctl start nginx
    systemctl enable nginx
    
    success "Nginx configured"
}

# 🔐 SETUP SSL CERTIFICATE
setup_ssl() {
    log "Setting up SSL certificate..."
    
    # Install SSL certificate using Certbot
    certbot --nginx -d "$DOMAIN" -d "www.$DOMAIN" --non-interactive --agree-tos --email "admin@$DOMAIN"
    
    # Setup auto-renewal
    crontab -l | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet"; } | crontab -
    
    success "SSL certificate installed"
}

# 🚀 DEPLOY APPLICATION
deploy_application() {
    log "Deploying application..."
    
    # Navigate to app directory
    cd "$APP_DIR"
    
    # Copy environment file
    cp /root/.env.production "$APP_DIR/.env.production"
    
    # Install dependencies
    npm ci --production
    
    # Generate Prisma client
    npx prisma generate
    
    # Run database migrations
    npx prisma migrate deploy
    
    # Build application
    npm run build
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'amazingpay',
    script: 'dist/src/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    env_file: '.env.production',
    log_file: '$LOG_DIR/app.log',
    error_file: '$LOG_DIR/error.log',
    out_file: '$LOG_DIR/out.log',
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF
    
    # Start application with PM2
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
    
    success "Application deployed"
}

# 📊 SETUP MONITORING
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Install PM2 monitoring
    pm2 install pm2-logrotate
    
    # Configure log rotation
    pm2 set pm2-logrotate:max_size 10M
    pm2 set pm2-logrotate:retain 30
    
    # Setup health check script
    cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
HEALTH_URL="http://localhost:3002/api/health"
if ! curl -f -s "$HEALTH_URL" > /dev/null; then
    echo "Health check failed, restarting application..."
    pm2 restart amazingpay
fi
EOF
    
    chmod +x /usr/local/bin/health-check.sh
    
    # Add to crontab
    crontab -l | { cat; echo "*/5 * * * * /usr/local/bin/health-check.sh"; } | crontab -
    
    success "Monitoring setup completed"
}

# 🔒 SECURITY HARDENING
security_hardening() {
    log "Applying security hardening..."
    
    # Configure fail2ban
    cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
EOF
    
    systemctl restart fail2ban
    
    # Secure shared memory
    echo "tmpfs /run/shm tmpfs defaults,noexec,nosuid 0 0" >> /etc/fstab
    
    success "Security hardening completed"
}

# 🎯 MAIN DEPLOYMENT FUNCTION
main() {
    log "🚀 Starting AmazingPay VPS Deployment"
    log "VPS: $VPS_IP | Domain: $DOMAIN | Database: $DB_NAME"
    
    check_system
    install_dependencies
    setup_database
    setup_directories
    setup_firewall
    setup_nginx
    setup_ssl
    deploy_application
    setup_monitoring
    security_hardening
    
    success "🎉 AmazingPay deployment completed successfully!"
    info "Application URL: https://$DOMAIN"
    info "API URL: https://$DOMAIN/api"
    info "Health Check: https://$DOMAIN/health"
    info "Application logs: $LOG_DIR"
}

# Run main function
main "$@"
