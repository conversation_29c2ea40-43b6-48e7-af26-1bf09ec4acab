# 🔄 IMMEDIATE CREDENTIAL ROTATION SCRIPT

## 🚨 **EXECUTE THESE STEPS IMMEDIATELY**

### 🔐 **1. DATABASE CREDENTIALS** (5 minutes)

#### **PostgreSQL Password Change**
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres -h localhost

-- Change postgres user password
ALTER USER postgres PASSWORD 'NEW_SECURE_PASSWORD_HERE_2024';

-- Create new application user with strong password
CREATE USER amazingpay_app WITH PASSWORD 'NEW_APP_PASSWORD_HERE_2024';
GRANT ALL PRIVILEGES ON DATABASE amazingpay TO amazingpay_app;

-- Verify changes
\du
\q
```

#### **Update Database Connection**
```bash
# Update your new .env file with new credentials
DATABASE_URL=postgresql://amazingpay_app:NEW_APP_PASSWORD_HERE_2024@localhost:5432/amazingpay
DB_PASSWORD=NEW_APP_PASSWORD_HERE_2024
```

---

### 🔑 **2. JWT SECRETS** (3 minutes)

#### **Generate New JWT Secret**
```bash
# Generate 256-bit secret
openssl rand -base64 64

# Example output: 
# XYZ123ABC456DEF789GHI012JKL345MNO678PQR901STU234VWX567YZA890BCD123EFG456HIJ789KLM012
```

#### **Update JWT Configuration**
```bash
# Add to your .env file
JWT_SECRET=XYZ123ABC456DEF789GHI012JKL345MNO678PQR901STU234VWX567YZA890BCD123EFG456HIJ789KLM012
JWT_REFRESH_SECRET=ABC123DEF456GHI789JKL012MNO345PQR678STU901VWX234YZA567BCD890EFG123HIJ456KLM789
```

---

### 🌐 **3. API KEYS ROTATION** (10 minutes)

#### **Binance API Keys**
1. Login to Binance → Account → API Management
2. **Delete old API keys immediately**
3. Create new API key with restrictions:
   - ✅ Enable Reading
   - ✅ Enable Spot & Margin Trading (if needed)
   - ❌ Disable Futures
   - ❌ Disable Withdrawals
   - Set IP restrictions to your server IP

```bash
# Update .env
BINANCE_API_KEY=your_new_binance_api_key
BINANCE_API_SECRET=your_new_binance_secret
```

#### **Etherscan API Key**
1. Go to Etherscan.io → My Account → API Keys
2. Generate new API key
3. Delete old key

```bash
# Update .env
ETHERSCAN_API_KEY=your_new_etherscan_key
```

#### **Twilio Credentials**
1. Login to Twilio Console → Account → API Keys & Tokens
2. Create new API Key
3. Delete old credentials

```bash
# Update .env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_new_twilio_token
```

---

### 📧 **4. EMAIL CREDENTIALS** (5 minutes)

#### **SMTP Password Reset**
1. Go to your email provider (Gmail, Outlook, etc.)
2. Generate new app-specific password
3. Delete old app password

```bash
# Update .env
EMAIL_PASSWORD=your_new_app_specific_password
SMTP_PASSWORD=your_new_smtp_password
```

---

### 🖥️ **5. SERVER SSH KEYS** (10 minutes)

#### **Generate New SSH Key Pair**
```bash
# Generate new SSH key
ssh-keygen -t ed25519 -C "amazingpay-secure-$(date +%Y%m%d)" -f ~/.ssh/amazingpay_new

# Copy public key to server
ssh-copy-id -i ~/.ssh/amazingpay_new.pub user@your-server-ip

# Test new key
ssh -i ~/.ssh/amazingpay_new user@your-server-ip

# If successful, remove old keys from server
ssh user@your-server-ip
nano ~/.ssh/authorized_keys
# Remove old key entries, keep only the new one
```

---

### 🔒 **6. ADDITIONAL SECURITY MEASURES** (5 minutes)

#### **CSRF Secret**
```bash
# Generate new CSRF secret
openssl rand -hex 32

# Update .env
CSRF_SECRET=your_new_csrf_secret_64_characters_long
```

#### **Session Secret**
```bash
# Generate session secret
openssl rand -base64 48

# Update .env
SESSION_SECRET=your_new_session_secret_here
```

---

## ✅ **VERIFICATION CHECKLIST**

### **Database** ☑️
- [ ] Old postgres password changed
- [ ] New application user created
- [ ] Database connection tested
- [ ] Application starts successfully

### **JWT** ☑️
- [ ] New JWT secret generated (256-bit)
- [ ] New refresh token secret generated
- [ ] Token generation tested
- [ ] Authentication working

### **API Keys** ☑️
- [ ] Binance old keys deleted
- [ ] Binance new keys created with restrictions
- [ ] Etherscan new key generated
- [ ] Twilio new credentials created
- [ ] All API integrations tested

### **Email** ☑️
- [ ] New app-specific password generated
- [ ] Old password deleted
- [ ] Email sending tested

### **SSH** ☑️
- [ ] New SSH key pair generated
- [ ] Public key added to server
- [ ] SSH connection tested
- [ ] Old keys removed from server

### **Application** ☑️
- [ ] New .env file created
- [ ] All credentials updated
- [ ] Application starts without errors
- [ ] All features working
- [ ] No credential-related errors in logs

---

## 🚨 **EMERGENCY ROLLBACK PLAN**

If anything breaks during rotation:

### **Database Rollback**
```sql
-- If new user doesn't work, temporarily use postgres
DATABASE_URL=postgresql://postgres:OLD_PASSWORD@localhost:5432/amazingpay
```

### **API Rollback**
```bash
# Keep old API keys active until new ones are verified
# Test each integration individually
```

### **SSH Rollback**
```bash
# Keep old SSH keys until new ones are confirmed working
# Test SSH access before removing old keys
```

---

## 📋 **POST-ROTATION TASKS**

1. **Test all application features**
2. **Monitor logs for credential errors**
3. **Update documentation with new setup**
4. **Schedule regular credential rotation (quarterly)**
5. **Update backup scripts with new credentials**

---

## ⏰ **TIMELINE**
- **Total Time**: 40 minutes
- **Critical Path**: Database → JWT → Application Test
- **Parallel Tasks**: API keys can be rotated while testing

**🔴 START IMMEDIATELY - EVERY MINUTE COUNTS!**
