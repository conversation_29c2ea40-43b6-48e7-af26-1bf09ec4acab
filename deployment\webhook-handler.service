[Unit]
Description=AmazingPay GitHub Webhook Handler
Documentation=https://github.com/your-username/amazingpay-flow
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/www/wwwroot/amazingpayme.com
ExecStart=/usr/bin/node scripts/github-webhook-handler.js
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# Environment variables
Environment=NODE_ENV=production
Environment=WEBHOOK_PORT=9000
Environment=WEBHOOK_SECRET=your-webhook-secret-here
Environment=APP_DIR=/www/wwwroot/amazingpayme.com
Environment=LOG_DIR=/var/log/amazingpay

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/www/wwwroot/amazingpayme.com /var/log/amazingpay /var/backups/amazingpay

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=amazingpay-webhook

[Install]
WantedBy=multi-user.target
