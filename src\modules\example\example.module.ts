import { Request, Response, NextFunction } from 'express';
// jscpd:ignore-file

import { ModuleFactory, ModuleRegistry, Container, Module } from '../../core/module';
import { Example as ImportedExample } from '@prisma/client';
import { Prisma as ImportedPrisma } from '@prisma/client';
import { logger, ErrorFactory } from '../../utils';
import { authMiddleware as ImportedauthMiddleware } from '../../middlewares/(auth).middleware';
import { Example as ImportedExample } from '@prisma/client';
import { Prisma as ImportedPrisma } from '@prisma/client';
import { logger, ErrorFactory } from '../../utils';
import { authMiddleware as ImportedauthMiddleware } from '../../middlewares/(auth).middleware';

/**
 * Example Module
 * This module provides a comprehensive example of using the enhanced factory system
 */
export class ExampleModule {
  private moduleFactory: ModuleFactory<
    Example,
    (Prisma).ExampleCreateInput,
    (Prisma).ExampleUpdateInput
  >;
  private moduleRegistry: ModuleRegistry;
  private container: Container;
  private module: Module;

  /**
   * Create a new example module
   */
  constructor() {
    this.moduleRegistry = new ModuleRegistry();
    this.container = new Container();

    // Create module factory
    this.moduleFactory = new ModuleFactory<
      Example,
      (Prisma).ExampleCreateInput,
      (Prisma).ExampleUpdateInput
    >('example', 'Example');

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('get', '/:id', (controller).getById)
      .addRoute('post', '/', (controller).create)
      .addRoute('put', '/:id', (controller).update)
      .addRoute('delete', '/:id', (controller).delete)
      .addRoute('get', '/search/:query', (controller).search)
      .addRoute('get', '/category/:category', (controller).getByCategory)
      .addRoute('get', '/status/:status', (controller).getByStatus)
      .addRoute('post', '/:id/activate', (controller).activate)
      .addRoute('post', '/:id/deactivate', (controller).deactivate)
      .addRoute('get', '/stats', (controller).getStats)
      .addMiddleware(authMiddleware);

    // Add custom repository methods
    this.moduleFactory.addRepositoryMethod('findByName', async (name: string) => {
      try {
        return await (repository).findByField('name', name);
      } catch (error) {
        logger.error(`Error finding example by name ${name}:`, error);
        throw error;
      }
    });

    this.moduleFactory.addRepositoryMethod(
      'findByCategory',
      async (category: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          return await (repository).findByFieldWithPagination('category', category, options);
        } catch (error) {
          logger.error(`Error finding examples by category ${category}:`, error);
          throw error;
        }
      }
    );

    this.moduleFactory.addRepositoryMethod(
      'findByStatus',
      async (status: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          return await (repository).findByFieldWithPagination('status', status, options);
        } catch (error) {
          logger.error(`Error finding examples by status ${status}:`, error);
          throw error;
        }
      }
    );

    this.moduleFactory.addRepositoryMethod(
      'search',
      async (query: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          const { limit = 10, offset = 0 } = options;

          // Search by name or description
          const examples = await (repository).findMany({
            where: {
              OR: [
                { name: { contains: query, mode: 'insensitive' } },
                { description: { contains: query, mode: 'insensitive' } },
              ],
            },
            skip: offset,
            take: limit,
          });

          // Count total results
          const total: number = await (repository).count({
            where: {
              OR: [
                { name: { contains: query, mode: 'insensitive' } },
                { description: { contains: query, mode: 'insensitive' } },
              ],
            },
          });

          return {
            data: examples,
            total,
          };
        } catch (error) {
          logger.error(`Error searching examples for query ${query}:`, error);
          throw error;
        }
      }
    );

    this.moduleFactory.addRepositoryMethod('getStats', async () => {
      try {
        // Get total count
        const total: number = await (repository).count();

        // Get count by status
        const activeCount = await (repository).count({
          where: { status: 'ACTIVE' },
        });

        const inactiveCount = await (repository).count({
          where: { status: 'INACTIVE' },
        });

        // Get count by category
        const categoryCounts = await (repository).prisma.(example).groupBy({
          by: ['category'],
          _count: { id: true },
        });

        // Get recent examples
        const recentExamples = await (repository).findMany({
          orderBy: { createdAt: 'desc' },
          take: 5,
        });

        return {
          total,
          byStatus: { active: activeCount, inactive: inactiveCount },
          byCategory: (categoryCounts).reduce((acc, curr) => {
            acc[(curr).type] = (curr)._count.id;
            return acc;
          }, {} as Record<string, number>),
          recent: recentExamples,
        };
      } catch (error) {
        logger.error('Error getting example stats:', error);
        throw error;
      }
    });

    // Add custom service methods
    this.moduleFactory.addServiceMethod(
      'search',
      async (query: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          return await (repository).search(query, options);
        } catch (error) {
          logger.error(`Error searching examples for query ${query}:`, error);
          throw (ErrorFactory).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'getByCategory',
      async (category: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          return await (repository).findByCategory(category, options);
        } catch (error) {
          logger.error(`Error getting examples by category ${category}:`, error);
          throw (ErrorFactory).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'getByStatus',
      async (status: string, options: { limit?: number; offset?: number } = {}) => {
        try {
          return await (repository).findByStatus(status, options);
        } catch (error) {
          logger.error(`Error getting examples by status ${status}:`, error);
          throw (ErrorFactory).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod('activate', async (id: string) => {
      try {
        // Get example
        const example = await (service).getById(id);

        // Check if example exists
        if (!example) {
          throw (ErrorFactory).notFound('Example', id);
        }

        // Check if example is already active
        if ((example).status === 'ACTIVE') {
          return example;
        }

        // Update example status
        const updatedExample = await (service).update(id, {
          status: 'ACTIVE',
          updatedAt: new Date(),
        } as (Prisma).ExampleUpdateInput);

        logger.info(`Example activated: ${id}`, {
          exampleId: id,
        });

        return updatedExample;
      } catch (error) {
        logger.error(`Error activating example ${id}:`, error);
        throw (ErrorFactory).handle(error);
      }
    });

    this.moduleFactory.addServiceMethod('deactivate', async (id: string) => {
      try {
        // Get example
        const example = await (service).getById(id);

        // Check if example exists
        if (!example) {
          throw (ErrorFactory).notFound('Example', id);
        }

        // Check if example is already inactive
        if ((example).status === 'INACTIVE') {
          return example;
        }

        // Update example status
        const updatedExample = await (service).update(id, {
          status: 'INACTIVE',
          updatedAt: new Date(),
        } as (Prisma).ExampleUpdateInput);

        logger.info(`Example deactivated: ${id}`, {
          exampleId: id,
        });

        return updatedExample;
      } catch (error) {
        logger.error(`Error deactivating example ${id}:`, error);
        throw (ErrorFactory).handle(error);
      }
    });

    this.moduleFactory.addServiceMethod('getStats', async () => {
      try {
        return await (repository).getStats();
      } catch (error) {
        logger.error('Error getting example stats:', error);
        throw (ErrorFactory).handle(error);
      }
    });

    // Add custom controller methods
    this.moduleFactory.addControllerMethod('search', async (req, res) => {
      try {
        // Get query from params
        const { query } = req.params;
        const { limit, offset } = req.query;

        // Parse pagination options
        const options: Record<string, unknown> = {
          limit: limit ? parseInt(limit as string, 10 : undefined,
          offset: offset ? parseInt(offset as string, 10 : undefined,
        };

        // Search examples
        const results = await (service).search(query, options);

        // Send success response
        return res.status(200).json({
          success: true,
          data: results,
        });
      } catch (error) {
        logger.error(`Error searching examples:`, error);
        return res.status(500).json({
          success: false,
          error: error.message || 'An error occurred while searching examples',
        });
      }
    });

    this.moduleFactory.addControllerMethod('getByCategory', async (req, res) => {
      try {
        // Get category from params
        const { category } = req.params;
        const { limit, offset } = req.query;

        // Parse pagination options
        const options: Record<string, unknown> = {
          limit: limit ? parseInt(limit as string, 10 : undefined,
          offset: offset ? parseInt(offset as string, 10 : undefined,
        };

        // Get examples by category
        const results = await (service).getByCategory(category, options);

        // Send success response
        return res.status(200).json({
          success: true,
          data: results,
        });
      } catch (error) {
        logger.error(`Error getting examples by category:`, error);
        return res.status(500).json({
          success: false,
          error: error.message || 'An error occurred while getting examples by category',
        });
      }
    });

    this.moduleFactory.addControllerMethod('getByStatus', async (req, res) => {
      try {
        // Get status from params
        const { status } = req.params;
        const { limit, offset } = req.query;

        // Parse pagination options
        const options: Record<string, unknown> = {
          limit: limit ? parseInt(limit as string, 10 : undefined,
          offset: offset ? parseInt(offset as string, 10 : undefined,
        };

        // Get examples by status
        const results = await (service).getByStatus(status, options);

        // Send success response
        return res.status(200).json({
          success: true,
          data: results,
        });
      } catch (error) {
        logger.error(`Error getting examples by status:`, error);
        return res.status(500).json({
          success: false,
          error: error.message || 'An error occurred while getting examples by status',
        });
      }
    });

    this.moduleFactory.addControllerMethod('activate', async (req, res) => {
      try {
        // Get example ID from params
        const { id } = req.params;

        // Activate example
        const example = await (service).activate(id);

        // Send success response
        return res.status(200).json({
          success: true,
          data: example,
        });
      } catch (error) {
        logger.error(`Error activating example:`, error);
        return res.status(500).json({
          success: false,
          error: error.message || 'An error occurred while activating example',
        });
      }
    });

    this.moduleFactory.addControllerMethod('deactivate', async (req, res) => {
      try {
        // Get example ID from params
        const { id } = req.params;

        // Deactivate example
        const example = await (service).deactivate(id);

        // Send success response
        return res.status(200).json({
          success: true,
          data: example,
        });
      } catch (error) {
        logger.error(`Error deactivating example:`, error);
        return res.status(500).json({
          success: false,
          error: error.message || 'An error occurred while deactivating example',
        });
      }
    });

    this.moduleFactory.addControllerMethod('getStats', async (req, res) => {
      try {
        // Get stats
        const stats = await (service).getStats();

        // Send success response
        return res.status(200).json({
          success: true,
          data: stats,
        });
      } catch (error) {
        logger.error(`Error getting example stats:`, error);
        return res.status(500).json({
          success: false,
          error: error.message || 'An error occurred while getting example stats',
        });
      }
    });

    // Create module
    this.module = {
      name: 'example',
      router,
      repository,
      service,
      controller,
      dependencies: [],
      initialize: async () => {
        logger.info('Initializing example module');

        // Register dependencies
        this.container.registerSingleton('exampleRepository', () => repository);
        this.container.registerSingleton('exampleService', () => service);
        this.container.registerSingleton('exampleController', () => controller);

        logger.info('Example module initialized');
      },
    };

    // Register the module
    this.moduleRegistry.registerModulethis.module);
  }

  /**
   * Get the module
   * @returns Example module
   */
  getModule(): Module {
    return this.module;
  }
}
