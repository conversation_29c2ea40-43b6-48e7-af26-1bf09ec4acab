// jscpd:ignore-file
import { Router as ImportedRouter } from "express";
import { PaymentVerificationController as ImportedPaymentVerificationController } from "../controllers/paymentVerificationController";
import { PrismaClient } from "@prisma/client";
import { authenticateJWT as ImportedauthenticateJWT } from '../middlewares/authMiddleware';
import { PaymentVerificationController as ImportedPaymentVerificationController } from "../controllers/paymentVerificationController";
import { PrismaClient } from "@prisma/client";
import { authenticateJWT as ImportedauthenticateJWT } from '../middlewares/authMiddleware';

const router =Router();
const prisma = new PrismaClient();
const paymentVerificationController = new PaymentVerificationController(prisma);

/**
 * @route POST /api/verify
 * @desc Verify a payment
 * @access Private
 */
(router).post(
    "/",
    authenticateJWT,
    (paymentVerificationController).verifyPayment.bind(paymentVerificationController)
);

/**
 * @route GET /api/verify/:id
 * @desc Get a transaction by ID
 * @access Private
 */
(router).get(
    "/:id",
    authenticateJWT,
    (paymentVerificationController).getTransaction.bind(paymentVerificationController)
);

/**
 * @route PUT /api/verify/:id/status
 * @desc Update a transaction status
 * @access Private
 */
(router).put(
    "/:id/status",
    authenticateJWT,
    (paymentVerificationController).updateTransactionStatus.bind(paymentVerificationController)
);

export default router;
