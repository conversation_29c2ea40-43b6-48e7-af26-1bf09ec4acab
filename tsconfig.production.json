{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "allowJs": true, "checkJs": false, "outDir": "./dist", "rootDir": "./src", "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noImplicitThis": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@amazingpay/server/*": ["src/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "forceConsistentCasingInFileNames": false, "resolveJsonModule": true, "allowUnreachableCode": true, "allowUnusedLabels": true, "noErrorTruncation": true, "preserveConstEnums": true, "removeComments": false, "sourceMap": true, "declaration": false, "declarationMap": false, "isolatedModules": false, "noEmitOnError": false, "noEmit": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "compileOnSave": false}