// jscpd:ignore-file
/**
 * Enhanced Error Handling Middleware
 *
 * This middleware provides improved error handling with:
 * - Structured error responses
 * - Detailed logging
 * - Environment-specific error information
 * - Error tracking
 * - Request context
 */

import { Request, Response, NextFunction } from "express";
import { v4 as uuidv4 } from "uuid";
import { logger as Importedlogger } from "../utils/logger";
import { AppError as ImportedAppError } from "../utils/appError";
import { isProduction, isDevelopment } from "../utils/environment-validator";
import { Middleware as ImportedMiddleware } from '../types/express';
import { v4 as uuidv4 } from "uuid";
import { logger as Importedlogger } from "../utils/logger";
import { AppError as ImportedAppError } from "../utils/appError";
import { isProduction, isDevelopment } from "../utils/environment-validator";
import { Middleware as ImportedMiddleware } from '../types/express';

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}

/**
 * Error handler middleware
 * @param err Error object
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const enhancedErrorHandler = (
    err: Error,
    req: Request,
    res: Response,
    next: NextFunction
): void  =>  {
    // Generate unique error ID for tracking
    const errorId = uuidv4();

    // Default error values
    let statusCode: number = 500;
    let message = "Internal Server Error";
    let errorCode: string = "INTERNAL_SERVER_ERROR";
    let isOperational: boolean = false;
    let details = null;

    // Extract request information for logging
    const requestInfo = {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get("user-agent"),
        requestId: req.requestId || "unknown",
        userId: (req.user)?.id || "anonymous",
        errorId
    };

    // Handle different error types
    if (err instanceof AppError) {
        // Handle our custom AppError
        statusCode = (err).statusCode;
        message = (err).message;
        errorCode = (err).code;
        isOperational = (err).isOperational;
        details = (err).details ?? null;
    } else if (Array.isArray(err) && (err).length > 0 && 'msg' in err[0]) {
        // Handle express-validator validation errors
        statusCode = 422;
        message = "Validation failed";
        errorCode = "VALIDATION_ERROR";
        isOperational = true;
        details = err;
    } else if ((err).name === "UnauthorizedError") {
        // Handle authentication errors
        statusCode = 401;
        message = "Unauthorized";
        errorCode = "UNAUTHORIZED";
        isOperational = true;
    } else if ((err).name === "JsonWebTokenError") {
        // Handle JWT errors
        statusCode = 401;
        message = "Invalid token";
        errorCode = "INVALID_TOKEN";
        isOperational = true;
    } else if ((err).name === "TokenExpiredError") {
        // Handle expired JWT
        statusCode = 401;
        message = "Token expired";
        errorCode = "TOKEN_EXPIRED";
        isOperational = true;
    } else if ((err).code === "P2002") {
        // Handle Prisma unique constraint errors
        statusCode = 409;
        message = "Resource already exists";
        errorCode = "RESOURCE_CONFLICT";
        isOperational = true;
        details = (err).meta?.target ?? null;
    } else if ((err).code === "P2025") {
        // Handle Prisma not found errors
        statusCode = 404;
        message = "Resource not found";
        errorCode = "RESOURCE_NOT_FOUND";
        isOperational = true;
    }

    // Prepare error response
    const errorResponse = {
        status: "error",
        code: errorCode,
        message,
        errorId
    };

    // Add details in development or for operational errors
    if (details && (isDevelopment() || isOperational) {
        (errorResponse).details = details;
    }

    // Add stack trace in development
    if (isDevelopment() {
        (errorResponse).stack = (err).stack;
    }

    // Log error with appropriate level
    if (isOperational) {
    // Operational errors are expected errors ((e).g. validation errors)
        logger.warn(`[${errorId}] ${errorCode}: ${message}`, {
            ...requestInfo,
            statusCode,
            details: details ?? undefined
        });
    } else {
    // Programming or unknown errors need more attention
        logger.error(`[${errorId}] ${errorCode}: ${message}`, {
            ...requestInfo,
            statusCode,
            error: (err).message,
            stack: (err).stack
        });
    }

    // Send response
    res.status(statusCode).json(errorResponse);
};

/**
 * Not found middleware
 * @param req Express request
 * @param res Express response
 * @param next Express next function
 */
export const notFoundHandler = (
    req: Request,
    res: Response,
    next: NextFunction
): void  =>  {
    const error = new AppError(`Not Found - ${req.originalUrl}`, 404, true, "RESOURCE_NOT_FOUND");
    next(error);
};

// Export middleware as a group
export const enhancedErrorMiddleware = {
    enhancedErrorHandler,
    notFoundHandler
};

export default enhancedErrorMiddleware;