#!/bin/bash

# AmazingPay Flow - Final 100% Completion Check
# This script verifies that everything is 100% production-ready

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🎯 AmazingPay Flow - 100% Completion Check${NC}"
    echo -e "${BLUE}==========================================\n${NC}"
}

print_section() {
    echo -e "${CYAN}📦 $1${NC}"
    echo "----------------------------------------"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to check application status
check_application_final() {
    print_section "APPLICATION STATUS"
    
    local score=0
    local total=4
    
    # Check PM2 processes
    if command -v pm2 &> /dev/null && pm2 list | grep -q "amazingpay-flow.*online"; then
        PROCESS_COUNT=$(pm2 list | grep "amazingpay-flow.*online" | wc -l)
        print_success "PM2 processes running ($PROCESS_COUNT instances)"
        score=$((score + 1))
    else
        print_error "PM2 processes not running"
    fi
    
    # Check health endpoint
    if curl -f http://localhost:3002/api/health > /dev/null 2>&1; then
        print_success "Health endpoint responding"
        score=$((score + 1))
    else
        print_error "Health endpoint not responding"
    fi
    
    # Check API endpoint
    if curl -f http://localhost:3002/api > /dev/null 2>&1; then
        print_success "API endpoint responding"
        score=$((score + 1))
    else
        print_error "API endpoint not responding"
    fi
    
    # Check port
    if lsof -i :3002 > /dev/null 2>&1; then
        print_success "Port 3002 in use"
        score=$((score + 1))
    else
        print_error "Port 3002 not in use"
    fi
    
    echo "Application Score: $score/$total"
    return $score
}

# Function to check database status
check_database_final() {
    print_section "DATABASE STATUS"
    
    local score=0
    local total=3
    
    # Check .env.production
    if [ -f ".env.production" ]; then
        source .env.production
        print_success ".env.production loaded"
        score=$((score + 1))
        
        if [ -n "$DATABASE_URL" ]; then
            print_success "DATABASE_URL configured"
            score=$((score + 1))
            
            # Test database connection with Node.js
            cat > temp_db_test.js << 'EOF'
const { PrismaClient } = require('@prisma/client');
async function testDB() {
    const prisma = new PrismaClient();
    try {
        await prisma.$connect();
        const userCount = await prisma.user.count();
        console.log('✅ Database connected, Users:', userCount);
        process.exit(0);
    } catch (error) {
        console.log('❌ Database connection failed');
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}
testDB();
EOF
            
            if node temp_db_test.js > /dev/null 2>&1; then
                print_success "Database connection verified"
                score=$((score + 1))
            else
                print_error "Database connection failed"
            fi
            
            rm temp_db_test.js
        else
            print_error "DATABASE_URL not configured"
        fi
    else
        print_error ".env.production not found"
    fi
    
    echo "Database Score: $score/$total"
    return $score
}

# Function to check configuration completeness
check_configuration_final() {
    print_section "CONFIGURATION STATUS"
    
    local score=0
    local total=6
    
    # Check essential files
    REQUIRED_FILES=(
        "package.json"
        "tsconfig.json"
        "prisma/schema.prisma"
        ".env.production"
        "ecosystem.config.js"
        "docker-compose.yml"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$file" ]; then
            print_success "$file exists"
            score=$((score + 1))
        else
            print_error "$file missing"
        fi
    done
    
    echo "Configuration Score: $score/$total"
    return $score
}

# Function to check scripts availability
check_scripts_final() {
    print_section "SCRIPTS STATUS"
    
    local score=0
    local total=8
    
    REQUIRED_SCRIPTS=(
        "production-manager.sh"
        "setup-external-services.sh"
        "setup-monitoring.sh"
        "setup-domain-ssl.sh"
        "backup-system.sh"
        "health-check.sh"
        "cleanup-system.sh"
        "check-status.sh"
    )
    
    for script in "${REQUIRED_SCRIPTS[@]}"; do
        if [ -f "scripts/$script" ] && [ -x "scripts/$script" ]; then
            print_success "$script (executable)"
            score=$((score + 1))
        else
            print_error "$script (missing or not executable)"
        fi
    done
    
    echo "Scripts Score: $score/$total"
    return $score
}

# Function to check monitoring setup
check_monitoring_final() {
    print_section "MONITORING STATUS"
    
    local score=0
    local total=4
    
    # Check monitoring directories
    if [ -d "monitoring" ]; then
        print_success "Monitoring directory exists"
        score=$((score + 1))
    else
        print_error "Monitoring directory missing"
    fi
    
    # Check backup directory
    if [ -d "backups" ]; then
        print_success "Backups directory exists"
        score=$((score + 1))
    else
        print_error "Backups directory missing"
    fi
    
    # Check logs directory
    if [ -d "logs" ]; then
        print_success "Logs directory exists"
        score=$((score + 1))
    else
        print_error "Logs directory missing"
    fi
    
    # Check if backup works
    if [ -f "scripts/backup-system.sh" ] && [ -x "scripts/backup-system.sh" ]; then
        print_success "Backup system ready"
        score=$((score + 1))
    else
        print_error "Backup system not ready"
    fi
    
    echo "Monitoring Score: $score/$total"
    return $score
}

# Function to check deployment readiness
check_deployment_final() {
    print_section "DEPLOYMENT READINESS"
    
    local score=0
    local total=5
    
    # Check if dist directory exists (production build)
    if [ -d "dist" ]; then
        print_success "Production build exists"
        score=$((score + 1))
    else
        print_error "Production build missing"
    fi
    
    # Check if node_modules exists
    if [ -d "node_modules" ]; then
        print_success "Dependencies installed"
        score=$((score + 1))
    else
        print_error "Dependencies not installed"
    fi
    
    # Check if Prisma client is generated
    if [ -d "node_modules/.prisma" ] || [ -d "node_modules/@prisma/client" ]; then
        print_success "Prisma client generated"
        score=$((score + 1))
    else
        print_error "Prisma client not generated"
    fi
    
    # Check Docker setup
    if [ -f "Dockerfile" ] && [ -f "docker-compose.yml" ]; then
        print_success "Docker configuration ready"
        score=$((score + 1))
    else
        print_error "Docker configuration incomplete"
    fi
    
    # Check if application is actually running
    if curl -f http://localhost:3002/api/health > /dev/null 2>&1; then
        print_success "Application running and accessible"
        score=$((score + 1))
    else
        print_error "Application not accessible"
    fi
    
    echo "Deployment Score: $score/$total"
    return $score
}

# Function to calculate overall completion percentage
calculate_completion() {
    local app_score=$1
    local db_score=$2
    local config_score=$3
    local scripts_score=$4
    local monitoring_score=$5
    local deployment_score=$6
    
    local total_score=$((app_score + db_score + config_score + scripts_score + monitoring_score + deployment_score))
    local max_score=30  # 4+3+6+8+4+5
    local percentage=$((total_score * 100 / max_score))
    
    echo ""
    print_section "OVERALL COMPLETION STATUS"
    echo "Total Score: $total_score/$max_score"
    echo "Completion: $percentage%"
    echo ""
    
    if [ $percentage -ge 95 ]; then
        print_success "🎉 CONGRATULATIONS! Your AmazingPay Flow is 100% PRODUCTION-READY!"
        echo ""
        print_info "🚀 Ready for:"
        print_info "   ✅ Production deployment"
        print_info "   ✅ Real-world transactions"
        print_info "   ✅ Scaling and monitoring"
        print_info "   ✅ External integrations"
    elif [ $percentage -ge 90 ]; then
        print_success "🎯 EXCELLENT! Your AmazingPay Flow is 95%+ ready!"
        print_info "Just a few minor items to complete for 100%"
    elif [ $percentage -ge 80 ]; then
        print_warning "⚠️  GOOD! Your AmazingPay Flow is 80%+ ready!"
        print_info "Some important items need attention"
    else
        print_error "❌ More work needed to reach production readiness"
    fi
    
    return $percentage
}

# Function to show next actions
show_next_actions() {
    local percentage=$1
    
    echo ""
    print_section "NEXT ACTIONS"
    
    if [ $percentage -ge 95 ]; then
        echo "🎯 Your system is production-ready! You can:"
        echo "   1. Deploy to production: docker-compose up -d"
        echo "   2. Configure domain: bash scripts/setup-domain-ssl.sh"
        echo "   3. Set up external services: bash scripts/setup-external-services.sh"
        echo "   4. Enable monitoring: docker-compose --profile monitoring up -d"
    else
        echo "🔧 To reach 100% completion:"
        echo "   1. Fix any failed checks above"
        echo "   2. Run: bash scripts/production-manager.sh start"
        echo "   3. Run: bash scripts/setup-monitoring.sh"
        echo "   4. Run: bash scripts/backup-system.sh"
        echo "   5. Re-run this check: bash scripts/final-completion-check.sh"
    fi
}

# Main execution
main() {
    print_header
    
    echo "Performing comprehensive 100% completion check..."
    echo "================================================="
    echo ""
    
    # Run all checks
    check_application_final
    APP_SCORE=$?
    echo ""
    
    check_database_final
    DB_SCORE=$?
    echo ""
    
    check_configuration_final
    CONFIG_SCORE=$?
    echo ""
    
    check_scripts_final
    SCRIPTS_SCORE=$?
    echo ""
    
    check_monitoring_final
    MONITORING_SCORE=$?
    echo ""
    
    check_deployment_final
    DEPLOYMENT_SCORE=$?
    echo ""
    
    # Calculate overall completion
    calculate_completion $APP_SCORE $DB_SCORE $CONFIG_SCORE $SCRIPTS_SCORE $MONITORING_SCORE $DEPLOYMENT_SCORE
    PERCENTAGE=$?
    
    show_next_actions $PERCENTAGE
    
    echo ""
    print_info "📊 Detailed status: bash scripts/check-status.sh"
    print_info "🔧 Manage application: bash scripts/production-manager.sh"
    print_info "💾 Create backup: bash scripts/backup-system.sh"
    
    exit $((100 - PERCENTAGE))
}

# Run completion check
main "$@"
