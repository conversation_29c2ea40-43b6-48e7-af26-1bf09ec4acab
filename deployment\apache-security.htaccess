# APACHE SECURITY CONFIGURATION
# Critical Financial Application - Production Ready
# Place this as .htaccess in your web root directory

# CRITICAL: Block access to hidden files and directories
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to environment files
<FilesMatch "\.(env|environment)$">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to configuration files
<FilesMatch "\.(config|conf|ini|cfg|cnf)$">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to source code files
<FilesMatch "\.(ts|tsx|js\.map|json|md|txt|yml|yaml|toml)$">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to package and build files
<FilesMatch "(package\.json|package-lock\.json|yarn\.lock|composer\.json|composer\.lock|tsconfig\.json|webpack\.config\.js|vite\.config\.js|next\.config\.js|nuxt\.config\.js|\.gitignore|\.gitmodules|\.dockerignore|Dockerfile|docker-compose\.yml)$">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to log files
<FilesMatch "\.(log|logs)$">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to backup and temporary files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp|temp|~|#)$">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to database files
<FilesMatch "\.(sql|sqlite|db|mdb)$">
    Require all denied
</FilesMatch>

# CRITICAL: Block access to sensitive directories
<DirectoryMatch "/(config|src|app|storage|logs|node_modules|vendor|\.git|\.svn|\.hg|tests|test|spec|docs|documentation|private|admin|wp-admin|phpmyadmin)/">
    Require all denied
</DirectoryMatch>

# CRITICAL: Disable directory browsing
Options -Indexes

# Block access to version control directories
<DirectoryMatch "\.git">
    Require all denied
</DirectoryMatch>

<DirectoryMatch "\.svn">
    Require all denied
</DirectoryMatch>

<DirectoryMatch "\.hg">
    Require all denied
</DirectoryMatch>

# Block access to common admin panels
<DirectoryMatch "/(admin|administrator|wp-admin|phpmyadmin|pma|adminer|cpanel|webmail)/">
    Require all denied
</DirectoryMatch>

# SECURITY HEADERS (CRITICAL FOR FINANCIAL APPS)
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Control referrer information
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'self';"
    
    # HTTP Strict Transport Security (HSTS)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide Apache version information
ServerTokens Prod
ServerSignature Off

# FORCE HTTPS
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect HTTP to HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Block access to sensitive files via rewrite
    RewriteRule ^\.env - [F,L]
    RewriteRule ^\.env\. - [F,L]
    RewriteRule ^config/ - [F,L]
    RewriteRule ^src/ - [F,L]
    RewriteRule ^app/ - [F,L]
    RewriteRule ^storage/ - [F,L]
    RewriteRule ^logs/ - [F,L]
    RewriteRule ^node_modules/ - [F,L]
    RewriteRule ^vendor/ - [F,L]
    RewriteRule ^\.git/ - [F,L]
    RewriteRule ^\.svn/ - [F,L]
    RewriteRule ^\.hg/ - [F,L]
    
    # Block access to package files
    RewriteRule ^package\.json$ - [F,L]
    RewriteRule ^package-lock\.json$ - [F,L]
    RewriteRule ^yarn\.lock$ - [F,L]
    RewriteRule ^composer\.json$ - [F,L]
    RewriteRule ^composer\.lock$ - [F,L]
    RewriteRule ^tsconfig\.json$ - [F,L]
    RewriteRule ^webpack\.config\.js$ - [F,L]
    RewriteRule ^\.gitignore$ - [F,L]
    RewriteRule ^Dockerfile$ - [F,L]
    RewriteRule ^docker-compose\.yml$ - [F,L]
    
    # Block access to backup files
    RewriteRule \.(bak|backup|old|orig|save|swp|tmp|temp)$ - [F,L]
    
    # Block access to log files
    RewriteRule \.(log|logs)$ - [F,L]
    
    # Block access to database files
    RewriteRule \.(sql|sqlite|db|mdb)$ - [F,L]
</IfModule>

# RATE LIMITING (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
    DOSLogDir           /var/log/apache2/evasive
</IfModule>

# COMPRESSION AND CACHING
<IfModule mod_deflate.c>
    # Compress text files
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# STATIC ASSET CACHING
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# MIME TYPE SECURITY
<IfModule mod_mime.c>
    # Force correct MIME types
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    
    # Prevent execution of scripts in uploads directory
    <Directory "uploads">
        <FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
            Require all denied
        </FilesMatch>
    </Directory>
</IfModule>

# CUSTOM ERROR PAGES
ErrorDocument 400 /error/400.html
ErrorDocument 401 /error/401.html
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html
ErrorDocument 502 /error/502.html
ErrorDocument 503 /error/503.html

# SECURITY LOGGING
<IfModule mod_log_config.c>
    # Log security violations
    LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" \"%{X-Forwarded-For}i\"" security
    
    # Log attempts to access sensitive files
    SetEnvIf Request_URI "\.(env|config|git|svn)" security_violation
    CustomLog /var/log/apache2/security_violations.log security env=security_violation
</IfModule>

# ADDITIONAL SECURITY MEASURES

# Prevent access to PHP files in certain directories
<Directory "uploads">
    <Files "*.php">
        Require all denied
    </Files>
</Directory>

<Directory "assets">
    <Files "*.php">
        Require all denied
    </Files>
</Directory>

# Limit file upload size (adjust as needed)
LimitRequestBody 10485760

# Disable server-side includes
Options -Includes

# Disable CGI execution
Options -ExecCGI

# Disable symbolic links
Options -FollowSymLinks

# Prevent access to .htaccess files
<Files ".htaccess">
    Require all denied
</Files>

# Prevent access to .htpasswd files
<Files ".htpasswd">
    Require all denied
</Files>

# Block suspicious user agents
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|wget|python|nikto|curl|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Block common attack patterns
<IfModule mod_rewrite.c>
    # Block SQL injection attempts
    RewriteCond %{QUERY_STRING} (union.*select|insert.*into|delete.*from|drop.*table) [NC]
    RewriteRule .* - [F,L]
    
    # Block XSS attempts
    RewriteCond %{QUERY_STRING} (<script|javascript:|vbscript:|onload|onerror) [NC]
    RewriteRule .* - [F,L]
    
    # Block file inclusion attempts
    RewriteCond %{QUERY_STRING} (\.\.\/|\.\.\\|\/etc\/passwd|\/proc\/self\/environ) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# END OF SECURITY CONFIGURATION
