"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyToken = exports.generateRefreshToken = exports.generateToken = exports.initializeJwtConfig = void 0;
// jscpd:ignore-file;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = require("../lib/logger");
const error_middleware_1 = __importDefault(require("../middlewares/error.middleware"));
const secrets_manager_1 = __importDefault(require("../utils/secrets-manager")); // User interface for JWT operations;
// Initialize secrets manager
let JWT_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN ?? '1d';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN ?? '7d';
/**
 * Initialize JWT configuration
 * This must be called before using any JWT functions
 */
const initializeJwtConfig = async () => {
    try {
        // Initialize secrets manager
        await secrets_manager_1.default.initialize();
        // Get JWT secret
        JWT_SECRET = secrets_manager_1.default.getSecret('JWT_SECRET')?.value;
        if (!JWT_SECRET) {
            throw new Error('JWT secret not found in secrets manager');
        }
        logger_1.logger.info('JWT configuration initialized successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to initialize JWT configuration:', error);
        throw new Error('Failed to initialize JWT configuration');
    }
};
exports.initializeJwtConfig = initializeJwtConfig;
// Generate access token
const generateToken = async (user) => {
    // Ensure JWT configuration is initialized
    if (!JWT_SECRET) {
        await (0, exports.initializeJwtConfig)();
    }
    const payload = {
        userId: user.id,
        email: user.email,
        role: user.role || 'MERCHANT',
        type: 'access',
        environment: process.env.NODE_ENV || 'development',
    };
    return jsonwebtoken_1.default.sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN,
        algorithm: 'HS256',
        issuer: `amazingpay-api-${process.env.NODE_ENV || 'development'}`,
        audience: `amazingpay-client-${process.env.NODE_ENV || 'development'}`,
    });
};
exports.generateToken = generateToken;
// Generate refresh token
const generateRefreshToken = async (user) => {
    // Ensure JWT configuration is initialized
    if (!JWT_SECRET) {
        await (0, exports.initializeJwtConfig)();
    }
    const payload = {
        userId: user.id,
        type: 'refresh',
        environment: process.env.NODE_ENV || 'development',
    };
    return jsonwebtoken_1.default.sign(payload, JWT_SECRET, {
        expiresIn: JWT_REFRESH_EXPIRES_IN,
        algorithm: 'HS256',
        issuer: `amazingpay-api-${process.env.NODE_ENV || 'development'}`,
        audience: `amazingpay-client-${process.env.NODE_ENV || 'development'}`,
    });
};
exports.generateRefreshToken = generateRefreshToken;
// Verify JWT token
const verifyToken = async (token) => {
    // Ensure JWT configuration is initialized
    if (!JWT_SECRET) {
        await (0, exports.initializeJwtConfig)();
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET, {
            algorithms: ['HS256'],
            issuer: `amazingpay-api-${process.env.NODE_ENV ?? 'development'}`,
            audience: `amazingpay-client-${process.env.NODE_ENV ?? 'development'}`,
        });
        // Verify that the token was issued for this environment
        if (decoded.environment !== process.env.NODE_ENV) {
            logger_1.logger.warn(`JWT token environment mismatch: token=${decoded.environment}, current=${process.env.NODE_ENV}`);
            throw new error_middleware_1.default('Invalid token for this environment', 401, true);
        }
        return decoded;
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            throw new error_middleware_1.default('Token has expired', 401);
        }
        else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            throw new error_middleware_1.default('Invalid token', 401);
        }
        throw new error_middleware_1.default('Token verification failed', 401);
    }
};
exports.verifyToken = verifyToken;
exports.default = {
    JWT_EXPIRES_IN,
    JWT_REFRESH_EXPIRES_IN,
    initializeJwtConfig: exports.initializeJwtConfig,
    generateToken: exports.generateToken,
    generateRefreshToken: exports.generateRefreshToken,
    verifyToken: exports.verifyToken,
    // Get JWT secret (for internal use only)
    getJwtSecret: () => JWT_SECRET,
};
