{"compilerOptions": {"target": "ES2020", "module": "commonjs", "allowJs": true, "checkJs": false, "outDir": "./dist", "rootDir": "./src", "strict": false, "noImplicitAny": false, "skipLibCheck": true, "skipDefaultLibCheck": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "resolveJsonModule": true, "allowUnreachableCode": true, "allowUnusedLabels": true, "noEmitOnError": false, "isolatedModules": false, "preserveConstEnums": true, "removeComments": false, "sourceMap": false, "declaration": false, "noResolve": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/**/index.ts"], "compileOnSave": false}