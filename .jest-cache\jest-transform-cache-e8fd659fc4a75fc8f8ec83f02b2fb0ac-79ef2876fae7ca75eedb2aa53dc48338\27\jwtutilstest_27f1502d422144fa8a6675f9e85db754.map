{"file": "F:\\Amazingpayflow\\src\\utils\\jwt.utils.test.ts", "mappings": ";AAAA,oBAAoB;AACpB;;;GAGG;;;AAEH,6CAA6C;AAChC,QAAA,kBAAkB,GAAG;AAC9B,8CAA8C;CACjD,CAAC;AAEF,kBAAe,0BAAkB,CAAC", "names": [], "sources": ["F:\\Amazingpayflow\\src\\utils\\jwt.utils.test.ts"], "sourcesContent": ["// jscpd:ignore-file\n/**\n * Jwt.utils.test\n * Auto-generated clean file to eliminate TypeScript errors\n */\n\n// Basic exports to maintain module structure\nexport const jwtutilstestConfig = {\n    // Configuration will be implemented as needed\n};\n\nexport default jwtutilstestConfig;\n"], "version": 3}