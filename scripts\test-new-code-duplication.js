#!/usr/bin/env node

/**
 * Test New Code Duplication Script
 * 
 * This script checks if new or modified code introduces duplication.
 * It's designed to be used in CI/CD pipelines for pull requests.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Configuration
const THRESHOLD = 0; // 0% duplication threshold
const CONFIG_FILE = '.jscpd.json';
const TEMP_DIR = '.jscpd-temp';
const REPORT_FILE = 'jscpd-report.json';

// Get changed files
function getChangedFiles() {
  try {
    // Get the list of changed files compared to the main branch
    const output = execSync('git diff --name-only origin/main HEAD').toString().trim();
    
    if (!output) {
      return [];
    }
    
    return output.split('\n')
      .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
      .map(file => path.resolve(file));
  } catch (error) {
    console.error('Error getting changed files:', error.message);
    return [];
  }
}

// Main function
async function main() {
  console.log('Testing new code for duplication...');
  
  // Get changed files
  const changedFiles = getChangedFiles();
  
  if (changedFiles.length === 0) {
    console.log('No relevant files changed. Skipping duplication check.');
    process.exit(0);
  }
  
  console.log(`Found ${changedFiles.length} changed files to check.`);
  
  // Ensure the temp directory exists
  if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
  }
  
  try {
    // Run jscpd on changed files only
    const filesArg = changedFiles.join(' ');
    execSync(`npx jscpd ${filesArg} --config ${CONFIG_FILE} --threshold ${THRESHOLD} --reporters json --output ${TEMP_DIR}`, {
      stdio: 'inherit'
    });
    
    console.log('✅ No duplication found in changed files!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Duplication found in changed files!');
    
    // Try to read the report to provide more details
    try {
      const reportPath = path.join(TEMP_DIR, REPORT_FILE);
      if (fs.existsSync(reportPath)) {
        const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
        
        console.log('\nDuplication Statistics:');
        console.log(`- Duplication Percentage: ${report.statistics.total.percentage}%`);
        console.log(`- Duplicated Lines: ${report.statistics.total.duplicatedLines} of ${report.statistics.total.lines}`);
        console.log(`- Clones Found: ${report.duplicates.length}`);
        
        if (report.duplicates.length > 0) {
          console.log('\nDuplications in Changed Files:');
          
          report.duplicates.forEach((dup, index) => {
            console.log(`\n${index + 1}. Between ${dup.firstFile.name} and ${dup.secondFile.name}`);
            console.log(`   First file: Lines ${dup.firstFile.start}-${dup.firstFile.end}`);
            console.log(`   Second file: Lines ${dup.secondFile.start}-${dup.secondFile.end}`);
            console.log('   Size: [REDACTED FOR SECURITY]');
            
            // Show a snippet of the duplicated code
            console.log('\n   Duplicated code snippet:');
            const snippet = dup.fragment.split('\n').slice(0, 3).join('\n');
            console.log(`   ${snippet}${dup.fragment.split('\n').length > 3 ? '\n   ...' : ''}`);
          });
        }
        
        console.log('\nTo fix duplication issues:');
        console.log('1. Use shared modules for common functionality');
        console.log('2. Extract duplicated code into reusable functions');
        console.log('3. Run "npm run check:duplication:fix" for assistance');
      }
    } catch (readError) {
      console.error('Error reading duplication report:', readError.message);
    }
    
    process.exit(1);
  } finally {
    // Clean up
    fs.rmSync(TEMP_DIR, { recursive: true, force: true });
  }
}

// Run the main function
main().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
