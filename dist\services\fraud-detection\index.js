"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VelocityRiskDetector = void 0;
/** Fraud Detection Module * * Centralized exports for the fraud detection system.*/ // Core exports export { FraudDetectionService } from './core /FraudDetectionService';
__exportStar(require("./core /FraudDetectionTypes"), exports); // Detector exports export { AmountRiskDetector } from './detectors /AmountRiskDetector';
var VelocityRiskDetector_1 = require("./detectors /VelocityRiskDetector"); // Rule engine exports export { RiskRuleEngine, HighRiskCountryRule, BlacklistRule, SuspiciousAmountPatternRule, UnusualTimeRule } from './rules /RiskRuleEngine';// Default export - main service class export { FraudDetectionService as default } from './core /FraudDetectionService';
Object.defineProperty(exports, "VelocityRiskDetector", { enumerable: true, get: function () { return VelocityRiskDetector_1.VelocityRiskDetector; } });
