{"file": "F:\\Amazingpayflow\\src\\tests\\unit\\database.config.test.ts", "mappings": ";;;AAAA,oBAAoB;AACP,QAAA,cAAc,GAAG;IAC1B,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE;IACnC,yBAAyB;CAC5B,CAAC;AAEF,kBAAe,sBAAc,CAAC", "names": [], "sources": ["F:\\Amazingpayflow\\src\\tests\\unit\\database.config.test.ts"], "sourcesContent": ["// jscpd:ignore-file\nexport const databaseConfig = {\n    url: process.env.DATABASE_URL ?? '',\n    // Database configuration\n};\n\nexport default databaseConfig;\n"], "version": 3}