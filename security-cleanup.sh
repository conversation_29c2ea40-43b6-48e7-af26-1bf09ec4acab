#!/bin/bash
# 🚨 EMERGENCY SECURITY CLEANUP SCRIPT
# This script removes sensitive files from Git history

echo "🚨 STARTING EMERGENCY SECURITY CLEANUP..."
echo "⚠️  WARNING: This will rewrite Git history!"
echo "📋 Make sure you have backups before proceeding"

read -p "Are you sure you want to continue? (yes/no): " confirm
if [ "$confirm" != "yes" ]; then
    echo "❌ Cleanup cancelled"
    exit 1
fi

echo "🧹 Removing sensitive files from Git history..."

# Remove environment files
echo "🔒 Cleaning .env files..."
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch .env .env.local .env.production .env.staging .env.development .env.test' \
--prune-empty --tag-name-filter cat -- --all

# Remove configuration files
echo "🔧 Cleaning configuration files..."
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch ecosystem.config.js ecosystem.vps.config.js' \
--prune-empty --tag-name-filter cat -- --all

# Remove deployment scripts
echo "🚀 Cleaning deployment scripts..."
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch deploy-on-vps.sh backup-vps.sh vps-setup.sh upload-to-github.sh upload-to-github.bat' \
--prune-empty --tag-name-filter cat -- --all

# Remove Docker override files
echo "🐳 Cleaning Docker override files..."
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch docker-compose.override.yml docker-compose.production.yml' \
--prune-empty --tag-name-filter cat -- --all

# Remove database files
echo "🗄️ Cleaning database files..."
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch *.db *.sqlite prisma/dev.db' \
--prune-empty --tag-name-filter cat -- --all

# Remove log files
echo "📝 Cleaning log files..."
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch logs/* *.log npm-debug.log*' \
--prune-empty --tag-name-filter cat -- --all

# Clean up refs
echo "🧽 Cleaning up references..."
rm -rf .git/refs/original/
git reflog expire --expire=now --all
git gc --prune=now --aggressive

echo "✅ Git history cleanup completed!"
echo "⚠️  IMPORTANT: You must force push to update remote repository:"
echo "   git push origin --force --all"
echo "   git push origin --force --tags"
echo ""
echo "🔄 Next steps:"
echo "1. Rotate ALL credentials immediately"
echo "2. Update .gitignore file"
echo "3. Create .env.example files"
echo "4. Set up GitHub Secrets"
