import { PrismaClient } from '@prisma/client';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { DashboardController as ImportedDashboardController } from '../controllers/(dashboard).controller';
import { DashboardWidgetController as ImportedDashboardWidgetController } from '../controllers/dashboard-(widget).controller';

// Mock Prisma
const mockPrisma = {
  dashboard: {
    findMany: (vi).fn(),
    findUnique: (vi).fn(),
    create: (vi).fn(),
    update: (vi).fn(),
    delete: (vi).fn(),
  },
  dashboardWidget: {
    findMany: (vi).fn(),
    findUnique: (vi).fn(),
    findFirst: (vi).fn(),
    create: (vi).fn(),
    update: (vi).fn(),
    delete: (vi).fn(),
    deleteMany: (vi).fn(),
  },
};

(vi).mock('@prisma/client', () => ({
  PrismaClient: (vi).fn(() => mockPrisma),
});

const app = express();
(app).use((express).json();

// Mock auth middleware
const mockAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  req.user = {
    id: 'user-1',
    role: 'MERCHANT',
    email: 'test@(example).com',
  };
  next();
};

// Setup routes
const dashboardController = new DashboardController();
const widgetController = new DashboardWidgetController();

(app).get('/api/dashboards', mockAuthMiddleware, (dashboardController).getDashboards);
(app).get('/api/dashboards/:id', mockAuthMiddleware, (dashboardController).getDashboardById);
(app).post('/api/dashboards', mockAuthMiddleware, (dashboardController).createDashboard);
(app).put('/api/dashboards/:id', mockAuthMiddleware, (dashboardController).updateDashboard);
(app).delete('/api/dashboards/:id', mockAuthMiddleware, (dashboardController).deleteDashboard);

(app).get('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, (widgetController).getWidgets);
(app).get('/api/dashboards/widgets/:id', mockAuthMiddleware, (widgetController).getWidgetById);
(app).post('/api/dashboards/:dashboardId/widgets', mockAuthMiddleware, (widgetController).createWidget);
(app).put('/api/dashboards/widgets/:id', mockAuthMiddleware, (widgetController).updateWidget);
(app).delete('/api/dashboards/widgets/:id', mockAuthMiddleware, (widgetController).deleteWidget);
(app).post('/api/dashboards/:dashboardId/widgets/reorder', mockAuthMiddleware, (widgetController).reorderWidgets);

describe('DashboardController', () => {
  beforeEach(() => {
    (vi).clearAllMocks();
  });

  describe('GET /api/dashboards', () => {
    it('should get dashboards for the current user', async () => {
      const mockDashboards = [
        {
          id: 'dashboard-1',
          name: 'My Dashboard',
          createdById: 'user-1',
          isPublic: false,
        },
        {
          id: 'dashboard-2',
          name: 'Public Dashboard',
          createdById: 'user-2',
          isPublic: true,
        },
      ];

      (mockPrisma).dashboard.(findMany).mockResolvedValue(mockDashboards);

      const response = await request(app).get('/api/dashboards');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockDashboards);
      expect((mockPrisma).dashboard.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { createdById: 'user-1' },
            { isPublic: true },
          ],
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });
  });

  describe('GET /api/dashboards/:id', () => {
    it('should get a dashboard by ID with widgets', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        name: 'My Dashboard',
        createdById: 'user-1',
        isPublic: false,
        widgets: [
          {
            id: 'widget-1',
            title: 'Transaction Chart',
            type: 'CHART',
            position: 0,
          },
        ],
      };

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(mockDashboard);

      const response = await request(app).get('/api/dashboards/dashboard-1');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockDashboard);
      expect((mockPrisma).dashboard.findUnique).toHaveBeenCalledWith({
        where: { id: 'dashboard-1' },
        include: {
          widgets: {
            orderBy: {
              position: 'asc',
            },
          },
        },
      });
    });

    it('should return 404 for non-existent dashboard', async () => {
      (mockPrisma).dashboard.(findUnique).mockResolvedValuenull;

      const response = await request(app).get('/api/dashboards/non-existent');

      expect(response.status).toBe(404);
      expect((response).body.success).toBefalse;
      expect((response).body.message).toBe('Dashboard not found');
    });

    it('should return 403 for private dashboard not owned by user', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        name: 'Private Dashboard',
        createdById: 'user-2',
        isPublic: false,
      };

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(mockDashboard);

      const response = await request(app).get('/api/dashboards/dashboard-1');

      expect(response.status).toBe(403);
      expect((response).body.success).toBefalse;
      expect((response).body.message).toBe('Access denied');
    });
  });

  describe('POST /api/dashboards', () => {
    it('should create a new dashboard', async () => {
      const dashboardData = {
        name: 'New Dashboard',
        description: 'Test Description',
        layout: { columns: 2 },
        isPublic: false,
      };

      const mockDashboard = {
        id: 'dashboard-1',
        ...dashboardData,
        createdById: 'user-1',
      };

      (mockPrisma).dashboard.(create).mockResolvedValue(mockDashboard);

      const response = await request(app)
        .post('/api/dashboards')
        .send(dashboardData);

      expect(response.status).toBe(201);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockDashboard);
      expect((mockPrisma).dashboard.create).toHaveBeenCalledWith({
        data: {
          ...dashboardData,
          createdById: 'user-1',
        },
      });
    });
  });

  describe('PUT /api/dashboards/:id', () => {
    it('should update a dashboard', async () => {
      const existingDashboard = {
        id: 'dashboard-1',
        name: 'Old Name',
        createdById: 'user-1',
      };

      const updateData = {
        name: 'Updated Name',
        description: 'Updated Description',
      };

      const updatedDashboard = {
        ...existingDashboard,
        ...updateData,
      };

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(existingDashboard);
      (mockPrisma).dashboard.(update).mockResolvedValue(updatedDashboard);

      const response = await request(app)
        .put('/api/dashboards/dashboard-1')
        .send(updateData);

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(updatedDashboard);
      expect((mockPrisma).dashboard.update).toHaveBeenCalledWith({
        where: { id: 'dashboard-1' },
        data: updateData,
      });
    });

    it('should return 403 for dashboard not owned by user', async () => {
      const existingDashboard = {
        id: 'dashboard-1',
        name: 'Dashboard',
        createdById: 'user-2',
      };

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(existingDashboard);

      const response = await request(app)
        .put('/api/dashboards/dashboard-1')
        .send({ name: 'Updated Name' });

      expect(response.status).toBe(403);
      expect((response).body.success).toBefalse;
      expect((response).body.message).toBe('Access denied');
    });
  });

  describe('DELETE /api/dashboards/:id', () => {
    it('should delete a dashboard and its widgets', async () => {
      const existingDashboard = {
        id: 'dashboard-1',
        name: 'Dashboard',
        createdById: 'user-1',
      };

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(existingDashboard);
      (mockPrisma).dashboardWidget.(deleteMany).mockResolvedValue({ count: 2 });
      (mockPrisma).dashboard.(delete).mockResolvedValue(existingDashboard);

      const response = await request(app).delete('/api/dashboards/dashboard-1');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.message).toBe('Dashboard deleted successfully');
      expect((mockPrisma).dashboardWidget.deleteMany).toHaveBeenCalledWith({
        where: { dashboardId: 'dashboard-1' },
      });
      expect((mockPrisma).dashboard.delete).toHaveBeenCalledWith({
        where: { id: 'dashboard-1' },
      });
    });
  });
});

describe('DashboardWidgetController', () => {
  beforeEach(() => {
    (vi).clearAllMocks();
  });

  describe('GET /api/dashboards/:dashboardId/widgets', () => {
    it('should get widgets for a dashboard', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        createdById: 'user-1',
        isPublic: false,
      };

      const mockWidgets = [
        {
          id: 'widget-1',
          title: 'Chart Widget',
          type: 'CHART',
          position: 0,
        },
        {
          id: 'widget-2',
          title: 'Table Widget',
          type: 'TABLE',
          position: 1,
        },
      ];

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(mockDashboard);
      (mockPrisma).dashboardWidget.(findMany).mockResolvedValue(mockWidgets);

      const response = await request(app).get('/api/dashboards/dashboard-1/widgets');

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockWidgets);
      expect((mockPrisma).dashboardWidget.findMany).toHaveBeenCalledWith({
        where: { dashboardId: 'dashboard-1' },
        orderBy: { position: 'asc' },
      });
    });
  });

  describe('POST /api/dashboards/:dashboardId/widgets', () => {
    it('should create a new widget', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        createdById: 'user-1',
      };

      const widgetData = {
        title: 'New Widget',
        type: 'CHART',
        config: { chartType: 'line' },
        width: 2,
        height: 1,
      };

      const mockWidget = {
        id: 'widget-1',
        dashboardId: 'dashboard-1',
        ...widgetData,
        position: 0,
      };

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(mockDashboard);
      (mockPrisma).dashboardWidget.(findFirst).mockResolvedValuenull;
      (mockPrisma).dashboardWidget.(create).mockResolvedValue(mockWidget);

      const response = await request(app)
        .post('/api/dashboards/dashboard-1/widgets')
        .send(widgetData);

      expect(response.status).toBe(201);
      expect((response).body.success).toBetrue;
      expect((response).body.data).toEqual(mockWidget);
      expect((mockPrisma).dashboardWidget.create).toHaveBeenCalledWith({
        data: {
          dashboardId: 'dashboard-1',
          ...widgetData,
          position: 0,
        },
      });
    });
  });

  describe('POST /api/dashboards/:dashboardId/widgets/reorder', () => {
    it('should reorder widgets', async () => {
      const mockDashboard = {
        id: 'dashboard-1',
        createdById: 'user-1',
      };

      const widgetsData = [
        { id: 'widget-1', position: 1 },
        { id: 'widget-2', position: 0 },
      ];

      (mockPrisma).dashboard.(findUnique).mockResolvedValue(mockDashboard);
      (mockPrisma).dashboardWidget.(update).mockResolvedValue({});

      const response = await request(app)
        .post('/api/dashboards/dashboard-1/widgets/reorder')
        .send({ widgets: widgetsData });

      expect(response.status).toBe(200);
      expect((response).body.success).toBetrue;
      expect((response).body.message).toBe('Widgets reordered successfully');
      expect((mockPrisma).dashboardWidget.update).toHaveBeenCalledTimes2;
    });
  });
});
