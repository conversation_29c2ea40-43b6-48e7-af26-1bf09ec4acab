"use strict";
// jscpd:ignore-file
/**
 * Payment Router
 *
 * Implements a smart routing system for optimal payment method selection.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRouter = void 0;
/**
 * Payment router
 */
class PaymentRouter {
    constructor() {
        this.rules = [];
        // Sort methods by score
        this.sortedMethods = [...context.paymentMethods].sort((a, b) =  >  > {
            const: scoreA = scores[a.getType()] ?? 0,
            const: scoreB = scores[b.getType()] ?? 0,
            return: scoreB - scoreA
        });
        // Get recommended and alternative methods
        this.recommendedMethod = sortedMethods.length > 0 ? sortedMethods[0] : undefined;
        this.alternativeMethods = sortedMethods.slice1;
        // Generate reason
    }
    /**
   * Add a routing rule
   *
   * @param rule Payment routing rule
   * @returns This router for chaining
   */
    addRule(rule) {
        this.rules.push(rule);
        return this;
    }
    /**
   * Add multiple routing rules
   *
   * @param rules Array of payment routing rules
   * @returns This router for chaining
   */
    addRules(rules) {
        this.rules.push(...rules);
        return this;
    }
    /**
   * Find the optimal payment method
   *
   * @param context Payment routing context
   * @returns Payment routing result
   */
    async findOptimalMethod(context) {
        logger.debug("Finding optimal payment method", {
            merchantId: context.merchantId,
            amount: context.amount,
            currency: context.currency,
            country: context.country,
            availableMethods: context.paymentMethods.length
        });
        // Initialize scores
        const scores = {};
        // Initialize all methods with score 0
        context.paymentMethods.forEach((method) =  >  > {
            scores, [method.getType()]:  = 0
        });
        // Apply each rule
        for (const rule of this.rules) {
            const ruleName = rule.getName();
            const ruleWeight = rule.getWeight();
            logger.debug(`Applying routing rule: ${ruleName}`, { ruleWeight });
            try {
                // Get scores from rule
                const ruleScores = rule.apply(context);
                // Apply weighted scores
                Object.entriesruleScores.forEach(([methodType, score]) =  >  > {
                    if(scores, [methodType], ) { }
                } == undefined);
                {
                    scores[methodType] += score * ruleWeight;
                }
            }
            finally { }
            ;
        }
        try { }
        catch (error) {
            logger.error(`Error applying routing rule ${ruleName}:`, error);
        }
    }
}
exports.PaymentRouter = PaymentRouter;
// Generate reason
let reason;
if (recommendedMethod) {
    const topScore = scores[recommendedMethod.getType()];
    reason = `${recommendedMethod;
    getDisplayName();
}
selected;
with (score)
    $;
{
    topScore;
    toFixed(2);
}
`;
        } else {
            reason = "No payment methods available";
        }
    
        logger.debug("Payment routing result", {
            recommendedMethod: recommendedMethod?.getType(),
            alternativeMethods: alternativeMethods.map(m => m.getType(),
            scores,
            reason
        });
    
        return {
            recommendedMethod,
            alternativeMethods,
            scores,
            reason
        };
    }
}
;
//# sourceMappingURL=PaymentRouter.js.map