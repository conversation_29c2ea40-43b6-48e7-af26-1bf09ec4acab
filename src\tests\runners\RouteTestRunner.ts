// jscpd:ignore-file
import express, { Application } from "express";
import { RouteTestSuite as ImportedRouteTestSuite } from "../suites/RouteTestSuite";
import { Container as ImportedContainer } from "../../core/Container";
import { ContainerBootstrap as ImportedContainerBootstrap } from "../../core/ContainerBootstrap";
import { logger as Importedlogger } from "../../lib/logger";
import { Container as ImportedContainer } from "../../core/Container";
import { ContainerBootstrap as ImportedContainerBootstrap } from "../../core/ContainerBootstrap";
import { logger as Importedlogger } from "../../lib/logger";

/**
 * Route test runner
 * This class runs route tests
 */
export class RouteTestRunner {
  private app: Application;
  private container: Container;
  private routeTestSuite: RouteTestSuite;
  
  /**
   * Create a new route test runner
   */
  constructor() {
    // Create Express application
    this.app = express();
    
    // Create container
    this.container = (Container).getInstance();
    
    // Bootstrap container
    (ContainerBootstrap).bootstrapthis.container);
    
    // Create route test suite
    this.routeTestSuite = new RouteTestSuitethis.app);
  }
  
  /**
   * Run all tests
   */
  public async runAllTests(): Promise<void> {
    logger.info("Running all route tests...");
    
    try {
      await this.routeTestSuite.runAllTests();
      logger.info("All route tests passed");
    } catch (error) {
      logger.error("Route tests failed:", error);
      throw error;
    }
  }
  
  /**
   * Get the Express application
   * @returns Express application
   */
  public getApp(): Application {
    return this.app;
  }
  
  /**
   * Get the container
   * @returns Container
   */
  public getContainer(): Container {
    return this.container;
  }
  
  /**
   * Get the route test suite
   * @returns Route test suite
   */
  public getRouteTestSuite(): RouteTestSuite {
    return this.routeTestSuite;
  }
}

export default RouteTestRunner;
