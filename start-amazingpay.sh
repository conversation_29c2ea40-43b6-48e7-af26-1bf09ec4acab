#!/bin/bash

# 🚀 START AMAZINGPAY WITH PM2 - HANDLES ALL ISSUES
# Fixes missing files and starts only the main application

echo "🚀 Starting AmazingPay with PM2..."
echo "📁 Root Directory: /www/wwwroot/Amazingpayflow"

# Ensure we're in the correct directory
if [[ "$PWD" != "/www/wwwroot/Amazingpayflow" ]]; then
    echo "📂 Changing to correct directory..."
    cd /www/wwwroot/Amazingpayflow || { echo "❌ Cannot access /www/wwwroot/Amazingpayflow"; exit 1; }
fi

# Stop all existing PM2 processes
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true

# Check if main application file exists
if [ -f "dist/src/index.js" ]; then
    echo "✅ Found main application file: dist/src/index.js"
    
    # Start with simplified config
    pm2 start ecosystem.simple.config.js --env production
    
elif [ -f "dist/index.js" ]; then
    echo "✅ Found main application file: dist/index.js"
    
    # Start with direct file
    pm2 start dist/index.js --name "amazingpay-main" --instances 2 --env production
    
else
    echo "⚠️ Built files not found, starting with npm..."
    
    # Start with npm script
    pm2 start npm --name "amazingpay-main" -- start
fi

# Save PM2 configuration
pm2 save

# Show status
echo ""
echo "📊 PM2 Status:"
pm2 status

echo ""
echo "🧪 Testing application..."
sleep 5

# Test health endpoint
if curl -f http://localhost:3002/api/health >/dev/null 2>&1; then
    echo "✅ Health check PASSED - Application is running!"
else
    echo "⚠️ Health check failed - Check PM2 logs:"
    pm2 logs --lines 10
fi

echo ""
echo "🎉 AmazingPay startup completed!"
echo ""
echo "📋 Management commands:"
echo "  📊 Status: pm2 status"
echo "  📝 Logs: pm2 logs"
echo "  🔄 Restart: pm2 restart all"
echo "  🛑 Stop: pm2 stop all"
echo ""
echo "🔗 Application URL: http://localhost:3002"
echo "🏥 Health Check: http://localhost:3002/api/health"
