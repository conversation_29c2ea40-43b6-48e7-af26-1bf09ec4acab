/**
 * Async Handler
 *
 * This module provides a utility function for handling async controller methods.
 */
import { Request, Response, NextFunction } from 'express';
/**
 * Wrap an async function to catch errors and pass them to the next middleware
 */
export declare const functionName: (fn?: (req: Request, res: Response, next: NextFunction) => any) => any;
//# sourceMappingURL=asyncHandler.d.ts.map