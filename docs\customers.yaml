paths:
  /api/customers:
    get:
      tags:
        - Customers
      summary: Get Customers
      description: Get a list of customers
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of items per page
        - in: query
          name: search
          schema:
            type: string
          description: Search term for email, name, or reference
        - in: query
          name: sortBy
          schema:
            type: string
            enum: [createdAt, email, firstName, lastName]
            default: createdAt
          description: Field to sort by
        - in: query
          name: sortOrder
          schema:
            type: string
            enum: [asc, desc]
            default: desc
          description: Sort order
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      customers:
                        type: array
                        items:
                          $ref: '#/components/schemas/Customer'
                      pagination:
                        type: object
                        properties:
                          page:
                            type: integer
                            example: 1
                          limit:
                            type: integer
                            example: 20
                          total:
                            type: integer
                            example: 100
                          totalPages:
                            type: integer
                            example: 5
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      tags:
        - Customers
      summary: Create Customer
      description: Create a new customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - firstName
                - lastName
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                firstName:
                  type: string
                  example: John
                lastName:
                  type: string
                  example: Doe
                phone:
                  type: string
                  example: +1234567890
                address:
                  type: string
                  example: 123 Main St
                city:
                  type: string
                  example: New York
                state:
                  type: string
                  example: NY
                postalCode:
                  type: string
                  example: 10001
                country:
                  type: string
                  example: US
                metadata:
                  type: object
                  example:
                    source: website
                    referral: partner123
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Customer'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/customers/{id}:
    get:
      tags:
        - Customers
      summary: Get Customer
      description: Get a customer by ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Customer ID
      responses:
        '200':
          description: Customer retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Customer'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    patch:
      tags:
        - Customers
      summary: Update Customer
      description: Update a customer
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Customer ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                firstName:
                  type: string
                  example: John
                lastName:
                  type: string
                  example: Doe
                phone:
                  type: string
                  example: +1234567890
                address:
                  type: string
                  example: 123 Main St
                city:
                  type: string
                  example: New York
                state:
                  type: string
                  example: NY
                postalCode:
                  type: string
                  example: 10001
                country:
                  type: string
                  example: US
                metadata:
                  type: object
                  example:
                    source: website
                    referral: partner123
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Customer'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    delete:
      tags:
        - Customers
      summary: Delete Customer
      description: Delete a customer
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Customer ID
      responses:
        '200':
          description: Customer deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Customer deleted successfully
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/customers/{id}/transactions:
    get:
      tags:
        - Customers
      summary: Get Customer Transactions
      description: Get transactions for a customer
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Customer ID
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [PENDING, PROCESSING, COMPLETED, FAILED, REFUNDED, CANCELLED]
          description: Filter by status
      responses:
        '200':
          description: Transactions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      transactions:
                        type: array
                        items:
                          $ref: '#/components/schemas/Transaction'
                      pagination:
                        type: object
                        properties:
                          page:
                            type: integer
                            example: 1
                          limit:
                            type: integer
                            example: 20
                          total:
                            type: integer
                            example: 100
                          totalPages:
                            type: integer
                            example: 5
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
