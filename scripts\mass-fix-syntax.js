#!/usr/bin/env node

/**
 * Mass Syntax Fixer for AmazingPay Flow
 * Fixes common TypeScript syntax errors in bulk
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Common syntax patterns to fix
const SYNTAX_FIXES = [
  // Object property syntax: {, → {
  {
    pattern: /\{\s*,/g,
    replacement: '{',
    description: 'Fix object property syntax {, → {',
  },

  // Variable assignment spacing: =value → = value
  {
    pattern: /:\s*any\s*=([^\s])/g,
    replacement: ': any = $1',
    description: 'Fix variable assignment spacing',
  },

  // Arrow function syntax: = > → =>
  {
    pattern: /=\s*>\s*/g,
    replacement: '=> ',
    description: 'Fix arrow function syntax = > → =>',
  },

  // Filter/map function syntax: function) => → (function) =>
  {
    pattern: /\.filter\(([^)]+)\)\s*=>/g,
    replacement: '.filter(($1)) =>',
    description: 'Fix filter function syntax',
  },

  {
    pattern: /\.map\(([^)]+)\)\s*=>/g,
    replacement: '.map(($1)) =>',
    description: 'Fix map function syntax',
  },

  // Property assignment expected: data: {, → data: {
  {
    pattern: /data:\s*\{\s*,/g,
    replacement: 'data: {',
    description: 'Fix data property syntax',
  },

  // Where clause syntax: where: {, → where: {
  {
    pattern: /where:\s*\{\s*,/g,
    replacement: 'where: {',
    description: 'Fix where clause syntax',
  },

  // Include clause syntax: include: {, → include: {
  {
    pattern: /include:\s*\{\s*,/g,
    replacement: 'include: {',
    description: 'Fix include clause syntax',
  },

  // forEach function syntax: .forEach(item) => → .forEach((item)) =>
  {
    pattern: /\.forEach\(([^)]+)\)\s*=>/g,
    replacement: '.forEach(($1)) =>',
    description: 'Fix forEach function syntax',
  },

  // Promise chain syntax: .then(result) => → .then((result)) =>
  {
    pattern: /\.then\(([^)]+)\)\s*=>/g,
    replacement: '.then(($1)) =>',
    description: 'Fix promise then syntax',
  },

  {
    pattern: /\.catch\(([^)]+)\)\s*=>/g,
    replacement: '.catch(($1)) =>',
    description: 'Fix promise catch syntax',
  },

  // Type annotation errors: : any: → : any =
  {
    pattern: /:\s*any:\s*/g,
    replacement: ': any = ',
    description: 'Fix type annotation syntax',
  },

  // Double equals errors: = == → ===
  {
    pattern: /\s*=\s*==/g,
    replacement: ' ===',
    description: 'Fix double equals syntax',
  },

  // Return statement syntax: return value: any; → return value;
  {
    pattern: /return\s+([^;]+):\s*any;/g,
    replacement: 'return $1;',
    description: 'Fix return statement syntax',
  },

  // Array parameter syntax: ...args[] → ...args
  {
    pattern: /\.\.\.([^)]+)\[\]/g,
    replacement: '...$1',
    description: 'Fix array parameter syntax',
  },

  // Critical syntax patterns from error analysis
  {
    pattern: /\{\s*,\s*([^}]+)\s*\}/g,
    replacement: '{ $1 }',
    description: 'Fix object literal with leading comma',
  },
  {
    pattern: /(\w+)\s*:\s*any\s*:/g,
    replacement: '$1:',
    description: 'Fix double colon type annotation',
  },
  {
    pattern: /(\w+)\s*=\s*==/g,
    replacement: '$1 ===',
    description: 'Fix double equals assignment',
  },
  {
    pattern: /\.forEach\(([^)]+)\)\s*=>\s*\{/g,
    replacement: '.forEach(($1) => {',
    description: 'Fix forEach arrow function syntax',
  },
  {
    pattern: /\.filter\(([^)]+)\)\s*=>\s*\{/g,
    replacement: '.filter(($1) => {',
    description: 'Fix filter arrow function syntax',
  },
  {
    pattern: /\.catch\(\(([^)]+)\)\)\s*=>\s*\{/g,
    replacement: '.catch(($1) => {',
    description: 'Fix catch arrow function syntax',
  },
  {
    pattern: /\.then\(([^)]+)\)\s*=>\s*\{/g,
    replacement: '.then(($1) => {',
    description: 'Fix then arrow function syntax',
  },
  {
    pattern: /return\s+(\w+):\s*any;/g,
    replacement: 'return $1;',
    description: 'Fix return statement with type annotation',
  },
  {
    pattern: /this:\s*any\./g,
    replacement: 'this.',
    description: 'Fix this with type annotation',
  },
  {
    pattern: /(\w+)\s*:\s*any\s*=\s*new\s+Date\(\)\s*:/g,
    replacement: '$1: Date = new Date();',
    description: 'Fix Date assignment with type annotation',
  },
];

// Files to process (final syntax cleanup - critical patterns)
const TARGET_FILES = [
  'src/services/identity-verification.service.ts',
  'src/services/analytics/payment-analytics.service.ts',
  'src/services/analytics/ApiAnalyticsService.ts',
  'src/lib/redis-manager.ts',
  'src/middlewares/enhanced-auth.middleware.ts',
  'src/services/rbac/RBACInitializer.ts',
  'src/controllers/admin.controller.ts',
  'src/controllers/fraud-detection.controller.ts',
  'src/utils/cache.ts',
  'src/utils/alerting.ts',
  'src/utils/cache-manager.ts',
  'src/utils/controller.utils.ts',
  'src/utils/log-rotation.ts',
  'src/utils/migration-manager.ts',
  'src/utils/encryption.ts',
  'src/utils/errors/ErrorFactory.ts',
  'src/services/notification/alert-notification.service.ts',
  'src/services/optimization/verification-optimization.service.ts',
  'src/services/payment-recommendation.service.ts',
  'src/services/payment/EnhancedPaymentService.ts',
  'src/services/payment/routing/PaymentRouter.ts',
  'src/services/payment/routing/rules/CommonRoutingRules.ts',
  'src/services/paymentVerificationService.ts',
  'src/services/websocket/verificationWebSocketService.ts',
  'src/tests/load-test.ts',
  'src/tests/run-tests.ts',
  'src/tests/security-test.ts',
];

function fixFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return { fixed: false, errors: [`File not found: ${filePath}`] };
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    const appliedFixes = [];

    // Apply each syntax fix
    SYNTAX_FIXES.forEach((fix) => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        fixCount += matches.length;
        appliedFixes.push(`${fix.description}: ${matches.length} fixes`);
      }
    });

    if (fixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${filePath}: ${fixCount} syntax errors`);
      appliedFixes.forEach((fix) => console.log(`   - ${fix}`));
      return { fixed: true, count: fixCount, fixes: appliedFixes };
    } else {
      console.log(`✨ ${filePath}: No syntax errors found`);
      return { fixed: false, count: 0 };
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return { fixed: false, errors: [error.message] };
  }
}

function checkCompilation(filePath) {
  try {
    execSync(`npx tsc --noEmit "${filePath}"`, { stdio: 'pipe' });
    return { success: true, errors: [] };
  } catch (error) {
    const output = error.stdout ? error.stdout.toString() : error.stderr.toString();
    const errorLines = output.split('\n').filter((line) => line.includes('error TS'));
    return { success: false, errors: errorLines };
  }
}

function main() {
  console.log('🚀 Starting Mass Syntax Fixer for AmazingPay Flow');
  console.log(`📁 Processing ${TARGET_FILES.length} files...\n`);

  let totalFixed = 0;
  let totalErrors = 0;
  const results = [];

  TARGET_FILES.forEach((filePath, index) => {
    console.log(`\n[${index + 1}/${TARGET_FILES.length}] Processing: ${filePath}`);

    // Check errors before fixing
    const beforeCheck = checkCompilation(filePath);
    const errorsBefore = beforeCheck.errors.length;

    // Apply fixes
    const result = fixFile(filePath);

    if (result.fixed) {
      // Check errors after fixing
      const afterCheck = checkCompilation(filePath);
      const errorsAfter = afterCheck.errors.length;
      const errorsFixed = errorsBefore - errorsAfter;

      console.log(`   📊 Errors: ${errorsBefore} → ${errorsAfter} (${errorsFixed} fixed)`);

      totalFixed += result.count;
      totalErrors += errorsFixed;

      results.push({
        file: filePath,
        syntaxFixes: result.count,
        errorsBefore,
        errorsAfter,
        errorsFixed,
      });
    }
  });

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 MASS SYNTAX FIXING SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Total syntax fixes applied: ${totalFixed}`);
  console.log(`🔧 Total compilation errors fixed: ${totalErrors}`);
  console.log(`📁 Files processed: ${results.length}/${TARGET_FILES.length}`);

  if (results.length > 0) {
    console.log('\n📋 Detailed Results:');
    results.forEach((result) => {
      console.log(`   ${result.file}:`);
      console.log(`     - Syntax fixes: ${result.syntaxFixes}`);
      console.log(
        `     - Errors: ${result.errorsBefore} → ${result.errorsAfter} (${result.errorsFixed} fixed)`
      );
    });
  }

  console.log('\n🎯 Next Steps:');
  console.log('1. Run full compilation check: npx tsc --noEmit');
  console.log('2. Continue with remaining high-error files');
  console.log('3. Test server startup once errors are under 500');
}

if (require.main === module) {
  main();
}
