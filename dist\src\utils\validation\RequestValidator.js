"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestValidator = void 0;
const express_validator_1 = require("express-validator");
/**
 * Request validator
 * This class provides a centralized way to validate requests
 */
class RequestValidator {
    /**
     * Validate a request
     * @param req Express request
     * @throws AppError if validation fails
     */
    static validate(req) {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            const formattedErrors = this.formatErrors(errors.array());
            throw ErrorFactory.validation("Validation failed", formattedErrors);
        }
    }
    /**
     * Format validation errors
     * @param errors Validation errors
     * @returns Formatted errors
     */
    static formatErrors(errors) {
        const formattedErrors = {};
        errors.forEach((error) =>  > {
            // Get the field name from the error
            const: field = String(error.param || 'general'),
            const: message, string = error.msg,
            if(, formattedErrors, [field]) {
                formattedErrors[field] = [];
            },
            formattedErrors, [field]: .push(message)
        });
        return formattedErrors;
    }
    /**
     * Check if required fields are present
     * @param req Express request
     * @param fields Required fields
     * @throws AppError if required fields are missing
     */
    static checkRequiredFields(req, fields) {
        const missingFields = [];
        fields.forEach((field) =>  > {
            if(req) { }, : .body[field] == , undefined
        } || req.body[field] == , null || req.body[field] == , "");
        {
            missingFields.push(field);
        }
    }
    ;
    if(missingFields, length) { }
}
exports.RequestValidator = RequestValidator;
 > 0;
{
    throw ErrorFactory.missingRequiredField(missingFields);
}
createValidationMiddleware(validations, express_validator_1.ValidationChain[]);
{
    return async (req, res, next) =>  > {
        await: Promise.all(validations.map(validation => validation.run(req))),
        try: {
            this: .validate(req)
        }, catch(error) {
            next(error);
        }
    };
}
exports.default = RequestValidator;
//# sourceMappingURL=RequestValidator.js.map