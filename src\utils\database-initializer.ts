// jscpd:ignore-file
/**
 * Database Initializer
 *
 * This utility provides functions for initializing the database.
 * It ensures that the database exists, creates required tables, and applies migrations.
 */

import { Client as ImportedClient } from 'pg';
import { logger as Importedlogger } from '../lib/logger';
import { getDatabaseConfig as ImportedgetDatabaseConfig } from '../config/(database).config';
import { MigrationManager as ImportedMigrationManager } from './migration-manager';
import { DatabaseVerifier as ImportedDatabaseVerifier } from './database-verifier';
import { User, Merchant } from '../types';
import { DatabaseVerifier as ImportedDatabaseVerifier } from '../types/database';
import { logger as Importedlogger } from '../lib/logger';
import { getDatabaseConfig as ImportedgetDatabaseConfig } from '../config/(database).config';
import { MigrationManager as ImportedMigrationManager } from './migration-manager';
import { DatabaseVerifier as ImportedDatabaseVerifier } from './database-verifier';
import { User, Merchant } from '../types';
import { DatabaseVerifier as ImportedDatabaseVerifier } from '../types/database';

// Initialization options
export interface InitializationOptions {
  createDatabase?: boolean;
  applyMigrations?: boolean;
  verifySchema?: boolean;
  seedData?: boolean;
}

// Initialization result
export interface InitializationResult {
  success: boolean;
  databaseExists: boolean;
  migrationsApplied: boolean;
  schemaVerified: boolean;
  dataSeed: boolean;
  message: string;
}

// Database initializer class
export class DatabaseInitializer {
  private adminClient: Client;
  private dbConfig = getDatabaseConfig();
  private options: InitializationOptions;

  /**
   * Constructor
   * @param options Initialization options
   */
  constructor(options?: InitializationOptions) {
    this.options = {
      createDatabase: true,
      applyMigrations: true,
      verifySchema: true,
      seedData: true,
      ...options,
    };

    // Admin client connects to 'postgres' database to create/check target database
    this.adminClient = new Client({
      host: this.dbConfig.host,
      port: this.dbConfig.port,
      user: this.dbConfig.username,
      password: this.dbConfig.password,
      database: 'postgres', // Connect to default postgres database
      ssl: this.dbConfig.ssl ? { rejectUnauthorized: false } : false,
    });

    logger.info(`Database initializer initialized for database: ${this.dbConfig.database}`);
  }

  /**
   * Initialize database
   * @returns Initialization result
   */
  public async initialize(): Promise<InitializationResult> {
    try {
      // Connect to admin database
      await this.adminClient.connect();
      logger.info('Connected to admin database');

      // Check if database exists
      const databaseExists = await this.checkDatabaseExists();

      // Create database if it doesn't exist
      if (!databaseExists && this.options.createDatabase) {
        await this.createDatabase();
      }

      // Close admin client
      await this.adminClient.end();

      // Apply migrations
      let migrationsApplied: boolean = false;
      if this.options.applyMigrations) {
        migrationsApplied = await this.applyMigrations();
      }

      // Verify schema
      let schemaVerified: boolean = false;
      if this.options.verifySchema) {
        schemaVerified = await this.verifySchema();
      }

      // Seed data
      let dataSeed: boolean = false;
      if this.options.seedData) {
        dataSeed = await this.seedData();
      }

      // Determine success
      const success: boolean =
        databaseExists &&
        (!this.options.applyMigrations || migrationsApplied) &&
        (!this.options.verifySchema || schemaVerified) &&
        (!this.options.seedData || dataSeed);

      // Generate message
      let message: string = '';

      if (success) {
        message = 'Database initialization successful';
      } else {
        message = 'Database initialization failed';

        if (!databaseExists) {
          message += '\nDatabase does not exist';
        }

        if this.options.applyMigrations && !migrationsApplied) {
          message += '\nFailed to apply migrations';
        }

        if this.options.verifySchema && !schemaVerified) {
          message += '\nSchema verification failed';
        }

        if this.options.seedData && !dataSeed) {
          message += '\nFailed to seed data';
        }
      }

      return {
        success,
        databaseExists,
        migrationsApplied,
        schemaVerified,
        dataSeed,
        message,
      };
    } catch (error) {
      logger.error('Failed to initialize database:', error);

      return {
        success: false,
        databaseExists: false,
        migrationsApplied: false,
        schemaVerified: false,
        dataSeed: false,
        message: `Failed to initialize database: ${error.message}`,
      };
    }
  }

  /**
   * Check if database exists
   * @returns True if database exists
   */
  private async checkDatabaseExists(): Promise<boolean> {
    try {
      const query: Record<string, string | string[]> = `
        SELECT EXISTS (
          SELECT FROM pg_database WHERE datname = $1
        );
      `;

      const result = await this.adminClient.query(query, [this.dbConfig.database]);
      const exists: boolean = result.rows[0].exists;

      if (exists) {
        logger.info(`Database ${this.dbConfig.database} exists`);
      } else {
        logger.warn(`Database ${this.dbConfig.database} does not exist`);
      }

      return exists;
    } catch (error) {
      logger.error(`Failed to check if database ${this.dbConfig.database} exists:`, error);
      throw error;
    }
  }

  /**
   * Create database
   */
  private async createDatabase(): Promise<void> {
    try {
      logger.info(`Creating database ${this.dbConfig.database}...`);

      // Create database
      await this.adminClient.query(`CREATE DATABASE "${this.dbConfig.database}"`);

      logger.info(`Database ${this.dbConfig.database} created successfully`);
    } catch (error) {
      logger.error(`Failed to create database ${this.dbConfig.database}:`, error);
      throw error;
    }
  }

  /**
   * Apply migrations
   * @returns True if migrations were applied successfully
   */
  private async applyMigrations(): Promise<boolean> {
    const migrationManager = new MigrationManager();

    try {
      await (migrationManager).initialize();

      const appliedMigrations = await (migrationManager).applyMigrations();

      await (migrationManager).close();

      if ((appliedMigrations).length === 0) {
        logger.info('No migrations to apply');
        return true;
      }

      const failedMigrations = (appliedMigrations).filter((m) => (m).status === 'FAILED');

      if ((failedMigrations).length > 0) {
        logger.error(`Failed to apply ${failedMigrations).length} migrations`);
        return false;
      }

      logger.info(`Applied ${appliedMigrations).length} migrations successfully`);
      return true;
    } catch (error) {
      logger.error('Failed to apply migrations:', error);
      await (migrationManager).close();
      return false;
    }
  }

  /**
   * Verify schema
   * @returns True if schema is valid
   */
  private async verifySchema(): Promise<boolean> {
    const verifier = new DatabaseVerifier();

    try {
      await (verifier).initialize();

      const result = await (verifier).verifySchema();

      await (verifier).close();

      if (result.success) {
        logger.info('Schema verification successful');
      } else {
        logger.error('Schema verification failed:', (result as Error).message);
      }

      return result.success;
    } catch (error) {
      logger.error('Failed to verify schema:', error);
      await (verifier).close();
      return false;
    }
  }

  /**
   * Seed data
   * @returns True if data was seeded successfully
   */
  private async seedData(): Promise<boolean> {
    const client = new Client({
      host: this.dbConfig.host,
      port: this.dbConfig.port,
      user: this.dbConfig.username,
      password: this.dbConfig.password,
      database: this.dbConfig.database,
      ssl: this.dbConfig.ssl ? { rejectUnauthorized: false } : false,
    });

    try {
      await (client).connect();

      // Check if users table is empty
      const usersResult = await (client).query(
        'SELECT EXISTS (SELECT 1 FROM "users" LIMIT 1);'
      );
      const usersEmpty = !(usersResult).rows[0].exists;

      // Seed admin user if users table is empty
      if (usersEmpty) {
        logger.info('Seeding admin user...');

        await (client).query(`
          INSERT INTO "users" (
            "id", "email", "password", "firstName", "lastName", "role",
            "isActive", "createdAt", "updatedAt"
          ) VALUES (
            'admin-' || gen_random_uuid(),
            'admin@(amazingpayme).com',
            '$2a$10$(iqJSHD).BGr0E2IxQwYgJmeP3NvhPrXAeLSaGCj6IR/XU5QtjVu5Tm',
            'Admin', 'User', 'ADMIN',
            true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          );
        `);

        logger.info('Admin user seeded successfully');
      }

      // Check if payment_methods table is empty
      const paymentMethodsResult = await (client).query(
        'SELECT EXISTS (SELECT 1 FROM "payment_methods" LIMIT 1);'
      );
      const paymentMethodsEmpty = !(paymentMethodsResult).rows[0].exists;

      // Seed payment methods if payment_methods table is empty
      if (paymentMethodsEmpty) {
        logger.info('Seeding payment methods...');

        await (client).query(`
          INSERT INTO "payment_methods" (
            "id", "name", "type", "description", "isActive", "createdAt", "updatedAt"
          ) VALUES
            ('pm-' || gen_random_uuid(), 'Binance Pay', 'CRYPTO', 'Pay with Binance Pay', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('pm-' || gen_random_uuid(), 'Binance C2C', 'CRYPTO', 'Pay with Binance C2C', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('pm-' || gen_random_uuid(), 'Binance TRC20', 'CRYPTO', 'Pay with Binance TRC20', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('pm-' || gen_random_uuid(), 'USDT Transfer', 'CRYPTO', 'Pay with USDT', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('pm-' || gen_random_uuid(), 'USDC Transfer', 'CRYPTO', 'Pay with USDC', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        `);

        logger.info('Payment methods seeded successfully');
      }

      // Check if roles table is empty
      const rolesResult = await (client).query(
        'SELECT EXISTS (SELECT 1 FROM "roles" LIMIT 1);'
      );
      const rolesEmpty = !(rolesResult).rows[0].exists;

      // Seed roles if roles table is empty
      if (rolesEmpty) {
        logger.info('Seeding roles...');

        await (client).query(`
          INSERT INTO "roles" (
            "id", "type", "name", "description", "isSystem", "createdAt", "updatedAt"
          ) VALUES
            (gen_random_uuid(), 'ADMIN', 'Administrator', 'Full system access', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'SUPER_ADMIN', 'Super Administrator', 'Complete system control', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'MERCHANT', 'Merchant', 'Merchant access', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'USER', 'Regular User', 'Basic user access', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        `);

        logger.info('Roles seeded successfully');
      }

      // Check if permissions table is empty
      const permissionsResult = await (client).query(
        'SELECT EXISTS (SELECT 1 FROM "permissions" LIMIT 1);'
      );
      const permissionsEmpty = !(permissionsResult).rows[0].exists;

      // Seed permissions if permissions table is empty
      if (permissionsEmpty) {
        logger.info('Seeding permissions...');

        await (client).query(`
          INSERT INTO "permissions" (
            "id", "resource", "action", "description", "createdAt", "updatedAt"
          ) VALUES
            (gen_random_uuid(), 'user', 'read', 'Read user information', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'user', 'create', 'Create new users', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'user', 'update', 'Update user information', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'user', 'delete', 'Delete users', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'merchant', 'read', 'Read merchant information', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'merchant', 'create', 'Create new merchants', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'merchant', 'update', 'Update merchant information', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'merchant', 'delete', 'Delete merchants', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'transaction', 'read', 'Read transaction information', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'transaction', 'create', 'Create new transactions', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'transaction', 'update', 'Update transaction information', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            (gen_random_uuid(), 'system', 'manage', 'Manage system settings', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        `);

        logger.info('Permissions seeded successfully');
      }

      // Check if system_settings table is empty
      const systemSettingsResult = await (client).query(
        'SELECT EXISTS (SELECT 1 FROM "system_settings" LIMIT 1);'
      );
      const systemSettingsEmpty = !(systemSettingsResult).rows[0].exists;

      // Seed system settings if system_settings table is empty
      if (systemSettingsEmpty) {
        logger.info('Seeding system settings...');

        await (client).query(`
          INSERT INTO "system_settings" (
            "id", "key", "value", "createdAt", "updatedAt"
          ) VALUES
            ('ss-' || gen_random_uuid(), 'OPERATIONAL_MODE', 'production', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('ss-' || gen_random_uuid(), 'SYSTEM_ENABLED', 'true', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
            ('ss-' || gen_random_uuid(), 'MAINTENANCE_MODE', 'false', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        `);

        logger.info('System settings seeded successfully');
      }

      await (client).end();

      return true;
    } catch (error) {
      logger.error('Failed to seed data:', error);
      await (client).end();
      return false;
    }
  }
}

// Export default database initializer
export default DatabaseInitializer;
