# Advanced Reporting API Documentation

## Overview

The Advanced Reporting API provides comprehensive reporting capabilities for the AmazingPay Flow system. It supports multiple report types, formats, scheduling, and dashboard management.

## Authentication

All endpoints require authentication via Bear<PERSON> token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Base URL

```
https://api.amazingpay.com/api
```

## Report Generation

### Generate Report

Generate a new report based on specified parameters.

**Endpoint:** `POST /reports/generate`

**Request Body:**

```json
{
  "type": "TRANSACTION",
  "format": "CSV",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "merchantId": "optional-merchant-id",
  "status": "COMPLETED"
}
```

**Parameters:**

- `type` (required): Report type - `TRANSACTION`, `CUSTOMER`, `PAYMENT_METHOD`, `SUBSCRIPTION`
- `format` (required): Export format - `CSV`, `PDF`, `EXCEL`, `JSON`
- `startDate` (optional): Start date for data filtering (YYYY-MM-DD)
- `endDate` (optional): End date for data filtering (YYYY-MM-DD)
- `merchantId` (optional): Filter by specific merchant
- `status` (optional): Filter by status (varies by report type)

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "report-123",
    "name": "Transaction Report",
    "type": "TRANSACTION",
    "format": "CSV",
    "filePath": "/reports/transaction_report_20231201.csv",
    "fileSize": 1024,
    "status": "COMPLETED",
    "createdAt": "2023-12-01T10:00:00Z"
  }
}
```

## Report Templates

### Get Report Templates

Retrieve all available report templates.

**Endpoint:** `GET /reports/templates`

**Query Parameters:**

- `includeSystem` (optional): Include system templates (default: true)

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "template-1",
      "name": "Monthly Transaction Summary",
      "description": "Summary of all transactions for the month",
      "type": "TRANSACTION",
      "config": {
        "columns": ["reference", "amount", "currency", "status"],
        "groupBy": [],
        "sortBy": "createdAt",
        "sortDirection": "desc"
      },
      "isSystem": true,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ]
}
```

### Create Report Template

Create a new report template.

**Endpoint:** `POST /reports/templates`

**Request Body:**

```json
{
  "name": "Custom Transaction Report",
  "description": "Custom transaction report with specific filters",
  "type": "TRANSACTION",
  "config": {
    "columns": ["reference", "amount", "currency", "status", "merchantName"],
    "groupBy": ["status"],
    "sortBy": "amount",
    "sortDirection": "desc"
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "template-123",
    "name": "Custom Transaction Report",
    "description": "Custom transaction report with specific filters",
    "type": "TRANSACTION",
    "config": {
      "columns": ["reference", "amount", "currency", "status", "merchantName"],
      "groupBy": ["status"],
      "sortBy": "amount",
      "sortDirection": "desc"
    },
    "isSystem": false,
    "createdById": "user-123",
    "createdAt": "2023-12-01T10:00:00Z"
  }
}
```

### Update Report Template

Update an existing report template.

**Endpoint:** `PUT /reports/templates/{id}`

**Request Body:**

```json
{
  "name": "Updated Template Name",
  "description": "Updated description",
  "config": {
    "columns": ["reference", "amount", "currency"],
    "sortBy": "createdAt"
  }
}
```

### Delete Report Template

Delete a report template.

**Endpoint:** `DELETE /reports/templates/{id}`

**Response:**

```json
{
  "success": true,
  "message": "Report template deleted successfully"
}
```

## Scheduled Reports

### Get Scheduled Reports

Retrieve all scheduled reports for the current user.

**Endpoint:** `GET /reports/scheduled`

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "scheduled-1",
      "name": "Weekly Transaction Report",
      "templateId": "template-1",
      "schedule": "0 0 * * 1",
      "isActive": true,
      "emailRecipients": ["<EMAIL>"],
      "lastRun": "2023-11-27T00:00:00Z",
      "nextRun": "2023-12-04T00:00:00Z",
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ]
}
```

### Create Scheduled Report

Create a new scheduled report.

**Endpoint:** `POST /reports/scheduled`

**Request Body:**

```json
{
  "name": "Weekly Transaction Report",
  "templateId": "template-1",
  "schedule": "0 0 * * 1",
  "isActive": true,
  "emailRecipients": ["<EMAIL>"],
  "parameters": {
    "format": "PDF",
    "status": "COMPLETED"
  }
}
```

**Schedule Format:**
Uses cron expression format:

- `0 0 * * 1` - Every Monday at midnight
- `0 9 * * 1-5` - Every weekday at 9 AM
- `0 0 1 * *` - First day of every month at midnight

### Run Scheduled Report

Manually trigger a scheduled report.

**Endpoint:** `POST /reports/scheduled/{id}/run`

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "report-run-123",
    "scheduledReportId": "scheduled-1",
    "status": "RUNNING",
    "startedAt": "2023-12-01T10:00:00Z"
  }
}
```

## Saved Reports

### Get Saved Reports

Retrieve all saved reports for the current user.

**Endpoint:** `GET /reports/saved`

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "report-1",
      "name": "Transaction Report - November 2023",
      "type": "TRANSACTION",
      "format": "CSV",
      "filePath": "/reports/transaction_report_202311.csv",
      "fileSize": 2048,
      "parameters": {
        "startDate": "2023-11-01",
        "endDate": "2023-11-30"
      },
      "createdAt": "2023-12-01T10:00:00Z"
    }
  ]
}
```

### Get Saved Report

Retrieve a specific saved report.

**Endpoint:** `GET /reports/saved/{id}`

### Delete Saved Report

Delete a saved report and its associated file.

**Endpoint:** `DELETE /reports/saved/{id}`

## Dashboard Management

### Get Dashboards

Retrieve all dashboards accessible to the current user.

**Endpoint:** `GET /dashboards`

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "dashboard-1",
      "name": "Executive Dashboard",
      "description": "High-level overview of key metrics",
      "layout": {
        "columns": 3,
        "rows": 2
      },
      "isPublic": false,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ]
}
```

### Create Dashboard

Create a new dashboard.

**Endpoint:** `POST /dashboards`

**Request Body:**

```json
{
  "name": "My Dashboard",
  "description": "Custom dashboard for monitoring",
  "layout": {
    "columns": 2,
    "rows": 3
  },
  "isPublic": false
}
```

### Dashboard Widgets

#### Get Dashboard Widgets

**Endpoint:** `GET /dashboards/{dashboardId}/widgets`

#### Create Widget

**Endpoint:** `POST /dashboards/{dashboardId}/widgets`

**Request Body:**

```json
{
  "title": "Transaction Volume Chart",
  "type": "CHART",
  "config": {
    "chartType": "line",
    "dataSource": "transactions",
    "timeRange": "30d"
  },
  "width": 2,
  "height": 1,
  "position": 0
}
```

## Health and Monitoring Endpoints

### System Health Check

Get the overall health status of the reporting system.

**Endpoint:** `GET /api/health/reports`

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "checks": {
    "database": {
      "status": "healthy",
      "responseTime": 45
    },
    "fileSystem": {
      "status": "healthy",
      "details": {
        "directory": "/reports",
        "fileCount": 150
      }
    },
    "reportingSystem": {
      "status": "healthy",
      "details": {
        "activeReports": 2,
        "queuedReports": 0,
        "failedReports": 0
      }
    },
    "memoryUsage": {
      "status": "healthy",
      "details": {
        "heapUsedMB": 128,
        "heapTotalMB": 256,
        "heapUsedPercent": 50
      }
    }
  }
}
```

### System Metrics

Get detailed performance metrics for the reporting system.

**Endpoint:** `GET /api/health/metrics`

**Query Parameters:**

- `hours` (optional): Time range in hours for metrics (default: 24)

**Response:**

```json
{
  "success": true,
  "data": {
    "systemHealth": {
      "status": "healthy",
      "metrics": {
        "activeReports": 2,
        "queuedReports": 0,
        "failedReports": 0,
        "avgGenerationTime": 1500,
        "diskUsagePercent": 45.2,
        "memoryUsagePercent": 67.8
      },
      "alerts": []
    },
    "performanceStats": {
      "totalReports": 156,
      "avgGenerationTime": 1450,
      "avgFileSize": 2048576,
      "reportsByType": {
        "TRANSACTION": 89,
        "CUSTOMER": 34,
        "PAYMENT_METHOD": 23,
        "SUBSCRIPTION": 10
      },
      "reportsByFormat": {
        "CSV": 78,
        "PDF": 45,
        "EXCEL": 23,
        "JSON": 10
      },
      "performanceTrend": "stable"
    }
  }
}
```

### Cleanup Old Reports

Trigger cleanup of old report files.

**Endpoint:** `POST /api/health/cleanup`

**Request Body:**

```json
{
  "maxAgeHours": 168
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "deletedFiles": 25,
    "freedSpace": 52428800
  },
  "message": "Cleanup completed: 25 files deleted, 50MB freed"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "code": "ERROR_CODE"
}
```

**Common Error Codes:**

- `UNAUTHORIZED` (401): Invalid or missing authentication token
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `VALIDATION_ERROR` (400): Invalid request parameters
- `INTERNAL_ERROR` (500): Server error

## Rate Limiting

API requests are rate-limited to prevent abuse:

- 100 requests per minute for report generation
- 1000 requests per minute for other endpoints

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```
