#!/bin/bash

# 🚀 QUICK FIX AND DEPLOY - ONE COMMAND SOLUTION
# Fixes all issues and deploys automatically

echo "🚀 AmazingPay Quick Fix & Deploy"
echo "==============================="

# Fix Git ownership
git config --global --add safe.directory /www/wwwroot/Amazingpayflow

# Create .env file
cat > .env << 'EOF'
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
DATABASE_URL=postgresql://postgres:CepWrkdzE5TL@localhost:5432/Amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=CepWrkdzE5TL
DB_NAME=Amazingpay
FRONTEND_URL=https://amazingpayme.com
API_URL=https://amazingpayme.com/api
DOMAIN=amazingpayme.com
VPS_IP=************
JWT_SECRET=AzP4y_Pr0d_JWT_S3cr3t_2024_V3ry_L0ng_4nd_S3cur3_K3y
JWT_EXPIRES_IN=1d
EOF

# Load environment
export $(cat .env | xargs)

# Setup database
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'CepWrkdzE5TL';" 2>/dev/null || true
sudo -u postgres psql -c "CREATE DATABASE \"Amazingpay\";" 2>/dev/null || true

# Install dependencies (skip husky)
export HUSKY=0
npm install --omit=dev --ignore-scripts

# Prisma setup
npx prisma generate
npx prisma migrate deploy

# Build and start
npm run build
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true
pm2 start ecosystem.config.js --env production
pm2 save

echo "✅ Deployment completed!"
echo "📊 Status: pm2 status"
echo "🔗 Health: curl http://localhost:3002/api/health"
