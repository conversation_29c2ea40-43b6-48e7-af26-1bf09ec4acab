#!/bin/bash

# 🚀 QUICK FIX AND DEPLOY - ONE COMMAND SOLUTION
# Fixes all issues and deploys automatically

echo "🚀 AmazingPay Quick Fix & Deploy"
echo "==============================="

# Fix Git ownership
git config --global --add safe.directory /www/wwwroot/Amazingpayflow

# Create .env file
cat > .env << 'EOF'
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
DATABASE_URL=postgresql://postgres:CepWrkdzE5TL@localhost:5432/Amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=CepWrkdzE5TL
DB_NAME=Amazingpay
FRONTEND_URL=https://amazingpayme.com
API_URL=https://amazingpayme.com/api
DOMAIN=amazingpayme.com
VPS_IP=************
JWT_SECRET=AzP4y_Pr0d_JWT_S3cr3t_2024_V3ry_L0ng_4nd_S3cur3_K3y
JWT_EXPIRES_IN=1d
EOF

# Load environment
export $(cat .env | xargs)

# Setup database
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'CepWrkdzE5TL';" 2>/dev/null || true
sudo -u postgres psql -c "CREATE DATABASE \"Amazingpay\";" 2>/dev/null || true

# Install dependencies (skip husky)
export HUSKY=0
npm install --omit=dev --ignore-scripts

# Prisma setup
npx prisma generate
npx prisma migrate deploy

# Fix npm global configuration and PM2
npm config set prefix /usr/local
export PATH="/usr/local/bin:/usr/bin:/bin:$PATH"

# Install PM2 properly
npm install -g pm2

# Create PM2 symlink if needed
PM2_PATH=$(find /usr -name "pm2" -type f 2>/dev/null | head -1)
if [ -n "$PM2_PATH" ]; then
    ln -sf "$PM2_PATH" /usr/local/bin/pm2
    chmod +x /usr/local/bin/pm2
fi

# Build and start
npm run build

# Start with PM2 (with fallback)
if command -v pm2 >/dev/null 2>&1; then
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true
    pm2 start ecosystem.config.js --env production
    pm2 save
    echo "✅ Started with PM2"
elif [ -f "/usr/local/bin/pm2" ]; then
    /usr/local/bin/pm2 stop all 2>/dev/null || true
    /usr/local/bin/pm2 delete all 2>/dev/null || true
    /usr/local/bin/pm2 start ecosystem.config.js --env production
    /usr/local/bin/pm2 save
    echo "✅ Started with PM2 (direct path)"
else
    # Fallback to npx
    npx pm2 stop all 2>/dev/null || true
    npx pm2 delete all 2>/dev/null || true
    npx pm2 start ecosystem.config.js --env production
    npx pm2 save
    echo "✅ Started with npx pm2"
fi

echo "✅ Deployment completed!"
echo "📊 Status: pm2 status (or npx pm2 status)"
echo "🔗 Health: curl http://localhost:3002/api/health"
