// jscpd:ignore-file
/**
 * Role Model
 *
 * Defines the role model for the RBAC system.
 */

import { Permission as ImportedPermission } from './permission.model';
import { Merchant as ImportedMerchant } from '../types';
import { Merchant as ImportedMerchant } from '../types';

/**
 * Role type
 */
export type RoleType =
  | 'super_admin'
  | 'admin'
  | 'financial_admin'
  | 'merchant_admin'
  | 'security_admin'
  | 'support_admin'
  | 'auditor'
  | 'custom';

/**
 * Role interface
 */
export interface Role {
  id: string;
  name: string;
  type: RoleType;
  description?: string;
  permissions: Permission[];
  isActive: boolean;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
}

/**
 * Role creation data
 */
export interface RoleCreateData {
  name: string;
  type: RoleType;
  description?: string;
  permissions: string[];
  isActive?: boolean;
  isSystem?: boolean;
}

/**
 * Role update data
 */
export interface RoleUpdateData {
  name?: string;
  description?: string;
  permissions?: string[];
  isActive?: boolean;
}

/**
 * System roles
 */
export const SYSTEM_ROLES: Record<string, RoleCreateData> = {
  SUPER_ADMIN: {
    name: 'Super Admin',
    type: 'super_admin',
    description: 'Full system access with all permissions',
    permissions: ['*'],
    isActive: true,
    isSystem: true,
  },
  ADMIN: {
    name: 'Admin',
    type: 'admin',
    description: 'General administrative access',
    permissions: [
      'admin:access',
      'merchants:view',
      'merchants:create',
      'merchants:update',
      'merchants:delete',
      'payments:view',
      'payments:create',
      'payments:update',
      'payments:delete',
      'settings:view',
      'settings:update',
    ],
    isActive: true,
    isSystem: true,
  },
  FINANCIAL_ADMIN: {
    name: 'Financial Admin',
    type: 'financial_admin',
    description: 'Access to financial operations',
    permissions: [
      'admin:access',
      'payments:view',
      'payments:create',
      'payments:update',
      'payments:delete',
      'transactions:view',
      'transactions:approve',
      'transactions:refund',
      'fees:view',
      'fees:update',
    ],
    isActive: true,
    isSystem: true,
  },
  MERCHANT_ADMIN: {
    name: 'Merchant Admin',
    type: 'merchant_admin',
    description: 'Access to merchant management',
    permissions: [
      'admin:access',
      'merchants:view',
      'merchants:create',
      'merchants:update',
      'merchants:delete',
      'merchant_approvals:view',
      'merchant_approvals:approve',
      'merchant_approvals:reject',
    ],
    isActive: true,
    isSystem: true,
  },
  SECURITY_ADMIN: {
    name: 'Security Admin',
    type: 'security_admin',
    description: 'Access to security settings and verification methods',
    permissions: [
      'admin:access',
      'verification_methods:view',
      'verification_methods:create',
      'verification_methods:update',
      'verification_methods:delete',
      'security_settings:view',
      'security_settings:update',
    ],
    isActive: true,
    isSystem: true,
  },
  SUPPORT_ADMIN: {
    name: 'Support Admin',
    type: 'support_admin',
    description: 'Access to merchant support functions',
    permissions: [
      'admin:access',
      'merchants:view',
      'payments:view',
      'transactions:view',
      'support_tickets:view',
      'support_tickets:respond',
      'support_tickets:close',
    ],
    isActive: true,
    isSystem: true,
  },
  AUDITOR: {
    name: 'Auditor',
    type: 'auditor',
    description: 'Read-only access to all data for auditing purposes',
    permissions: [
      'admin:access',
      'merchants:view',
      'payments:view',
      'transactions:view',
      'settings:view',
      'audit_logs:view',
    ],
    isActive: true,
    isSystem: true,
  },
};
