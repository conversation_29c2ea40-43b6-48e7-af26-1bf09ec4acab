<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AmazingPay - Professional Payment Gateway</title>
  
  <!-- External Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  
  <style>
    /* Reset and Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    :root {
      --primary-color: #2563eb;
      --primary-dark: #1d4ed8;
      --primary-light: #3b82f6;
      --secondary-color: #10b981;
      --accent-color: #f59e0b;
      --text-primary: #1f2937;
      --text-secondary: #6b7280;
      --text-light: #9ca3af;
      --bg-primary: #ffffff;
      --bg-secondary: #f8fafc;
      --bg-dark: #0f172a;
      --border-color: #e5e7eb;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
      --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      line-height: 1.6;
      color: var(--text-primary);
      background: var(--bg-primary);
      overflow-x: hidden;
    }

    /* Navigation */
    .navbar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }

    .navbar.scrolled {
      background: rgba(255, 255, 255, 0.98);
      box-shadow: var(--shadow-lg);
    }

    .nav-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80px;
    }

    .logo {
      font-size: 1.75rem;
      font-weight: 800;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: var(--text-primary);
      font-weight: 500;
      transition: all 0.3s ease;
      position: relative;
    }

    .nav-links a::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 0;
      height: 2px;
      background: var(--primary-color);
      transition: width 0.3s ease;
    }

    .nav-links a:hover::after {
      width: 100%;
    }

    .cta-button {
      background: var(--gradient-primary);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-md);
    }

    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
    }

    /* Hero Section */
    .hero {
      min-height: 100vh;
      display: flex;
      align-items: center;
      position: relative;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      overflow: hidden;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
      opacity: 0.3;
    }

    .hero-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
      position: relative;
      z-index: 2;
    }

    .hero-content h1 {
      font-size: 3.5rem;
      font-weight: 800;
      color: white;
      margin-bottom: 1.5rem;
      line-height: 1.1;
    }

    .hero-content p {
      font-size: 1.25rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .hero-buttons {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .btn-primary {
      background: white;
      color: var(--primary-color);
      padding: 1rem 2rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-lg);
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      padding: 1rem 2rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
    }

    .btn-primary:hover {
      transform: translateY(-3px);
      box-shadow: var(--shadow-xl);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
    }

    /* Hero Animation Canvas */
    .hero-animation {
      position: relative;
      height: 500px;
      border-radius: 20px;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    #hero-canvas {
      width: 100%;
      height: 100%;
      border-radius: 20px;
    }

    /* Features Section */
    .features {
      padding: 6rem 0;
      background: var(--bg-secondary);
    }

    .features-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .section-header {
      text-align: center;
      margin-bottom: 4rem;
    }

    .section-header h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }

    .section-header p {
      font-size: 1.125rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
    }

    .feature-card {
      background: white;
      padding: 2rem;
      border-radius: 20px;
      box-shadow: var(--shadow-md);
      transition: all 0.3s ease;
      border: 1px solid var(--border-color);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-xl);
    }

    .feature-icon {
      width: 60px;
      height: 60px;
      background: var(--gradient-primary);
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;
      color: white;
      font-size: 1.5rem;
    }

    .feature-card h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .feature-card p {
      color: var(--text-secondary);
      line-height: 1.6;
    }

    /* Stats Section */
    .stats {
      padding: 4rem 0;
      background: var(--bg-dark);
      color: white;
    }

    .stats-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      text-align: center;
    }

    .stat-item h3 {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
      background: var(--gradient-success);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .stat-item p {
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .nav-links {
        display: none;
      }
      
      .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
      }
      
      .hero-content h1 {
        font-size: 2.5rem;
      }
      
      .hero-buttons {
        justify-content: center;
      }
    }

    /* Animation Classes */
    .fade-in {
      opacity: 0;
      transform: translateY(30px);
    }

    .slide-in-left {
      opacity: 0;
      transform: translateX(-50px);
    }

    .slide-in-right {
      opacity: 0;
      transform: translateX(50px);
    }

    .scale-in {
      opacity: 0;
      transform: scale(0.8);
    }

    /* Loading Animation */
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--bg-dark);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 3px solid rgba(255, 255, 255, 0.1);
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
  </div>

  <!-- Navigation -->
  <nav class="navbar" id="navbar">
    <div class="nav-container">
      <div class="logo">AmazingPay</div>
      <ul class="nav-links">
        <li><a href="#home">Home</a></li>
        <li><a href="#features">Features</a></li>
        <li><a href="#pricing">Pricing</a></li>
        <li><a href="#docs">Documentation</a></li>
        <li><a href="#contact">Contact</a></li>
      </ul>
      <a href="#get-started" class="cta-button">Get Started</a>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero" id="home">
    <div class="hero-container">
      <div class="hero-content">
        <h1 class="fade-in">Next-Generation Payment Gateway</h1>
        <p class="fade-in">Secure, fast, and reliable payment processing for modern businesses. Accept payments globally with enterprise-grade security and real-time analytics.</p>
        <div class="hero-buttons fade-in">
          <a href="#get-started" class="btn-primary">
            <i class="fas fa-rocket"></i>
            Start Free Trial
          </a>
          <a href="#demo" class="btn-secondary">
            <i class="fas fa-play"></i>
            Watch Demo
          </a>
        </div>
      </div>
      <div class="hero-animation">
        <canvas id="hero-canvas"></canvas>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features" id="features">
    <div class="features-container">
      <div class="section-header fade-in">
        <h2>Powerful Features for Modern Payments</h2>
        <p>Everything you need to accept, process, and manage payments with confidence</p>
      </div>
      <div class="features-grid">
        <div class="feature-card scale-in">
          <div class="feature-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h3>Enterprise Security</h3>
          <p>Bank-grade encryption, PCI DSS compliance, and advanced fraud detection to keep your transactions secure.</p>
        </div>
        <div class="feature-card scale-in">
          <div class="feature-icon">
            <i class="fas fa-bolt"></i>
          </div>
          <h3>Lightning Fast</h3>
          <p>Process payments in milliseconds with our optimized infrastructure and global CDN network.</p>
        </div>
        <div class="feature-card scale-in">
          <div class="feature-icon">
            <i class="fas fa-globe"></i>
          </div>
          <h3>Global Reach</h3>
          <p>Accept payments in 150+ currencies with local payment methods and multi-language support.</p>
        </div>
        <div class="feature-card scale-in">
          <div class="feature-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>Real-time Analytics</h3>
          <p>Comprehensive dashboards and reporting tools to track performance and optimize your business.</p>
        </div>
        <div class="feature-card scale-in">
          <div class="feature-icon">
            <i class="fas fa-code"></i>
          </div>
          <h3>Developer Friendly</h3>
          <p>RESTful APIs, SDKs for popular languages, and comprehensive documentation for easy integration.</p>
        </div>
        <div class="feature-card scale-in">
          <div class="feature-icon">
            <i class="fas fa-headset"></i>
          </div>
          <h3>24/7 Support</h3>
          <p>Round-the-clock technical support and dedicated account management for enterprise clients.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="stats">
    <div class="stats-container">
      <div class="stat-item fade-in">
        <h3 class="counter" data-target="99.9">0</h3>
        <p>Uptime Guarantee</p>
      </div>
      <div class="stat-item fade-in">
        <h3 class="counter" data-target="150">0</h3>
        <p>Supported Countries</p>
      </div>
      <div class="stat-item fade-in">
        <h3 class="counter" data-target="1000000">0</h3>
        <p>Transactions Daily</p>
      </div>
      <div class="stat-item fade-in">
        <h3 class="counter" data-target="50000">0</h3>
        <p>Happy Merchants</p>
      </div>
    </div>
  </section>

  <script>
    // Initialize GSAP
    gsap.registerPlugin(ScrollTrigger);

    // Loading Animation
    window.addEventListener('load', () => {
      gsap.to('#loadingOverlay', {
        opacity: 0,
        duration: 0.5,
        onComplete: () => {
          document.getElementById('loadingOverlay').style.display = 'none';
          initializeAnimations();
        }
      });
    });

    // Initialize all animations
    function initializeAnimations() {
      // Navbar scroll effect
      window.addEventListener('scroll', () => {
        const navbar = document.getElementById('navbar');
        if (window.scrollY > 50) {
          navbar.classList.add('scrolled');
        } else {
          navbar.classList.remove('scrolled');
        }
      });

      // Hero animations
      gsap.timeline()
        .from('.hero-content h1', { opacity: 0, y: 50, duration: 1, ease: 'power3.out' })
        .from('.hero-content p', { opacity: 0, y: 30, duration: 0.8, ease: 'power3.out' }, '-=0.5')
        .from('.hero-buttons', { opacity: 0, y: 30, duration: 0.8, ease: 'power3.out' }, '-=0.3')
        .from('.hero-animation', { opacity: 0, scale: 0.8, duration: 1, ease: 'power3.out' }, '-=0.8');

      // Scroll-triggered animations
      gsap.utils.toArray('.fade-in').forEach(element => {
        gsap.from(element, {
          opacity: 0,
          y: 50,
          duration: 1,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: element,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        });
      });

      gsap.utils.toArray('.scale-in').forEach(element => {
        gsap.from(element, {
          opacity: 0,
          scale: 0.8,
          duration: 0.8,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: element,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        });
      });

      // Counter animation
      gsap.utils.toArray('.counter').forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        gsap.to(counter, {
          innerHTML: target,
          duration: 2,
          ease: 'power2.out',
          snap: { innerHTML: 1 },
          scrollTrigger: {
            trigger: counter,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        });
      });

      // Initialize 3D Hero Animation
      initHeroAnimation();
    }

    // 3D Hero Animation with Three.js
    function initHeroAnimation() {
      const canvas = document.getElementById('hero-canvas');
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, canvas.offsetWidth / canvas.offsetHeight, 0.1, 1000);
      const renderer = new THREE.WebGLRenderer({ canvas, alpha: true, antialias: true });
      
      renderer.setSize(canvas.offsetWidth, canvas.offsetHeight);
      renderer.setClearColor(0x000000, 0);

      // Create animated geometry
      const geometry = new THREE.TorusKnotGeometry(10, 3, 100, 16);
      const material = new THREE.MeshBasicMaterial({ 
        color: 0x3b82f6,
        wireframe: true,
        transparent: true,
        opacity: 0.6
      });
      const torusKnot = new THREE.Mesh(geometry, material);
      scene.add(torusKnot);

      // Add particles
      const particlesGeometry = new THREE.BufferGeometry();
      const particlesCount = 1000;
      const posArray = new Float32Array(particlesCount * 3);

      for (let i = 0; i < particlesCount * 3; i++) {
        posArray[i] = (Math.random() - 0.5) * 100;
      }

      particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
      const particlesMaterial = new THREE.PointsMaterial({
        size: 0.005,
        color: 0xffffff,
        transparent: true,
        opacity: 0.8
      });

      const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
      scene.add(particlesMesh);

      camera.position.z = 30;

      // Animation loop
      function animate() {
        requestAnimationFrame(animate);

        torusKnot.rotation.x += 0.01;
        torusKnot.rotation.y += 0.01;
        
        particlesMesh.rotation.y += 0.002;

        renderer.render(scene, camera);
      }

      animate();

      // Handle resize
      window.addEventListener('resize', () => {
        camera.aspect = canvas.offsetWidth / canvas.offsetHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(canvas.offsetWidth, canvas.offsetHeight);
      });
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  </script>
</body>
</html>
