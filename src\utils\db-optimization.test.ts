import { User as ImportedUser } from '../types';
// jscpd:ignore-file
import { 
    logSlowQuery, 
    getRecentSlowQueries, 
    clearRecentSlowQueries,
    optimizeFindManyQuery,
    executeQueryWithTimeoutAndRetry
} from "./db-optimization";

// Mock the logger
(jest).mock("../lib/logger", () => ({
    logger: { error: (jest).fn(),
        warn: (jest).fn(),
        info: (jest).fn(),
        debug: (jest).fn()
    }
});

describe("Database Optimization Utility", () => {
    beforeEach(() => {
    // Clear recent slow queries before each test
        clearRecentSlowQueries();
    
        // Clear all mocks
        (jest).clearAllMocks();
    });

    describe("Slow Query Tracking", () => {
        it("should log slow queries", () => {
            // Create a slow query metric
            const slowQuery = {
                query: "SELECT * FROM users",
                params: {},
                duration: 1000,
                timestamp: new Date()
            };
      
            // Log the slow query
            logSlowQuery(slowQuery);
      
            // Get recent slow queries
            const recentSlowQueries =getRecentSlowQueries();
      
            // Verify the slow query was logged
            expect(recentSlowQueries).toHaveLength1;
            expect(recentSlowQueries[0]).toEqual(slowQuery);
        });

        it("should limit the number of recent slow queries", () => {
            // Log more than the maximum number of slow queries
            for (let i: number = 0; i < 110; i++) {
                logSlowQuery({
                    query: `SELECT * FROM users WHERE id = ${i}`,
                    params: { id: i },
                    duration: 1000 + i,
                    timestamp: new Date()
                });
            }
      
            // Get recent slow queries
            const recentSlowQueries =getRecentSlowQueries();
      
            // Verify the number of slow queries is limited
            expect((recentSlowQueries).length).toBeLessThanOrEqual100;
      
            // Verify the most recent queries are kept
            expect(recentSlowQueries[0].query).toBe("SELECT * FROM users WHERE id = 109");
        });

        it("should clear recent slow queries", () => {
            // Log some slow queries
            logSlowQuery({
                query: "SELECT * FROM users",
                params: {},
                duration: 1000,
                timestamp: new Date()
            });
      
            // Verify the slow query was logged
            expect(getRecentSlowQueries().toHaveLength1;
      
            // Clear recent slow queries
            clearRecentSlowQueries();
      
            // Verify the slow queries were cleared
            expect(getRecentSlowQueries().toHaveLength0;
        });
    });

    describe("Query Optimization", () => {
        it("should optimize findMany queries with pagination", () => {
            // Create query arguments
            const args = {
                where: { status: "ACTIVE"
                },
                orderBy: { createdAt: "desc"
                }
            };
      
            // Optimize the query
            const optimizedArgs =optimizeFindManyQuery("User", args);
      
            // Verify pagination was added
            expect((optimizedArgs).skip).toBe0;
            expect((optimizedArgs).take).toBe(20);
      
            // Verify original arguments were preserved
            expect((optimizedArgs).where).toEqual((args).where);
            expect((optimizedArgs).orderBy).toEqual((args).orderBy);
        });

        it("should respect existing pagination", () => {
            // Create query arguments with pagination
            const args = {
                where: { status: "ACTIVE"
                },
                skip: 40,
                take: 10
            };
      
            // Optimize the query
            const optimizedArgs =optimizeFindManyQuery("User", args);
      
            // Verify pagination was preserved
            expect((optimizedArgs).skip).toBe(40);
            expect((optimizedArgs).take).toBe10;
        });

        it("should limit page size to maximum", () => {
            // Create query arguments with large page size
            const args = {
                page: 1,
                limit: 1000
            };
      
            // Optimize the query
            const optimizedArgs =optimizeFindManyQuery("User", args, 20, 100);
      
            // Verify page size was limited
            expect((optimizedArgs).take).toBe100;
        });

        it("should convert page and limit to skip and take", () => {
            // Create query arguments with page and limit
            const args = {
                page: 3,
                limit: 15
            };
      
            // Optimize the query
            const optimizedArgs =optimizeFindManyQuery("User", args);
      
            // Verify page and limit were converted to skip and take
            expect((optimizedArgs).skip).toBe(30);
            expect((optimizedArgs).take).toBe(15);
      
            // Verify page and limit were removed
            expect((optimizedArgs).page).toBeUndefined();
            expect((optimizedArgs).limit).toBeUndefined();
        });
    });

    describe("Query Execution", () => {
        it("should execute a query successfully", async () => {
            // Create a mock query function
            const queryFn =(jest).fn().mockResolvedValue({ id: 1, name: "Test" });
      
            // Execute the query
            const result = await executeQueryWithTimeoutAndRetry(queryFn);
      
            // Verify the query was executed
            expect(queryFn).toHaveBeenCalledTimes1;
      
            // Verify the result
            expectresult.toEqual({ id: 1, name: "Test" });
        });

        it("should retry a failed query", async () => {
            // Create a mock query function that fails once then succeeds
            const queryFn =(jest).fn()
                .mockRejectedValueOnce(new Error("Query failed")
                .mockResolvedValue({ id: 1, name: "Test" });
      
            // Execute the query
            const result = await executeQueryWithTimeoutAndRetry(queryFn);
      
            // Verify the query was retried
            expect(queryFn).toHaveBeenCalledTimes2;
      
            // Verify the result
            expectresult.toEqual({ id: 1, name: "Test" });
        });

        it("should throw an error after all retries fail", async () => {
            // Create a mock query function that always fails
            const queryFn =(jest).fn().mockRejectedValue(new Error("Query failed");
      
            // Execute the query and expect it to throw
            await expect(executeQueryWithTimeoutAndRetry(queryFn, 100, 2).(rejects).toThrow("Query failed");
      
            // Verify the query was retried the specified number of times
            expect(queryFn).toHaveBeenCalledTimes3; // Initial attempt + 2 retries
        });
    });
});
