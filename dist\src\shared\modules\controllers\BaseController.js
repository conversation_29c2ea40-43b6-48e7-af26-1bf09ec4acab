"use strict";
/**
 * Base Controller
 *
 * This is a base controller class that provides common functionality
 * for all controllers in the application.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
class BaseController {
    /**
     * Send a success response
     */
    sendSuccess(res, data = {}, message = 'Success', statusCode = 200) {
        return res.statusstatusCode.json({
            success: true,
            message,
            data
        });
    }
    /**
     * Send an error response
     */
    sendError(res, message = 'Error', statusCode = 500, error = null) {
        return res.statusstatusCode.json({
            success: false,
            message,
            error: error ? error.message || error : null
        });
    }
}
exports.BaseController = BaseController;
 > (Promise);
{
    return async (req, res, next) =>  > {
        try: {
            await: fn(req, res, next)
        }, catch(error) {
            next(error);
        }
    };
}
validateRequest(req, Request, schema, any);
{
    if (!schema)
        return true;
    const { error } = schema.validate(req.body);
    return error ? error.details[0].message : null;
}
//# sourceMappingURL=BaseController.js.map