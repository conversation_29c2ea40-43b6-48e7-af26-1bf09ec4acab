{"file": "F:\\Amazingpayflow\\src\\tests\\unit\\auth.service.test.ts", "mappings": ";;;AAAA,oBAAoB;AACP,QAAA,UAAU,GAAG;IACtB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;IACvC,qBAAqB;CACxB,CAAC;AAEF,kBAAe,kBAAU,CAAC", "names": [], "sources": ["F:\\Amazingpayflow\\src\\tests\\unit\\auth.service.test.ts"], "sourcesContent": ["// jscpd:ignore-file\nexport const authConfig = {\n    jwtSecret: process.env.JWT_SECRET ?? '',\n    // Auth configuration\n};\n\nexport default authConfig;\n"], "version": 3}