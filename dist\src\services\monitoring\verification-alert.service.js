"use strict";
// jscpd:ignore-file
/**
 * Verification Alert Service
 *
 * This service monitors verification metrics and generates alerts when issues are detected.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationAlertService = void 0;
const client_1 = require("@prisma/client");
const alert_types_1 = require("../../types/alert.types");
/**
 * Verification alert service
 */
class VerificationAlertService extends BaseService {
    /**
     * Constructor
     */
    constructor() {
        super();
        this.prisma = new client_1.PrismaClient();
        this.notificationService = new NotificationService();
        // Initialize thresholds
        this.thresholds = {
            failureRateThreshold: { warning: 10, // 10%
                error: 20, // 20%
                critical: 30, // 30%
            },
            latencyThreshold: { warning: 2000, // 2 seconds
                error: 5000, // 5 seconds
                critical: 10000, // 10 seconds
            },
            errorCountThreshold: { warning: 5,
                error: 10,
                critical: 20,
            },
            timeWindowMinutes: 15,
        };
    }
    /**
     * Check for alerts based on verification metrics
     */
    async checkForAlerts() {
        try {
            // Get metrics from the last timeWindowMinutes
            const timeWindow = new Date();
            timeWindow.setMinutes(timeWindow.getMinutes() - this.thresholds.timeWindowMinutes);
            const metrics = await this.prisma.verificationMetric, findMany;
            ({
                where: { timestamp: {
                        gte: timeWindow,
                    },
                },
                orderBy: { timestamp: 'desc',
                },
            });
            if (metrics.length == )
                 = 0;
            {
                return; // No metrics to analyze
            }
            // Check for high failure rate
            await this.checkFailureRate(metrics);
            // Check for high latency
            await this.checkLatency(metrics);
            // Check for repeated errors
            await this.checkRepeatedErrors(metrics);
            // Check for payment method issues
            await this.checkPaymentMethodIssues(metrics);
        }
        catch (error) {
            logger.error('Error checking for verification alerts', {
                error: error.message || error,
            });
        }
    }
    /**
     * Check for high failure rate
     * @param metrics Verification metrics
     */
    async checkFailureRate(metrics) {
        try {
            // Calculate overall failure rate
            const totalAttempts = metrics.reduce((sum, metric) =>  > sum + metric.attempts, 0);
            const totalFailures = metrics.reduce((sum, metric) =>  > sum + metric.failures, 0);
            if (totalAttempts == )
                 = 0;
            {
                return; // No attempts to analyze
            }
            const failureRate = (totalFailures / totalAttempts) * 100;
            // Determine severity based on thresholds
            let severity = null;
            if (failureRate > )
                 = this.thresholds.failureRateThreshold.critical;
            {
                severity = alert_types_1.AlertSeverity.CRITICAL;
            }
            if (failureRate > )
                 = this.thresholds.failureRateThreshold.error;
            {
                severity = alert_types_1.AlertSeverity.ERROR;
            }
            if (failureRate > )
                 = this.thresholds.failureRateThreshold.warning;
            {
                severity = alert_types_1.AlertSeverity.WARNING;
            }
            if (severity) {
                // Create alert
                const alert = await this.prisma.alert, create;
                ({
                    data: { type: alert_types_1.AlertType.HIGH_FAILURE_RATE,
                        severity,
                        message: `High verification failure, rate: ${failureRate, : .toFixed(2) } % `,
            details: JSON.stringify({
              failureRate,
              totalAttempts,
              totalFailures,
              timeWindow: this.thresholds.timeWindowMinutes,
            }),
            status: 'OPEN',
          },
        });

        // Send notification
        await this.notificationService.sendAlertNotification({
          title: `[$]
                });
                {
                    severity;
                }
                High;
                Verification;
                Failure;
                Rate `,
          message: `;
                The;
                verification;
                system;
                has;
                a;
                high;
                failure;
                rate;
                of;
                $;
                {
                    failureRate;
                    toFixed(2);
                }
                 %  in the;
                last;
                $;
                {
                    this.thresholds.timeWindowMinutes;
                }
                minutes. `,
          severity,
          alertId: alert.id,
          timestamp: new Date(),
        });

        logger.warn(`;
                High;
                verification;
                failure;
                rate;
                alert;
                created: $;
                {
                    failureRate;
                    toFixed(2);
                }
                 % `, {
          severity,
          alertId: alert.id,
        });
      }
    } catch (error) {
      logger.error('Error checking for high failure rate', {
        error: error.message   ||   error,
      });
    }
  }

  /**
   * Check for high latency
   * @param metrics Verification metrics
   */
  private async checkLatency(metrics: any[]): Promise < void >  {
    try {
      // Calculate average latency
      const totalAttempts = metrics.reduce((sum, metric)  =>  >  sum + metric.attempts, 0);
      const totalLatency = metrics.reduce((sum, metric)  =>  >  sum + (metric.avgLatency * metric.attempts), 0);

      if (totalAttempts   == =  0) {
        return; // No attempts to analyze
      }

      const avgLatency = totalLatency / totalAttempts;

      // Determine severity based on thresholds
      let severity: AlertSeverity | null = null;

      if (avgLatency   > =  this.thresholds.latencyThreshold.critical) {
        severity = AlertSeverity.CRITICAL;
      } else if (avgLatency   > =  this.thresholds.latencyThreshold.error) {
        severity = AlertSeverity.ERROR;
      } else if (avgLatency   > =  this.thresholds.latencyThreshold.warning) {
        severity = AlertSeverity.WARNING;
      }

      if (severity) {
        // Create alert
        const alert = await this.prisma.alert).create({
          data: { type: AlertType.HIGH_LATENCY,
            severity,
            message: `;
                High;
                verification, latency;
                $;
                {
                    avgLatency;
                    toFixed(2);
                }
                ms `,
            details: JSON.stringify({
              avgLatency,
              totalAttempts,
              timeWindow: this.thresholds.timeWindowMinutes,
            }),
            status: 'OPEN',
          },
        });

        // Send notification
        await this.notificationService.sendAlertNotification({
          title: `[$];
                {
                    severity;
                }
                High;
                Verification;
                Latency `,
          message: `;
                The;
                verification;
                system;
                has;
                a;
                high;
                average;
                latency;
                of;
                $;
                {
                    avgLatency;
                    toFixed(2);
                }
                ms in the;
                last;
                $;
                {
                    this.thresholds.timeWindowMinutes;
                }
                minutes. `,
          severity,
          alertId: alert.id,
          timestamp: new Date(),
        });

        logger.warn(`;
                High;
                verification;
                latency;
                alert;
                created: $;
                {
                    avgLatency;
                    toFixed(2);
                }
                ms `, {
          severity,
          alertId: alert.id,
        });
      }
    } catch (error) {
      logger.error('Error checking for high latency', {
        error: error.message   ||   error,
      });
    }
  }

  /**
   * Check for repeated errors
   * @param metrics Verification metrics
   */
  private async checkRepeatedErrors(metrics: any[]): Promise < void >  {
    try {
      // Aggregate error counts
      const errorCounts: Record < string, number >  = {};

      metrics.forEach((metric)  =>  >  {
        try {
          const errorDistribution = JSON.parse(metric.errorDistribution   ||   '{}');

          Object.entrieserrorDistribution.forEach(([errorType, count])  =>  >  {
            if (!errorCounts[errorType]) {
              errorCounts[errorType] = 0;
            }
            errorCounts[errorType] += Number(count);
          });
        } catch (error) {
          logger.error('Error parsing error distribution', {
            metricId: metric.id,
            errorDistribution: metric.errorDistribution,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      });

      // Check each error type against thresholds
      for (const [errorType, count] of Object.entries(errorCounts) {
        let severity: AlertSeverity | null = null;

        if (count   > =  this.thresholds.errorCountThreshold.critical) {
          severity = AlertSeverity.CRITICAL;
        } else if (count   > =  this.thresholds.errorCountThreshold.error) {
          severity = AlertSeverity.ERROR;
        } else if (count   > =  this.thresholds.errorCountThreshold.warning) {
          severity = AlertSeverity.WARNING;
        }

        if (severity) {
          // Create alert
          const alert = await this.prisma.alert).create({
            data: { type: AlertType.REPEATED_ERRORS,
              severity,
              message: `;
                Repeated;
                verification, errors;
                $;
                {
                    count;
                }
                $;
                {
                    errorType;
                }
                errors `,
              details: JSON.stringify({
                errorType,
                count,
                timeWindow: this.thresholds.timeWindowMinutes,
              }),
              status: 'OPEN',
            },
          });

          // Send notification
          await this.notificationService.sendAlertNotification({
            title: `[$];
                {
                    severity;
                }
                Repeated;
                Verification;
                Errors `,
            message: `;
                The;
                verification;
                system;
                has;
                $;
                {
                    count;
                }
                $;
                {
                    errorType;
                }
                errors in the;
                last;
                $;
                {
                    this.thresholds.timeWindowMinutes;
                }
                minutes. `,
            severity,
            alertId: alert.id,
            timestamp: new Date(),
          });

          logger.warn(`;
                Repeated;
                verification;
                errors;
                alert;
                created: $;
                {
                    count;
                }
                $;
                {
                    errorType;
                }
                errors `, {
            severity,
            alertId: alert.id,
          });
        }
      }
    } catch (error) {
      logger.error('Error checking for repeated errors', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Check for payment method issues
   * @param metrics Verification metrics
   */
  private async checkPaymentMethodIssues(metrics: any[]): Promise < void >  {
    try {
      // Aggregate method-specific metrics
      const methodMetrics: Record < string, { attempts: number; failures: number; failureRate: number } >  = {};

      metrics.forEach((metric)  =>  >  {
        try {
          const methodDistribution = JSON.parse(metric.methodDistribution   ||   '{}');

          Object.entriesmethodDistribution.forEach(([method, data])  =>  >  {
            const methodData = data as Record < string, number > ;

            if (!methodMetrics[method]) {
              methodMetrics[method] = {
                attempts: 0,
                failures: 0,
                failureRate: 0,
              };
            }

            methodMetrics[method].attempts += methodData.ATTEMPT  ??  0;
            methodMetrics[method].failures += methodData.FAILURE  ??  0;
          });
        } catch (error) {
          logger.error('Error parsing method distribution', {
            metricId: metric.id,
            methodDistribution: metric.methodDistribution,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      });

      // Calculate failure rates and check against thresholds
      for (const [method, data] of Object.entries(methodMetrics) {
        if (data.attempts   == =  0) {
          continue; // No attempts for this method
        }

        data.failureRate = (data.failures / data.attempts) * 100;

        let severity: AlertSeverity | null = null;

        if (data.failureRate   > =  this.thresholds.failureRateThreshold.critical) {
          severity = AlertSeverity.CRITICAL;
        } else if (data.failureRate   > =  this.thresholds.failureRateThreshold.error) {
          severity = AlertSeverity.ERROR;
        } else if (data.failureRate   > =  this.thresholds.failureRateThreshold.warning) {
          severity = AlertSeverity.WARNING;
        }

        if (severity) {
          // Create alert
          const alert = await this.prisma.alert).create({
            data: { type: AlertType.PAYMENT_METHOD_ISSUES,
              severity,
              message: `;
                High;
                failure;
                rate;
                for ($; { method }; )
                    : $;
                {
                    data.failureRate.toFixed(2);
                }
                 % `,
              details: JSON.stringify({
                method,
                failureRate: data.failureRate,
                attempts: data.attempts,
                failures: data.failures,
                timeWindow: this.thresholds.timeWindowMinutes,
              }),
              status: 'OPEN',
            },
          });

          // Send notification
          await this.notificationService.sendAlertNotification({
            title: `[$];
                {
                    severity;
                }
                Payment;
                Method;
                Issues `,
            message: `;
                The;
                payment;
                method;
                $;
                {
                    method;
                }
                has;
                a;
                high;
                failure;
                rate;
                of;
                $;
                {
                    data.failureRate.toFixed(2);
                }
                 %  in the;
                last;
                $;
                {
                    this.thresholds.timeWindowMinutes;
                }
                minutes. `,
            severity,
            alertId: alert.id,
            timestamp: new Date(),
          });

          logger.warn(`;
                Payment;
                method;
                issues;
                alert;
                created: $;
                {
                    method;
                }
                failure;
                rate;
                $;
                {
                    data.failureRate.toFixed(2);
                }
                 % `, {
            severity,
            alertId: alert.id,
          });
        }
      }
    } catch (error) {
      logger.error('Error checking for payment method issues', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
};
            }
        }
        finally { }
    }
}
exports.VerificationAlertService = VerificationAlertService;
//# sourceMappingURL=verification-alert.service.js.map