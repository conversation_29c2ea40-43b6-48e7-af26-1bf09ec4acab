#!/bin/bash
# 🤖 AUTOMATED SECURITY RESPONSE SCRIPT
# This script automates most security actions that can be done programmatically

set -e  # Exit on any error

echo "🚨 STARTING AUTOMATED SECURITY RESPONSE..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Step 1: Generate new secure credentials
echo ""
echo "🔑 STEP 1: GENERATING NEW SECURE CREDENTIALS..."
echo "=================================================="

# Generate JWT Secret (256-bit)
JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
print_status "Generated new JWT secret (256-bit)"

# Generate JWT Refresh Secret
JWT_REFRESH_SECRET=$(openssl rand -base64 64 | tr -d '\n')
print_status "Generated new JWT refresh secret"

# Generate CSRF Secret
CSRF_SECRET=$(openssl rand -hex 32)
print_status "Generated new CSRF secret"

# Generate Session Secret
SESSION_SECRET=$(openssl rand -base64 48 | tr -d '\n')
print_status "Generated new session secret"

# Generate Encryption Key
ENCRYPTION_KEY=$(openssl rand -hex 32)
print_status "Generated new encryption key"

# Generate API Secret
API_SECRET_KEY=$(openssl rand -base64 32 | tr -d '\n')
print_status "Generated new API secret key"

# Generate database password
DB_PASSWORD=$(openssl rand -base64 24 | tr -d '\n' | tr '+/' 'Az')
print_status "Generated new database password"

# Step 2: Create secure .env file
echo ""
echo "📝 STEP 2: CREATING SECURE .ENV FILE..."
echo "=================================================="

cat > .env << EOF
# 🔒 AMAZINGPAY SECURE ENVIRONMENT - AUTO-GENERATED $(date)
# ⚠️ NEVER COMMIT THIS FILE TO VERSION CONTROL!

# Server Configuration
PORT=3002
HOST=localhost
NODE_ENV=development
API_PREFIX=/api

# Frontend Configuration
FRONTEND_URL=http://localhost:5173
API_URL=http://localhost:3002/api
DOMAIN=amazingpayme.com

# 🔒 DATABASE CONFIGURATION - NEW SECURE CREDENTIALS
DATABASE_URL=postgresql://amazingpay_app:${DB_PASSWORD}@localhost:5432/amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=amazingpay_app
DB_PASSWORD=${DB_PASSWORD}
DB_NAME=amazingpay
DB_SSL=false
DB_CONNECTION_POOL_MIN=5
DB_CONNECTION_POOL_MAX=20
DB_STATEMENT_TIMEOUT=30000

# 🔑 JWT CONFIGURATION - NEW SECURE SECRETS
JWT_SECRET=${JWT_SECRET}
JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=amazingpayme.com
JWT_AUDIENCE=amazingpayme.com

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_PREFLIGHT_CONTINUE=false
CORS_OPTIONS_SUCCESS_STATUS=204
CORS_MAX_AGE=86400

# 🛡️ SECURITY CONFIGURATION - NEW SECURE SECRETS
BCRYPT_SALT_ROUNDS=12
CSRF_ENABLED=true
CSRF_SECRET=${CSRF_SECRET}
CSRF_TOKEN_EXPIRATION=3600000
XSS_PROTECTION=true
CONTENT_SECURITY_POLICY=true
HSTS=true
HSTS_MAX_AGE=31536000
FRAME_GUARD=true
NO_SNIFF=true
REFERRER_POLICY=strict-origin-when-cross-origin

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
AUTH_RATE_LIMIT_WINDOW_MS=3600000
AUTH_RATE_LIMIT_MAX=5
PASSWORD_RESET_RATE_LIMIT_WINDOW_MS=3600000
PASSWORD_RESET_RATE_LIMIT_MAX=3

# 📧 EMAIL CONFIGURATION - UPDATE THESE MANUALLY
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USER=MANUAL_UPDATE_REQUIRED
EMAIL_PASSWORD=MANUAL_UPDATE_REQUIRED
EMAIL_FROM=<EMAIL>
EMAIL_SECURE=true

# 📱 SMS CONFIGURATION - UPDATE THESE MANUALLY
TWILIO_ACCOUNT_SID=MANUAL_UPDATE_REQUIRED
TWILIO_AUTH_TOKEN=MANUAL_UPDATE_REQUIRED
TWILIO_PHONE_NUMBER=+**********

# 💰 BINANCE API CONFIGURATION - UPDATE THESE MANUALLY
BINANCE_API_URL=https://api.binance.com
BINANCE_API_KEY=MANUAL_UPDATE_REQUIRED
BINANCE_API_SECRET=MANUAL_UPDATE_REQUIRED

# ⛓️ BLOCKCHAIN API CONFIGURATION - UPDATE THESE MANUALLY
TRONSCAN_API_URL=https://apilist.tronscan.org/api
ETHERSCAN_API_URL=https://api.etherscan.io/api
ETHERSCAN_API_KEY=MANUAL_UPDATE_REQUIRED

# 📝 LOGGING CONFIGURATION
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7

# 📊 ADVANCED REPORTING CONFIGURATION
REPORTS_DIR=./reports
MAX_MEMORY_USAGE=*********
BATCH_SIZE=1000
STREAMING_THRESHOLD=*********

# 📧 SMTP CONFIGURATION FOR REPORTS
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=MANUAL_UPDATE_REQUIRED
SMTP_PASSWORD=MANUAL_UPDATE_REQUIRED

# 🔐 ADDITIONAL SECURITY SECRETS - AUTO-GENERATED
SESSION_SECRET=${SESSION_SECRET}
ENCRYPTION_KEY=${ENCRYPTION_KEY}
API_SECRET_KEY=${API_SECRET_KEY}

# 🚨 SECURITY INCIDENT TRACKING
SECURITY_INCIDENT_DATE=$(date +%Y-%m-%d)
CREDENTIAL_ROTATION_DATE=$(date +%Y-%m-%d)
NEXT_ROTATION_DUE=$(date -d "+3 months" +%Y-%m-%d)
EOF

print_status "Created secure .env file with auto-generated secrets"

# Step 3: Generate database setup script
echo ""
echo "🗄️ STEP 3: CREATING DATABASE SETUP SCRIPT..."
echo "=================================================="

cat > setup-secure-database.sql << EOF
-- 🔒 SECURE DATABASE SETUP SCRIPT
-- Auto-generated on $(date)

-- Create new secure application user
CREATE USER amazingpay_app WITH PASSWORD '${DB_PASSWORD}';

-- Grant necessary privileges
GRANT ALL PRIVILEGES ON DATABASE amazingpay TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO amazingpay_app;

-- Change postgres password (uncomment and set new password)
-- ALTER USER postgres PASSWORD 'NEW_POSTGRES_PASSWORD_HERE';

-- Verify users
\du

-- Show current database
SELECT current_database();
EOF

print_status "Created database setup script"

# Step 4: Create SSH key generation script
echo ""
echo "🔑 STEP 4: CREATING SSH KEY GENERATION SCRIPT..."
echo "=================================================="

cat > generate-ssh-keys.sh << 'EOF'
#!/bin/bash
# 🔑 SSH KEY GENERATION SCRIPT

echo "🔑 Generating new SSH keys..."

# Generate new SSH key pair
ssh-keygen -t ed25519 -C "amazingpay-secure-$(date +%Y%m%d)" -f ~/.ssh/amazingpay_secure -N ""

echo "✅ SSH keys generated:"
echo "   Private key: ~/.ssh/amazingpay_secure"
echo "   Public key:  ~/.ssh/amazingpay_secure.pub"
echo ""
echo "📋 Next steps:"
echo "1. Copy public key to your server:"
echo "   ssh-copy-id -i ~/.ssh/amazingpay_secure.pub user@your-server"
echo "2. Test connection:"
echo "   ssh -i ~/.ssh/amazingpay_secure user@your-server"
echo "3. Update your deployment scripts to use new key"
EOF

chmod +x generate-ssh-keys.sh
print_status "Created SSH key generation script"

# Step 5: Clean git history (if in git repo)
echo ""
echo "🧹 STEP 5: CHECKING GIT REPOSITORY..."
echo "=================================================="

if [ -d ".git" ]; then
    print_info "Git repository detected"
    
    # Create backup branch
    git branch backup-before-security-cleanup 2>/dev/null || true
    print_status "Created backup branch"
    
    # Remove sensitive files from git tracking
    git rm --cached .env 2>/dev/null || true
    git rm --cached ecosystem.config.js 2>/dev/null || true
    git rm --cached ecosystem.vps.config.js 2>/dev/null || true
    git rm --cached deploy-on-vps.sh 2>/dev/null || true
    git rm --cached backup-vps.sh 2>/dev/null || true
    git rm --cached vps-setup.sh 2>/dev/null || true
    git rm --cached upload-to-github.sh 2>/dev/null || true
    git rm --cached upload-to-github.bat 2>/dev/null || true
    
    print_status "Removed sensitive files from git tracking"
    
    # Commit changes
    git add .gitignore 2>/dev/null || true
    git commit -m "🔒 Security: Remove sensitive files and enhance .gitignore" 2>/dev/null || true
    
    print_status "Committed security changes"
else
    print_warning "Not a git repository - skipping git cleanup"
fi

# Step 6: Create manual action checklist
echo ""
echo "📋 STEP 6: CREATING MANUAL ACTION CHECKLIST..."
echo "=================================================="

cat > MANUAL_ACTIONS_REQUIRED.md << EOF
# 📋 MANUAL ACTIONS REQUIRED

## ✅ AUTOMATED ACTIONS COMPLETED
- [x] Generated new secure secrets (JWT, CSRF, Session, etc.)
- [x] Created secure .env file with auto-generated credentials
- [x] Created database setup script
- [x] Created SSH key generation script
- [x] Cleaned git tracking of sensitive files

## 🔴 MANUAL ACTIONS STILL REQUIRED

### 1. MAKE REPOSITORY PRIVATE (2 minutes) - CRITICAL!
- [ ] Go to: https://github.com/Amazingteam-eg/Amazingpayflow/settings
- [ ] Scroll to "Danger Zone"
- [ ] Click "Change repository visibility" → "Make private"

### 2. UPDATE DATABASE (5 minutes)
\`\`\`bash
# Run the generated database script
psql -U postgres -d amazingpay -f setup-secure-database.sql
\`\`\`

### 3. GENERATE SSH KEYS (3 minutes)
\`\`\`bash
# Run the SSH key generation script
./generate-ssh-keys.sh
\`\`\`

### 4. UPDATE API KEYS (10 minutes)
Update these in your .env file:
- [ ] TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN
- [ ] BINANCE_API_KEY and BINANCE_API_SECRET  
- [ ] ETHERSCAN_API_KEY
- [ ] EMAIL_USER and EMAIL_PASSWORD

### 5. TEST APPLICATION (5 minutes)
\`\`\`bash
# Test with new credentials
npm start
\`\`\`

### 6. CLEAN GIT HISTORY (10 minutes)
\`\`\`bash
# Run the comprehensive cleanup
./security-cleanup.sh
git push origin --force --all
\`\`\`

## 🎯 TOTAL TIME: ~35 minutes
## 🚨 PRIORITY: Make repository private FIRST!
EOF

print_status "Created manual action checklist"

# Step 7: Summary
echo ""
echo "🎉 AUTOMATED SECURITY RESPONSE COMPLETE!"
echo "=================================================="
print_status "Generated new secure credentials"
print_status "Created secure .env file"
print_status "Created database setup script"
print_status "Created SSH key generation script"
print_status "Cleaned git tracking"
print_status "Created manual action checklist"

echo ""
print_warning "CRITICAL: Repository is still PUBLIC!"
print_warning "NEXT STEP: Make repository private immediately"
print_info "Follow instructions in: MANUAL_ACTIONS_REQUIRED.md"

echo ""
echo "📊 SECURITY STATUS:"
echo "   🤖 Automated: 70% complete"
echo "   👤 Manual:    30% remaining"
echo "   ⏰ Time:      ~35 minutes total"
echo ""
print_info "Run: cat MANUAL_ACTIONS_REQUIRED.md"
