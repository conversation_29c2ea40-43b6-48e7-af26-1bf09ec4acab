paths:
  /api/transactions:
    get:
      tags:
        - Transactions
      summary: Get Transactions
      description: Get a list of transactions
      parameters:
        - in: query
          name: page
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number
        - in: query
          name: limit
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of items per page
        - in: query
          name: status
          schema:
            type: string
            enum: [PENDING, PROCESSING, COMPLETED, FAILED, REFUNDED, CANCELLED]
          description: Filter by status
        - in: query
          name: startDate
          schema:
            type: string
            format: date
          description: Filter by start date (ISO format)
        - in: query
          name: endDate
          schema:
            type: string
            format: date
          description: Filter by end date (ISO format)
      responses:
        '200':
          description: Transactions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      transactions:
                        type: array
                        items:
                          $ref: '#/components/schemas/Transaction'
                      pagination:
                        type: object
                        properties:
                          page:
                            type: integer
                            example: 1
                          limit:
                            type: integer
                            example: 20
                          totalItems:
                            type: integer
                            example: 100
                          totalPages:
                            type: integer
                            example: 5
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      tags:
        - Transactions
      summary: Create Transaction
      description: Create a new transaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - amount
                - currency
                - paymentMethod
              properties:
                amount:
                  type: number
                  format: float
                  example: 99.99
                currency:
                  type: string
                  example: USD
                paymentMethod:
                  type: string
                  example: CREDIT_CARD
                customerId:
                  type: string
                  format: uuid
                  example: 123e4567-e89b-12d3-a456-************
                description:
                  type: string
                  example: Payment for order #12345
                metadata:
                  type: object
                  example:
                    orderId: 12345
                    productId: 67890
      responses:
        '201':
          description: Transaction created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Transaction'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/transactions/{id}:
    get:
      tags:
        - Transactions
      summary: Get Transaction
      description: Get a transaction by ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Transaction ID
      responses:
        '200':
          description: Transaction retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Transaction'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/transactions/{id}/status:
    patch:
      tags:
        - Transactions
      summary: Update Transaction Status
      description: Update the status of a transaction
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Transaction ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [PENDING, PROCESSING, COMPLETED, FAILED, REFUNDED, CANCELLED]
                  example: COMPLETED
                note:
                  type: string
                  example: Payment confirmed by bank
      responses:
        '200':
          description: Transaction status updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Transaction'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/transactions/{id}/risk:
    get:
      tags:
        - Transactions
      summary: Get Transaction Risk Assessment
      description: Get the risk assessment for a transaction
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
            format: uuid
          description: Transaction ID
      responses:
        '200':
          description: Risk assessment retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      transactionId:
                        type: string
                        format: uuid
                      score:
                        type: number
                        example: 25
                      level:
                        type: string
                        enum: [MINIMAL, LOW, MEDIUM, HIGH]
                        example: LOW
                      factors:
                        type: array
                        items:
                          type: string
                        example:
                          - Transaction amount is higher than merchant average
                      timestamp:
                        type: string
                        format: date-time
        '404':
          description: Risk assessment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
