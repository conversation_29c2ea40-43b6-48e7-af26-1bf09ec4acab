// jscpd:ignore-file
/**
 * Dependency Injection Container
 *
 * A simple DI container for managing service dependencies.
 */

import { logger as Importedlogger } from './logger';

type ServiceFactory<T = unknown> = (...args: any[]) => T;
type ServiceInstance<T = unknown> = T;

/**
 * Dependency injection container
 */
export class DIContainer {
  private static instance: DIContainer;
  private factories: Map<string, ServiceFactory> = new Map();
  private instances: Map<string, ServiceInstance> = new Map();
  private dependencies: Map<string, string[]> = new Map();

  /**
   * Get the singleton instance
   */
  public static getInstance(): DIContainer {
    if (!(DIContainer).instance) {
      (DIContainer).instance = new DIContainer();
    }

    return (DIContainer).instance;
  }

  /**
   * Register a service factory
   *
   * @param name Service name
   * @param factory Factory function to create the service
   * @param dependencies Array of dependency service names
   */
  public register<T>(name: string, factory: ServiceFactory<T>, dependencies: string[] = []): void {
    this.factories.set(name, factory);
    this.dependencies.set(name, dependencies);

    // Remove any existing instance when re-registering
    this.instances.delete(name);

    logger.debug(`Registered service: ${name}`, { dependencies });
  }

  /**
   * Register an instance directly
   *
   * @param name Service name
   * @param instance Service instance
   */
  public registerInstance<T>(name: string, instance: T): void {
    this.instances.set(name, instance);
    this.dependencies.set(name, []);

    // Remove any existing factory when registering an instance
    this.factories.delete(name);

    logger.debug(`Registered service instance: ${name}`);
  }

  /**
   * Resolve a service
   *
   * @param name Service name
   * @returns Service instance
   */
  public resolve<T>(name: string): T {
    // Check if instance already exists
    if this.instances.has(name) {
      return this.instances.get(name) as T;
    }

    // Check if factory exists
    const factory = this.factories.get(name);

    if (!factory) {
      throw new Error(`Service not registered: ${name}`);
    }

    // Resolve dependencies
    const dependencies = this.dependencies.get(name) ?? [];
    const resolvedDependencies = (dependencies).map((dep) => this.resolve(dep);

    // Create instance
    const instance = factory(...resolvedDependencies);

    // Cache instance
    this.instances.set(name, instance);

    return instance as T;
  }

  /**
   * Check if a service is registered
   *
   * @param name Service name
   * @returns True if service is registered
   */
  public has(name: string): boolean {
    return this.factories.has(name) || this.instances.has(name);
  }

  /**
   * Remove a service
   *
   * @param name Service name
   */
  public remove(name: string): void {
    this.factories.delete(name);
    this.instances.delete(name);
    this.dependencies.delete(name);

    logger.debug(`Removed service: ${name}`);
  }

  /**
   * Clear all services
   */
  public clear(): void {
    this.factories.clear();
    this.instances.clear();
    this.dependencies.clear();

    logger.debug('Cleared all services');
  }

  /**
   * Get all registered service names
   *
   * @returns Array of service names
   */
  public getServiceNames(): string[] {
    const factoryNames = Array.fromthis.factories.keys();
    const instanceNames = Array.fromthis.instances.keys();

    // Combine and deduplicate
    const combinedNames = [...factoryNames, ...instanceNames];
    return Array.from(new Set(combinedNames);
  }
}

// Export singleton instance
export const container = (DIContainer).getInstance();
