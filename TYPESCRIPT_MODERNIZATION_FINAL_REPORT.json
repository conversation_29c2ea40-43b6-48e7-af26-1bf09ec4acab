{"summary": {"startTime": "2025-05-27T23:28:19.072Z", "duration": "678ms", "initialErrors": 6391, "finalErrors": 11957, "errorsFixed": -5566, "successRate": "-87.1", "filesProcessed": 470, "filesWithFixes": 451, "totalFixes": 2913}, "files": [{"file": "src\\config\\api\\production.api.config.ts", "fixes": 8}, {"file": "src\\config\\auth.ts", "fixes": 9}, {"file": "src\\config\\database\\production.config.ts", "fixes": 7}, {"file": "src\\config\\database.config.ts", "fixes": 8}, {"file": "src\\config\\database.ts", "fixes": 5}, {"file": "src\\config\\env.config.ts", "fixes": 4}, {"file": "src\\config\\environment\\production.config.ts", "fixes": 10}, {"file": "src\\config\\environment.ts", "fixes": 4}, {"file": "src\\config\\index.ts", "fixes": 10}, {"file": "src\\config\\rbac\\PermissionGroups.ts", "fixes": 2}, {"file": "src\\config\\rbac\\RoleTemplates.ts", "fixes": 3}, {"file": "src\\config\\security\\production.security.config.ts", "fixes": 10}, {"file": "src\\config\\verification\\PredefinedVerificationPolicies.ts", "fixes": 8}, {"file": "src\\controllers\\admin\\AdminController.ts", "fixes": 2}, {"file": "src\\controllers\\admin\\mappers\\AdminResponseMapper.ts", "fixes": 9}, {"file": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "fixes": 10}, {"file": "src\\controllers\\admin\\services\\AdminBusinessService.ts", "fixes": 8}, {"file": "src\\controllers\\admin\\services\\AdminValidationService.ts", "fixes": 10}, {"file": "src\\controllers\\admin\\types\\AdminControllerTypes.ts", "fixes": 3}, {"file": "src\\controllers\\admin\\__tests__\\AdminController.test.ts", "fixes": 5}, {"file": "src\\controllers\\advanced-report.controller.ts", "fixes": 8}, {"file": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "fixes": 7}, {"file": "src\\controllers\\alert-aggregation\\mappers\\ResponseMapper.ts", "fixes": 7}, {"file": "src\\controllers\\alert-aggregation\\services\\AlertAggregationBusinessService.ts", "fixes": 8}, {"file": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "fixes": 8}, {"file": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "fixes": 11}, {"file": "src\\controllers\\alert-aggregation\\types\\AlertAggregationTypes.ts", "fixes": 3}, {"file": "src\\controllers\\alert-analytics.controller.ts", "fixes": 11}, {"file": "src\\controllers\\alert-notification.controller.ts", "fixes": 4}, {"file": "src\\controllers\\alert.controller.ts", "fixes": 10}, {"file": "src\\controllers\\AlertAggregationController.ts", "fixes": 11}, {"file": "src\\controllers\\AlertAnalyticsController.ts", "fixes": 3}, {"file": "src\\controllers\\AlertController.ts", "fixes": 9}, {"file": "src\\controllers\\analytics.controller.ts", "fixes": 6}, {"file": "src\\controllers\\api-analytics.controller.ts", "fixes": 6}, {"file": "src\\controllers\\auth.controller.ts", "fixes": 6}, {"file": "src\\controllers\\base\\BaseController.ts", "fixes": 7}, {"file": "src\\controllers\\base\\CrudController.ts", "fixes": 4}, {"file": "src\\controllers\\base.controller.ts", "fixes": 11}, {"file": "src\\controllers\\binance-pay-webhook.controller.ts", "fixes": 9}, {"file": "src\\controllers\\binance-verification.controller.ts", "fixes": 6}, {"file": "src\\controllers\\binance.controller.ts", "fixes": 5}, {"file": "src\\controllers\\blockchain-verification.controller.ts", "fixes": 6}, {"file": "src\\controllers\\cache.controller.ts", "fixes": 5}, {"file": "src\\controllers\\dashboard-widget.controller.ts", "fixes": 8}, {"file": "src\\controllers\\dashboard.controller.ts", "fixes": 9}, {"file": "src\\controllers\\email.controller.ts", "fixes": 7}, {"file": "src\\controllers\\enhanced-payment.controller.ts", "fixes": 8}, {"file": "src\\controllers\\enhanced-risk-engine.controller.ts", "fixes": 9}, {"file": "src\\controllers\\enhanced-verification.controller.ts", "fixes": 7}, {"file": "src\\controllers\\example.controller.ts", "fixes": 3}, {"file": "src\\controllers\\fee-management.controller.ts", "fixes": 9}, {"file": "src\\controllers\\fraud-detection\\FraudDetectionController.ts", "fixes": 7}, {"file": "src\\controllers\\fraud-detection\\mappers\\FraudDetectionResponseMapper.ts", "fixes": 8}, {"file": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "fixes": 10}, {"file": "src\\controllers\\fraud-detection\\services\\FraudDetectionBusinessService.ts", "fixes": 11}, {"file": "src\\controllers\\fraud-detection\\types\\FraudDetectionControllerTypes.ts", "fixes": 3}, {"file": "src\\controllers\\fraud-detection\\validators\\BaseValidator.ts", "fixes": 7}, {"file": "src\\controllers\\fraud-detection\\validators\\FraudConfigValidator.ts", "fixes": 11}, {"file": "src\\controllers\\fraud-detection\\validators\\TransactionRiskValidator.ts", "fixes": 8}, {"file": "src\\controllers\\health.controller.ts", "fixes": 9}, {"file": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "fixes": 4}, {"file": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "fixes": 7}, {"file": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "fixes": 10}, {"file": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "fixes": 9}, {"file": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "fixes": 3}, {"file": "src\\controllers\\location.controller.ts", "fixes": 4}, {"file": "src\\controllers\\log.controller.ts", "fixes": 4}, {"file": "src\\controllers\\merchant\\MerchantController.ts", "fixes": 10}, {"file": "src\\controllers\\merchant-relationship.controller.ts", "fixes": 9}, {"file": "src\\controllers\\merchant-segmentation.controller.ts", "fixes": 7}, {"file": "src\\controllers\\merchant-self-service.controller.ts", "fixes": 9}, {"file": "src\\controllers\\merchant-subscription.controller.ts", "fixes": 7}, {"file": "src\\controllers\\merchant.controller.ts", "fixes": 9}, {"file": "src\\controllers\\monitoring\\verification-monitoring.controller.ts", "fixes": 9}, {"file": "src\\controllers\\monitoring.controller.ts", "fixes": 9}, {"file": "src\\controllers\\multi-factor-auth.controller.ts", "fixes": 6}, {"file": "src\\controllers\\notification.controller.ts", "fixes": 8}, {"file": "src\\controllers\\operational-mode.controller.ts", "fixes": 10}, {"file": "src\\controllers\\optimization\\verification-optimization.controller.ts", "fixes": 7}, {"file": "src\\controllers\\payment-method.controller.ts", "fixes": 11}, {"file": "src\\controllers\\payment-page.controller.ts", "fixes": 5}, {"file": "src\\controllers\\payment-recommendation.controller.ts", "fixes": 11}, {"file": "src\\controllers\\payment-routing.controller.ts", "fixes": 9}, {"file": "src\\controllers\\payment-verification.controller.ts", "fixes": 8}, {"file": "src\\controllers\\payment.controller.ts", "fixes": 8}, {"file": "src\\controllers\\paymentVerificationController.ts", "fixes": 4}, {"file": "src\\controllers\\push-notification.controller.ts", "fixes": 8}, {"file": "src\\controllers\\shared\\BaseResponseMapper.ts", "fixes": 5}, {"file": "src\\controllers\\sms.controller.ts", "fixes": 7}, {"file": "src\\controllers\\subscription-history.controller.ts", "fixes": 7}, {"file": "src\\controllers\\subscription-plans.controller.ts", "fixes": 4}, {"file": "src\\controllers\\subscription.controller.ts", "fixes": 5}, {"file": "src\\controllers\\system.controller.ts", "fixes": 6}, {"file": "src\\controllers\\telegram-webhook.controller.ts", "fixes": 7}, {"file": "src\\controllers\\transaction-analytics.controller.ts", "fixes": 5}, {"file": "src\\controllers\\transaction.controller.ts", "fixes": 8}, {"file": "src\\controllers\\user.controller.ts", "fixes": 10}, {"file": "src\\controllers\\verification-method.controller.ts", "fixes": 6}, {"file": "src\\controllers\\verification-policy.controller.ts", "fixes": 7}, {"file": "src\\controllers\\verification.controller.ts", "fixes": 9}, {"file": "src\\controllers\\version.controller.ts", "fixes": 7}, {"file": "src\\controllers\\webhook.controller.ts", "fixes": 8}, {"file": "src\\docs\\api-documentation.ts", "fixes": 6}, {"file": "src\\index.ts", "fixes": 8}, {"file": "src\\lib\\database-init.ts", "fixes": 4}, {"file": "src\\lib\\DIContainer.ts", "fixes": 7}, {"file": "src\\lib\\EventBus.ts", "fixes": 11}, {"file": "src\\lib\\logger.ts", "fixes": 9}, {"file": "src\\lib\\mode-specific-db.ts", "fixes": 6}, {"file": "src\\lib\\ModuleRegistry.ts", "fixes": 9}, {"file": "src\\lib\\prisma-client.ts", "fixes": 11}, {"file": "src\\lib\\prisma.ts", "fixes": 10}, {"file": "src\\lib\\redis-client.ts", "fixes": 4}, {"file": "src\\lib\\redis-manager.ts", "fixes": 11}, {"file": "src\\middlewares\\api-response.middleware.ts", "fixes": 5}, {"file": "src\\middlewares\\apiResponseMiddleware.ts", "fixes": 10}, {"file": "src\\middlewares\\audit.middleware.ts", "fixes": 8}, {"file": "src\\middlewares\\auth.middleware.test.ts", "fixes": 6}, {"file": "src\\middlewares\\auth.middleware.ts", "fixes": 11}, {"file": "src\\middlewares\\auth.ts", "fixes": 9}, {"file": "src\\middlewares\\authorization.middleware.ts", "fixes": 10}, {"file": "src\\middlewares\\enhanced-auth.middleware.ts", "fixes": 9}, {"file": "src\\middlewares\\enhanced-error.middleware.ts", "fixes": 10}, {"file": "src\\middlewares\\enhanced-rate-limit.middleware.ts", "fixes": 7}, {"file": "src\\middlewares\\environment.middleware.ts", "fixes": 6}, {"file": "src\\middlewares\\error.middleware.test.ts", "fixes": 5}, {"file": "src\\middlewares\\error.middleware.ts", "fixes": 9}, {"file": "src\\middlewares\\errorHandler.middleware.ts", "fixes": 9}, {"file": "src\\middlewares\\errorHandler.ts", "fixes": 11}, {"file": "src\\middlewares\\feature-flags.middleware.ts", "fixes": 4}, {"file": "src\\middlewares\\merchantAccessMiddleware.ts", "fixes": 7}, {"file": "src\\middlewares\\monitoring.middleware.ts", "fixes": 6}, {"file": "src\\middlewares\\operational-mode.middleware.ts", "fixes": 6}, {"file": "src\\middlewares\\performance-monitor.middleware.ts", "fixes": 10}, {"file": "src\\middlewares\\rate-limit.middleware.ts", "fixes": 6}, {"file": "src\\middlewares\\rbac.middleware.ts", "fixes": 6}, {"file": "src\\middlewares\\request-id.middleware.ts", "fixes": 5}, {"file": "src\\middlewares\\security.middleware.ts", "fixes": 11}, {"file": "src\\middlewares\\validation.middleware.ts", "fixes": 12}, {"file": "src\\middlewares\\versionMiddleware.ts", "fixes": 7}, {"file": "src\\models\\permission.model.ts", "fixes": 2}, {"file": "src\\models\\role.model.ts", "fixes": 2}, {"file": "src\\modules\\auth\\auth.module.ts", "fixes": 1}, {"file": "src\\modules\\example\\example.module.ts", "fixes": 8}, {"file": "src\\modules\\example\\example.test.ts", "fixes": 5}, {"file": "src\\modules\\index.ts", "fixes": 1}, {"file": "src\\modules\\merchant\\merchant.module.ts", "fixes": 10}, {"file": "src\\modules\\merchants\\merchants.module.ts", "fixes": 1}, {"file": "src\\modules\\payment\\payment.module.ts", "fixes": 11}, {"file": "src\\modules\\payments\\payments.module.ts", "fixes": 1}, {"file": "src\\modules\\transactions\\transactions.module.ts", "fixes": 1}, {"file": "src\\modules\\user\\user.module.ts", "fixes": 6}, {"file": "src\\modules\\users\\users.module.ts", "fixes": 1}, {"file": "src\\modules\\webhook\\webhook.module.ts", "fixes": 10}, {"file": "src\\routes\\admin.routes.ts", "fixes": 2}, {"file": "src\\routes\\advanced-report.routes.ts", "fixes": 2}, {"file": "src\\routes\\alert-aggregation.routes.ts", "fixes": 2}, {"file": "src\\routes\\alert-analytics.routes.ts", "fixes": 2}, {"file": "src\\routes\\alert-notification.routes.ts", "fixes": 3}, {"file": "src\\routes\\alert.routes.ts", "fixes": 2}, {"file": "src\\routes\\alerting.routes.ts", "fixes": 7}, {"file": "src\\routes\\analytics.routes.ts", "fixes": 3}, {"file": "src\\routes\\api-analytics.routes.ts", "fixes": 3}, {"file": "src\\routes\\auth.routes.ts", "fixes": 2}, {"file": "src\\routes\\binance-pay-webhook.routes.ts", "fixes": 3}, {"file": "src\\routes\\binance.routes.ts", "fixes": 3}, {"file": "src\\routes\\blockchain-verification.routes.ts", "fixes": 6}, {"file": "src\\routes\\cache.routes.ts", "fixes": 3}, {"file": "src\\routes\\dashboard.routes.ts", "fixes": 4}, {"file": "src\\routes\\email.routes.ts", "fixes": 2}, {"file": "src\\routes\\enhanced-payment.routes.ts", "fixes": 2}, {"file": "src\\routes\\enhanced-risk-engine.routes.ts", "fixes": 3}, {"file": "src\\routes\\enhanced-verification.routes.ts", "fixes": 2}, {"file": "src\\routes\\example.routes.ts", "fixes": 3}, {"file": "src\\routes\\fee-management-test.routes.ts", "fixes": 3}, {"file": "src\\routes\\fee-management.routes.ts", "fixes": 4}, {"file": "src\\routes\\fraud-detection.routes.ts", "fixes": 2}, {"file": "src\\routes\\health.routes.ts", "fixes": 7}, {"file": "src\\routes\\identity-verification.routes.ts", "fixes": 2}, {"file": "src\\routes\\index.ts", "fixes": 4}, {"file": "src\\routes\\location.routes.ts", "fixes": 3}, {"file": "src\\routes\\log.routes.ts", "fixes": 2}, {"file": "src\\routes\\merchant-relationship.routes.ts", "fixes": 3}, {"file": "src\\routes\\merchant-segmentation.routes.ts", "fixes": 3}, {"file": "src\\routes\\merchant-self-service.routes.ts", "fixes": 3}, {"file": "src\\routes\\merchant.routes.ts", "fixes": 3}, {"file": "src\\routes\\monitoring-dashboard.routes.ts", "fixes": 8}, {"file": "src\\routes\\monitoring.routes.ts", "fixes": 3}, {"file": "src\\routes\\multi-factor-auth.routes.ts", "fixes": 3}, {"file": "src\\routes\\notification.routes.ts", "fixes": 2}, {"file": "src\\routes\\operational-mode.routes.ts", "fixes": 2}, {"file": "src\\routes\\payment-method.routes.ts", "fixes": 3}, {"file": "src\\routes\\payment-page.routes.ts", "fixes": 3}, {"file": "src\\routes\\payment-recommendation.routes.ts", "fixes": 2}, {"file": "src\\routes\\payment-routing.routes.ts", "fixes": 2}, {"file": "src\\routes\\payment-verification.routes.ts", "fixes": 3}, {"file": "src\\routes\\payment.routes.ts", "fixes": 2}, {"file": "src\\routes\\paymentVerificationRoutes.ts", "fixes": 2}, {"file": "src\\routes\\performance.routes.ts", "fixes": 6}, {"file": "src\\routes\\push-notification.routes.ts", "fixes": 2}, {"file": "src\\routes\\receipt.routes.ts", "fixes": 3}, {"file": "src\\routes\\sms.routes.ts", "fixes": 2}, {"file": "src\\routes\\subscription.routes.ts", "fixes": 2}, {"file": "src\\routes\\system.routes.ts", "fixes": 2}, {"file": "src\\routes\\telegram.routes.ts", "fixes": 2}, {"file": "src\\routes\\transaction.routes.ts", "fixes": 3}, {"file": "src\\routes\\user.routes.ts", "fixes": 2}, {"file": "src\\routes\\v1\\index.ts", "fixes": 3}, {"file": "src\\routes\\v1\\merchant.routes.ts", "fixes": 2}, {"file": "src\\routes\\v1\\payment-method.routes.ts", "fixes": 2}, {"file": "src\\routes\\verification-method.routes.ts", "fixes": 3}, {"file": "src\\routes\\verification-monitoring.routes.ts", "fixes": 2}, {"file": "src\\routes\\verification-optimization.routes.ts", "fixes": 2}, {"file": "src\\routes\\verification-policy.routes.ts", "fixes": 2}, {"file": "src\\routes\\verification.routes.ts", "fixes": 2}, {"file": "src\\routes\\verify.routes.ts", "fixes": 2}, {"file": "src\\routes\\version.routes.ts", "fixes": 3}, {"file": "src\\routes\\webhook.routes.ts", "fixes": 5}, {"file": "src\\routes\\websocket-monitoring.routes.ts", "fixes": 5}, {"file": "src\\security\\security-audit.ts", "fixes": 8}, {"file": "src\\services\\advanced-report.service.ts", "fixes": 11}, {"file": "src\\services\\alert-aggregation.service.ts", "fixes": 13}, {"file": "src\\services\\alert-analytics.service.ts", "fixes": 9}, {"file": "src\\services\\alert.service.ts", "fixes": 9}, {"file": "src\\services\\analytics\\ApiAnalyticsService.ts", "fixes": 11}, {"file": "src\\services\\analytics\\payment-analytics.service.ts", "fixes": 11}, {"file": "src\\services\\audit.service.ts", "fixes": 3}, {"file": "src\\services\\auth.service.ts", "fixes": 8}, {"file": "src\\services\\base\\BaseService.ts", "fixes": 4}, {"file": "src\\services\\base.service.ts", "fixes": 7}, {"file": "src\\services\\binance-api.service.ts", "fixes": 12}, {"file": "src\\services\\binance-pay.service.ts", "fixes": 8}, {"file": "src\\services\\binance.service.ts", "fixes": 6}, {"file": "src\\services\\binanceApiService.ts", "fixes": 11}, {"file": "src\\services\\blockchain\\binance-api.service.ts", "fixes": 11}, {"file": "src\\services\\blockchain\\blockchain-api.service.ts", "fixes": 10}, {"file": "src\\services\\blockchain-api.service.ts", "fixes": 10}, {"file": "src\\services\\blockchain-verification.service.ts", "fixes": 11}, {"file": "src\\services\\cache\\analytics-cache.service.ts", "fixes": 12}, {"file": "src\\services\\email.service.ts", "fixes": 10}, {"file": "src\\services\\enhanced-risk-engine.service.implementation.ts", "fixes": 12}, {"file": "src\\services\\enhanced-risk-engine.service.ts", "fixes": 10}, {"file": "src\\services\\enhanced-subscription.service.ts", "fixes": 10}, {"file": "src\\services\\example.service.ts", "fixes": 8}, {"file": "src\\services\\fee-management.service.ts", "fixes": 13}, {"file": "src\\services\\fraud-detection\\core\\FraudDetectionService.ts", "fixes": 11}, {"file": "src\\services\\fraud-detection\\core\\FraudDetectionTypes.ts", "fixes": 3}, {"file": "src\\services\\fraud-detection\\detectors\\AmountRiskDetector.ts", "fixes": 13}, {"file": "src\\services\\fraud-detection\\detectors\\VelocityRiskDetector.ts", "fixes": 12}, {"file": "src\\services\\fraud-detection\\rules\\RiskRuleEngine.ts", "fixes": 12}, {"file": "src\\services\\fraud-detection\\__tests__\\FraudDetectionService.test.ts", "fixes": 5}, {"file": "src\\services\\fraud-detection.service.ts", "fixes": 13}, {"file": "src\\services\\identity-verification\\blockchain-identity.service.ts", "fixes": 10}, {"file": "src\\services\\identity-verification\\core\\IdentityVerificationError.ts", "fixes": 1}, {"file": "src\\services\\identity-verification\\core\\IdentityVerificationService.ts", "fixes": 9}, {"file": "src\\services\\identity-verification\\core\\IdentityVerificationTypes.ts", "fixes": 2}, {"file": "src\\services\\identity-verification\\methods\\EthereumSignatureVerification.ts", "fixes": 8}, {"file": "src\\services\\identity-verification\\utils\\BlockchainUtils.ts", "fixes": 9}, {"file": "src\\services\\identity-verification\\__tests__\\IdentityVerificationService.production.test.ts", "fixes": 5}, {"file": "src\\services\\identity-verification\\__tests__\\IdentityVerificationService.test.ts", "fixes": 5}, {"file": "src\\services\\identity-verification.service.ts", "fixes": 11}, {"file": "src\\services\\index.ts", "fixes": 1}, {"file": "src\\services\\log.service.ts", "fixes": 7}, {"file": "src\\services\\merchant-events.service.ts", "fixes": 1}, {"file": "src\\services\\merchant-relationship.service.ts", "fixes": 8}, {"file": "src\\services\\merchant-segmentation.service.ts", "fixes": 11}, {"file": "src\\services\\merchant-self-service.service.ts", "fixes": 8}, {"file": "src\\services\\merchant.service.ts", "fixes": 10}, {"file": "src\\services\\monitoring\\payment-monitoring.service.ts", "fixes": 10}, {"file": "src\\services\\monitoring\\verification-alert.service.ts", "fixes": 10}, {"file": "src\\services\\monitoring\\verification-monitoring.service.ts", "fixes": 10}, {"file": "src\\services\\monitoring.service.ts", "fixes": 8}, {"file": "src\\services\\multi-factor-auth.service.ts", "fixes": 11}, {"file": "src\\services\\notification\\alert-notification.service.ts", "fixes": 6}, {"file": "src\\services\\notification-events.service.ts", "fixes": 4}, {"file": "src\\services\\notification.service.ts", "fixes": 11}, {"file": "src\\services\\optimization\\verification-optimization.service.ts", "fixes": 9}, {"file": "src\\services\\payment\\EnhancedPaymentService.ts", "fixes": 6}, {"file": "src\\services\\payment\\fees\\FeeCalculator.ts", "fixes": 7}, {"file": "src\\services\\payment\\fees\\strategies\\CommonFeeStrategies.ts", "fixes": 10}, {"file": "src\\services\\payment\\gateways\\BinanceGateway.ts", "fixes": 9}, {"file": "src\\services\\payment\\methods\\BinanceTRC20PaymentMethod.ts", "fixes": 9}, {"file": "src\\services\\payment\\routing\\PaymentRouter.ts", "fixes": 9}, {"file": "src\\services\\payment\\routing\\rules\\CommonRoutingRules.ts", "fixes": 9}, {"file": "src\\services\\payment-method.service.ts", "fixes": 8}, {"file": "src\\services\\payment-page.service.ts", "fixes": 9}, {"file": "src\\services\\payment-recommendation.service.ts", "fixes": 10}, {"file": "src\\services\\payment-routing.service.ts", "fixes": 10}, {"file": "src\\services\\payment-verification.service.ts", "fixes": 10}, {"file": "src\\services\\payment.service.ts", "fixes": 10}, {"file": "src\\services\\paymentVerificationService.ts", "fixes": 7}, {"file": "src\\services\\push-notification.service.ts", "fixes": 9}, {"file": "src\\services\\rbac\\RBACInitializer.ts", "fixes": 7}, {"file": "src\\services\\rbac.service.ts", "fixes": 10}, {"file": "src\\services\\report-monitoring.service.ts", "fixes": 10}, {"file": "src\\services\\report-optimization.service.ts", "fixes": 11}, {"file": "src\\services\\reporting\\core\\ReportService.ts", "fixes": 6}, {"file": "src\\services\\reporting\\core\\ReportTypes.ts", "fixes": 2}, {"file": "src\\services\\reporting\\export\\CSVExporter.ts", "fixes": 10}, {"file": "src\\services\\reporting\\generators\\TransactionReportGenerator.ts", "fixes": 10}, {"file": "src\\services\\reporting\\scheduling\\ReportScheduler.ts", "fixes": 6}, {"file": "src\\services\\sms.service.ts", "fixes": 7}, {"file": "src\\services\\subscription.service.ts", "fixes": 9}, {"file": "src\\services\\system\\OperationalModeService.ts", "fixes": 9}, {"file": "src\\services\\system\\SystemInitializer.ts", "fixes": 8}, {"file": "src\\services\\system.service.ts", "fixes": 6}, {"file": "src\\services\\telegram.service.ts", "fixes": 7}, {"file": "src\\services\\transaction.service.ts", "fixes": 9}, {"file": "src\\services\\two-factor-auth.service.ts", "fixes": 7}, {"file": "src\\services\\user.service.ts", "fixes": 9}, {"file": "src\\services\\validation\\paymentMethodValidator.ts", "fixes": 9}, {"file": "src\\services\\verification\\binance-verification.service.ts", "fixes": 8}, {"file": "src\\services\\verification\\blockchain-verification.service.ts", "fixes": 8}, {"file": "src\\services\\verification\\policy\\VerificationPolicy.ts", "fixes": 8}, {"file": "src\\services\\verification\\policy\\VerificationPolicyManager.ts", "fixes": 5}, {"file": "src\\services\\verification\\processors\\LoggingPreProcessor.ts", "fixes": 3}, {"file": "src\\services\\verification\\processors\\NotificationPostProcessor.ts", "fixes": 3}, {"file": "src\\services\\verification\\processors\\VerificationPostProcessor.ts", "fixes": 2}, {"file": "src\\services\\verification\\processors\\VerificationPreProcessor.ts", "fixes": 2}, {"file": "src\\services\\verification\\strategies\\BinanceTRC20VerificationStrategy.ts", "fixes": 11}, {"file": "src\\services\\verification\\unified-verification.service.ts", "fixes": 9}, {"file": "src\\services\\verification\\VerificationChain.ts", "fixes": 11}, {"file": "src\\services\\verification\\VerificationService.ts", "fixes": 7}, {"file": "src\\services\\verification-method.service.ts", "fixes": 11}, {"file": "src\\services\\verification.service.ts", "fixes": 7}, {"file": "src\\services\\webhook.service.ts", "fixes": 10}, {"file": "src\\services\\websocket\\verification-realtime.service.ts", "fixes": 7}, {"file": "src\\services\\websocket\\verificationWebSocketService.ts", "fixes": 7}, {"file": "src\\shared\\modules\\controllers\\BaseController.ts", "fixes": 5}, {"file": "src\\shared\\modules\\controllers\\CrudController.ts", "fixes": 1}, {"file": "src\\shared\\modules\\services\\BaseService.ts", "fixes": 2}, {"file": "src\\shared\\modules\\types\\common.ts", "fixes": 2}, {"file": "src\\shared\\modules\\utils\\asyncHandler.ts", "fixes": 4}, {"file": "src\\shared\\modules\\utils\\responseUtils.ts", "fixes": 5}, {"file": "src\\tests\\advanced-report.controller.test.ts", "fixes": 4}, {"file": "src\\tests\\advanced-report.service.test.ts", "fixes": 4}, {"file": "src\\tests\\advanced-report.test.ts", "fixes": 4}, {"file": "src\\tests\\dashboard.controller.test.ts", "fixes": 4}, {"file": "src\\tests\\e2e\\advanced-reporting.e2e.test.ts", "fixes": 5}, {"file": "src\\tests\\environment-separation.test.ts", "fixes": 4}, {"file": "src\\tests\\helpers\\RouteTestHelper.ts", "fixes": 5}, {"file": "src\\tests\\integration\\advanced-reporting-integration.test.ts", "fixes": 8}, {"file": "src\\tests\\integration\\module-interactions.test.ts", "fixes": 7}, {"file": "src\\tests\\integration\\shared-modules.test.ts", "fixes": 11}, {"file": "src\\tests\\load-test.ts", "fixes": 12}, {"file": "src\\tests\\payment\\verification.test.ts", "fixes": 4}, {"file": "src\\tests\\payment-methods\\payment-method.controller.test.ts", "fixes": 4}, {"file": "src\\tests\\performance\\performance.test.ts", "fixes": 8}, {"file": "src\\tests\\production-mode.test.ts", "fixes": 4}, {"file": "src\\tests\\route-tests.ts", "fixes": 3}, {"file": "src\\tests\\run-tests.ts", "fixes": 8}, {"file": "src\\tests\\runners\\RouteTestRunner.ts", "fixes": 4}, {"file": "src\\tests\\security-test.ts", "fixes": 9}, {"file": "src\\tests\\services\\cryptoService.test.ts", "fixes": 4}, {"file": "src\\tests\\services\\identity-verification.service.test.ts", "fixes": 4}, {"file": "src\\tests\\services\\verification-optimization.service.test.ts", "fixes": 4}, {"file": "src\\tests\\services\\verification-realtime.service.test.ts", "fixes": 4}, {"file": "src\\tests\\setup.ts", "fixes": 7}, {"file": "src\\tests\\shared\\modules\\controllers\\BaseController.test.ts", "fixes": 6}, {"file": "src\\tests\\shared\\modules\\controllers\\CrudController.test.ts", "fixes": 5}, {"file": "src\\tests\\shared\\modules\\services\\BaseService.test.ts", "fixes": 5}, {"file": "src\\tests\\shared\\modules\\utils\\asyncHandler.test.ts", "fixes": 6}, {"file": "src\\tests\\shared\\modules\\utils\\responseUtils.test.ts", "fixes": 5}, {"file": "src\\tests\\suites\\RouteTestSuite.ts", "fixes": 5}, {"file": "src\\tests\\unit\\alert-types.test.ts", "fixes": 4}, {"file": "src\\tests\\unit\\auth.service.test.ts", "fixes": 4}, {"file": "src\\tests\\unit\\database.config.test.ts", "fixes": 4}, {"file": "src\\tests\\utils\\assertions\\CustomAssertions.ts", "fixes": 13}, {"file": "src\\tests\\utils\\core\\TestTypes.ts", "fixes": 4}, {"file": "src\\tests\\utils\\factories\\MockFactories.ts", "fixes": 12}, {"file": "src\\tests\\utils\\generators\\DataGenerators.ts", "fixes": 10}, {"file": "src\\tests\\utils\\index.ts", "fixes": 5}, {"file": "src\\tests\\utils\\runners\\TestRunners.ts", "fixes": 10}, {"file": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "fixes": 9}, {"file": "src\\tests\\utils\\TestUtility.ts", "fixes": 11}, {"file": "src\\tests\\verification\\binance-verification.service.test.ts", "fixes": 4}, {"file": "src\\tests\\verification\\blockchain-verification.service.test.ts", "fixes": 4}, {"file": "src\\tests\\verification.test.ts", "fixes": 4}, {"file": "src\\tests\\vitest-simple.test.ts", "fixes": 4}, {"file": "src\\types\\admin.ts", "fixes": 2}, {"file": "src\\types\\alert.types.ts", "fixes": 2}, {"file": "src\\types\\analytics.ts", "fixes": 4}, {"file": "src\\types\\binanceTypes.ts", "fixes": 1}, {"file": "src\\types\\common.ts", "fixes": 5}, {"file": "src\\types\\database.ts", "fixes": 2}, {"file": "src\\types\\express.ts", "fixes": 3}, {"file": "src\\types\\index.ts", "fixes": 2}, {"file": "src\\types\\merchant.ts", "fixes": 2}, {"file": "src\\types\\payment.ts", "fixes": 2}, {"file": "src\\types\\paymentMethodTypes.ts", "fixes": 2}, {"file": "src\\types\\prisma.ts", "fixes": 3}, {"file": "src\\types\\services.ts", "fixes": 2}, {"file": "src\\types\\user.ts", "fixes": 2}, {"file": "src\\types\\verification.ts", "fixes": 3}, {"file": "src\\utils\\alerting.test.ts", "fixes": 5}, {"file": "src\\utils\\alerting.ts", "fixes": 7}, {"file": "src\\utils\\app-error.ts", "fixes": 4}, {"file": "src\\utils\\appError.ts", "fixes": 1}, {"file": "src\\utils\\array.ts", "fixes": 7}, {"file": "src\\utils\\asyncHandler.ts", "fixes": 7}, {"file": "src\\utils\\cache-manager.ts", "fixes": 3}, {"file": "src\\utils\\cache.test.ts", "fixes": 5}, {"file": "src\\utils\\cache.ts", "fixes": 13}, {"file": "src\\utils\\common.ts", "fixes": 11}, {"file": "src\\utils\\controller-utils.ts", "fixes": 10}, {"file": "src\\utils\\controller.utils.ts", "fixes": 9}, {"file": "src\\utils\\controllerUtils.ts", "fixes": 8}, {"file": "src\\utils\\cookie.ts", "fixes": 7}, {"file": "src\\utils\\CryptoUtils.ts", "fixes": 9}, {"file": "src\\utils\\csrf.ts", "fixes": 9}, {"file": "src\\utils\\database-initializer.ts", "fixes": 9}, {"file": "src\\utils\\database-verifier.ts", "fixes": 8}, {"file": "src\\utils\\date.ts", "fixes": 3}, {"file": "src\\utils\\db-optimization.test.ts", "fixes": 6}, {"file": "src\\utils\\db-optimization.ts", "fixes": 6}, {"file": "src\\utils\\domain.ts", "fixes": 4}, {"file": "src\\utils\\encryption.ts", "fixes": 6}, {"file": "src\\utils\\environment-validator.ts", "fixes": 4}, {"file": "src\\utils\\error-handler.ts", "fixes": 8}, {"file": "src\\utils\\errorHandling.ts", "fixes": 1}, {"file": "src\\utils\\errors\\AppError.ts", "fixes": 6}, {"file": "src\\utils\\errors\\ErrorFactory.ts", "fixes": 6}, {"file": "src\\utils\\errors\\ServiceError.ts", "fixes": 2}, {"file": "src\\utils\\feature-flags.ts", "fixes": 4}, {"file": "src\\utils\\formatting.ts", "fixes": 5}, {"file": "src\\utils\\health-monitor.ts", "fixes": 11}, {"file": "src\\utils\\jwt.ts", "fixes": 9}, {"file": "src\\utils\\jwt.utils.test.ts", "fixes": 6}, {"file": "src\\utils\\jwt.utils.ts", "fixes": 9}, {"file": "src\\utils\\log-rotation.ts", "fixes": 7}, {"file": "src\\utils\\memory-store.ts", "fixes": 9}, {"file": "src\\utils\\migration-manager.ts", "fixes": 8}, {"file": "src\\utils\\monitoring.ts", "fixes": 10}, {"file": "src\\utils\\object.ts", "fixes": 8}, {"file": "src\\utils\\repositoryUtils.ts", "fixes": 10}, {"file": "src\\utils\\response\\ResponseFactory.ts", "fixes": 6}, {"file": "src\\utils\\response-formatter.ts", "fixes": 7}, {"file": "src\\utils\\secrets-manager.ts", "fixes": 8}, {"file": "src\\utils\\service-config.ts", "fixes": 11}, {"file": "src\\utils\\storage-path.ts", "fixes": 4}, {"file": "src\\utils\\string.ts", "fixes": 10}, {"file": "src\\utils\\test-connection.ts", "fixes": 5}, {"file": "src\\utils\\test-db-connection.ts", "fixes": 5}, {"file": "src\\utils\\types.ts", "fixes": 2}, {"file": "src\\utils\\UtilityRegistry.ts", "fixes": 1}, {"file": "src\\utils\\validation\\RequestValidator.ts", "fixes": 9}, {"file": "src\\utils\\validation.ts", "fixes": 11}, {"file": "src\\utils\\verification-error-handler.ts", "fixes": 10}, {"file": "src\\utils\\verification-utils.ts", "fixes": 10}, {"file": "src\\utils\\websocket-monitor.ts", "fixes": 7}], "errors": []}