// Database setup script
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 Setting up the AmazingPay database...');

// Function to execute a command and log the output
function runCommand(command) {
  console.log(`\n🔄 Running command: ${command}`);
  try {
    const output = execSync(command, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`❌ Command failed: ${error.message}`);
    return false;
  }
}

// Main function to set up the database
async function setupDatabase() {
  try {
    // Check if Prisma is installed
    console.log('🔍 Checking if <PERSON>rism<PERSON> is installed...');
    try {
      execSync('npx prisma --version', { stdio: 'pipe' });
      console.log('✅ Prisma is installed.');
    } catch (error) {
      console.log('⚠️ Prisma is not installed. Installing Prisma...');
      if (!runCommand('npm install prisma --save-dev')) {
        throw new Error('Failed to install Prisma.');
      }
      console.log('✅ Prisma installed successfully.');
    }

    // Generate Prisma client
    console.log('\n🔧 Generating Prisma client...');
    if (!runCommand('npx prisma generate')) {
      throw new Error('Failed to generate Prisma client.');
    }
    console.log('✅ Prisma client generated successfully.');

    // Run database migrations
    console.log('\n🔧 Running database migrations...');
    if (!runCommand('npx prisma migrate dev --name init')) {
      throw new Error('Failed to run database migrations.');
    }
    console.log('✅ Database migrations completed successfully.');

    // Seed the database
    console.log('\n🔧 Seeding the database...');
    if (!runCommand('npx prisma db seed')) {
      console.log('⚠️ Database seeding failed. This might be because the seed command is not configured.');
      console.log('⚠️ You can add a seed script to your package.json:');
      console.log('⚠️ "prisma": { "seed": "ts-node prisma/seed.ts" }');
    } else {
      console.log('✅ Database seeded successfully.');
    }

    // Open Prisma Studio to view the database
    console.log('\n🔧 Would you like to open Prisma Studio to view the database?');
    console.log('🔧 Run this command: npx prisma studio');

    console.log('\n✅ Database setup completed successfully!');
    console.log('🎉 Your AmazingPay database is now ready to use.');
    
  } catch (error) {
    console.error(`\n❌ Database setup failed: ${error.message}`);
    console.log('\n⚠️ Troubleshooting tips:');
    console.log('1. Make sure PostgreSQL is running');
    console.log('2. Check your database credentials in the .env file');
    console.log('3. Make sure you have the necessary permissions to create databases');
    console.log('4. Try running the commands manually:');
    console.log('   - npx prisma generate');
    console.log('   - npx prisma migrate dev --name init');
    console.log('   - npx prisma db seed');
  }
}

// Run the setup
setupDatabase().catch(console.error);
