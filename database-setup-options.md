# 🗄️ DATABASE SETUP OPTIONS

## 🔴 **OPTION 1: PostgreSQL Command Line (Recommended)**

### **If PostgreSQL is installed:**
```bash
# Method 1: Direct psql command
psql -U postgres -d amazingpay -f setup-secure-database.sql

# Method 2: Interactive psql
psql -U postgres -d amazingpay
# Then copy-paste the SQL commands from setup-secure-database.sql
```

### **If psql is not in PATH:**
```bash
# Windows - Find PostgreSQL installation
# Usually in: C:\Program Files\PostgreSQL\[version]\bin\psql.exe

# Add to PATH or use full path:
"C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -d amazingpay -f setup-secure-database.sql
```

---

## 🔴 **OPTION 2: pgAdmin GUI (Easy)**

### **Steps:**
1. **Open pgAdmin** (PostgreSQL GUI tool)
2. **Connect to your PostgreSQL server**
3. **Right-click on 'amazingpay' database** → Query Tool
4. **Copy and paste this SQL:**

```sql
-- Create new secure application user
CREATE USER amazingpay_app WITH PASSWORD 'AzP4y_S3cur3_2024_Db_P4ssw0rd';

-- Grant necessary privileges
GRANT ALL PRIVILEGES ON DATABASE amazingpay TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO amazingpay_app;
GRANT USAGE ON SCHEMA public TO amazingpay_app;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO amazingpay_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO amazingpay_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO amazingpay_app;

-- Verify users
SELECT usename FROM pg_user;

-- Show granted privileges
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_schema='public' AND grantee='amazingpay_app';
```

5. **Click Execute (F5)**

---

## 🔴 **OPTION 3: Application-Level Setup (Automatic)**

### **If you can't access PostgreSQL directly, I'll create a Node.js script:**

```javascript
// This will be created as setup-database.js
const { Pool } = require('pg');

async function setupDatabase() {
  const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'amazingpay',
    password: 'your_postgres_password', // Update this
    port: 5432,
  });

  try {
    // Create user and grant privileges
    await pool.query(`CREATE USER amazingpay_app WITH PASSWORD 'AzP4y_S3cur3_2024_Db_P4ssw0rd'`);
    await pool.query(`GRANT ALL PRIVILEGES ON DATABASE amazingpay TO amazingpay_app`);
    console.log('✅ Database setup completed successfully!');
  } catch (error) {
    console.log('Database setup result:', error.message);
  } finally {
    await pool.end();
  }
}

setupDatabase();
```

---

## 🔴 **OPTION 4: Docker PostgreSQL**

### **If using Docker:**
```bash
# Connect to PostgreSQL container
docker exec -it your_postgres_container psql -U postgres -d amazingpay

# Or run SQL file directly
docker exec -i your_postgres_container psql -U postgres -d amazingpay < setup-secure-database.sql
```

---

## 🔴 **OPTION 5: Cloud Database (AWS RDS, Google Cloud SQL, etc.)**

### **For cloud databases:**
1. **Use your cloud provider's console**
2. **Connect via their web interface**
3. **Execute the SQL commands from setup-secure-database.sql**

---

## ✅ **VERIFICATION STEPS**

### **After running any option above:**

1. **Test new user connection:**
```bash
# Test connection with new user
psql -U amazingpay_app -d amazingpay -h localhost
# Password: AzP4y_S3cur3_2024_Db_P4ssw0rd
```

2. **Verify in your application:**
```bash
# Your .env file should work with:
DATABASE_URL=postgresql://amazingpay_app:AzP4y_S3cur3_2024_Db_P4ssw0rd@localhost:5432/amazingpay
```

3. **Test application startup:**
```bash
npm start
# Should connect successfully with new credentials
```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **"User already exists"**
```sql
-- Drop and recreate if needed
DROP USER IF EXISTS amazingpay_app;
CREATE USER amazingpay_app WITH PASSWORD 'AzP4y_S3cur3_2024_Db_P4ssw0rd';
```

#### **"Permission denied"**
```sql
-- Make sure you're connected as postgres user
-- Or use a superuser account
```

#### **"Database doesn't exist"**
```sql
-- Create database first
CREATE DATABASE amazingpay;
```

#### **"Connection refused"**
- Check if PostgreSQL service is running
- Verify connection details (host, port, database name)
- Check firewall settings

---

## 🎯 **CHOOSE YOUR METHOD**

1. **Technical users**: Use Option 1 (Command Line)
2. **GUI preference**: Use Option 2 (pgAdmin)
3. **No direct DB access**: Use Option 3 (Node.js script)
4. **Docker setup**: Use Option 4 (Docker commands)
5. **Cloud database**: Use Option 5 (Cloud console)

**Pick the method that works best for your setup and proceed!**
