#!/usr/bin/env node

/**
 * AUDIT READINESS ASSESSMENT
 * Comprehensive evaluation for external audits and ISO compliance
 */

const fs = require('fs');
const path = require('path');

console.log('🏛️ AUDIT READINESS ASSESSMENT');
console.log('==============================');
console.log('🔍 Evaluating readiness for external audits and ISO compliance');
console.log('');

const assessmentResults = {
    securityControls: { score: 0, maxScore: 20, items: [] },
    documentation: { score: 0, maxScore: 15, items: [] },
    technicalCompliance: { score: 0, maxScore: 25, items: [] },
    organizationalReadiness: { score: 0, maxScore: 20, items: [] },
    riskManagement: { score: 0, maxScore: 20, items: [] }
};

function assessSecurityControls() {
    console.log('🔒 1. Security Controls Assessment');
    console.log('==================================');
    
    const controls = [
        { name: 'Environment Variable Protection', file: 'scripts/security-audit.js', weight: 3 },
        { name: 'Access Control Implementation', file: 'src/middlewares/auth.middleware.ts', weight: 3 },
        { name: 'Encryption at Rest', file: 'src/config/database.config.ts', weight: 2 },
        { name: 'Secure Configuration Management', file: 'src/utils/secure-config.ts', weight: 3 },
        { name: 'Input Validation', file: 'src/middlewares/validation.middleware.ts', weight: 2 },
        { name: 'Error Handling', file: 'src/middlewares/error.middleware.ts', weight: 2 },
        { name: 'Logging and Monitoring', file: 'src/lib/logger.ts', weight: 2 },
        { name: 'Session Management', file: 'src/config/auth.ts', weight: 3 }
    ];
    
    let totalScore = 0;
    
    for (const control of controls) {
        const exists = fs.existsSync(control.file);
        const score = exists ? control.weight : 0;
        totalScore += score;
        
        assessmentResults.securityControls.items.push({
            name: control.name,
            implemented: exists,
            score: score,
            maxScore: control.weight,
            file: control.file
        });
        
        if (exists) {
            console.log(`✅ ${control.name}: Implemented (${score}/${control.weight})`);
        } else {
            console.log(`❌ ${control.name}: Missing (${score}/${control.weight})`);
        }
    }
    
    assessmentResults.securityControls.score = totalScore;
    console.log(`📊 Security Controls Score: ${totalScore}/${assessmentResults.securityControls.maxScore}`);
}

function assessDocumentation() {
    console.log('\n📋 2. Documentation Assessment');
    console.log('==============================');
    
    const requiredDocs = [
        { name: 'Security Policy', file: 'docs/security-policy.md', weight: 3 },
        { name: 'API Documentation', file: 'docs/api-documentation.md', weight: 2 },
        { name: 'Deployment Guide', file: 'docs/deployment-guide.md', weight: 2 },
        { name: 'Incident Response Plan', file: 'docs/incident-response.md', weight: 3 },
        { name: 'Data Flow Diagrams', file: 'docs/data-flow-diagrams.md', weight: 2 },
        { name: 'Risk Assessment', file: 'docs/risk-assessment.md', weight: 3 }
    ];
    
    let totalScore = 0;
    
    for (const doc of requiredDocs) {
        const exists = fs.existsSync(doc.file);
        const score = exists ? doc.weight : 0;
        totalScore += score;
        
        assessmentResults.documentation.items.push({
            name: doc.name,
            exists: exists,
            score: score,
            maxScore: doc.weight,
            file: doc.file
        });
        
        if (exists) {
            console.log(`✅ ${doc.name}: Available (${score}/${doc.weight})`);
        } else {
            console.log(`❌ ${doc.name}: Missing (${score}/${doc.weight})`);
        }
    }
    
    assessmentResults.documentation.score = totalScore;
    console.log(`📊 Documentation Score: ${totalScore}/${assessmentResults.documentation.maxScore}`);
}

function assessTechnicalCompliance() {
    console.log('\n🔧 3. Technical Compliance Assessment');
    console.log('=====================================');
    
    const technicalChecks = [
        { name: 'TypeScript Zero Errors', check: () => checkTypeScriptErrors(), weight: 5 },
        { name: 'Security Audit Passed', check: () => checkSecurityAudit(), weight: 5 },
        { name: 'Environment Configuration', check: () => checkEnvironmentConfig(), weight: 3 },
        { name: 'Database Security', check: () => checkDatabaseSecurity(), weight: 4 },
        { name: 'API Security', check: () => checkApiSecurity(), weight: 3 },
        { name: 'Code Quality Standards', check: () => checkCodeQuality(), weight: 3 },
        { name: 'Dependency Security', check: () => checkDependencySecurity(), weight: 2 }
    ];
    
    let totalScore = 0;
    
    for (const check of technicalChecks) {
        const passed = check.check();
        const score = passed ? check.weight : 0;
        totalScore += score;
        
        assessmentResults.technicalCompliance.items.push({
            name: check.name,
            passed: passed,
            score: score,
            maxScore: check.weight
        });
        
        if (passed) {
            console.log(`✅ ${check.name}: Compliant (${score}/${check.weight})`);
        } else {
            console.log(`❌ ${check.name}: Non-compliant (${score}/${check.weight})`);
        }
    }
    
    assessmentResults.technicalCompliance.score = totalScore;
    console.log(`📊 Technical Compliance Score: ${totalScore}/${assessmentResults.technicalCompliance.maxScore}`);
}

function assessOrganizationalReadiness() {
    console.log('\n🏢 4. Organizational Readiness Assessment');
    console.log('=========================================');
    
    const orgChecks = [
        { name: 'Security Policies Defined', file: 'docs/security-policy.md', weight: 4 },
        { name: 'Incident Response Procedures', file: 'docs/incident-response.md', weight: 4 },
        { name: 'Change Management Process', file: 'docs/change-management.md', weight: 3 },
        { name: 'Access Control Procedures', file: 'docs/access-control.md', weight: 3 },
        { name: 'Training Documentation', file: 'docs/security-training.md', weight: 3 },
        { name: 'Vendor Management', file: 'docs/vendor-management.md', weight: 3 }
    ];
    
    let totalScore = 0;
    
    for (const check of orgChecks) {
        const exists = fs.existsSync(check.file);
        const score = exists ? check.weight : 0;
        totalScore += score;
        
        assessmentResults.organizationalReadiness.items.push({
            name: check.name,
            implemented: exists,
            score: score,
            maxScore: check.weight,
            file: check.file
        });
        
        if (exists) {
            console.log(`✅ ${check.name}: Implemented (${score}/${check.weight})`);
        } else {
            console.log(`❌ ${check.name}: Missing (${score}/${check.weight})`);
        }
    }
    
    assessmentResults.organizationalReadiness.score = totalScore;
    console.log(`📊 Organizational Readiness Score: ${totalScore}/${assessmentResults.organizationalReadiness.maxScore}`);
}

function assessRiskManagement() {
    console.log('\n⚠️ 5. Risk Management Assessment');
    console.log('=================================');
    
    const riskChecks = [
        { name: 'Risk Assessment Completed', file: 'docs/risk-assessment.md', weight: 5 },
        { name: 'Business Continuity Plan', file: 'docs/business-continuity.md', weight: 4 },
        { name: 'Disaster Recovery Plan', file: 'docs/disaster-recovery.md', weight: 4 },
        { name: 'Vulnerability Management', file: 'docs/vulnerability-management.md', weight: 3 },
        { name: 'Third-party Risk Assessment', file: 'docs/third-party-risk.md', weight: 2 },
        { name: 'Compliance Monitoring', file: 'docs/compliance-monitoring.md', weight: 2 }
    ];
    
    let totalScore = 0;
    
    for (const check of riskChecks) {
        const exists = fs.existsSync(check.file);
        const score = exists ? check.weight : 0;
        totalScore += score;
        
        assessmentResults.riskManagement.items.push({
            name: check.name,
            implemented: exists,
            score: score,
            maxScore: check.weight,
            file: check.file
        });
        
        if (exists) {
            console.log(`✅ ${check.name}: Implemented (${score}/${check.weight})`);
        } else {
            console.log(`❌ ${check.name}: Missing (${score}/${check.weight})`);
        }
    }
    
    assessmentResults.riskManagement.score = totalScore;
    console.log(`📊 Risk Management Score: ${totalScore}/${assessmentResults.riskManagement.maxScore}`);
}

// Helper functions for technical checks
function checkTypeScriptErrors() {
    try {
        const { execSync } = require('child_process');
        execSync('npx tsc --project tsconfig.ultimate-zero.json --noEmit --skipLibCheck', { 
            stdio: 'pipe',
            timeout: 30000
        });
        return true;
    } catch (error) {
        return false;
    }
}

function checkSecurityAudit() {
    return fs.existsSync('security-audit-report.json');
}

function checkEnvironmentConfig() {
    return fs.existsSync('.env.example') && fs.existsSync('src/utils/secure-config.ts');
}

function checkDatabaseSecurity() {
    return fs.existsSync('src/config/database.config.ts');
}

function checkApiSecurity() {
    return fs.existsSync('src/middlewares/auth.middleware.ts');
}

function checkCodeQuality() {
    return fs.existsSync('validation-report-clean.json');
}

function checkDependencySecurity() {
    try {
        const { execSync } = require('child_process');
        execSync('npm audit --audit-level=high', { stdio: 'pipe', timeout: 30000 });
        return true;
    } catch (error) {
        return false;
    }
}

function generateAuditReadinessReport() {
    console.log('\n🏛️ AUDIT READINESS REPORT');
    console.log('==========================');
    
    const totalScore = assessmentResults.securityControls.score +
                      assessmentResults.documentation.score +
                      assessmentResults.technicalCompliance.score +
                      assessmentResults.organizationalReadiness.score +
                      assessmentResults.riskManagement.score;
    
    const maxTotalScore = assessmentResults.securityControls.maxScore +
                         assessmentResults.documentation.maxScore +
                         assessmentResults.technicalCompliance.maxScore +
                         assessmentResults.organizationalReadiness.maxScore +
                         assessmentResults.riskManagement.maxScore;
    
    const readinessPercentage = Math.round((totalScore / maxTotalScore) * 100);
    
    console.log(`\n🎯 OVERALL AUDIT READINESS: ${readinessPercentage}%`);
    console.log('================================================');
    console.log(`📊 Total Score: ${totalScore}/${maxTotalScore}`);
    console.log('');
    console.log('📋 CATEGORY BREAKDOWN:');
    console.log(`   🔒 Security Controls: ${assessmentResults.securityControls.score}/${assessmentResults.securityControls.maxScore} (${Math.round((assessmentResults.securityControls.score/assessmentResults.securityControls.maxScore)*100)}%)`);
    console.log(`   📋 Documentation: ${assessmentResults.documentation.score}/${assessmentResults.documentation.maxScore} (${Math.round((assessmentResults.documentation.score/assessmentResults.documentation.maxScore)*100)}%)`);
    console.log(`   🔧 Technical Compliance: ${assessmentResults.technicalCompliance.score}/${assessmentResults.technicalCompliance.maxScore} (${Math.round((assessmentResults.technicalCompliance.score/assessmentResults.technicalCompliance.maxScore)*100)}%)`);
    console.log(`   🏢 Organizational Readiness: ${assessmentResults.organizationalReadiness.score}/${assessmentResults.organizationalReadiness.maxScore} (${Math.round((assessmentResults.organizationalReadiness.score/assessmentResults.organizationalReadiness.maxScore)*100)}%)`);
    console.log(`   ⚠️ Risk Management: ${assessmentResults.riskManagement.score}/${assessmentResults.riskManagement.maxScore} (${Math.round((assessmentResults.riskManagement.score/assessmentResults.riskManagement.maxScore)*100)}%)`);
    
    // Audit readiness recommendations
    if (readinessPercentage >= 90) {
        console.log('\n🏆 EXCELLENT AUDIT READINESS!');
        console.log('==============================');
        console.log('✅ Ready for external security audits');
        console.log('✅ Ready for ISO 27001 certification');
        console.log('✅ Meets enterprise security standards');
        console.log('🚀 RECOMMENDATION: Proceed with external audits');
    } else if (readinessPercentage >= 75) {
        console.log('\n✅ GOOD AUDIT READINESS');
        console.log('========================');
        console.log('✅ Ready for external security audits with minor improvements');
        console.log('⚠️ Some documentation gaps for ISO 27001');
        console.log('🔧 RECOMMENDATION: Address gaps before ISO certification');
    } else if (readinessPercentage >= 60) {
        console.log('\n⚠️ MODERATE AUDIT READINESS');
        console.log('============================');
        console.log('⚠️ Significant preparation needed');
        console.log('❌ Not ready for comprehensive audits');
        console.log('🔧 RECOMMENDATION: Complete preparation before external audits');
    } else {
        console.log('\n❌ LOW AUDIT READINESS');
        console.log('=======================');
        console.log('❌ Extensive preparation required');
        console.log('❌ Not ready for external audits');
        console.log('🔧 RECOMMENDATION: Focus on foundational security and documentation');
    }
    
    // Save detailed report
    const reportPath = 'audit-readiness-report.json';
    fs.writeFileSync(reportPath, JSON.stringify({
        ...assessmentResults,
        summary: {
            totalScore,
            maxTotalScore,
            readinessPercentage,
            timestamp: new Date().toISOString()
        }
    }, null, 2), 'utf8');
    
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    
    return readinessPercentage >= 75;
}

async function main() {
    console.log('🚀 Starting audit readiness assessment...\n');
    
    // Run all assessments
    assessSecurityControls();
    assessDocumentation();
    assessTechnicalCompliance();
    assessOrganizationalReadiness();
    assessRiskManagement();
    
    // Generate final report
    const isReady = generateAuditReadinessReport();
    
    console.log('\n🏛️ AUDIT READINESS ASSESSMENT COMPLETE!');
    console.log('========================================');
    
    if (isReady) {
        console.log('🏆 READY FOR EXTERNAL AUDITS AND ISO COMPLIANCE!');
        process.exit(0);
    } else {
        console.log('⚠️ PREPARATION NEEDED BEFORE EXTERNAL AUDITS');
        process.exit(1);
    }
}

main().catch(console.error);
