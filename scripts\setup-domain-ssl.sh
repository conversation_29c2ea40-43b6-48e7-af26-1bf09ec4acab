#!/bin/bash

# AmazingPay Flow - Domain & SSL Setup Script
# This script helps configure domain and SSL for production deployment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🌐 AmazingPay Flow - Domain & SSL Setup${NC}"
    echo -e "${BLUE}======================================\n${NC}"
}

print_status() {
    echo -e "${CYAN}📦 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to configure domain settings
configure_domain() {
    print_status "Configuring domain settings..."
    
    echo -n "Enter your domain name (e.g., amazingpay.com): "
    read DOMAIN_NAME
    
    echo -n "Enter your API subdomain (e.g., api.amazingpay.com): "
    read API_DOMAIN
    
    if [ -z "$DOMAIN_NAME" ]; then
        print_error "Domain name is required"
        return 1
    fi
    
    if [ -z "$API_DOMAIN" ]; then
        API_DOMAIN="api.$DOMAIN_NAME"
    fi
    
    print_info "Domain: $DOMAIN_NAME"
    print_info "API Domain: $API_DOMAIN"
    
    # Update .env.production with domain settings
    if [ -f ".env.production" ]; then
        cp .env.production .env.production.domain.backup
        
        sed -i.bak "s|FRONTEND_URL=.*|FRONTEND_URL=https://$DOMAIN_NAME|g" .env.production
        sed -i.bak "s|API_URL=.*|API_URL=https://$API_DOMAIN/api|g" .env.production
        sed -i.bak "s|DOMAIN=.*|DOMAIN=$DOMAIN_NAME|g" .env.production
        sed -i.bak "s|CORS_ORIGIN=.*|CORS_ORIGIN=https://$DOMAIN_NAME|g" .env.production
        sed -i.bak "s|JWT_ISSUER=.*|JWT_ISSUER=$DOMAIN_NAME|g" .env.production
        sed -i.bak "s|JWT_AUDIENCE=.*|JWT_AUDIENCE=$DOMAIN_NAME|g" .env.production
        
        print_success "Domain configuration updated in .env.production"
    else
        print_error ".env.production file not found"
        return 1
    fi
}

# Function to create Nginx configuration
create_nginx_config() {
    print_status "Creating Nginx configuration..."
    
    mkdir -p nginx/ssl
    
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=auth:10m rate=1r/s;
    
    # Upstream backend
    upstream amazingpay_backend {
        server amazingpay-app:3002;
        keepalive 32;
    }
    
    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name $DOMAIN_NAME $API_DOMAIN;
        return 301 https://\$server_name\$request_uri;
    }
    
    # Main application server
    server {
        listen 443 ssl http2;
        server_name $DOMAIN_NAME;
        
        # SSL configuration
        ssl_certificate /etc/nginx/ssl/$DOMAIN_NAME.crt;
        ssl_certificate_key /etc/nginx/ssl/$DOMAIN_NAME.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin";
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' https:; frame-ancestors 'none';";
        
        # Frontend static files (if serving frontend)
        location / {
            root /var/www/html;
            try_files \$uri \$uri/ /index.html;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
    
    # API server
    server {
        listen 443 ssl http2;
        server_name $API_DOMAIN;
        
        # SSL configuration
        ssl_certificate /etc/nginx/ssl/$DOMAIN_NAME.crt;
        ssl_certificate_key /etc/nginx/ssl/$DOMAIN_NAME.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin";
        
        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://amazingpay_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            proxy_cache_bypass \$http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # Auth routes with stricter rate limiting
        location /api/auth/ {
            limit_req zone=auth burst=5 nodelay;
            
            proxy_pass http://amazingpay_backend;
            proxy_http_version 1.1;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # Health check
        location /health {
            access_log off;
            proxy_pass http://amazingpay_backend/api/health;
        }
    }
}
EOF
    
    print_success "Nginx configuration created"
}

# Function to generate self-signed SSL certificate (for development)
generate_self_signed_ssl() {
    print_status "Generating self-signed SSL certificate..."
    
    mkdir -p nginx/ssl
    
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/$DOMAIN_NAME.key \
        -out nginx/ssl/$DOMAIN_NAME.crt \
        -subj "/C=US/ST=State/L=City/O=Organization/CN=$DOMAIN_NAME"
    
    print_success "Self-signed SSL certificate generated"
    print_warning "For production, replace with a valid SSL certificate from Let's Encrypt or a CA"
}

# Function to create Let's Encrypt setup script
create_letsencrypt_script() {
    print_status "Creating Let's Encrypt setup script..."
    
    cat > scripts/setup-letsencrypt.sh << 'EOF'
#!/bin/bash

# Let's Encrypt SSL Certificate Setup
# Run this script on your production server

set -e

DOMAIN=$1
EMAIL=$2

if [ -z "$DOMAIN" ] || [ -z "$EMAIL" ]; then
    echo "Usage: $0 <domain> <email>"
    echo "Example: $0 amazingpay.com <EMAIL>"
    exit 1
fi

# Install certbot
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot..."
    sudo apt-get update
    sudo apt-get install -y certbot python3-certbot-nginx
fi

# Stop nginx temporarily
sudo systemctl stop nginx

# Generate certificate
sudo certbot certonly --standalone \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    -d $DOMAIN \
    -d api.$DOMAIN

# Copy certificates to nginx directory
sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem nginx/ssl/$DOMAIN.crt
sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem nginx/ssl/$DOMAIN.key

# Set proper permissions
sudo chown $USER:$USER nginx/ssl/$DOMAIN.*

# Start nginx
sudo systemctl start nginx

echo "✅ Let's Encrypt SSL certificate installed successfully"
echo "📋 Certificate will auto-renew via cron job"

# Setup auto-renewal
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

EOF
    
    chmod +x scripts/setup-letsencrypt.sh
    print_success "Let's Encrypt setup script created"
}

# Function to update CORS settings
update_cors_settings() {
    print_status "Updating CORS settings..."
    
    if [ -f ".env.production" ]; then
        # Allow both main domain and API domain
        CORS_ORIGINS="https://$DOMAIN_NAME,https://$API_DOMAIN"
        sed -i.bak "s|CORS_ORIGIN=.*|CORS_ORIGIN=$CORS_ORIGINS|g" .env.production
        
        print_success "CORS settings updated"
    fi
}

# Main execution
main() {
    print_header
    
    # Configure domain
    configure_domain
    
    # Create Nginx configuration
    create_nginx_config
    
    # Generate self-signed SSL (for development)
    generate_self_signed_ssl
    
    # Create Let's Encrypt script
    create_letsencrypt_script
    
    # Update CORS settings
    update_cors_settings
    
    echo ""
    print_success "🎉 Domain & SSL configuration completed!"
    echo ""
    print_info "📋 Next steps:"
    print_info "1. Point your domain DNS to this server's IP address"
    print_info "2. For production, run: ./scripts/setup-letsencrypt.sh $DOMAIN_NAME admin@$DOMAIN_NAME"
    print_info "3. Start Nginx: docker-compose up -d nginx"
    print_info "4. Test your domain: https://$DOMAIN_NAME"
    print_info "5. Test your API: https://$API_DOMAIN/api/health"
    echo ""
    print_info "🔗 Configured domains:"
    print_info "   Main: https://$DOMAIN_NAME"
    print_info "   API: https://$API_DOMAIN"
}

# Run main function
main "$@"
