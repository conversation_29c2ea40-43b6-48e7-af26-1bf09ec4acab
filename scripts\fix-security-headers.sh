#!/bin/bash

# 🔒 SECURITY HEADERS FIX SCRIPT
# Fixes missing security headers for AmazingPay deployment
# Domain: amazingpayme.com | VPS: ************

set -e

# 🎯 CONFIGURATION
DOMAIN="amazingpayme.com"
NGINX_SITE="/etc/nginx/sites-available/amazingpayme.com"
NGINX_ENABLED="/etc/nginx/sites-enabled/amazingpayme.com"

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 🔍 CHECK CURRENT NGINX CONFIGURATION
check_nginx_config() {
    log "Checking current Nginx configuration..."
    
    if [ ! -f "$NGINX_SITE" ]; then
        error "Nginx site configuration not found: $NGINX_SITE"
    fi
    
    # Check if security headers are present
    if grep -q "X-Frame-Options" "$NGINX_SITE"; then
        success "X-Frame-Options header found in config"
    else
        warning "X-Frame-Options header missing from config"
    fi
    
    if grep -q "Strict-Transport-Security" "$NGINX_SITE"; then
        success "HSTS header found in config"
    else
        warning "HSTS header missing from config"
    fi
    
    if grep -q "X-Content-Type-Options" "$NGINX_SITE"; then
        success "X-Content-Type-Options header found in config"
    else
        warning "X-Content-Type-Options header missing from config"
    fi
}

# 🔧 UPDATE NGINX CONFIGURATION
update_nginx_config() {
    log "Updating Nginx configuration with enhanced security headers..."
    
    # Create backup
    cp "$NGINX_SITE" "$NGINX_SITE.backup.$(date +%Y%m%d-%H%M%S)"
    success "Backup created"
    
    # Create enhanced configuration
    cat > "$NGINX_SITE" << 'EOF'
# 🌐 Enhanced Nginx Configuration for AmazingPay Production
# VPS: ************ | Domain: amazingpayme.com
# Security-first configuration for financial applications

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;
limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=20r/s;

# Upstream backend
upstream amazingpay_backend {
    least_conn;
    server 127.0.0.1:3002 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name amazingpayme.com www.amazingpayme.com ************;
    
    # Security headers even for redirects
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

# Main HTTPS server
server {
    listen 443 ssl http2;
    server_name amazingpayme.com www.amazingpayme.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/amazingpayme.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/amazingpayme.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/amazingpayme.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 🔒 ENHANCED SECURITY HEADERS FOR FINANCIAL APPLICATION
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https:; frame-ancestors 'none'; base-uri 'self'; form-action 'self';" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()" always;
    add_header X-Permitted-Cross-Domain-Policies "none" always;
    add_header Cross-Origin-Embedder-Policy "require-corp" always;
    add_header Cross-Origin-Opener-Policy "same-origin" always;
    add_header Cross-Origin-Resource-Policy "same-origin" always;
    
    # Hide server information
    server_tokens off;
    more_clear_headers Server;
    more_set_headers "Server: AmazingPay-Secure";
    
    # Basic Settings
    root /www/wwwroot/amazingpayme.com/public;
    index index.html index.htm;
    
    # Client Settings
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # API Routes with Rate Limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
        
        # Additional security headers for API
        add_header X-API-Security "enabled" always;
        add_header Cache-Control "no-store, no-cache, must-revalidate" always;
        
        # Proxy settings
        proxy_pass http://amazingpay_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Authentication endpoints with stricter rate limiting
    location /api/auth/ {
        limit_req zone=auth burst=5 nodelay;
        limit_req_status 429;
        
        # Extra security for authentication
        add_header X-Auth-Security "maximum" always;
        add_header Cache-Control "no-store, no-cache, must-revalidate, private" always;
        
        proxy_pass http://amazingpay_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Auth-Request $request_uri;
    }
    
    # Health check endpoint (no rate limiting)
    location /api/health {
        proxy_pass http://amazingpay_backend;
        access_log off;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static files with caching and security
    location / {
        limit_req zone=general burst=50 nodelay;
        
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable" always;
            add_header Vary "Accept-Encoding" always;
            # Security headers for static files
            add_header X-Content-Type-Options nosniff always;
            add_header X-Frame-Options DENY always;
        }
        
        # Cache HTML files for shorter period
        location ~* \.(html|htm)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate" always;
            # Security headers for HTML
            add_header X-Content-Type-Options nosniff always;
            add_header X-Frame-Options DENY always;
        }
    }
    
    # Block access to sensitive files
    location ~ /\.(env|git|svn|htaccess|htpasswd|config) {
        deny all;
        return 404;
    }
    
    location ~ /(config|logs|backups|scripts|node_modules|src)/ {
        deny all;
        return 404;
    }
    
    # Block common attack patterns
    location ~ /(wp-admin|wp-login|phpmyadmin|admin|administrator|xmlrpc\.php) {
        deny all;
        return 404;
    }
    
    # Robots.txt
    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
    }
    
    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    
    # Logging
    access_log /var/log/nginx/amazingpay-access.log combined;
    error_log /var/log/nginx/amazingpay-error.log warn;
}

# IP-based access (fallback)
server {
    listen 443 ssl http2;
    server_name ************;
    
    # Use same SSL certificate
    ssl_certificate /etc/letsencrypt/live/amazingpayme.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/amazingpayme.com/privkey.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    
    # Redirect to domain name
    return 301 https://amazingpayme.com$request_uri;
}
EOF
    
    success "Enhanced Nginx configuration created"
}

# 🔄 RELOAD NGINX
reload_nginx() {
    log "Testing and reloading Nginx configuration..."
    
    # Test configuration
    if nginx -t; then
        success "Nginx configuration test passed"
    else
        error "Nginx configuration test failed"
    fi
    
    # Reload Nginx
    systemctl reload nginx
    success "Nginx reloaded successfully"
}

# 🧪 TEST SECURITY HEADERS
test_security_headers() {
    log "Testing security headers..."
    
    sleep 5  # Wait for Nginx to fully reload
    
    # Test headers
    local test_url="https://$DOMAIN"
    
    echo "Testing security headers for: $test_url"
    
    # Use curl to test headers
    local headers=$(curl -I -s "$test_url" || echo "")
    
    if echo "$headers" | grep -i "x-frame-options" > /dev/null; then
        success "X-Frame-Options header present"
    else
        warning "X-Frame-Options header missing"
    fi
    
    if echo "$headers" | grep -i "strict-transport-security" > /dev/null; then
        success "HSTS header present"
    else
        warning "HSTS header missing"
    fi
    
    if echo "$headers" | grep -i "x-content-type-options" > /dev/null; then
        success "X-Content-Type-Options header present"
    else
        warning "X-Content-Type-Options header missing"
    fi
    
    if echo "$headers" | grep -i "content-security-policy" > /dev/null; then
        success "Content-Security-Policy header present"
    else
        warning "Content-Security-Policy header missing"
    fi
}

# 🎯 MAIN FUNCTION
main() {
    log "🔒 Starting Security Headers Fix"
    log "Domain: $DOMAIN"
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
    fi
    
    check_nginx_config
    update_nginx_config
    reload_nginx
    test_security_headers
    
    success "🎉 Security headers fix completed!"
    info "Run the security validation script again to verify all issues are resolved"
    info "Command: ./scripts/pre-launch-security-validation.sh https://amazingpayme.com"
}

# Run main function
main "$@"
