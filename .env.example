# ENVIRONMENT VARIABLES TEMPLATE
# ================================
# CRITICAL SECURITY NOTICE:
# 1. Copy this file to .env and fill in your actual values
# 2. NEVER commit .env files to version control!
# 3. Use strong, unique secrets for production
# 4. Rotate secrets regularly
# 5. Never log or expose these values in code
# ================================

# Server Configuration
PORT=3002
HOST=localhost
NODE_ENV=development
API_PREFIX=/api

# Frontend Configuration
FRONTEND_URL=http://localhost:5173
API_URL=http://localhost:3002/api
DOMAIN=amazingpayme.com

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=amazingpay
DB_SSL=false
DB_CONNECTION_POOL_MIN=5
DB_CONNECTION_POOL_MAX=20
DB_STATEMENT_TIMEOUT=30000

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=amazingpayme.com
JWT_AUDIENCE=amazingpayme.com

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_PREFLIGHT_CONTINUE=false
CORS_OPTIONS_SUCCESS_STATUS=204
CORS_MAX_AGE=86400

# Security Configuration
BCRYPT_SALT_ROUNDS=10
CSRF_ENABLED=true
CSRF_SECRET=amazingpay-csrf-secret
CSRF_TOKEN_EXPIRATION=3600000
XSS_PROTECTION=true
CONTENT_SECURITY_POLICY=true
HSTS=true
HSTS_MAX_AGE=********
FRAME_GUARD=true
NO_SNIFF=true
REFERRER_POLICY=strict-origin-when-cross-origin

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
AUTH_RATE_LIMIT_WINDOW_MS=3600000
AUTH_RATE_LIMIT_MAX=5
PASSWORD_RESET_RATE_LIMIT_WINDOW_MS=3600000
PASSWORD_RESET_RATE_LIMIT_MAX=3

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>
EMAIL_SECURE=false

# SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Binance API Configuration
BINANCE_API_URL=https://api.binance.com
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret

# Blockchain API Configuration
TRONSCAN_API_URL=https://apilist.tronscan.org/api
ETHERSCAN_API_URL=https://api.etherscan.io/api
ETHERSCAN_API_KEY=your_etherscan_api_key

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7
