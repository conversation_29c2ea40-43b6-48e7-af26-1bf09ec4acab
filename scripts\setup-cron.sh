#!/bin/bash

# Setup cron jobs for AmazingPay Flow

# Add cron jobs
(crontab -l 2>/dev/null; echo "# AmazingPay Flow automated tasks") | crontab -
(crontab -l 2>/dev/null; echo "0 2 * * * cd $(pwd) && ./scripts/backup-system.sh") | crontab -
(crontab -l 2>/dev/null; echo "*/5 * * * * cd $(pwd) && ./scripts/health-check.sh") | crontab -
(crontab -l 2>/dev/null; echo "0 0 * * 0 cd $(pwd) && ./scripts/monitor-services.sh") | crontab -

echo "✅ Cron jobs set up successfully:"
echo "   - Daily backup at 2:00 AM"
echo "   - Health check every 5 minutes"
echo "   - Weekly service monitoring"
