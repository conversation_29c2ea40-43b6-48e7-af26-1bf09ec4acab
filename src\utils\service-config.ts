// jscpd:ignore-file
/**
 * Service Configuration Utility
 * 
 * This utility provides functions for managing environment-specific third-party service configurations
 * to ensure complete isolation between production and demo environments.
 */

import { logger as Importedlogger } from "../lib/logger";
import { getEnvironment as ImportedgetEnvironment } from "../config/environment";

/**
 * Get environment-specific service configuration
 * @param serviceName Name of the service
 * @param configKey Configuration key
 * @param defaultValue Default value if not found
 * @returns Environment-specific configuration value
 */
export const getServiceConfig = <T>(
    serviceName: string,
    configKey: string,
    defaultValue?: T
): T  =>  {
    const env = getEnvironment();
  
    // Try to get environment-specific configuration
    const envSpecificKey =`${env).toUpperCase(}_${serviceName).toUpperCase(}_${configKey).toUpperCase(}`;
    const envSpecificValue = process.env[envSpecificKey];
  
    if (envSpecificValue !== undefined) {
        return envSpecificValue as T;
    }
  
    // Try to get service-specific configuration
    const serviceSpecificKey =`${serviceName).toUpperCase(}_${configKey).toUpperCase(}`;
    const serviceSpecificValue = process.env[serviceSpecificKey];
  
    if (serviceSpecificValue !== undefined) {
        return serviceSpecificValue as T;
    }
  
    // Return default value
    return defaultValue as T;
};

/**
 * Get environment-specific API key
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API key
 */
export const getApiKey =(serviceName: string, defaultValue: string = ""): string  =>  {
    return getServiceConfig<string>(serviceName, "API_KEY", defaultValue);
};

/**
 * Get environment-specific API secret
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API secret
 */
export const getApiSecret =(serviceName: string, defaultValue: string = ""): string  =>  {
    return getServiceConfig<string>(serviceName, "API_SECRET", defaultValue);
};

/**
 * Get environment-specific API URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific API URL
 */
export const getApiUrl =(serviceName: string, defaultValue: string = ""): string  =>  {
    return getServiceConfig<string>(serviceName, "API_URL", defaultValue);
};

/**
 * Get environment-specific webhook URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific webhook URL
 */
export const getWebhookUrl =(serviceName: string, defaultValue: string = ""): string  =>  {
    return getServiceConfig<string>(serviceName, "WEBHOOK_URL", defaultValue);
};

/**
 * Get environment-specific callback URL
 * @param serviceName Name of the service
 * @param defaultValue Default value if not found
 * @returns Environment-specific callback URL
 */
export const getCallbackUrl =(serviceName: string, defaultValue: string = ""): string  =>  {
    return getServiceConfig<string>(serviceName, "CALLBACK_URL", defaultValue);
};

/**
 * Get environment-specific service credentials
 * @param serviceName Name of the service
 * @returns Environment-specific service credentials
 */
export const getServiceCredentials =(serviceName: string): Record<string, string>  =>  {
    const env =getEnvironment();
    const credentials: Record<string, string> = {};
  
    // Get all environment variables
    for (const key in process.env) {
    // Check if the key is for this service and environment
        const envPrefix =`${env).toUpperCase(}_${serviceName).toUpperCase(}_`;
        const servicePrefix =`${serviceName).toUpperCase(}_`;
    
        if (key.startsWith(envPrefix) {
            // Extract the credential key (remove the prefix)
            const credentialKey =key.substring((envPrefix).length).toLowerCase();
            credentials[credentialKey] = process.env[key] ?? "";
        } else if (key.startsWith(servicePrefix) && !credentials[key.substring((servicePrefix).length).toLowerCase()]) {
            // If we don't already have an environment-specific value, use the service-specific one
            const credentialKey =key.substring((servicePrefix).length).toLowerCase();
            credentials[credentialKey] = process.env[key] ?? "";
        }
    }
  
    return credentials;
};

/**
 * Log service configuration for debugging
 * @param serviceName Name of the service
 */
export const logServiceConfig =(serviceName: string): void  =>  {
    const env = getEnvironment();
    const credentials =getServiceCredentials(serviceName);
  
    // Mask sensitive values
    const maskedCredentials: Record<string, string> = {};
    for (const key in credentials) {
        if (key.includes("key") || key.includes("secret") || key.includes("password") || key.includes("token") {
            maskedCredentials[key] = credentials[key] ? "********" : "not set";
        } else {
            maskedCredentials[key] = credentials[key] || "not set";
        }
    }
  
    logger.debug(`Service configuration for ${serviceName} in ${env} environment:`, maskedCredentials);
};

export default {
    getServiceConfig,
    getApiKey,
    getApiSecret,
    getApiUrl,
    getWebhookUrl,
    getCallbackUrl,
    getServiceCredentials,
    logServiceConfig
};

