{"version": 3, "file": "security-test.js", "sourceRoot": "", "sources": ["../../../src/tests/security-test.ts"], "names": [], "mappings": ";;;;;AACA,oBAAoB;AACpB;;;;;;;;;;;;;;GAcG;AAEH,kDAA0B;AAG1B,gBAAgB;AAChB,MAAM,QAAQ,GAAW,uBAAuB,CAAC;AAEjD,yBAAyB;AACzB,KAAK,UAAU,gBAAgB;IAC7B,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAE1C,IAAI,WAAW,GAAW,CAAC,CAAC;IAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;IAGkC,AAAF,IAAK,OAAgB,CAAA,CAAA;IAAG,CAAC;QAC1E,UAAU,EAAE,CAAC;QACb,IAAI,CAAC;YACH,MAAM,MAAM,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACjC,WAAW,EAAE,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACnC,OAAO,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,wBAAwB;IACxB,MAAM,OAAO,CAAC,uBAAuB,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;QACtD,KAAK,EAAC,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,SAAS,CAAC;QAEtD,6BAA6B;QAC7B,MAAM,CAAC,QAAQ,IAAA,CAAC,AAAD,EAAA,EAAA,CAAC,OAAO;KAAA,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;IAC/E,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC9D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;IAC7D,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;IACrE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;AAC/D,CAAC;AAAC,CAAC;AAEH,uBAAuB;AACvB,MAAM,OAAO,CAAC,sBAAsB,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;IACrD,GAAG,EAAC;QACF,oDAAoD;QACpD,KAAK,EAAC,eAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,iBAAiB,EAAE;YAC7C,KAAK,EAAE,kBAAkB;YACzB,QAAQ,EAAE,UAAU;SACrB,CAAC;QAEF,2DAA2D;QAC3D,KAAK,EAAC,IAAI,KAAK,CAAC,2CAA2C,CAAC;KAC7D,EAAC,KAAK,CAAE,KAAK;QACZ,kCAAkC;QAClC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;CACF,CAAC,CAAC;AAEH,sBAAsB;AACtB,MAAM,OAAO,CAAC,qBAAqB,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;IACpD,GAAG,EAAC;QACF,6CAA6C;QAC7C,KAAK,EAAC,UAAU,EAAE,MAAM,GAAG,uCAAuC;QAElE,KAAK,EAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,iBAAiB,UAAU,EAAE,CAAC;KAI1D,EAAC,KAAK,CAAE,KAAK;QACZ,+CAA+C;QAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC;CACF,CAAC,CAAC;AAEH,gCAAgC;AAChC,MAAM,OAAO,CAAC,+BAA+B,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;IAC9D,GAAG,EAAC;QACF,uDAAuD;QACvD,KAAK,EAAC,mBAAmB,EAAE,MAAM,GAAG,aAAa;QAEjD,KAAK,EAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,iBAAiB,mBAAmB,EAAE,CAAC;KAGnE,EAAC,KAAK,CAAE,KAAK;QACZ,+CAA+C;QAC/C,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC;CACF,CAAC,CAAC;AAEH,qBAAqB;AACrB,MAAM,OAAO,CAAC,oBAAoB,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;IACnD,kDAAkD;IAClD,KAAK,EAAC,QAAQ,EAAE,GAAG,EAAA,CAAC,CAAC,EAAC,AAAD,GAAG,EAAE;IACI,GAAG,EAAA,EAAE,CAAC;CAAA,EAAE,CAAC,CAAA;AAAC,CAAC;IACvC,QAAQ,CAAC,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,aAAa,CAAC,CAAC,KAAK,CAAE,CAAC,KAAK,EAAG,EAAE,CAAE,AAAF,GAAK,KAAK,CAAC,QAAQ,CAAC,CAAA,CAAC;AAC3F,CAAC;AAED,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAE9C,8DAA8D;AAC9D,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,EAAE,CAAE,AAAF,GAAK,GAAG,IAAQ,GAAG,CAAC,MAAM,IAAM,AAAD,EAAI,GAAG,CAAC,CAAC;AACpF,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC;AACjC,CAAC;AAEH,sBAAsB;AACtB,MAAM,OAAO,CAAC,qBAAqB,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;IACpD,GAAG,EAAC;QACF,gEAAgE;QAChE,KAAK,EAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,CAAC;QAE5C,+DAA+D;QAC/D,KAAK,EAAC,IAAI,KAAK,CAAC,sDAAsD,CAAC;KACxE,EAAC,KAAK,CAAE,KAAK;QACZ,qCAAqC;QACrC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;CACF,CAAC,CAAC;AAEH,mBAAmB;AACnB,MAAM,OAAO,CAAC,kBAAkB,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;IACjD,GAAG,EAAC;QACF,6DAA6D;QAC7D,KAAK,EAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,EAAE;YAC3C,OAAO,EAAE,EAAE,aAAa,EAAE,0BAA0B,EAAE;SACvD,CAAC;QAEF,4DAA4D;QAC5D,KAAK,EAAC,IAAI,KAAK,CAAC,gDAAgD,CAAC;KAClE,EAAC,KAAK,CAAE,KAAK;QACZ,qCAAqC;QACrC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;CACF,CAAC,CAAC;AAEH,mBAAmB;AACnB,MAAM,OAAO,CAAC,kBAAkB,EAAE,KAAK,IAAK,EAAE,CAAE,AAAF,GAAK;IACjD,uDAAuD;IACvD,KAAK,EAAC,UAAU,EAAE,MAAM,GACtB,mLAAmL;IAErL,GAAG,EAAC;QACF,6DAA6D;QAC7D,KAAK,EAAC,eAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,gBAAgB,EAAE;YAC3C,OAAO,EAAE,EAAE,aAAa,EAAE,UAAU,UAAU,EAAE,EAAE;SACnD,CAAC;QAEF,4DAA4D;QAC5D,KAAK,EAAC,IAAI,KAAK,CAAC,gDAAgD,CAAC;KAClE,EAAC,KAAK,CAAE,KAAK;QACZ,qCAAqC;QACrC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;CACF,CAAC,CAAC;AAEH,gBAAgB;AAChB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;AACxC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AACrC,OAAO,CAAC,GAAG,CACT,WAAW,WAAW,IAAI,UAAU,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,EAAC,GAAC,CAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4B3F,CAAA"}