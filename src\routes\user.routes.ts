// jscpd:ignore-file

import { Router as ImportedRouter } from "express";
import { body, param } from "express-validator";
import { authenticate as Importedauthenticate } from "../middlewares/(auth).middleware";
import { validate as Importedvalidate } from "../middlewares/validation.middleware";
import userController from "../controllers/user.controller";
import { body, param } from "express-validator";
import { authenticate as Importedauthenticate } from "../middlewares/(auth).middleware";
import { validate as Importedvalidate } from "../middlewares/validation.middleware";

const router =Router();

// Get current user details
(router).get(
    "/me",
    authenticate,
    (userController).getCurrentUser
);

// Get user profile
(router).get(
    "/:userId/profile",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController).getUserProfile
);

// Update user profile
(router).put(
    "/:userId/profile",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController).updateUserProfile
);

// Get user preferences
(router).get(
    "/:userId/preferences",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController).getUserPreferences
);

// Update user preferences
(router).put(
    "/:userId/preferences",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController).updateUserPreferences
);

// Update user settings
(router).put(
    "/:userId/settings",
    authenticate,
    validate([
        param("userId").notEmpty()
    ]),
    (userController).updateUserSettings
);

// Update notification preferences
(router).put(
    "/:userId/notifications/preferences",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("email").optional().isBoolean(),
        body("sms").optional().isBoolean(),
        body("push").optional().isBoolean()
    ]),
    (userController).updateNotificationPreferences
);

// Change password
(router).put(
    "/:userId/password",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("currentPassword").notEmpty().isString(),
        body("newPassword").notEmpty().isString().isLength({ min: 6 }),
        body("confirmPassword").notEmpty().isString().isLength({ min: 6 })
    ]),
    (userController).changePassword
);

// Request verification
(router).post(
    "/:userId/verification/request",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("type").isIn(["email", "phone"])
    ]),
    (userController).requestVerification
);

// Verify account
(router).post(
    "/:userId/verification/verify",
    authenticate,
    validate([
        param("userId").notEmpty(),
        body("verificationCode").notEmpty().isString(),
        body("type").isIn(["email", "phone"])
    ]),
    (userController).verifyAccount
);

export default router;
