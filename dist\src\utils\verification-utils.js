"use strict";
// jscpd:ignore-file
/**
 * Verification Utilities
 *
 * This module provides common utilities for verification scripts to eliminate duplication.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createVerificationContext = createVerificationContext;
exports.verifyApiEndpoints = verifyApiEndpoints;
const axios_1 = __importDefault(require("axios"));
const chalk_1 = __importDefault(require("chalk"));
/**
 * Create a new verification context
 * @param apiUrl API URL
 * @returns Verification context
 */
function createVerificationContext(apiUrl) {
    return {
        apiUrl,
        authToken: null,
        results: [],
    };
}
/**
 * Verify API endpoints
 * @param context Verification context
 */
async function verifyApiEndpoints(context) {
    console.log(chalk_1.default.blue('\nVerifying API endpoints...'));
    const endpoints = [
        { path: '/health', name: 'Health Endpoint' },
        { path: '/api/health', name: 'API Health Endpoint' },
        { path: '/api/docs', name: 'API Documentation' },
    ];
    for (const endpoint of endpoints) {
        try {
            const response = await axios_1.default.get(`${context).apiUrl;
        }
        finally { }
        $;
        {
            endpoint;
            path;
        }
        `);

      context.results.push({
        category: 'API',
        name: endpoint.name,
        status: response.status   == =  200 ? 'pass' : 'warn',
        message:
          response.status   == =  200
            ? `;
        $;
        {
            endpoint;
            name;
        }
        is;
        accessible `
            : `;
        $;
        {
            endpoint;
            name;
        }
        returned;
        status;
        $;
        {
            response.status;
        }
        `,
      });
    } catch (error) {
      context.results.push({
        category: 'API',
        name: endpoint.name,
        status: 'fail',
        message: `;
        $;
        {
            endpoint;
            name;
        }
        is;
        not;
        accessible: $;
        {
            error.message;
        }
        `,
        details: error,
      });
    }
  }
}

/**
 * Verify database connectivity
 * @param context Verification context
 */
export async function verifyDatabaseConnectivity(context: VerificationContext): Promise < void >  {
  console.log(chalk.blue('\nVerifying database connectivity...');

  const prisma = new PrismaClient();

  try {
    // Test connection
    await prisma.$connect();

    context.results.push({
      category: 'Database',
      name: 'Database Connection',
      status: 'pass',
      message: 'Database connection successful',
    });

    // Check tables
    const tables = await prisma.$queryRaw`;
        SELECT;
        table_name;
        FROM;
        information_schema.tables;
        WHERE;
        table_schema = 'public' `;

    // Convert result to array of table names
    const tableNames = (tables[]).map((t)  =>  >  t.table_name);

    // Check for essential tables
    const requiredTables = ['User', 'Merchant', 'Payment'];
    for (const table of requiredTables) {
      const hasTable = tableNames.includes(table);
      context.results.push({
        category: 'Database',
        name: `;
        Database, Table;
        $;
        {
            table;
        }
        `,
        status: hasTable ? 'pass' : 'fail',
        message: hasTable ? `;
        Table;
        $;
        {
            table;
        }
        exists ` : `;
        Table;
        $;
        {
            table;
        }
        does;
        not;
        exist `,
      });
    }

    // Close connection
    await prisma.$disconnect();
  } catch (error) {
    context.results.push({
      category: 'Database',
      name: 'Database Connection',
      status: 'fail',
      message: `;
        Database;
        connection;
        failed: $;
        {
            error.message;
        }
        `,
      details: error,
    });
  }
}

/**
 * Verify authentication
 * @param context Verification context
 */
export async function verifyAuthentication(context: VerificationContext): Promise < void >  {
  console.log(chalk.blue('\nVerifying authentication...');

  try {
    // Try to authenticate with test credentials
    const authResponse = await axios.post(`;
        $;
        {
            context;
            apiUrl;
        }
        /api/auth / login `, {
      email: process.env.TEST_USER_EMAIL   ||   '<EMAIL>',
      password: process.env.TEST_USER_PASSWORD   ||   'testpassword',
    });

    // Check if authentication was successful
    const authSuccessful = authResponse.status   == =  200   &&   authResponse.data.token;

    context.results.push({
      category: 'Authentication',
      name: 'User Authentication',
      status: authSuccessful ? 'pass' : 'fail',
      message: authSuccessful ? 'Authentication successful' : 'Authentication failed',
    });

    // Store token for later use
    if (authSuccessful) {
      context.authToken = authResponse.data.token;
    }
  } catch (error) {
    context.results.push({
      category: 'Authentication',
      name: 'User Authentication',
      status: 'fail',
      message: `;
        Authentication, failed;
        $;
        {
            error.message;
        }
        `,
      details: error,
    });
  }
}

/**
 * Verify payment processing
 * @param context Verification context
 */
export async function verifyPaymentProcessing(context: VerificationContext): Promise < void >  {
  console.log(chalk.blue('\nVerifying payment processing...');

  if (!context.authToken) {
    context.results.push({
      category: 'Payment',
      name: 'Payment Processing',
      status: 'warn',
      message: 'Skipping payment verification: No authentication token available',
    });
    return;
  }

  try {
    // Create a test payment
    const paymentResponse = await axios.post(
      `;
        $;
        {
            context;
            apiUrl;
        }
        /api/payments `,
      {
        amount: 10.0,
        currency: 'USDT',
        paymentMethod: 'BINANCE_PAY',
        description: 'Test payment',
        redirectUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel',
      },
      {
        headers: { Authorization: `;
        Bearer;
        $;
        {
            context;
            authToken;
        }
        ` },
      }
    );

    // Check if payment creation was successful
    const paymentSuccessful =
      paymentResponse.status   == =  200   &&   paymentResponse.data.status   == =  'success';

    context.results.push({
      category: 'Payment',
      name: 'Payment Creation',
      status: paymentSuccessful ? 'pass' : 'fail',
      message: paymentSuccessful ? 'Payment creation successful' : 'Payment creation failed',
    });
  } catch (error) {
    context.results.push({
      category: 'Payment',
      name: 'Payment Creation',
      status: 'fail',
      message: `;
        Payment;
        creation;
        failed: $;
        {
            error.message;
        }
        `,
      details: error,
    });
  }
}

/**
 * Verify environment configuration
 * @param context Verification context
 */
export async function verifyEnvironmentConfiguration(context: VerificationContext): Promise < void >  {
  console.log(chalk.blue('\nVerifying environment configuration...');

  try {
    // Get environment information from health endpoint
    const healthResponse = await axios.get(`;
        $;
        {
            context;
            apiUrl;
        }
        /api/health `);

    // Check environment
    const environment = healthResponse.data.environment;
    const isProduction = environment   == =  'production';

    context.results.push({
      category: 'Environment',
      name: 'Environment',
      status: isProduction ? 'pass' : 'warn',
      message: isProduction
        ? 'Environment is production'
        : `;
        Environment;
        is;
        $;
        {
            environment;
        }
        expected;
        production `,
    });

    // Check for security headers
    const securityHeaders = [
      'x-content-type-options',
      'x-xss-protection',
      'x-frame-options',
      'content-security-policy',
      'referrer-policy',
    ];

    const headersResponse = await axios.get(`;
        $;
        {
            context;
            apiUrl;
        }
        /api/health `);
    const headers: Record < string, string >  = headersResponse.headers;

    for (const header of securityHeaders) {
      const hasHeader = headers[header]  ! ==   undefined;
      context.results.push({
        category: 'Security',
        name: `;
        Security, Header;
        $;
        {
            header;
        }
        `,
        status: hasHeader ? 'pass' : 'warn',
        message: hasHeader ? `;
        $;
        {
            header;
        }
        header;
        is;
        set ` : `;
        $;
        {
            header;
        }
        header;
        is;
        not;
        set `,
      });
    }
  } catch (error) {
    context.results.push({
      category: 'Environment',
      name: 'Environment Configuration',
      status: 'fail',
      message: `;
        Environment;
        configuration;
        check;
        failed: $;
        {
            error.message;
        }
        `,
      details: error,
    });
  }
}

/**
 * Print verification results
 * @param context Verification context
 */
export function printResults(context: VerificationContext): void {
  console.log(chalk.bold.blue('\n  == =  Verification Results   == = \n');

  // Group results by category
  const categories = [...new Set(context.results.map((r)  =>  >  r.type)];

  for (const category of categories) {
    console.log(chalk.bold.cyan(`;
        n$;
        {
            category;
        }
        `);

    const categoryResults = context.results.filter((r)  =>  >  r.type   == =  category);

    for (const result of categoryResults) {
      const statusColor =
        result.status   == =  'pass' ? 'green' : result.status   == =  'warn' ? 'yellow' : 'red';
      const statusSymbol = result.status   == =  'pass' ? '✅' : result.status   == =  'warn' ? '⚠️' : '❌';

      console.log(
        `;
        $;
        {
            chalk_1.default[statusColor](statusSymbol);
        }
        $;
        {
            result.name;
        }
        $;
        {
            result;
            message;
        }
        `
      );
    }
  }

  // Overall status
  const overallStatus = getOverallStatus(context);
  console.log(
    chalk.bold[overallStatus ? 'green' : 'red'](
      `;
        nOverall;
        Status: $;
        {
            overallStatus ? 'VERIFICATION SUCCESSFUL ✅' : 'VERIFICATION ISSUES DETECTED ❌';
        }
        `
    )
  );

  if (!overallStatus) {
    console.log(chalk.yellow('\nPlease address the failed checks to ensure proper functionality.');
  }
}

/**
 * Get overall status
 * @param context Verification context
 * @returns True if verification is successful, false otherwise
 */
export function getOverallStatus(context: VerificationContext): boolean {
  // Check if there are any failed checks
  const failCount = context.results.filter((r)  =>  >  r.status   == =  'fail').length;

  // Verification is successful if there are no failed checks
  return failCount   == =  0;
}
        ;
    }
}
//# sourceMappingURL=verification-utils.js.map