#!/bin/bash

# 💾 AMAZINGPAY BACKUP SCRIPT
# Creates comprehensive backups with versioning and rollback capability
# VPS: ************ | Domain: amazingpayme.com

set -e

# 🎯 CONFIGURATION
APP_DIR="/www/wwwroot/amazingpayme.com"
BACKUP_DIR="/var/backups/amazingpay"
LOG_DIR="/var/log/amazingpay"
DB_NAME="Amazingpay"
RETENTION_DAYS=30

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_DIR/backup.log"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_DIR/backup.log"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_DIR/backup.log"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_DIR/backup.log"
    exit 1
}

# 📋 USAGE
usage() {
    echo "Usage: $0 [backup-name]"
    echo ""
    echo "Examples:"
    echo "  $0                           # Auto-generated name"
    echo "  $0 pre-deployment           # Custom name"
    echo "  $0 manual-backup-v1.2.0     # Version-specific backup"
    echo ""
    echo "Options:"
    echo "  -h, --help                   # Show this help"
    echo "  -l, --list                   # List available backups"
    echo "  -r, --restore <backup-name>  # Restore from backup"
    echo "  -c, --cleanup                # Clean old backups"
    exit 1
}

# 📂 LIST BACKUPS
list_backups() {
    log "Available backups:"
    echo ""
    
    if [ ! -d "$BACKUP_DIR" ] || [ -z "$(ls -A $BACKUP_DIR 2>/dev/null)" ]; then
        warning "No backups found"
        return
    fi
    
    echo -e "${BLUE}Name${NC}\t\t\t${BLUE}Date${NC}\t\t\t${BLUE}Size${NC}\t${BLUE}Type${NC}"
    echo "--------------------------------------------------------------------"
    
    for backup in "$BACKUP_DIR"/*; do
        if [ -d "$backup" ]; then
            local name=$(basename "$backup")
            local date=$(stat -c %y "$backup" | cut -d' ' -f1,2 | cut -d'.' -f1)
            local size=$(du -sh "$backup" | cut -f1)
            local type="manual"
            
            if [ -f "$backup/metadata.json" ]; then
                type=$(jq -r '.backup_type // "manual"' "$backup/metadata.json" 2>/dev/null || echo "manual")
            fi
            
            echo -e "${name}\t${date}\t${size}\t${type}"
        fi
    done
    echo ""
}

# 💾 CREATE BACKUP
create_backup() {
    local backup_name="$1"
    
    # Generate backup name if not provided
    if [ -z "$backup_name" ]; then
        backup_name="backup-$(date +%Y%m%d-%H%M%S)"
    fi
    
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log "Creating backup: $backup_name"
    
    # Check if backup already exists
    if [ -d "$backup_path" ]; then
        error "Backup '$backup_name' already exists"
    fi
    
    # Create backup directory
    mkdir -p "$backup_path"
    
    # Backup application files
    log "Backing up application files..."
    rsync -av \
        --exclude=node_modules \
        --exclude=.git \
        --exclude=logs \
        --exclude=coverage \
        --exclude=test-reports \
        --exclude=backups \
        "$APP_DIR/" "$backup_path/app/" || error "Failed to backup application files"
    
    # Backup database
    log "Backing up database..."
    if sudo -u postgres pg_dump "$DB_NAME" > "$backup_path/database.sql"; then
        gzip "$backup_path/database.sql"
        success "Database backup completed"
    else
        error "Failed to backup database"
    fi
    
    # Backup environment files
    log "Backing up environment configuration..."
    if [ -f "$APP_DIR/.env.production" ]; then
        cp "$APP_DIR/.env.production" "$backup_path/.env.production"
    fi
    
    # Backup Nginx configuration
    if [ -f "/etc/nginx/sites-available/amazingpayme.com" ]; then
        cp "/etc/nginx/sites-available/amazingpayme.com" "$backup_path/nginx.conf"
    fi
    
    # Backup PM2 configuration
    if [ -f "$APP_DIR/ecosystem.config.js" ]; then
        cp "$APP_DIR/ecosystem.config.js" "$backup_path/ecosystem.config.js"
    fi
    
    # Create metadata
    log "Creating backup metadata..."
    cat > "$backup_path/metadata.json" << EOF
{
  "name": "$backup_name",
  "timestamp": "$(date -Iseconds)",
  "commit": "$(cd $APP_DIR && git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "branch": "$(cd $APP_DIR && git branch --show-current 2>/dev/null || echo 'unknown')",
  "backup_type": "manual",
  "app_version": "$(cd $APP_DIR && npm list --depth=0 | head -1 | grep -o '@.*' | cut -d'@' -f2 || echo 'unknown')",
  "node_version": "$(node --version)",
  "database_size": "$(sudo -u postgres psql -d $DB_NAME -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" | xargs)",
  "backup_size": "$(du -sh $backup_path | cut -f1)"
}
EOF
    
    # Create backup summary
    log "Creating backup summary..."
    cat > "$backup_path/README.md" << EOF
# AmazingPay Backup: $backup_name

## Backup Information
- **Created:** $(date)
- **Type:** Manual backup
- **Application Directory:** $APP_DIR
- **Database:** $DB_NAME

## Contents
- \`app/\` - Application files (excluding node_modules)
- \`database.sql.gz\` - PostgreSQL database dump
- \`.env.production\` - Environment configuration
- \`nginx.conf\` - Nginx configuration
- \`ecosystem.config.js\` - PM2 configuration
- \`metadata.json\` - Backup metadata

## Restore Instructions
\`\`\`bash
# Restore this backup
./scripts/create-backup.sh --restore $backup_name

# Or manually:
# 1. Stop application: pm2 stop amazingpay
# 2. Restore files: rsync -av $backup_path/app/ $APP_DIR/
# 3. Restore database: zcat $backup_path/database.sql.gz | sudo -u postgres psql -d $DB_NAME
# 4. Start application: pm2 start ecosystem.config.js
\`\`\`
EOF
    
    # Set permissions
    chown -R www-data:www-data "$backup_path"
    chmod -R 750 "$backup_path"
    
    # Calculate final size
    local final_size=$(du -sh "$backup_path" | cut -f1)
    
    success "Backup created successfully!"
    log "Backup location: $backup_path"
    log "Backup size: $final_size"
}

# 🔄 RESTORE BACKUP
restore_backup() {
    local backup_name="$1"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [ -z "$backup_name" ]; then
        error "Backup name is required for restore"
    fi
    
    if [ ! -d "$backup_path" ]; then
        error "Backup '$backup_name' not found"
    fi
    
    log "Restoring from backup: $backup_name"
    
    # Confirm restore
    echo -e "${YELLOW}⚠️  This will overwrite the current application and database!${NC}"
    echo -n "Are you sure you want to continue? (yes/no): "
    read -r confirmation
    
    if [ "$confirmation" != "yes" ]; then
        log "Restore cancelled"
        exit 0
    fi
    
    # Create a backup of current state before restore
    log "Creating backup of current state before restore..."
    create_backup "pre-restore-$(date +%Y%m%d-%H%M%S)"
    
    # Stop application
    log "Stopping application..."
    pm2 stop amazingpay || true
    
    # Restore application files
    log "Restoring application files..."
    rsync -av --delete "$backup_path/app/" "$APP_DIR/"
    
    # Restore database
    log "Restoring database..."
    if [ -f "$backup_path/database.sql.gz" ]; then
        zcat "$backup_path/database.sql.gz" | sudo -u postgres psql -d "$DB_NAME"
    elif [ -f "$backup_path/database.sql" ]; then
        sudo -u postgres psql -d "$DB_NAME" < "$backup_path/database.sql"
    else
        warning "No database backup found"
    fi
    
    # Restore environment
    log "Restoring environment configuration..."
    if [ -f "$backup_path/.env.production" ]; then
        cp "$backup_path/.env.production" "$APP_DIR/.env.production"
    fi
    
    # Restore Nginx configuration
    if [ -f "$backup_path/nginx.conf" ]; then
        cp "$backup_path/nginx.conf" "/etc/nginx/sites-available/amazingpayme.com"
        nginx -t && systemctl reload nginx
    fi
    
    # Install dependencies and restart
    log "Installing dependencies..."
    cd "$APP_DIR"
    npm ci --production
    
    log "Starting application..."
    pm2 start ecosystem.config.js --env production
    
    # Health check
    sleep 10
    if curl -f -s http://localhost:3002/api/health > /dev/null; then
        success "Restore completed successfully!"
    else
        warning "Restore completed but health check failed"
    fi
}

# 🧹 CLEANUP OLD BACKUPS
cleanup_backups() {
    log "Cleaning up old backups (keeping last $RETENTION_DAYS days)..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        warning "Backup directory does not exist"
        return
    fi
    
    local deleted_count=0
    
    find "$BACKUP_DIR" -maxdepth 1 -type d -mtime +$RETENTION_DAYS | while read -r old_backup; do
        if [ "$old_backup" != "$BACKUP_DIR" ]; then
            log "Deleting old backup: $(basename "$old_backup")"
            rm -rf "$old_backup"
            ((deleted_count++))
        fi
    done
    
    success "Cleanup completed. Deleted $deleted_count old backups."
}

# 🎯 MAIN FUNCTION
main() {
    # Ensure directories exist
    mkdir -p "$BACKUP_DIR" "$LOG_DIR"
    
    case "${1:-}" in
        -h|--help)
            usage
            ;;
        -l|--list)
            list_backups
            ;;
        -r|--restore)
            restore_backup "$2"
            ;;
        -c|--cleanup)
            cleanup_backups
            ;;
        *)
            create_backup "$1"
            ;;
    esac
}

# Run main function
main "$@"
