-- 🔒 SECURE DATABASE SETUP SCRIPT
-- Auto-generated for AmazingPay security incident response
-- Execute this script to create secure database credentials

-- Create new secure application user
CREATE USER amazingpay_app WITH PASSWORD 'AzP4y_S3cur3_2024_Db_P4ssw0rd';

-- Grant necessary privileges to the application user
GRANT ALL PRIVILEGES ON DATABASE amazingpay TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO amazingpay_app;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO amazingpay_app;

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO amazingpay_app;

-- Grant default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO amazingpay_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO amazingpay_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO amazingpay_app;

-- Change postgres password (RECOMMENDED - uncomment and set new password)
-- ALTER USER postgres PASSWORD 'NEW_POSTGRES_PASSWORD_2024_SECURE';

-- Verify users
\du

-- Show current database
SELECT current_database();

-- Show granted privileges
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_schema='public' AND grantee='amazingpay_app';

-- Test connection (you can run this separately)
-- \c amazingpay amazingpay_app
