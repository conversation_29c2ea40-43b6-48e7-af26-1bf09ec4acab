import { Server as WebSocketServer } from 'socket.io';
export declare class WebSocketMonitor {
    private static instance;
    private io;
    private stats;
    private monitoringInterval;
    private historyLimit;
    /**
     * Private constructor to enforce singleton pattern
     */
    private constructor();
    /**
     * Get the singleton instance
     */
    static getInstance(): WebSocketMonitor;
    /**
     * Initialize the WebSocket monitor with a socket.io server instance
     */
    initialize(io: WebSocketServer): void;
    /**
     * Set up event listeners for socket.io events
     */
    private setupEventListeners;
    socket: any;
    on(: any): any;
}
//# sourceMappingURL=websocket-monitor.d.ts.map