#!/bin/bash

# AmazingPay Flow - External Services Configuration Script
# This script helps configure all external services and API integrations

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🔌 AmazingPay Flow - External Services Setup${NC}"
    echo -e "${BLUE}============================================\n${NC}"
}

print_status() {
    echo -e "${CYAN}📦 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# Function to configure email service
configure_email_service() {
    print_status "Configuring Email Service (SMTP)..."
    
    echo "Choose your email provider:"
    echo "1. Gmail"
    echo "2. SendGrid"
    echo "3. Mailgun"
    echo "4. Custom SMTP"
    echo -n "Enter choice (1-4): "
    read EMAIL_CHOICE
    
    case $EMAIL_CHOICE in
        1)
            EMAIL_HOST="smtp.gmail.com"
            EMAIL_PORT="587"
            EMAIL_SECURE="true"
            echo -n "Enter Gmail address: "
            read EMAIL_USER
            echo -n "Enter Gmail App Password: "
            read -s EMAIL_PASSWORD
            echo
            ;;
        2)
            EMAIL_HOST="smtp.sendgrid.net"
            EMAIL_PORT="587"
            EMAIL_SECURE="true"
            EMAIL_USER="apikey"
            echo -n "Enter SendGrid API Key: "
            read -s EMAIL_PASSWORD
            echo
            ;;
        3)
            EMAIL_HOST="smtp.mailgun.org"
            EMAIL_PORT="587"
            EMAIL_SECURE="true"
            echo -n "Enter Mailgun SMTP username: "
            read EMAIL_USER
            echo -n "Enter Mailgun SMTP password: "
            read -s EMAIL_PASSWORD
            echo
            ;;
        4)
            echo -n "Enter SMTP host: "
            read EMAIL_HOST
            echo -n "Enter SMTP port: "
            read EMAIL_PORT
            echo -n "Use SSL/TLS? (y/n): "
            read SSL_CHOICE
            EMAIL_SECURE=$([ "$SSL_CHOICE" = "y" ] && echo "true" || echo "false")
            echo -n "Enter SMTP username: "
            read EMAIL_USER
            echo -n "Enter SMTP password: "
            read -s EMAIL_PASSWORD
            echo
            ;;
        *)
            print_error "Invalid choice"
            return 1
            ;;
    esac
    
    echo -n "Enter 'From' email address: "
    read EMAIL_FROM
    
    # Update .env.production
    if [ -f ".env.production" ]; then
        cp .env.production .env.production.email.backup
        
        sed -i.bak "s|EMAIL_HOST=.*|EMAIL_HOST=$EMAIL_HOST|g" .env.production
        sed -i.bak "s|EMAIL_PORT=.*|EMAIL_PORT=$EMAIL_PORT|g" .env.production
        sed -i.bak "s|EMAIL_SECURE=.*|EMAIL_SECURE=$EMAIL_SECURE|g" .env.production
        sed -i.bak "s|EMAIL_USER=.*|EMAIL_USER=$EMAIL_USER|g" .env.production
        sed -i.bak "s|EMAIL_PASSWORD=.*|EMAIL_PASSWORD=$EMAIL_PASSWORD|g" .env.production
        sed -i.bak "s|EMAIL_FROM=.*|EMAIL_FROM=$EMAIL_FROM|g" .env.production
        
        print_success "Email service configured"
    fi
}

# Function to configure Twilio SMS
configure_twilio_sms() {
    print_status "Configuring Twilio SMS Service..."
    
    echo -n "Enter Twilio Account SID: "
    read TWILIO_ACCOUNT_SID
    echo -n "Enter Twilio Auth Token: "
    read -s TWILIO_AUTH_TOKEN
    echo
    echo -n "Enter Twilio Phone Number (with country code, e.g., +**********): "
    read TWILIO_PHONE_NUMBER
    
    # Update .env.production
    if [ -f ".env.production" ]; then
        cp .env.production .env.production.twilio.backup
        
        sed -i.bak "s|TWILIO_ACCOUNT_SID=.*|TWILIO_ACCOUNT_SID=$TWILIO_ACCOUNT_SID|g" .env.production
        sed -i.bak "s|TWILIO_AUTH_TOKEN=.*|TWILIO_AUTH_TOKEN=$TWILIO_AUTH_TOKEN|g" .env.production
        sed -i.bak "s|TWILIO_PHONE_NUMBER=.*|TWILIO_PHONE_NUMBER=$TWILIO_PHONE_NUMBER|g" .env.production
        
        print_success "Twilio SMS service configured"
    fi
}

# Function to configure Binance API
configure_binance_api() {
    print_status "Configuring Binance API..."
    
    echo "Choose Binance environment:"
    echo "1. Production (api.binance.com)"
    echo "2. Testnet (testnet.binance.vision)"
    echo -n "Enter choice (1-2): "
    read BINANCE_CHOICE
    
    case $BINANCE_CHOICE in
        1)
            BINANCE_API_URL="https://api.binance.com"
            print_warning "Using PRODUCTION Binance API - real money transactions!"
            ;;
        2)
            BINANCE_API_URL="https://testnet.binance.vision"
            print_info "Using Binance TESTNET - safe for testing"
            ;;
        *)
            print_error "Invalid choice"
            return 1
            ;;
    esac
    
    echo -n "Enter Binance API Key: "
    read BINANCE_API_KEY
    echo -n "Enter Binance API Secret: "
    read -s BINANCE_API_SECRET
    echo
    
    # Update .env.production
    if [ -f ".env.production" ]; then
        cp .env.production .env.production.binance.backup
        
        sed -i.bak "s|BINANCE_API_URL=.*|BINANCE_API_URL=$BINANCE_API_URL|g" .env.production
        sed -i.bak "s|BINANCE_API_KEY=.*|BINANCE_API_KEY=$BINANCE_API_KEY|g" .env.production
        sed -i.bak "s|BINANCE_API_SECRET=.*|BINANCE_API_SECRET=$BINANCE_API_SECRET|g" .env.production
        
        print_success "Binance API configured"
    fi
}

# Function to configure blockchain APIs
configure_blockchain_apis() {
    print_status "Configuring Blockchain APIs..."
    
    # Etherscan API
    echo -n "Enter Etherscan API Key (for Ethereum transactions): "
    read ETHERSCAN_API_KEY
    
    # Update .env.production
    if [ -f ".env.production" ]; then
        cp .env.production .env.production.blockchain.backup
        
        sed -i.bak "s|ETHERSCAN_API_KEY=.*|ETHERSCAN_API_KEY=$ETHERSCAN_API_KEY|g" .env.production
        
        print_success "Blockchain APIs configured"
    fi
}

# Function to generate secure secrets
generate_secure_secrets() {
    print_status "Generating secure secrets..."
    
    # Generate JWT secret
    JWT_SECRET=$(openssl rand -base64 32)
    CSRF_SECRET=$(openssl rand -base64 32)
    
    # Update .env.production
    if [ -f ".env.production" ]; then
        cp .env.production .env.production.secrets.backup
        
        sed -i.bak "s|JWT_SECRET=.*|JWT_SECRET=$JWT_SECRET|g" .env.production
        sed -i.bak "s|CSRF_SECRET=.*|CSRF_SECRET=$CSRF_SECRET|g" .env.production
        
        print_success "Secure secrets generated"
    fi
}

# Function to configure Redis
configure_redis() {
    print_status "Configuring Redis..."
    
    echo "Choose Redis setup:"
    echo "1. Local Redis (localhost)"
    echo "2. Redis Cloud"
    echo "3. Custom Redis URL"
    echo -n "Enter choice (1-3): "
    read REDIS_CHOICE
    
    case $REDIS_CHOICE in
        1)
            REDIS_URL="redis://localhost:6379"
            REDIS_PASSWORD=""
            ;;
        2)
            echo -n "Enter Redis Cloud URL: "
            read REDIS_URL
            echo -n "Enter Redis password: "
            read -s REDIS_PASSWORD
            echo
            ;;
        3)
            echo -n "Enter Redis URL: "
            read REDIS_URL
            echo -n "Enter Redis password (leave empty if none): "
            read -s REDIS_PASSWORD
            echo
            ;;
        *)
            print_error "Invalid choice"
            return 1
            ;;
    esac
    
    # Update .env.production
    if [ -f ".env.production" ]; then
        cp .env.production .env.production.redis.backup
        
        sed -i.bak "s|REDIS_URL=.*|REDIS_URL=$REDIS_URL|g" .env.production
        if [ -n "$REDIS_PASSWORD" ]; then
            echo "REDIS_PASSWORD=$REDIS_PASSWORD" >> .env.production
        fi
        
        print_success "Redis configured"
    fi
}

# Function to test all configurations
test_configurations() {
    print_status "Testing configurations..."
    
    # Create test script
    cat > temp_test_config.js << 'EOF'
const nodemailer = require('nodemailer');
const twilio = require('twilio');
const axios = require('axios');
require('dotenv').config({ path: '.env.production' });

async function testEmail() {
    try {
        const transporter = nodemailer.createTransporter({
            host: process.env.EMAIL_HOST,
            port: process.env.EMAIL_PORT,
            secure: process.env.EMAIL_SECURE === 'true',
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASSWORD
            }
        });
        
        await transporter.verify();
        console.log('✅ Email configuration valid');
    } catch (error) {
        console.log('❌ Email configuration failed:', error.message);
    }
}

async function testTwilio() {
    try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
        console.log('✅ Twilio configuration valid');
    } catch (error) {
        console.log('❌ Twilio configuration failed:', error.message);
    }
}

async function testBinance() {
    try {
        const response = await axios.get(`${process.env.BINANCE_API_URL}/api/v3/ping`);
        if (response.status === 200) {
            console.log('✅ Binance API connection valid');
        }
    } catch (error) {
        console.log('❌ Binance API connection failed:', error.message);
    }
}

async function runTests() {
    console.log('🧪 Testing external service configurations...\n');
    await testEmail();
    await testTwilio();
    await testBinance();
    console.log('\n✅ Configuration tests completed');
}

runTests();
EOF
    
    # Run tests
    node temp_test_config.js
    
    # Clean up
    rm temp_test_config.js
}

# Function to create service monitoring script
create_monitoring_script() {
    print_status "Creating service monitoring script..."
    
    cat > scripts/monitor-services.sh << 'EOF'
#!/bin/bash

# Service Monitoring Script
# Monitors all external services and sends alerts if any are down

source .env.production

check_email() {
    echo "📧 Checking email service..."
    # Add email service check logic here
}

check_twilio() {
    echo "📱 Checking Twilio SMS service..."
    # Add Twilio check logic here
}

check_binance() {
    echo "💰 Checking Binance API..."
    curl -s "$BINANCE_API_URL/api/v3/ping" > /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ Binance API is responding"
    else
        echo "❌ Binance API is down"
    fi
}

check_redis() {
    echo "🔄 Checking Redis..."
    # Add Redis check logic here
}

echo "🔍 AmazingPay Flow - Service Health Check"
echo "========================================"
check_email
check_twilio
check_binance
check_redis
echo "✅ Health check completed"
EOF
    
    chmod +x scripts/monitor-services.sh
    print_success "Service monitoring script created"
}

# Main execution
main() {
    print_header
    
    echo "This script will help you configure all external services for AmazingPay Flow."
    echo "You can skip any service by pressing Enter when prompted."
    echo ""
    
    # Configure each service
    configure_email_service
    echo ""
    
    configure_twilio_sms
    echo ""
    
    configure_binance_api
    echo ""
    
    configure_blockchain_apis
    echo ""
    
    configure_redis
    echo ""
    
    # Generate secure secrets
    generate_secure_secrets
    echo ""
    
    # Test configurations
    print_info "Would you like to test the configurations? (y/n)"
    read -n 1 TEST_CHOICE
    echo ""
    if [ "$TEST_CHOICE" = "y" ]; then
        test_configurations
    fi
    
    # Create monitoring script
    create_monitoring_script
    
    echo ""
    print_success "🎉 External services configuration completed!"
    echo ""
    print_info "📋 Configured services:"
    print_info "   ✅ Email service"
    print_info "   ✅ Twilio SMS"
    print_info "   ✅ Binance API"
    print_info "   ✅ Blockchain APIs"
    print_info "   ✅ Redis cache"
    print_info "   ✅ Security secrets"
    echo ""
    print_info "🔧 Next steps:"
    print_info "1. Restart your application to load new configuration"
    print_info "2. Test each service integration"
    print_info "3. Set up monitoring alerts"
    print_info "4. Configure backup schedules"
}

# Run main function
main "$@"
