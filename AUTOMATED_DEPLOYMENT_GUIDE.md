# 🚀 AmazingPay Flow - Automated Deployment Guide

## **✅ YES! Your Project Has Full Automated Deployment with Rollback**

Your AmazingPay Flow project now includes a comprehensive automated deployment system that allows you to:

- ✅ **Modify code on GitHub** and see changes automatically deployed
- ✅ **Automatic rollback** if deployment fails
- ✅ **Manual rollback** to any previous version
- ✅ **Zero-downtime deployments** with health checks
- ✅ **Complete audit trail** of all deployments

## **🎯 How It Works**

### **1. GitHub → VPS Automated Flow**

```mermaid
graph LR
    A[Push to GitHub] --> B[GitHub Actions CI/CD]
    B --> C[Tests & Security Scan]
    C --> D[Build & Package]
    D --> E[Deploy to VPS]
    E --> F[Health Check]
    F --> G[Success ✅]
    F --> H[Rollback ❌]
```

### **2. Deployment Triggers**

| Trigger | Action | Automatic |
|---------|--------|-----------|
| Push to `main` branch | Production deployment | ✅ Yes |
| Push to `develop` branch | Staging deployment | ✅ Yes |
| Manual trigger | Deploy any branch | 🔧 Manual |
| Webhook call | Instant deployment | ✅ Yes |

## **🔧 Setup Instructions**

### **Step 1: Initial Setup**

```bash
# Make deployment manager executable
chmod +x scripts/deployment-manager.sh

# Run initial setup
./scripts/deployment-manager.sh setup
```

### **Step 2: Configure GitHub Secrets**

Add these secrets to your GitHub repository (`Settings > Secrets and variables > Actions`):

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `VPS_HOST` | Your VPS IP address | `*************` |
| `VPS_USER` | VPS username | `root` |
| `VPS_SSH_KEY` | Private SSH key | `-----BEGIN OPENSSH PRIVATE KEY-----` |
| `VPS_PORT` | SSH port (optional) | `22` |
| `SLACK_WEBHOOK` | Slack notifications (optional) | `https://hooks.slack.com/...` |

### **Step 3: Install Webhook Handler**

```bash
# Install webhook handler on your VPS
./scripts/deployment-manager.sh install-webhook
```

### **Step 4: Configure GitHub Webhook**

1. Go to your GitHub repository
2. Navigate to `Settings > Webhooks`
3. Click `Add webhook`
4. Configure:
   - **URL**: `https://your-domain.com/webhook`
   - **Content type**: `application/json`
   - **Secret**: (use the generated webhook secret)
   - **Events**: Select "Just the push event"

### **Step 5: Test the Setup**

```bash
# Test VPS connection
./scripts/deployment-manager.sh test-connection

# Check application status
./scripts/deployment-manager.sh status
```

## **🚀 Deployment Methods**

### **Method 1: Automatic (Recommended)**

Simply push code to GitHub:

```bash
git add .
git commit -m "Add new feature"
git push origin main
```

**What happens automatically:**
1. ✅ GitHub Actions runs tests
2. ✅ Security scanning
3. ✅ Build application
4. ✅ Deploy to VPS
5. ✅ Health check
6. ✅ Rollback if issues

### **Method 2: Manual Deployment**

```bash
# Deploy specific branch
./scripts/deployment-manager.sh deploy main

# Deploy development branch
./scripts/deployment-manager.sh deploy develop
```

### **Method 3: Webhook Trigger**

```bash
# Manually trigger webhook deployment
./scripts/deployment-manager.sh trigger-webhook
```

## **🔄 Rollback Capabilities**

### **Automatic Rollback**

- ✅ **Health check fails** → Automatic rollback
- ✅ **Build fails** → No deployment
- ✅ **Tests fail** → No deployment

### **Manual Rollback**

```bash
# List available backups
./scripts/deployment-manager.sh list-backups

# Rollback to specific backup
./scripts/deployment-manager.sh rollback backup-20231225-120000
```

### **Emergency Rollback**

```bash
# SSH to VPS and rollback immediately
ssh root@your-vps-ip
cd /www/wwwroot/amazingpay-flow
./restore-backup.sh $(ls -t /var/backups/amazingpay/ | head -1)
```

## **📊 Monitoring & Management**

### **Application Status**

```bash
# Get comprehensive status
./scripts/deployment-manager.sh status

# View application logs
./scripts/deployment-manager.sh logs 100
```

### **Real-time Monitoring**

```bash
# SSH to VPS for real-time monitoring
ssh root@your-vps-ip

# PM2 monitoring
pm2 monit

# Real-time logs
pm2 logs amazingpay-flow --follow
```

## **🛡️ Safety Features**

### **Pre-deployment Checks**

- ✅ **TypeScript compilation** must pass
- ✅ **Unit tests** must pass (80%+ coverage)
- ✅ **Integration tests** must pass
- ✅ **Security scan** must pass
- ✅ **Performance tests** (for main branch)

### **Deployment Safety**

- ✅ **Automatic backup** before each deployment
- ✅ **Database migration** safety checks
- ✅ **Health check** after deployment
- ✅ **Automatic rollback** on failure
- ✅ **Zero-downtime** deployment

### **Approval Process**

For production deployments:
- ✅ **Manual approval** required (configurable)
- ✅ **Multiple approvers** supported
- ✅ **Deployment notifications** via Slack

## **📋 Deployment Workflow**

### **Development Workflow**

```bash
# 1. Create feature branch
git checkout -b feature/new-payment-method

# 2. Make changes and test locally
npm test
npm run build

# 3. Push to GitHub
git push origin feature/new-payment-method

# 4. Create Pull Request
# → Triggers CI/CD tests

# 5. Merge to develop
# → Automatic staging deployment

# 6. Merge to main
# → Automatic production deployment (with approval)
```

### **Hotfix Workflow**

```bash
# 1. Create hotfix branch from main
git checkout main
git checkout -b hotfix/critical-security-fix

# 2. Make fix and test
npm test

# 3. Push and merge to main
git push origin hotfix/critical-security-fix
# → Immediate production deployment after approval
```

## **🔧 Configuration Files**

### **GitHub Actions Workflow**

- **File**: `.github/workflows/ci-cd.yml`
- **Features**: Complete CI/CD pipeline with VPS deployment
- **Triggers**: Push to main/develop, manual dispatch

### **VPS Webhook Handler**

- **File**: `webhook-handler.js` (on VPS)
- **Port**: 9000
- **Features**: GitHub webhook processing, deployment automation

### **Deployment Scripts**

- **Manager**: `scripts/deployment-manager.sh`
- **Setup**: `scripts/setup-automated-deployment.sh`
- **VPS Deploy**: `deploy-on-vps.sh`
- **Backup**: `backup-vps.sh`
- **Restore**: `restore-backup.sh`

## **📞 Support & Troubleshooting**

### **Common Issues**

| Issue | Solution |
|-------|----------|
| Deployment fails | Check logs: `./scripts/deployment-manager.sh logs` |
| Health check fails | Verify application is running: `pm2 status` |
| Webhook not triggered | Check GitHub webhook settings and secret |
| SSH connection fails | Verify VPS credentials and SSH key |

### **Emergency Contacts**

```bash
# Check application status
curl https://your-domain.com/api/health

# Check webhook handler
curl https://your-domain.com/webhook/health

# Manual deployment
./scripts/deployment-manager.sh deploy main
```

## **🎉 Summary**

**YES! Your AmazingPay Flow has complete automated deployment capabilities:**

✅ **GitHub Integration** - Push code → Automatic deployment  
✅ **Quality Gates** - Tests, security, performance checks  
✅ **Zero Downtime** - Rolling deployments with health checks  
✅ **Automatic Rollback** - Instant recovery from failures  
✅ **Manual Control** - Deploy any branch, rollback any version  
✅ **Monitoring** - Real-time status and logging  
✅ **Safety** - Backups, approvals, notifications  

**Your deployment system is enterprise-grade and production-ready! 🚀**
