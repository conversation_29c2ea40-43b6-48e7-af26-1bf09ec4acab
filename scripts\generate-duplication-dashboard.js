#!/usr/bin/env node

/**
 * Generate Duplication Dashboard Script
 * 
 * This script generates an HTML dashboard for monitoring code duplication over time.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Configuration
const outputDir = path.resolve('reports', 'duplication-dashboard');
const historyFile = path.join(outputDir, 'duplication-history.json');
const dashboardFile = path.join(outputDir, 'index.html');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Run jscpd to get current duplication stats
console.log('Running duplication check...');
let currentStats = null;

try {
  // Run jscpd with JSON reporter
  execSync('npx jscpd . --config .jscpd.json --reporters json --output .jscpd-temp', { stdio: 'pipe' });
  
  // Read the JSON report
  const reportPath = path.resolve('.jscpd-temp', 'jscpd-report.json');
  if (fs.existsSync(reportPath)) {
    const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    currentStats = {
      timestamp: new Date().toISOString(),
      percentage: report.statistics.total.percentage,
      duplicatedLines: report.statistics.total.duplicatedLines,
      totalLines: report.statistics.total.lines,
      duplicatedTokens: report.statistics.total.duplicatedTokens,
      totalTokens: report.statistics.total.tokens,
      clones: report.duplicates.length
    };
    
    // Clean up temporary report
    fs.rmSync('.jscpd-temp', { recursive: true, force: true });
  }
} catch (error) {
  console.error('Error running duplication check:', error.message);
  process.exit(1);
}

if (!currentStats) {
  console.error('Failed to get current duplication stats.');
  process.exit(1);
}

console.log(`Current duplication: ${currentStats.percentage}%`);

// Load or initialize history
let history = [];
if (fs.existsSync(historyFile)) {
  try {
    history = JSON.parse(fs.readFileSync(historyFile, 'utf8'));
  } catch (error) {
    console.error('Error reading history file:', error.message);
  }
}

// Add current stats to history
history.push(currentStats);

// Keep only the last 30 entries
if (history.length > 30) {
  history = history.slice(history.length - 30);
}

// Save updated history
fs.writeFileSync(historyFile, JSON.stringify(history, null, 2));
console.log(`Updated history file with ${history.length} entries.`);

// Generate dashboard HTML
const generateDashboard = (history) => {
  const dates = history.map(entry => {
    const date = new Date(entry.timestamp);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  });
  
  const percentages = history.map(entry => entry.percentage);
  const lines = history.map(entry => entry.duplicatedLines);
  const tokens = history.map(entry => entry.duplicatedTokens);
  const clones = history.map(entry => entry.clones);
  
  const latestEntry = history[history.length - 1];
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Code Duplication Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1, h2 {
      color: #333;
    }
    .stats-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 30px;
    }
    .stat-card {
      flex: 1;
      min-width: 200px;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }
    .stat-value {
      font-size: 2em;
      font-weight: bold;
      margin: 10px 0;
    }
    .stat-label {
      font-size: 0.9em;
      color: #666;
    }
    .chart-container {
      margin-bottom: 30px;
      height: 300px;
    }
    .good {
      background-color: #d4edda;
      color: #155724;
    }
    .warning {
      background-color: #fff3cd;
      color: #856404;
    }
    .danger {
      background-color: #f8d7da;
      color: #721c24;
    }
    .info {
      background-color: #d1ecf1;
      color: #0c5460;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      color: #666;
      font-size: 0.8em;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Code Duplication Dashboard</h1>
    <p>Last updated: ${new Date(latestEntry.timestamp).toLocaleString()}</p>
    
    <div class="stats-container">
      <div class="stat-card ${latestEntry.percentage <= 1 ? 'good' : latestEntry.percentage <= 3 ? 'warning' : 'danger'}">
        <div class="stat-label">Duplication Percentage</div>
        <div class="stat-value">${latestEntry.percentage}%</div>
        <div class="stat-label">of total code</div>
      </div>
      
      <div class="stat-card info">
        <div class="stat-label">Duplicated Lines</div>
        <div class="stat-value">${latestEntry.duplicatedLines}</div>
        <div class="stat-label">of ${latestEntry.totalLines} total lines</div>
      </div>
      
      <div class="stat-card info">
        <div class="stat-label">Duplicated Tokens</div>
        <div class="stat-value">${latestEntry.duplicatedTokens}</div>
        <div class="stat-label">of ${latestEntry.totalTokens} total tokens</div>
      </div>
      
      <div class="stat-card ${latestEntry.clones === 0 ? 'good' : latestEntry.clones <= 5 ? 'warning' : 'danger'}">
        <div class="stat-label">Clones Found</div>
        <div class="stat-value">${latestEntry.clones}</div>
        <div class="stat-label">duplicated code blocks</div>
      </div>
    </div>
    
    <h2>Duplication Trend</h2>
    <div class="chart-container">
      <canvas id="duplicationChart"></canvas>
    </div>
    
    <h2>Duplicated Lines Trend</h2>
    <div class="chart-container">
      <canvas id="linesChart"></canvas>
    </div>
    
    <h2>Clones Trend</h2>
    <div class="chart-container">
      <canvas id="clonesChart"></canvas>
    </div>
    
    <div class="footer">
      <p>Generated by the AmazingPay Flow Duplication Dashboard Tool</p>
      <p>Run <code>npm run duplication:dashboard</code> to update this dashboard</p>
    </div>
  </div>
  
  <script>
    // Duplication Percentage Chart
    const duplicationCtx = document.getElementById('duplicationChart').getContext('2d');
    new Chart(duplicationCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(dates)},
        datasets: [{
          label: 'Duplication Percentage',
          data: ${JSON.stringify(percentages)},
          borderColor: '#007bff',
          backgroundColor: 'rgba(0, 123, 255, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Percentage (%)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Date'
            }
          }
        }
      }
    });
    
    // Duplicated Lines Chart
    const linesCtx = document.getElementById('linesChart').getContext('2d');
    new Chart(linesCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(dates)},
        datasets: [{
          label: 'Duplicated Lines',
          data: ${JSON.stringify(lines)},
          borderColor: '#28a745',
          backgroundColor: 'rgba(40, 167, 69, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Lines'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Date'
            }
          }
        }
      }
    });
    
    // Clones Chart
    const clonesCtx = document.getElementById('clonesChart').getContext('2d');
    new Chart(clonesCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(dates)},
        datasets: [{
          label: 'Clones Found',
          data: ${JSON.stringify(clones)},
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Count'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Date'
            }
          }
        }
      }
    });
  </script>
</body>
</html>`;
};

// Generate and save dashboard
const dashboardHtml = generateDashboard(history);
fs.writeFileSync(dashboardFile, dashboardHtml);
console.log(`Generated dashboard at ${dashboardFile}`);

// Open the dashboard in the default browser
try {
  const command = process.platform === 'win32' 
    ? `start "" "${dashboardFile}"` 
    : process.platform === 'darwin' 
      ? `open "${dashboardFile}"` 
      : `xdg-open "${dashboardFile}"`;
  
  execSync(command);
  console.log('Opened dashboard in browser.');
} catch (error) {
  console.log(`Dashboard generated at ${dashboardFile}. Open it in your browser to view.`);
}
