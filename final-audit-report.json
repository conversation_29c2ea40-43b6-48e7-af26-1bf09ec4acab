{"typescript": {"configs": [{"config": "tsconfig.json", "errors": 0, "status": "PERFECT"}, {"config": "tsconfig.ultimate-zero.json", "errors": 0, "status": "PERFECT"}, {"config": "tsconfig.zero-errors.json", "errors": 0, "status": "PERFECT"}], "totalErrors": 0}, "types": {"unsafe": 0, "unknown": 0, "redundant": 0}, "duplication": {"count": 0}, "security": {"vulnerabilities": 0}, "quality": {"issues": 0}}