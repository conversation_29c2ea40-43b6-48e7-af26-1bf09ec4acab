# Testing Shared Modules Guide

This guide provides instructions for testing shared modules in the AmazingPay Flow codebase. Proper testing ensures that our shared modules work correctly and remain duplication-free.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Types](#test-types)
3. [Testing Shared Controllers](#testing-shared-controllers)
4. [Testing Shared Services](#testing-shared-services)
5. [Testing Utility Functions](#testing-utility-functions)
6. [Integration Testing](#integration-testing)
7. [Duplication Testing](#duplication-testing)
8. [Test Coverage Requirements](#test-coverage-requirements)
9. [Running Tests](#running-tests)
10. [Writing New Tests](#writing-new-tests)

## Testing Philosophy

Our testing approach for shared modules follows these principles:

1. **Comprehensive Coverage**: Shared modules must have near-complete test coverage (95%+)
2. **Behavior Testing**: Tests should verify behavior, not implementation details
3. **Edge Cases**: All edge cases and error conditions must be tested
4. **Independence**: Tests should be independent and not rely on external services
5. **Readability**: Tests should be clear and serve as documentation

## Test Types

We use several types of tests for shared modules:

1. **Unit Tests**: Test individual functions and methods in isolation
2. **Integration Tests**: Test how components work together
3. **Duplication Tests**: Verify that no duplication exists in the codebase

## Testing Shared Controllers

When testing shared controller base classes:

### BaseController

Test the following methods:

- `sendSuccess`: Verify it sends correct response with various parameters
- `sendError`: Verify it handles different error types correctly
- `asyncHandler`: Verify it properly handles async functions and errors
- `validateRequest`: Verify it correctly validates requests with different schemas

### CrudController

Test the following methods:

- `getAll`: Verify it retrieves all items and handles errors
- `getById`: Verify it retrieves items by ID and handles not-found cases
- `create`: Verify it creates items, validates input, and handles errors
- `update`: Verify it updates items, validates input, and handles not-found cases
- `delete`: Verify it deletes items and handles not-found cases

Example test structure:

```typescript
describe('CrudController', () => {
  let controller: CrudController;
  let mockService: any;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  
  beforeEach(() => {
    // Set up mocks and controller instance
  });
  
  describe('getAll', () => {
    it('should return all items successfully', async () => {
      // Test happy path
    });
    
    it('should handle errors', async () => {
      // Test error handling
    });
  });
  
  // Additional test cases for other methods
});
```

## Testing Shared Services

When testing shared service base classes:

### BaseService

Test the following methods:

- `findAll`: Verify it retrieves all items with different query parameters
- `findById`: Verify it retrieves items by ID and handles not-found cases
- `create`: Verify it creates items correctly
- `update`: Verify it updates items and handles not-found cases
- `delete`: Verify it deletes items and handles not-found cases

Example test structure:

```typescript
describe('BaseService', () => {
  let service: BaseService;
  let mockModel: any;
  
  beforeEach(() => {
    // Set up mocks and service instance
  });
  
  describe('findAll', () => {
    it('should call model.findMany with query', async () => {
      // Test with query parameters
    });
    
    it('should call model.findMany with empty object when no query provided', async () => {
      // Test without query parameters
    });
  });
  
  // Additional test cases for other methods
});
```

## Testing Utility Functions

When testing shared utility functions:

### Response Utilities

Test the following functions:

- `sendSuccess`: Verify it sends correct response with various parameters
- `sendError`: Verify it handles different error types correctly
- `createApiResponse`: Verify it creates correct response objects

### Async Handler

Test the following:

- Successful execution of async functions
- Error handling for rejected promises
- Proper passing of request, response, and next parameters

Example test structure:

```typescript
describe('asyncHandler', () => {
  it('should call the handler function with request, response, and next', async () => {
    // Test function execution
  });
  
  it('should call next with error when handler rejects', async () => {
    // Test error handling
  });
});
```

## Integration Testing

Integration tests verify that shared modules work correctly together:

1. Create a test Express app
2. Define test routes using shared controllers and services
3. Use supertest to make HTTP requests to the app
4. Verify that responses are correct

Example integration test:

```typescript
describe('Shared Modules Integration', () => {
  let app: express.Application;
  let testController: TestController;
  
  beforeAll(() => {
    // Set up Express app with routes using shared modules
  });
  
  it('should get all items', async () => {
    const response = await request(app).get('/api/items');
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
  
  // Additional test cases for other endpoints
});
```

## Duplication Testing

We use automated tests to verify that the codebase remains duplication-free:

1. **Full Duplication Test**: Checks the entire codebase for duplication
   ```bash
   npm run test:duplication
   ```

2. **New Code Duplication Test**: Checks only changed files for duplication
   ```bash
   npm run test:new-code-duplication
   ```

These tests fail if duplication is found, ensuring that our zero-duplication policy is enforced.

## Test Coverage Requirements

We maintain strict test coverage requirements for shared modules:

- **Branches**: 90% coverage
- **Functions**: 95% coverage
- **Lines**: 95% coverage
- **Statements**: 95% coverage

To check test coverage:

```bash
npm test -- --coverage
```

## Running Tests

To run all tests:

```bash
npm test
```

To run tests for shared modules only:

```bash
npm test -- --testPathPattern=src/tests/shared
```

To run both unit tests and duplication tests:

```bash
npm run test:all
```

## Writing New Tests

When writing new tests for shared modules:

1. **Follow the Existing Pattern**: Use the same structure as existing tests
2. **Test All Edge Cases**: Include tests for error conditions and edge cases
3. **Mock Dependencies**: Use Jest mocks for external dependencies
4. **Keep Tests Focused**: Each test should verify one specific behavior
5. **Use Descriptive Names**: Test names should clearly describe what they're testing

Example of a well-structured test:

```typescript
it('should handle error objects with message property', () => {
  // Arrange
  const error = { message: 'Error message' };
  
  // Act
  sendError(mockResponse as Response, 'Error', 500, error);
  
  // Assert
  expect(mockResponse.json).toHaveBeenCalledWith({
    success: false,
    message: 'Error',
    error: 'Error message'
  });
});
```

By following these guidelines, we ensure that our shared modules are well-tested, reliable, and remain duplication-free.
