// jscpd:ignore-file
import { Request, Response, NextFunction } from 'express';
import { BaseController } from "../../core/BaseController";
import { MerchantService as ImportedMerchantService } from "../../services/refactored/merchant.service";
import { ErrorFactory as ImportedErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { Merchant as ImportedMerchant } from '../types';
import { BaseController } from "../../core/BaseController";
import { MerchantService as ImportedMerchantService } from "../../services/refactored/merchant.service";
import { ErrorFactory as ImportedErrorFactory } from "../../utils/errors/ErrorFactory";
import { logger as Importedlogger } from "../../lib/logger";
import { Merchant as ImportedMerchant } from '../types';


/**
 * Merchant controller
 * This controller handles merchant-related operations
 */
export class MerchantController extends BaseController {
  private merchantService: MerchantService;
  
  /**
   * Create a new merchant controller
   */
  constructor() {
    super();
    this.merchantService = new MerchantService();
  }
  
  /**
   * Get all merchants
   */
  getMerchants = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can get all merchants
    this.checkAdminRole(userRole);
    
    // Parse pagination parameters
    const { limit, offset } = this.parsePagination(req);
    
    // Get merchants
    const result = await this.merchantService.getMerchants({
      limit,
      offset
    });
    
    // Send paginated response
    return this.sendPaginatedSuccess(
      res,
      result.data,
      result.total,
      limit,
      offset
    );
  });
  
  /**
   * Get a merchant by ID
   */
  getMerchant = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Get merchant ID from params
    const { id } = req.params;
    
    // Check if user has permission to view this merchant
    if (userRole !== 'ADMIN' && merchantId !== id) {
      throw (ErrorFactory).authorization('You do not have permission to view this merchant');
    }
    
    // Get merchant
    const merchant = await this.merchantService.getMerchantById(id);
    
    // Check if merchant exists
    if (!merchant) {
      throw (ErrorFactory).notFound('Merchant', id);
    }
    
    // Send success response
    return this.sendSuccess(res, merchant);
  });
  
  /**
   * Create a new merchant
   */
  createMerchant = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can create merchants
    this.checkAdminRole(userRole);
    
    // Get request body
    const { name, email, phone, website, address, country, businessType, taxId } = req.body;
    
    // Validate required fields
    if (!name || !email) {
      throw (ErrorFactory).validation('Name and email are required');
    }
    
    // Check if email is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!(emailRegex).test(email) {
      throw (ErrorFactory).validation('Invalid email format');
    }
    
    // Create merchant
    const merchant = await this.merchantService.createMerchant({
      name,
      email,
      phone,
      website,
      address,
      country,
      businessType,
      taxId,
      status: 'PENDING'
    });
    
    // Send success response
    return this.sendSuccess(res, merchant, 201);
  });
  
  /**
   * Update a merchant
   */
  updateMerchant = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Get merchant ID from params
    const { id } = req.params;
    
    // Check if user has permission to update this merchant
    if (userRole !== 'ADMIN' && merchantId !== id) {
      throw (ErrorFactory).authorization('You do not have permission to update this merchant');
    }
    
    // Get request body
    const { name, email, phone, website, address, country, businessType, taxId, status } = req.body;
    
    // Get merchant
    const merchant = await this.merchantService.getMerchantById(id);
    
    // Check if merchant exists
    if (!merchant) {
      throw (ErrorFactory).notFound('Merchant', id);
    }
    
    // Prepare update data
    const updateData = {};
    
    if (name) (updateData).name = name;
    if (email) {
      // Check if email is valid
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!(emailRegex).test(email) {
        throw (ErrorFactory).validation('Invalid email format');
      }
      (updateData).email = email;
    }
    if (phone) (updateData).phone = phone;
    if (website) (updateData).website = website;
    if (address) (updateData).address = address;
    if (country) (updateData).country = country;
    if (businessType) (updateData).businessType = businessType;
    if (taxId) (updateData).taxId = taxId;
    
    // Only admins can update status
    if (userRole === 'ADMIN' && status) {
      (updateData).status = status;
    }
    
    // Update merchant
    const updatedMerchant = await this.merchantService.updateMerchant(id, updateData);
    
    // Send success response
    return this.sendSuccess(res, updatedMerchant);
  });
  
  /**
   * Delete a merchant
   */
  deleteMerchant = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole } = this.checkAuthorization(req);
    
    // Only admins can delete merchants
    this.checkAdminRole(userRole);
    
    // Get merchant ID from params
    const { id } = req.params;
    
    // Delete merchant
    await this.merchantService.deleteMerchant(id);
    
    // Send success response
    return this.sendSuccess(res, { message: 'Merchant deleted successfully' });
  });
  
  /**
   * Get current merchant
   */
  getCurrentMerchant = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Check if user is a merchant
    this.checkMerchantRole(userRole);
    
    // Check if merchant ID exists
    if (!merchantId) {
      throw (ErrorFactory).notFound('Merchant', 'current');
    }
    
    // Get merchant
    const merchant = await this.merchantService.getMerchantById(merchantId);
    
    // Check if merchant exists
    if (!merchant) {
      throw (ErrorFactory).notFound('Merchant', merchantId);
    }
    
    // Send success response
    return this.sendSuccess(res, merchant);
  });
  
  /**
   * Update current merchant
   */
  updateCurrentMerchant = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Check if user is a merchant
    this.checkMerchantRole(userRole);
    
    // Check if merchant ID exists
    if (!merchantId) {
      throw (ErrorFactory).notFound('Merchant', 'current');
    }
    
    // Get request body
    const { name, email, phone, website, address, country, businessType, taxId } = req.body;
    
    // Prepare update data
    const updateData = {};
    
    if (name) (updateData).name = name;
    if (email) {
      // Check if email is valid
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!(emailRegex).test(email) {
        throw (ErrorFactory).validation('Invalid email format');
      }
      (updateData).email = email;
    }
    if (phone) (updateData).phone = phone;
    if (website) (updateData).website = website;
    if (address) (updateData).address = address;
    if (country) (updateData).country = country;
    if (businessType) (updateData).businessType = businessType;
    if (taxId) (updateData).taxId = taxId;
    
    // Update merchant
    const updatedMerchant = await this.merchantService.updateMerchant(merchantId, updateData);
    
    // Send success response
    return this.sendSuccess(res, updatedMerchant);
  });
  
  /**
   * Get merchant statistics
   */
  getMerchantStats = this.createHandler(async (req: Request, res: Response) => {
    // Check authorization
    const { userRole, merchantId } = this.checkAuthorization(req);
    
    // Get merchant ID from params or use current merchant ID
    const targetMerchantId = req.params.id || merchantId;
    
    // Check if user has permission to view this merchant's stats
    if (userRole !== 'ADMIN' && merchantId !== targetMerchantId) {
      throw (ErrorFactory).authorization('You do not have permission to view this merchant\'s statistics');
    }
    
    // Parse date range
    const { startDate, endDate } = req.query;
    const dateRange = this.parseDateRange(
      startDate as string || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      endDate as string || new Date().toISOString()
    );
    
    // Get merchant statistics
    const stats = await this.merchantService.getMerchantStats(targetMerchantId, {
      startDate: (dateRange).startDate,
      endDate: (dateRange).endDate
    });
    
    // Send success response
    return this.sendSuccess(res, stats);
  });
}
