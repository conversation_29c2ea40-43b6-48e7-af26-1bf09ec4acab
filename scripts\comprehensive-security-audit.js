#!/usr/bin/env node

/**
 * AmazingPay Flow - Comprehensive Security & Quality Audit
 * 
 * This script performs a thorough audit of the entire codebase for:
 * - Security vulnerabilities
 * - Code quality issues
 * - Deployment readiness
 * - Functionality completeness
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 AmazingPay Flow - Comprehensive Security Audit');
console.log('='.repeat(60));

let auditResults = {
    security: { passed: 0, failed: 0, warnings: 0, issues: [] },
    quality: { passed: 0, failed: 0, warnings: 0, issues: [] },
    functionality: { passed: 0, failed: 0, warnings: 0, issues: [] },
    deployment: { passed: 0, failed: 0, warnings: 0, issues: [] }
};

function logResult(category, type, message, severity = 'info') {
    const timestamp = new Date().toISOString();
    const result = { timestamp, message, severity };
    
    auditResults[category].issues.push(result);
    
    if (severity === 'error') {
        auditResults[category].failed++;
        console.log(`❌ [${category.toUpperCase()}] ${message}`);
    } else if (severity === 'warning') {
        auditResults[category].warnings++;
        console.log(`⚠️  [${category.toUpperCase()}] ${message}`);
    } else {
        auditResults[category].passed++;
        console.log(`✅ [${category.toUpperCase()}] ${message}`);
    }
}

// Security Audit Functions
function auditEnvironmentFiles() {
    console.log('\n🔒 SECURITY AUDIT: Environment Files');
    console.log('-'.repeat(40));
    
    const envFiles = ['.env', '.env.production', '.env.vps.production', '.env.example'];
    
    envFiles.forEach(file => {
        if (fs.existsSync(file)) {
            const content = fs.readFileSync(file, 'utf8');
            
            // Check for hardcoded secrets
            if (content.includes('password123') || content.includes('secret123')) {
                logResult('security', 'check', `Potential hardcoded secrets in ${file}`, 'warning');
            } else {
                logResult('security', 'check', `No obvious hardcoded secrets in ${file}`);
            }
            
            // Check for JWT secret strength
            const jwtMatch = content.match(/JWT_SECRET=(.+)/);
            if (jwtMatch && jwtMatch[1].length < 32) {
                logResult('security', 'check', `JWT secret in ${file} is too short (< 32 chars)`, 'error');
            } else if (jwtMatch) {
                logResult('security', 'check', `JWT secret in ${file} has adequate length`);
            }
            
            // Check for database URL exposure
            if (content.includes('DATABASE_URL') && !content.includes('localhost')) {
                logResult('security', 'check', `Database URL in ${file} - ensure it's not exposed`, 'warning');
            }
        }
    });
}

function auditDependencies() {
    console.log('\n🔒 SECURITY AUDIT: Dependencies');
    console.log('-'.repeat(40));
    
    try {
        // Check package.json for known vulnerable packages
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
        
        // Known vulnerable patterns
        const vulnerablePatterns = [
            { name: 'lodash', version: '<4.17.21', reason: 'Prototype pollution' },
            { name: 'axios', version: '<0.21.1', reason: 'SSRF vulnerability' },
            { name: 'jsonwebtoken', version: '<8.5.1', reason: 'Algorithm confusion' }
        ];
        
        Object.keys(dependencies).forEach(dep => {
            const vulnerable = vulnerablePatterns.find(v => v.name === dep);
            if (vulnerable) {
                logResult('security', 'dependency', `${dep} may be vulnerable: ${vulnerable.reason}`, 'warning');
            }
        });
        
        logResult('security', 'dependency', 'Dependency vulnerability check completed');
        
    } catch (error) {
        logResult('security', 'dependency', `Failed to check dependencies: ${error.message}`, 'error');
    }
}

function auditCodeSecurity() {
    console.log('\n🔒 SECURITY AUDIT: Code Security');
    console.log('-'.repeat(40));
    
    const securityPatterns = [
        { pattern: /eval\s*\(/, file: '', issue: 'Use of eval() function', severity: 'error' },
        { pattern: /innerHTML\s*=/, file: '', issue: 'Potential XSS via innerHTML', severity: 'warning' },
        { pattern: /document\.write\s*\(/, file: '', issue: 'Use of document.write', severity: 'warning' },
        { pattern: /process\.env\[.*\]/, file: '', issue: 'Dynamic environment variable access', severity: 'warning' },
        { pattern: /exec\s*\(/, file: '', issue: 'Command execution', severity: 'error' },
        { pattern: /\.query\s*\(.*\+/, file: '', issue: 'Potential SQL injection', severity: 'error' }
    ];
    
    function scanDirectory(dir) {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(file)) {
                scanDirectory(filePath);
            } else if (file.endsWith('.ts') || file.endsWith('.js')) {
                const content = fs.readFileSync(filePath, 'utf8');
                
                securityPatterns.forEach(pattern => {
                    if (pattern.pattern.test(content)) {
                        logResult('security', 'code', `${pattern.issue} in ${filePath}`, pattern.severity);
                    }
                });
            }
        });
    }
    
    try {
        scanDirectory('src');
        logResult('security', 'code', 'Code security scan completed');
    } catch (error) {
        logResult('security', 'code', `Code security scan failed: ${error.message}`, 'error');
    }
}

// Quality Audit Functions
function auditCodeQuality() {
    console.log('\n📊 QUALITY AUDIT: Code Quality');
    console.log('-'.repeat(40));
    
    try {
        // Check TypeScript compilation
        execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
        logResult('quality', 'typescript', 'TypeScript compilation successful');
    } catch (error) {
        logResult('quality', 'typescript', 'TypeScript compilation has errors', 'error');
    }
    
    // Check for TODO/FIXME comments
    try {
        const result = execSync('grep -r "TODO\\|FIXME\\|HACK" src/ || true', { encoding: 'utf8' });
        const todoCount = (result.match(/TODO/g) || []).length;
        const fixmeCount = (result.match(/FIXME/g) || []).length;
        
        if (todoCount > 0 || fixmeCount > 0) {
            logResult('quality', 'todos', `Found ${todoCount} TODOs and ${fixmeCount} FIXMEs`, 'warning');
        } else {
            logResult('quality', 'todos', 'No TODO/FIXME comments found');
        }
    } catch (error) {
        logResult('quality', 'todos', 'Could not check for TODO/FIXME comments', 'warning');
    }
}

// Functionality Audit Functions
function auditPaymentMethods() {
    console.log('\n💳 FUNCTIONALITY AUDIT: Payment Methods');
    console.log('-'.repeat(40));
    
    const paymentFiles = [
        'src/services/payment/PaymentService.ts',
        'src/controllers/payment/PaymentController.ts',
        'src/services/payment/methods/CryptoPaymentService.ts',
        'src/services/payment/methods/BankTransferService.ts'
    ];
    
    paymentFiles.forEach(file => {
        if (fs.existsSync(file)) {
            const content = fs.readFileSync(file, 'utf8');
            
            // Check for essential payment methods
            if (content.includes('processPayment') || content.includes('createPayment')) {
                logResult('functionality', 'payment', `Payment processing logic found in ${file}`);
            } else {
                logResult('functionality', 'payment', `No payment processing logic in ${file}`, 'warning');
            }
            
            // Check for error handling
            if (content.includes('try') && content.includes('catch')) {
                logResult('functionality', 'payment', `Error handling present in ${file}`);
            } else {
                logResult('functionality', 'payment', `Missing error handling in ${file}`, 'error');
            }
        } else {
            logResult('functionality', 'payment', `Payment file missing: ${file}`, 'error');
        }
    });
}

function auditVerificationMethods() {
    console.log('\n🔐 FUNCTIONALITY AUDIT: Verification Methods');
    console.log('-'.repeat(40));
    
    const verificationFiles = [
        'src/services/identity-verification/IdentityVerificationService.ts',
        'src/controllers/identity-verification/IdentityVerificationController.ts'
    ];
    
    verificationFiles.forEach(file => {
        if (fs.existsSync(file)) {
            const content = fs.readFileSync(file, 'utf8');
            
            // Check for verification methods
            const verificationMethods = ['verifyIdentity', 'verifySignature', 'verifyDocument'];
            const foundMethods = verificationMethods.filter(method => content.includes(method));
            
            if (foundMethods.length > 0) {
                logResult('functionality', 'verification', `Verification methods found in ${file}: ${foundMethods.join(', ')}`);
            } else {
                logResult('functionality', 'verification', `No verification methods in ${file}`, 'error');
            }
        } else {
            logResult('functionality', 'verification', `Verification file missing: ${file}`, 'error');
        }
    });
}

// Deployment Audit Functions
function auditDeploymentReadiness() {
    console.log('\n🚀 DEPLOYMENT AUDIT: Deployment Readiness');
    console.log('-'.repeat(40));
    
    const requiredFiles = [
        'package.json',
        'tsconfig.json',
        'prisma/schema.prisma',
        '.env.production',
        'ecosystem.config.js',
        'Dockerfile',
        'docker-compose.yml'
    ];
    
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            logResult('deployment', 'files', `Required file present: ${file}`);
        } else {
            logResult('deployment', 'files', `Required file missing: ${file}`, 'error');
        }
    });
    
    // Check if build directory exists
    if (fs.existsSync('dist')) {
        logResult('deployment', 'build', 'Build directory exists');
    } else {
        logResult('deployment', 'build', 'Build directory missing - run npm run build', 'warning');
    }
    
    // Check if node_modules exists
    if (fs.existsSync('node_modules')) {
        logResult('deployment', 'dependencies', 'Dependencies installed');
    } else {
        logResult('deployment', 'dependencies', 'Dependencies not installed - run npm install', 'error');
    }
}

// Generate Audit Report
function generateAuditReport() {
    console.log('\n📋 AUDIT REPORT SUMMARY');
    console.log('='.repeat(60));
    
    let totalPassed = 0;
    let totalFailed = 0;
    let totalWarnings = 0;
    
    Object.keys(auditResults).forEach(category => {
        const result = auditResults[category];
        totalPassed += result.passed;
        totalFailed += result.failed;
        totalWarnings += result.warnings;
        
        console.log(`\n${category.toUpperCase()}:`);
        console.log(`  ✅ Passed: ${result.passed}`);
        console.log(`  ❌ Failed: ${result.failed}`);
        console.log(`  ⚠️  Warnings: ${result.warnings}`);
    });
    
    console.log('\n' + '='.repeat(60));
    console.log(`TOTAL RESULTS:`);
    console.log(`  ✅ Passed: ${totalPassed}`);
    console.log(`  ❌ Failed: ${totalFailed}`);
    console.log(`  ⚠️  Warnings: ${totalWarnings}`);
    
    const totalChecks = totalPassed + totalFailed + totalWarnings;
    const successRate = totalChecks > 0 ? ((totalPassed / totalChecks) * 100).toFixed(1) : 0;
    
    console.log(`\n📊 SUCCESS RATE: ${successRate}%`);
    
    if (totalFailed === 0 && totalWarnings === 0) {
        console.log('\n🎉 AUDIT RESULT: EXCELLENT - Ready for production deployment!');
    } else if (totalFailed === 0) {
        console.log('\n✅ AUDIT RESULT: GOOD - Minor warnings, but ready for deployment');
    } else if (totalFailed <= 2) {
        console.log('\n⚠️  AUDIT RESULT: FAIR - Some issues need attention before deployment');
    } else {
        console.log('\n❌ AUDIT RESULT: POOR - Critical issues must be fixed before deployment');
    }
    
    // Save detailed report
    const reportPath = 'SECURITY_AUDIT_REPORT.json';
    fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    
    return { totalFailed, totalWarnings, successRate };
}

// Main Audit Execution
async function runComprehensiveAudit() {
    try {
        // Security Audits
        auditEnvironmentFiles();
        auditDependencies();
        auditCodeSecurity();
        
        // Quality Audits
        auditCodeQuality();
        
        // Functionality Audits
        auditPaymentMethods();
        auditVerificationMethods();
        
        // Deployment Audits
        auditDeploymentReadiness();
        
        // Generate Report
        const results = generateAuditReport();
        
        // Exit with appropriate code
        process.exit(results.totalFailed > 0 ? 1 : 0);
        
    } catch (error) {
        console.error('\n💥 Audit failed with error:', error.message);
        process.exit(1);
    }
}

// Run the audit
runComprehensiveAudit();
