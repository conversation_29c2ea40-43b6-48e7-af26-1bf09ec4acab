#!/bin/bash

# 🚀 SIMPLE AAPANEL DEPLOYMENT FOR AMAZINGPAY
# Run this script in your aaPanel directory: /www/wwwroot/Amazingpayflow

set -e

# 🎯 CONFIGURATION
DOMAIN="amazingpayme.com"
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"
NODE_PORT="3002"

# 🎨 COLORS
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${CYAN}🚀 AmazingPay aaPanel Simple Deployment${NC}"
echo -e "${CYAN}=====================================${NC}"
echo ""
echo -e "${BLUE}Domain:${NC} $DOMAIN"
echo -e "${BLUE}Database:${NC} $DB_NAME"
echo -e "${BLUE}Port:${NC} $NODE_PORT"
echo -e "${BLUE}Directory:${NC} $(pwd)"
echo ""

# 📦 STEP 1: INSTALL DEPENDENCIES
echo -e "${YELLOW}📦 Step 1: Installing dependencies...${NC}"
if [ -f "package.json" ]; then
    npm ci --production
    echo -e "${GREEN}✅ Dependencies installed${NC}"
else
    echo -e "${RED}❌ package.json not found${NC}"
    exit 1
fi
echo ""

# 🗄️ STEP 2: SETUP DATABASE
echo -e "${YELLOW}🗄️ Step 2: Setting up database...${NC}"

# Check if PostgreSQL is running
if systemctl is-active --quiet postgresql; then
    echo -e "${GREEN}✅ PostgreSQL is running${NC}"
else
    echo -e "${YELLOW}⚠️ Starting PostgreSQL...${NC}"
    systemctl start postgresql || echo "PostgreSQL start failed"
fi

# Setup database
sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" 2>/dev/null || echo "Password already set"
sudo -u postgres psql -c "CREATE DATABASE \"$DB_NAME\";" 2>/dev/null || echo "Database already exists"

echo -e "${GREEN}✅ Database setup completed${NC}"
echo ""

# 🔨 STEP 3: BUILD APPLICATION
echo -e "${YELLOW}🔨 Step 3: Building application...${NC}"

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# Build application
npm run build

echo -e "${GREEN}✅ Application built successfully${NC}"
echo ""

# 🚀 STEP 4: START APPLICATION
echo -e "${YELLOW}🚀 Step 4: Starting application...${NC}"

# Install PM2 if not present
if ! command -v pm2 &> /dev/null; then
    npm install -g pm2
    echo -e "${GREEN}✅ PM2 installed${NC}"
fi

# Stop existing processes
pm2 stop all 2>/dev/null || echo "No existing PM2 processes"
pm2 delete all 2>/dev/null || echo "No processes to delete"

# Start application
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

echo -e "${GREEN}✅ Application started with PM2${NC}"
echo ""

# 🔒 STEP 5: FIX SECURITY HEADERS
echo -e "${YELLOW}🔒 Step 5: Fixing security headers...${NC}"

if [ -f "scripts/complete-security-fix.sh" ]; then
    chmod +x scripts/complete-security-fix.sh
    ./scripts/complete-security-fix.sh
    echo -e "${GREEN}✅ Security headers fixed${NC}"
else
    echo -e "${YELLOW}⚠️ Security fix script not found, skipping...${NC}"
fi
echo ""

# 🧪 STEP 6: TEST DEPLOYMENT
echo -e "${YELLOW}🧪 Step 6: Testing deployment...${NC}"

# Wait for application to start
sleep 10

# Test local health endpoint
if curl -f http://localhost:$NODE_PORT/api/health &> /dev/null; then
    echo -e "${GREEN}✅ Local health check passed${NC}"
else
    echo -e "${RED}❌ Local health check failed${NC}"
fi

# Test domain (if accessible)
if curl -f https://$DOMAIN/api/health &> /dev/null; then
    echo -e "${GREEN}✅ Domain health check passed${NC}"
else
    echo -e "${YELLOW}⚠️ Domain health check failed (SSL may need setup)${NC}"
fi

echo ""

# 📋 STEP 7: DEPLOYMENT SUMMARY
echo -e "${YELLOW}📋 Deployment Summary${NC}"
echo ""
echo -e "${CYAN}✅ COMPLETED STEPS:${NC}"
echo "  📦 Dependencies installed"
echo "  🗄️ Database configured"
echo "  🔨 Application built"
echo "  🚀 PM2 process started"
echo "  🔒 Security headers configured"
echo ""

echo -e "${CYAN}🔧 APPLICATION STATUS:${NC}"
echo "  🌐 Domain: https://$DOMAIN"
echo "  🔗 Health Check: https://$DOMAIN/api/health"
echo "  📊 API Docs: https://$DOMAIN/api/docs"
echo "  🔌 Local Port: $NODE_PORT"
echo ""

echo -e "${CYAN}📊 MANAGEMENT COMMANDS:${NC}"
echo "  📈 Check Status: pm2 status"
echo "  📝 View Logs: pm2 logs"
echo "  🔄 Restart App: pm2 restart all"
echo "  🛑 Stop App: pm2 stop all"
echo ""

echo -e "${CYAN}🌐 AAPANEL CONFIGURATION:${NC}"
echo "  1. Go to aaPanel → Website → Add Site"
echo "  2. Domain: $DOMAIN"
echo "  3. Root Directory: $(pwd)/public"
echo "  4. Enable SSL certificate"
echo "  5. Configure reverse proxy to localhost:$NODE_PORT"
echo ""

echo -e "${CYAN}🔒 SECURITY VALIDATION:${NC}"
if [ -f "scripts/pre-launch-security-validation.sh" ]; then
    echo "  Run: ./scripts/pre-launch-security-validation.sh https://$DOMAIN"
else
    echo "  Manual check: curl -I https://$DOMAIN"
fi
echo ""

echo -e "${GREEN}🎉 aaPanel deployment completed successfully!${NC}"
echo -e "${BLUE}Your AmazingPay application is now running on aaPanel${NC}"
echo ""

# 📞 TROUBLESHOOTING
echo -e "${YELLOW}📞 Troubleshooting:${NC}"
echo ""
echo -e "${CYAN}If you encounter issues:${NC}"
echo "  🔍 Check PM2 logs: pm2 logs"
echo "  🗄️ Test database: sudo -u postgres psql -d $DB_NAME -c \"SELECT 1;\""
echo "  🌐 Check Nginx: systemctl status nginx"
echo "  🔧 Restart services: systemctl restart nginx && pm2 restart all"
echo ""

echo -e "${CYAN}Next steps in aaPanel:${NC}"
echo "  1. Configure domain and SSL in aaPanel dashboard"
echo "  2. Set up reverse proxy to port $NODE_PORT"
echo "  3. Test all application endpoints"
echo "  4. Configure monitoring and backups"
echo ""

echo -e "${YELLOW}⚡ Deployment completed! Configure aaPanel website settings to complete setup.${NC}"
