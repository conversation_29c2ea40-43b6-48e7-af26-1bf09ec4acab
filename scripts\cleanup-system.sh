#!/bin/bash

# AmazingPay Flow - System Cleanup Script
# This script cleans up disk space and optimizes the system

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}🧹 AmazingPay Flow - System Cleanup${NC}"
    echo -e "${BLUE}==================================\n${NC}"
}

print_status() {
    echo -e "${BLUE}📦 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check disk space
check_disk_space() {
    DISK_USAGE=$(df . | awk 'NR==2 {print $5}' | sed 's/%//')
    echo "Current disk usage: $DISK_USAGE%"
    return $DISK_USAGE
}

# Function to clean npm cache
clean_npm_cache() {
    print_status "Cleaning npm cache..."
    npm cache clean --force > /dev/null 2>&1 || true
    print_success "npm cache cleaned"
}

# Function to clean node_modules in subdirectories
clean_node_modules() {
    print_status "Cleaning unnecessary node_modules..."
    
    # Find and remove node_modules in subdirectories (keep main one)
    find . -name "node_modules" -type d -not -path "./node_modules" -exec rm -rf {} + 2>/dev/null || true
    
    print_success "Unnecessary node_modules cleaned"
}

# Function to clean build artifacts
clean_build_artifacts() {
    print_status "Cleaning build artifacts..."
    
    # Clean TypeScript build cache
    rm -rf .tsbuildinfo 2>/dev/null || true
    
    # Clean temporary files
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "temp_*" -delete 2>/dev/null || true
    
    print_success "Build artifacts cleaned"
}

# Function to clean logs
clean_old_logs() {
    print_status "Cleaning old logs..."
    
    if [ -d "logs" ]; then
        # Keep only last 7 days of logs
        find logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
        find logs -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
    fi
    
    print_success "Old logs cleaned"
}

# Function to clean old backups
clean_old_backups() {
    print_status "Cleaning old backups..."
    
    if [ -d "backups" ]; then
        # Keep only last 30 days of backups
        find backups -name "*.gz" -mtime +30 -delete 2>/dev/null || true
        find backups -name "*.tar.gz" -mtime +30 -delete 2>/dev/null || true
        find backups -name "*.sql" -mtime +30 -delete 2>/dev/null || true
    fi
    
    print_success "Old backups cleaned"
}

# Function to clean git objects
clean_git_objects() {
    print_status "Cleaning git objects..."
    
    if [ -d ".git" ]; then
        git gc --prune=now --aggressive > /dev/null 2>&1 || true
        git remote prune origin > /dev/null 2>&1 || true
    fi
    
    print_success "Git objects cleaned"
}

# Function to clean system temp files
clean_system_temp() {
    print_status "Cleaning system temp files..."
    
    # Clean Windows temp files if on Windows
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
        rm -rf /tmp/* 2>/dev/null || true
    fi
    
    # Clean common temp directories
    rm -rf .tmp 2>/dev/null || true
    rm -rf tmp 2>/dev/null || true
    
    print_success "System temp files cleaned"
}

# Function to optimize package.json
optimize_packages() {
    print_status "Optimizing packages..."
    
    # Remove unused packages (this would require manual review)
    # For now, just clean the package-lock
    if [ -f "package-lock.json" ]; then
        npm ci --production=false > /dev/null 2>&1 || true
    fi
    
    print_success "Packages optimized"
}

# Function to show disk space savings
show_savings() {
    NEW_USAGE=$(df . | awk 'NR==2 {print $5}' | sed 's/%//')
    SAVED=$((DISK_USAGE - NEW_USAGE))
    
    echo ""
    print_success "🎉 Cleanup completed!"
    echo "📊 Disk space summary:"
    echo "   Before: $DISK_USAGE%"
    echo "   After:  $NEW_USAGE%"
    echo "   Saved:  $SAVED%"
    echo ""
}

# Main cleanup process
main() {
    print_header
    
    echo "Starting system cleanup..."
    check_disk_space
    DISK_USAGE=$?
    echo ""
    
    clean_npm_cache
    clean_node_modules
    clean_build_artifacts
    clean_old_logs
    clean_old_backups
    clean_git_objects
    clean_system_temp
    optimize_packages
    
    show_savings
    
    if [ $NEW_USAGE -lt 80 ]; then
        print_success "✅ Disk space is now healthy!"
    elif [ $NEW_USAGE -lt 90 ]; then
        print_warning "⚠️  Disk space is acceptable but monitor closely"
    else
        print_error "❌ Disk space is still critical - manual cleanup needed"
    fi
}

# Run cleanup
main "$@"
