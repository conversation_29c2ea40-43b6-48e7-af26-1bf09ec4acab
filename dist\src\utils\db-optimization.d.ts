/**
 * Database Query Optimization Utility
 * Re-exports from shared DatabaseUtils to eliminate duplication
 */
import { PrismaClient } from "@prisma/client";
/**
 * Query performance metrics
 */
interface QueryMetrics {
    query: string;
    params: Record<string, string>;
    duration: number;
    timestamp: Date;
}
/**
 * Log a slow query
 * @param metrics Query metrics
 */
export declare const logSlowQuery: (metrics: QueryMetrics) => any;
/**
 * Get recent slow queries
 * @returns Recent slow queries
 */
export declare const getRecentSlowQueries: boolean;
/**
 * Clear recent slow queries
 */
export declare const clearRecentSlowQueries: boolean;
/**
 * Set up query performance monitoring
 * @param prismaClient Prisma client instance
 */
export declare const setupQueryPerformanceMonitoring: (prismaClient?: PrismaClient) => any;
/**
 * Optimize a findMany query by adding pagination and limiting fields
 * @param model Model name
 * @param args Query arguments
 * @param defaultPageSize Default page size
 * @param maxPageSize Maximum page size
 * @returns Optimized query arguments
 */
export declare const optimizeFindManyQuery: (model: string) => any;
/**
 * Execute a query with timeout and retry
 * @param queryFn Function that executes the query
 * @param timeout Timeout in milliseconds
 * @param retries Number of retries
 * @returns Query result
 */
export declare const executeQueryWithTimeoutAndRetry: any;
/**
 * Initialize database optimization
 */
export declare const initializeDatabaseOptimization: boolean;
declare const _default: {
    logSlowQuery: (metrics: QueryMetrics) => any;
    getRecentSlowQueries: boolean;
    clearRecentSlowQueries: boolean;
    setupQueryPerformanceMonitoring: (prismaClient?: PrismaClient) => any;
    optimizeFindManyQuery: (model: string) => any;
    executeQueryWithTimeoutAndRetry: any;
    initializeDatabaseOptimization: boolean;
};
export default _default;
//# sourceMappingURL=db-optimization.d.ts.map