#!/usr/bin/env node

/**
 * Production Build Script for AmazingPay Flow
 * 
 * This script handles the complete production build process:
 * 1. Environment validation
 * 2. Dependency installation
 * 3. Database setup
 * 4. TypeScript compilation
 * 5. Build verification
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

console.log(chalk.blue.bold('🚀 AmazingPay Flow - Production Build'));
console.log(chalk.blue('=' .repeat(50)));

/**
 * Execute command with error handling
 */
function executeCommand(command, description) {
    console.log(chalk.yellow(`\n📦 ${description}...`));
    try {
        execSync(command, { stdio: 'inherit' });
        console.log(chalk.green(`✅ ${description} completed successfully`));
        return true;
    } catch (error) {
        console.error(chalk.red(`❌ ${description} failed:`));
        console.error(chalk.red(error.message));
        return false;
    }
}

/**
 * Check if file exists
 */
function checkFile(filePath, description) {
    if (fs.existsSync(filePath)) {
        console.log(chalk.green(`✅ ${description} found`));
        return true;
    } else {
        console.log(chalk.red(`❌ ${description} not found: ${filePath}`));
        return false;
    }
}

/**
 * Main build process
 */
async function buildProduction() {
    console.log(chalk.cyan('\n🔍 Step 1: Environment Validation'));
    
    // Check required files
    const requiredFiles = [
        { path: 'package.json', desc: 'Package configuration' },
        { path: 'tsconfig.json', desc: 'TypeScript configuration' },
        { path: 'prisma/schema.prisma', desc: 'Prisma schema' },
        { path: 'src/index.ts', desc: 'Application entry point' }
    ];
    
    let allFilesExist = true;
    for (const file of requiredFiles) {
        if (!checkFile(file.path, file.desc)) {
            allFilesExist = false;
        }
    }
    
    if (!allFilesExist) {
        console.error(chalk.red('\n❌ Missing required files. Build aborted.'));
        process.exit(1);
    }
    
    console.log(chalk.cyan('\n📦 Step 2: Clean Previous Build'));
    if (fs.existsSync('dist')) {
        executeCommand('rm -rf dist', 'Cleaning previous build');
    }
    
    console.log(chalk.cyan('\n📦 Step 3: Install Dependencies'));
    if (!executeCommand('npm ci --production=false', 'Installing all dependencies')) {
        process.exit(1);
    }
    
    console.log(chalk.cyan('\n🗄️ Step 4: Database Setup'));
    if (!executeCommand('npm run prisma:generate', 'Generating Prisma client')) {
        process.exit(1);
    }
    
    console.log(chalk.cyan('\n🔨 Step 5: TypeScript Compilation'));
    if (!executeCommand('npm run build', 'Compiling TypeScript')) {
        process.exit(1);
    }
    
    console.log(chalk.cyan('\n✅ Step 6: Build Verification'));
    
    // Check build output
    const buildFiles = [
        { path: 'dist/index.js', desc: 'Main application file' },
        { path: 'dist/routes', desc: 'Routes directory' },
        { path: 'dist/controllers', desc: 'Controllers directory' },
        { path: 'dist/services', desc: 'Services directory' }
    ];
    
    let buildValid = true;
    for (const file of buildFiles) {
        if (!checkFile(file.path, file.desc)) {
            buildValid = false;
        }
    }
    
    if (!buildValid) {
        console.error(chalk.red('\n❌ Build verification failed. Some files are missing.'));
        process.exit(1);
    }
    
    // Check build size
    const stats = fs.statSync('dist');
    console.log(chalk.green(`📊 Build size: ${(stats.size / 1024).toFixed(2)} KB`));
    
    console.log(chalk.green.bold('\n🎉 Production build completed successfully!'));
    console.log(chalk.cyan('\n📋 Next steps:'));
    console.log(chalk.white('1. Configure production environment variables'));
    console.log(chalk.white('2. Set up production database'));
    console.log(chalk.white('3. Deploy to production server'));
    console.log(chalk.white('4. Start application with: npm start'));
    
    console.log(chalk.blue('\n🚀 Ready for production deployment!'));
}

// Run build process
buildProduction().catch(error => {
    console.error(chalk.red('\n💥 Build process failed:'));
    console.error(chalk.red(error.message));
    process.exit(1);
});
