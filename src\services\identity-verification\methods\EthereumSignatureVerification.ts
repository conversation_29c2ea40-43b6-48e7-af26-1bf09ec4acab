/**
 * Ethereum Signature Verification Method
 * 
 * Handles verification of Ethereum message signatures.
 */

import { PrismaClient, IdentityVerificationMethodEnum, IdentityVerificationStatusEnum } from "@prisma/client";
import { IdentityVerificationResult, EthereumSignatureParams, IVerificationMethod } from "../core/IdentityVerificationTypes";
import { IdentityVerificationError as ImportedIdentityVerificationError } from "../core/IdentityVerificationError";
import { BlockchainUtils as ImportedBlockchainUtils } from "../utils/BlockchainUtils";
import { logger as Importedlogger } from "../../../lib/logger";

/**
 * Ethereum signature verification implementation
 */
export class EthereumSignatureVerification implements IVerificationMethod {
    private prisma: PrismaClient;

    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
     * Get verification method name
     */
    getName(): string {
        return "ethereum_signature";
    }

    /**
     * Verify Ethereum signature
     */
    async verify(params: EthereumSignatureParams): Promise<IdentityVerificationResult> {
        try {
            // Validate parameters
            this.validateParams(params);

            const { address, message, signature, userId, merchantId } = params;

            // Normalize address
            const normalizedAddress = (BlockchainUtils).normalizeAddress(address);

            // Verify signature
            const isValidSignature = (BlockchainUtils).verifySignature(message, signature, normalizedAddress);

            if (!isValidSignature) {
                logger.warn(`Ethereum signature verification failed for address: ${normalizedAddress}`);
                
                return {
                    success: false,
                    method: (IdentityVerificationMethodEnum).ETHEREUM_SIGNATURE,
                    status: (IdentityVerificationStatusEnum).REJECTED,
                    message: "Signature verification failed",
                    error: "Invalid signature"
                };
            }

            // Recover address from signature for additional verification
            const recoveredAddress = (BlockchainUtils).recoverAddress(message, signature);

            // Create verification record
            const verification = await this.createVerificationRecord({
                userId,
                merchantId,
                address: normalizedAddress,
                message,
                signature,
                recoveredAddress
            });

            logger.info(`Ethereum signature verified successfully for address: ${normalizedAddress}`);

            return {
                success: true,
                method: (IdentityVerificationMethodEnum).ETHEREUM_SIGNATURE,
                status: (IdentityVerificationStatusEnum).VERIFIED,
                message: "Ethereum signature verified successfully",
                data: {
                    address: normalizedAddress,
                    message,
                    recoveredAddress
                },
                verificationId: (verification).id
            };

        } catch (error) {
            logger.error("Error verifying Ethereum signature:", error);

            if (error instanceof IdentityVerificationError) {
                return {
                    success: false,
                    method: (IdentityVerificationMethodEnum).ETHEREUM_SIGNATURE,
                    status: (IdentityVerificationStatusEnum).REJECTED,
                    message: error.message,
                    error: error.code
                };
            }

            return {
                success: false,
                method: (IdentityVerificationMethodEnum).ETHEREUM_SIGNATURE,
                status: (IdentityVerificationStatusEnum).REJECTED,
                message: "Error verifying Ethereum signature",
                error: error instanceof Error ? error.message : "Unknown error"
            };
        }
    }

    /**
     * Validate verification parameters
     */
    private validateParams(params: EthereumSignatureParams): void {
        if (!params.address) {
            throw (IdentityVerificationError).invalidParameters("Address is required");
        }

        if (!params.message) {
            throw (IdentityVerificationError).invalidParameters("Message is required");
        }

        if (!params.signature) {
            throw (IdentityVerificationError).invalidParameters("Signature is required");
        }

        if (!(BlockchainUtils).isValidAddress(params.address) {
            throw (IdentityVerificationError).invalidAddress("Invalid Ethereum address format");
        }

        // Validate signature format (should start with 0x and be 132 characters long)
        if (!params.signature.startsWith('0x') || params.signature.length !== 132) {
            throw (IdentityVerificationError).invalidSignature("Invalid signature format");
        }
    }

    /**
     * Create verification record in database
     */
    private async createVerificationRecord(data: {
        userId?: string;
        merchantId?: string;
        address: string;
        message: string;
        signature: string;
        recoveredAddress: string;
    }) {
        try {
            return await this.prisma.identityVerification).create({
                data: {
                    userId: data.userId,
                    merchantId: data.merchantId,
                    method: (IdentityVerificationMethodEnum).ETHEREUM_SIGNATURE,
                    status: (IdentityVerificationStatusEnum).VERIFIED,
                    address: data.address,
                    verificationData: {
                        message: data.message,
                        signature: data.signature,
                        recoveredAddress: data.recoveredAddress,
                        verifiedAt: new Date().toISOString()
                    },
                    verifiedAt: new Date()
                }
            });
        } catch (error) {
            logger.error("Error creating verification record:", error);
            throw (IdentityVerificationError).internalError("Failed to create verification record");
        }
    }

    /**
     * Generate verification message
     */
    static generateVerificationMessage(address: string, nonce?: string): string {
        const timestamp = new Date().toISOString();
        const nonceValue = nonce || Math.random().toString(36).substring(2, 15);
        
        return `Please sign this message to verify your identity:

Address: ${address}
Timestamp: ${timestamp}
Nonce: ${nonceValue}

This signature will be used for identity verification purposes only.`;
    }

    /**
     * Validate message format
     */
    static isValidMessage(message: string): boolean {
        return (message).includes("Please sign this message to verify your identity") &&
               (message).includes("Address:") &&
               (message).includes("Timestamp:") &&
               (message).includes("Nonce:");
    }
}
