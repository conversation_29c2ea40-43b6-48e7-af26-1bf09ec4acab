#!/usr/bin/env node

/**
 * AmazingPay Flow - Fix Type Safety Script
 * 
 * This script fixes all type safety issues including:
 * - Replacing 'any' types with proper types
 * - Fixing 'unknown' types that should be specific types
 * - Adding proper Express middleware types
 * - Ensuring all variables have correct types
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 AmazingPay Flow - Fixing Type Safety Issues');
console.log('='.repeat(60));

let fixedFiles = [];
let totalIssues = 0;
let fixedIssues = 0;

// Type replacements for better type safety
const typeReplacements = {
    // Express middleware types
    'export const authenticate: unknown =': 'export const authenticate: RequestHandler =',
    'export const authenticateJWT: unknown =': 'export const authenticateJWT: RequestHandler =',
    'export const isAdmin: unknown =': 'export const isAdmin: RequestHandler =',
    'export const isMerchantOrAdmin: unknown =': 'export const isMerchantOrAdmin: RequestHandler =',
    'export const noSniff: unknown =': 'export const noSniff: RequestHandler =',
    'export const frameGuard: unknown =': 'export const frameGuard: RequestHandler =',
    'export const xssFilter: unknown =': 'export const xssFilter: RequestHandler =',
    'export const referrerPolicy: unknown =': 'export const referrerPolicy: RequestHandler =',
    'export const permissionsPolicy: unknown =': 'export const permissionsPolicy: RequestHandler =',
    'export const cacheControl: unknown =': 'export const cacheControl: RequestHandler =',
    'export const sqlInjectionProtection: unknown =': 'export const sqlInjectionProtection: RequestHandler =',
    'export const environmentBanner: unknown =': 'export const environmentBanner: RequestHandler =',
    'export const requestValidation: unknown =': 'export const requestValidation: RequestHandler =',
    'export const securityMiddleware: unknown =': 'export const securityMiddleware: RequestHandler[] =',
    'export const contentSecurityPolicy: unknown =': 'export const contentSecurityPolicy: RequestHandler =',
    'export const csrfProtection: unknown =': 'export const csrfProtection: RequestHandler =',
    'export const hsts: unknown =': 'export const hsts: RequestHandler =',
    
    // Variable declarations that should be typed
    'const logger: unknown =': 'const logger =',
    'const authHeader: unknown =': 'const authHeader =',
    'const token: unknown =': 'const token =',
    'const decoded: unknown =': 'const decoded =',
    'const sqlInjectionPattern: unknown =': 'const sqlInjectionPattern =',
    'const queryParams: unknown =': 'const queryParams =',
    'const contentLength: unknown =': 'const contentLength =',
    
    // Function return types
    'export const authorize: unknown =': 'export const authorize =',
    'export const auth: unknown =': 'export const auth =',
    
    // Any types to proper types
    'req: any': 'req: Request',
    'res: any': 'res: Response',
    'next: any': 'next: NextFunction',
    'error: any': 'error: Error',
    'err: any': 'err: Error',
    'data: any': 'data: unknown',
    'payload: any': 'payload: Record<string, unknown>',
    'body: any': 'body: Record<string, unknown>',
    'params: any': 'params: Record<string, string>',
    'query: any': 'query: Record<string, string | string[]>',
    'headers: any': 'headers: Record<string, string>',
    'config: any': 'config: Record<string, unknown>',
    'options: any': 'options: Record<string, unknown>',
    'metadata: any': 'metadata: Record<string, unknown>',
    'result: any': 'result: unknown',
    'response: any': 'response: unknown',
    
    // Generic types
    'Array<any>': 'Array<unknown>',
    'Promise<any>': 'Promise<unknown>',
    'Record<string, any>': 'Record<string, unknown>',
    'Record<any, any>': 'Record<string, unknown>',
    
    // Type assertions
    'as any': 'as unknown',
    '<any>': '<unknown>',
    
    // Array types
    ': any[]': ': unknown[]',
    'any[]': 'unknown[]',
    
    // Function parameters
    '(any)': '(unknown)',
    '(...args: any[])': '(...args: unknown[])',
    
    // Catch blocks
    'catch (error: any)': 'catch (error: Error)',
    'catch (err: any)': 'catch (err: Error)',
    'catch (e: any)': 'catch (e: Error)',
};

// Import statements to add
const requiredImports = {
    'RequestHandler': "import { Request, Response, NextFunction, RequestHandler } from 'express';",
    'Request': "import { Request, Response, NextFunction } from 'express';",
    'Response': "import { Request, Response, NextFunction } from 'express';",
    'NextFunction': "import { Request, Response, NextFunction } from 'express';",
    'PrismaClient': "import { PrismaClient } from '@prisma/client';",
};

function findAllTypeScriptFiles(dir) {
    const files = [];
    
    function scanDirectory(currentDir) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory() && !['node_modules', '.git', 'dist', 'coverage'].includes(item)) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                files.push(fullPath);
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

function countTypeIssues(content) {
    let issues = 0;
    
    // Count any types
    const anyMatches = content.match(/:\s*any\b/g) || [];
    issues += anyMatches.length;
    
    // Count problematic unknown types
    const unknownMiddleware = content.match(/export const \w+: unknown =/g) || [];
    issues += unknownMiddleware.length;
    
    // Count any arrays
    const anyArrays = content.match(/any\[\]/g) || [];
    issues += anyArrays.length;
    
    // Count any generics
    const anyGenerics = content.match(/Array<any>|Promise<any>|Record<[^,>]*,\s*any>/g) || [];
    issues += anyGenerics.length;
    
    return issues;
}

function addRequiredImports(content, filePath) {
    const needsRequestHandler = content.includes('RequestHandler');
    const needsExpress = content.includes('Request') || content.includes('Response') || content.includes('NextFunction');
    const needsPrisma = content.includes('PrismaClient');
    
    let imports = [];
    
    if (needsRequestHandler && !content.includes('import { Request, Response, NextFunction, RequestHandler }')) {
        imports.push("import { Request, Response, NextFunction, RequestHandler } from 'express';");
    } else if (needsExpress && !content.includes('import { Request, Response, NextFunction }')) {
        imports.push("import { Request, Response, NextFunction } from 'express';");
    }
    
    if (needsPrisma && !content.includes('import { PrismaClient }')) {
        imports.push("import { PrismaClient } from '@prisma/client';");
    }
    
    if (imports.length > 0) {
        // Find the first import statement or add at the top
        const firstImportMatch = content.match(/^import .+;$/m);
        if (firstImportMatch) {
            const firstImportIndex = content.indexOf(firstImportMatch[0]);
            content = content.slice(0, firstImportIndex) + 
                     imports.join('\n') + '\n' + 
                     content.slice(firstImportIndex);
        } else {
            // Add at the top after any comments
            const lines = content.split('\n');
            let insertIndex = 0;
            
            // Skip initial comments
            while (insertIndex < lines.length && 
                   (lines[insertIndex].startsWith('//') || 
                    lines[insertIndex].startsWith('/*') || 
                    lines[insertIndex].trim() === '')) {
                insertIndex++;
            }
            
            lines.splice(insertIndex, 0, ...imports, '');
            content = lines.join('\n');
        }
    }
    
    return content;
}

function fixTypeIssues(filePath) {
    console.log(`\n🔍 Processing: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    const originalIssueCount = countTypeIssues(content);
    
    if (originalIssueCount === 0) {
        console.log(`  ✅ No type issues found`);
        return;
    }
    
    console.log(`  📊 Found ${originalIssueCount} type issues`);
    totalIssues += originalIssueCount;
    
    // Apply type replacements
    for (const [pattern, replacement] of Object.entries(typeReplacements)) {
        const regex = new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        content = content.replace(regex, replacement);
    }
    
    // Add required imports
    content = addRequiredImports(content, filePath);
    
    // Additional pattern fixes
    
    // Fix function return types
    content = content.replace(/\):\s*any\s*{/g, '): unknown {');
    content = content.replace(/\):\s*any\s*=>/g, '): unknown =>');
    
    // Fix variable declarations
    content = content.replace(/let\s+([^:]+):\s*any/g, 'let $1: unknown');
    content = content.replace(/const\s+([^:]+):\s*any/g, 'const $1: unknown');
    
    // Fix interface properties
    content = content.replace(/(\w+):\s*any;/g, '$1: unknown;');
    content = content.replace(/(\w+):\s*any,/g, '$1: unknown,');
    
    const finalIssueCount = countTypeIssues(content);
    const fixedCount = originalIssueCount - finalIssueCount;
    
    if (fixedCount > 0) {
        fs.writeFileSync(filePath, content);
        fixedFiles.push({
            file: filePath,
            originalCount: originalIssueCount,
            fixedCount: fixedCount,
            remainingCount: finalIssueCount
        });
        fixedIssues += fixedCount;
        
        console.log(`  ✅ Fixed ${fixedCount} type issues`);
        if (finalIssueCount > 0) {
            console.log(`  ⚠️  ${finalIssueCount} issues remaining (may need manual review)`);
        }
    } else {
        console.log(`  ⚠️  No type issues could be automatically fixed`);
    }
}

function generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 TYPE SAFETY REPORT');
    console.log('='.repeat(60));
    
    console.log(`\n📊 Summary:`);
    console.log(`  Total files processed: ${fixedFiles.length}`);
    console.log(`  Total type issues found: ${totalIssues}`);
    console.log(`  Total type issues fixed: ${fixedIssues}`);
    console.log(`  Remaining type issues: ${totalIssues - fixedIssues}`);
    
    if (fixedFiles.length > 0) {
        console.log(`\n📁 Files modified:`);
        fixedFiles.forEach(file => {
            console.log(`  ${file.file}:`);
            console.log(`    Fixed: ${file.fixedCount}, Remaining: ${file.remainingCount}`);
        });
    }
    
    const successRate = totalIssues > 0 ? ((fixedIssues / totalIssues) * 100).toFixed(1) : 100;
    console.log(`\n🎯 Success Rate: ${successRate}%`);
    
    if (totalIssues - fixedIssues === 0) {
        console.log('\n🎉 SUCCESS: All type safety issues have been fixed!');
    } else {
        console.log('\n⚠️  Some type issues remain and may need manual review');
    }
    
    // Save detailed report
    const report = {
        summary: {
            totalFiles: fixedFiles.length,
            totalIssues,
            fixedIssues,
            remainingIssues: totalIssues - fixedIssues,
            successRate: parseFloat(successRate)
        },
        files: fixedFiles,
        timestamp: new Date().toISOString()
    };
    
    fs.writeFileSync('TYPE_SAFETY_REPORT.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved to: TYPE_SAFETY_REPORT.json');
}

function main() {
    try {
        console.log('🔍 Scanning for TypeScript files...');
        const tsFiles = findAllTypeScriptFiles('src');
        console.log(`📁 Found ${tsFiles.length} TypeScript files`);
        
        console.log('\n🔧 Processing files...');
        tsFiles.forEach(fixTypeIssues);
        
        generateReport();
        
        // Try to compile to check for errors
        console.log('\n🔍 Checking TypeScript compilation...');
        try {
            execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
            console.log('✅ TypeScript compilation successful');
        } catch (error) {
            console.log('⚠️  TypeScript compilation has errors - manual review may be needed');
        }
        
        console.log('\n🎯 Type safety fixing completed!');
        
    } catch (error) {
        console.error('\n💥 Error during type safety fixing:', error.message);
        process.exit(1);
    }
}

main();
