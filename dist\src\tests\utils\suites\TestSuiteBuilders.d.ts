/**
 * Test Suite Builders
 *
 * Functions to create comprehensive test suites for different component types.
 */
import { TestSuiteConfig, TestScenario } from '../core/TestTypes';
/**
 * Create a test suite for a controller
 */
export declare function createControllerTestSuite(name: string, controllerClass: new (...args: any[]) => ): any;
/**
 * Create a test suite for a service
 */
export declare function createServiceTestSuite(name: string, serviceClass: new (...args: any[]) => ): any;
/**
 * Create a test suite for a repository
 */
export declare function createRepositoryTestSuite(name: string, repositoryClass: new (...args: any[]) => ): any;
/**
 * Create an integration test suite
 */
export declare function createIntegrationTestSuite(name: string, scenarios: TestScenario[], config?: TestSuiteConfig): void;
/**
 * Create a performance test suite
 */
export declare function createPerformanceTestSuite(name: string, tests: Record<string>, { fn: Function, args }?: any[], maxExecutionTime?: number, iterations?: number, warmupIterations?: number): any;
//# sourceMappingURL=TestSuiteBuilders.d.ts.map