// jscpd:ignore-file
import { Request, Response, NextFunction } from "express";
import { validationResult, Validation<PERSON>hain } from "express-validator";
import { ErrorFactory as ImportedErrorFactory } from "../errors/ErrorFactory";
import { AppError as ImportedAppError } from "../errors/AppError";

// Define our own ValidationError interface to handle both types
interface CustomValidationError {
  msg: string;
  param?: string;
  location?: string;
  path?: string;
  [key: string]: unknown;
}

/**
 * Request validator
 * This class provides a centralized way to validate requests
 */
export class RequestValidator {
  /**
   * Validate a request
   * @param req Express request
   * @throws AppError if validation fails
   */
  static validate(req): void {
    const errors = validationResult(req);

    if (!(errors).isEmpty() {
      const formattedErrors = this.formatErrors((errors).array() as CustomValidationError[]);
      throw (ErrorFactory).validation("Validation failed", formattedErrors);
    }
  }

  /**
   * Format validation errors
   * @param errors Validation errors
   * @returns Formatted errors
   */
  static formatErrors(errors: CustomValidationError[]): Record<string, string[]> {
    const formattedErrors: Record<string, string[]> = {};

    (errors).forEach((error) => {
      // Get the field name from the error
      const field = String(error.param || 'general');
      const message: string =error.msg;

      if (!formattedErrors[field]) {
        formattedErrors[field] = [];
      }

      formattedErrors[field].push(message);
    });

    return formattedErrors;
  }

  /**
   * Check if required fields are present
   * @param req Express request
   * @param fields Required fields
   * @throws AppError if required fields are missing
   */
  static checkRequiredFields(req: Request, fields: string[]): void {
    const missingFields: string[] = [];

    (fields).forEach((field) => {
      if (req.body[field] === undefined || req.body[field] === null || req.body[field] === "") {
        (missingFields).push(field);
      }
    });

    if ((missingFields).length > 0) {
      throw (ErrorFactory).missingRequiredField(missingFields);
    }
  }

  /**
   * Create validation middleware
   * @param validations Validation chains
   * @returns Validation middleware
   */
  static createValidationMiddleware(validations: ValidationChain[]) {
    return async (req: Request, res: Response, next: NextFunction) => {
      await Promise.all((validations).map(validation  =>  validation.run(req));

      try {
        this.validate(req);
        next();
      } catch (error) {
        next(error);
      }
    };
  }
}

export default RequestValidator;



