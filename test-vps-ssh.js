#!/usr/bin/env node

/**
 * 🔐 VPS SSH CONNECTION TESTER
 * Tests SSH connectivity to VPS for deployment setup
 */

const { exec } = require('child_process');
const fs = require('fs');

// 🎯 CONFIGURATION
const CONFIG = {
    vpsIP: '************',
    sshUser: 'root',
    sshPort: 22,
    testCommands: [
        'whoami',
        'pwd',
        'ls -la /www/wwwroot/',
        'systemctl status nginx',
        'systemctl status postgresql',
        'pm2 list'
    ]
};

// 🎨 COLORS
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

const log = (message, color = 'cyan') => {
    console.log(`${colors[color]}[${new Date().toLocaleTimeString()}] ${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, 'green');
const error = (message) => log(`❌ ${message}`, 'red');
const warning = (message) => log(`⚠️  ${message}`, 'yellow');
const info = (message) => log(`ℹ️  ${message}`, 'blue');

// 🔐 TEST SSH CONNECTION
async function testSSHConnection() {
    log('🔐 Testing SSH Connection to VPS...', 'cyan');
    
    return new Promise((resolve) => {
        const sshCommand = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${CONFIG.sshUser}@${CONFIG.vpsIP} "echo 'SSH connection successful'"`;
        
        exec(sshCommand, (error, stdout, stderr) => {
            if (error) {
                if (error.message.includes('Permission denied')) {
                    error('SSH Permission denied - SSH key not configured');
                    info('You need to:');
                    console.log('  1. Generate SSH key: ssh-keygen -t rsa -b 4096');
                    console.log('  2. Copy to VPS: ssh-copy-id root@************');
                    console.log('  3. Or add your public key to VPS ~/.ssh/authorized_keys');
                } else if (error.message.includes('Connection refused')) {
                    error('SSH Connection refused - VPS may not allow SSH or wrong port');
                } else if (error.message.includes('No route to host')) {
                    error('No route to host - Check VPS IP and network connectivity');
                } else {
                    error(`SSH connection failed: ${error.message}`);
                }
                resolve(false);
            } else {
                success('SSH connection successful!');
                if (stdout) {
                    info(`Response: ${stdout.trim()}`);
                }
                resolve(true);
            }
        });
    });
}

// 🔧 TEST VPS SERVICES
async function testVPSServices() {
    log('🔧 Testing VPS Services...', 'cyan');
    
    if (!await testSSHConnection()) {
        warning('Cannot test VPS services without SSH access');
        return false;
    }
    
    const serviceTests = [
        { service: 'nginx', command: 'systemctl is-active nginx' },
        { service: 'postgresql', command: 'systemctl is-active postgresql' },
        { service: 'pm2', command: 'pm2 ping' }
    ];
    
    let servicesRunning = 0;
    
    for (const test of serviceTests) {
        await new Promise((resolve) => {
            const sshCommand = `ssh -o ConnectTimeout=10 ${CONFIG.sshUser}@${CONFIG.vpsIP} "${test.command}"`;
            
            exec(sshCommand, (error, stdout, stderr) => {
                if (!error && stdout.trim() === 'active') {
                    success(`${test.service} is running`);
                    servicesRunning++;
                } else if (!error && stdout.includes('pong')) {
                    success(`${test.service} is running`);
                    servicesRunning++;
                } else {
                    warning(`${test.service} is not running or not installed`);
                }
                resolve();
            });
        });
    }
    
    return servicesRunning > 0;
}

// 📁 CHECK VPS DIRECTORY STRUCTURE
async function checkVPSDirectories() {
    log('📁 Checking VPS Directory Structure...', 'cyan');
    
    const directories = [
        '/www/wwwroot/amazingpayme.com',
        '/var/log/amazingpay',
        '/var/backups/amazingpay'
    ];
    
    for (const dir of directories) {
        await new Promise((resolve) => {
            const sshCommand = `ssh -o ConnectTimeout=10 ${CONFIG.sshUser}@${CONFIG.vpsIP} "ls -la ${dir}"`;
            
            exec(sshCommand, (error, stdout, stderr) => {
                if (!error) {
                    success(`Directory ${dir} exists`);
                    if (dir.includes('amazingpayme.com') && stdout) {
                        info('Application directory contents:');
                        console.log(stdout.split('\n').slice(0, 5).join('\n'));
                    }
                } else {
                    warning(`Directory ${dir} does not exist yet`);
                }
                resolve();
            });
        });
    }
}

// 🎯 GENERATE SSH SETUP INSTRUCTIONS
function generateSSHInstructions() {
    const instructions = `
# 🔐 SSH SETUP INSTRUCTIONS FOR VPS

## If SSH connection failed, follow these steps:

### Option 1: Generate and Copy SSH Key (Recommended)

\`\`\`bash
# 1. Generate SSH key (if you don't have one)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 2. Copy public key to VPS
ssh-copy-id root@************

# 3. Test connection
ssh root@************
\`\`\`

### Option 2: Manual SSH Key Setup

\`\`\`bash
# 1. Display your public key
cat ~/.ssh/id_rsa.pub

# 2. Login to VPS with password and add the key
ssh root@************
mkdir -p ~/.ssh
echo "YOUR_PUBLIC_KEY_HERE" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
\`\`\`

### Option 3: Use Password Authentication (Less Secure)

If you prefer password authentication:
\`\`\`bash
ssh root@************
# Enter your VPS password when prompted
\`\`\`

## GitHub Secrets Configuration

Once SSH is working, add these secrets to your GitHub repository:

1. Go to GitHub Repository → Settings → Secrets and variables → Actions
2. Add these secrets:

\`\`\`
VPS_HOST=************
VPS_USERNAME=root
VPS_SSH_KEY=[Your private SSH key content]
WEBHOOK_SECRET=[Generate with: openssl rand -hex 32]
\`\`\`

## Testing Deployment

After SSH setup:
\`\`\`bash
# Test SSH connection
node test-vps-ssh.js

# Run VPS deployment
ssh root@************
cd /www/wwwroot/amazingpayme.com
./scripts/vps-deployment.sh
\`\`\`
`;

    fs.writeFileSync('SSH_SETUP_INSTRUCTIONS.md', instructions);
    success('SSH setup instructions saved to SSH_SETUP_INSTRUCTIONS.md');
}

// 🎯 MAIN TEST FUNCTION
async function runSSHTests() {
    log('🔐 Starting VPS SSH Connection Tests...', 'cyan');
    console.log('='.repeat(60));
    
    // Test basic SSH connection
    const sshWorking = await testSSHConnection();
    console.log('');
    
    if (sshWorking) {
        // Test VPS services
        await testVPSServices();
        console.log('');
        
        // Check directory structure
        await checkVPSDirectories();
        console.log('');
        
        success('🎉 SSH connection is working! VPS is ready for deployment.');
    } else {
        warning('⚠️ SSH connection failed. Setup required.');
        generateSSHInstructions();
    }
    
    console.log('='.repeat(60));
    log('📊 SSH TEST SUMMARY', 'cyan');
    
    if (sshWorking) {
        success('✅ SSH connection successful');
        success('✅ Ready to deploy to VPS');
        info('Next steps:');
        console.log('  1. Run: ssh root@************');
        console.log('  2. Upload your code to VPS');
        console.log('  3. Run deployment script');
    } else {
        error('❌ SSH setup required');
        info('Next steps:');
        console.log('  1. Check SSH_SETUP_INSTRUCTIONS.md');
        console.log('  2. Configure SSH key authentication');
        console.log('  3. Test connection again');
    }
}

// Run tests
if (require.main === module) {
    runSSHTests().catch(err => {
        error(`SSH test execution failed: ${err.message}`);
        process.exit(1);
    });
}

module.exports = { runSSHTests, testSSHConnection };
