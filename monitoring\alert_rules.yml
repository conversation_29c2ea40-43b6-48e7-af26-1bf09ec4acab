# Advanced Alert Rules for AmazingPay Flow
# Comprehensive monitoring with business-critical alerts

groups:
  # System Health Alerts
  - name: system_health
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk space is below 10% for {{ $labels.instance }}"

  # Application Health Alerts
  - name: application_health
    rules:
      - alert: ApplicationDown
        expr: up{job="amazingpay-api"} == 0
        for: 1m
        labels:
          severity: critical
          service: application
        annotations:
          summary: "AmazingPay API is down"
          description: "{{ $labels.instance }} has been down for more than 1 minute"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is above 2 seconds"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
          service: application
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for the last 5 minutes"

      - alert: LowThroughput
        expr: rate(http_requests_total[5m]) < 10
        for: 10m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "Low request throughput"
          description: "Request rate is below 10 requests/second for 10 minutes"

  # Database Alerts
  - name: database_health
    rules:
      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "PostgreSQL database is down"
          description: "Database has been unreachable for more than 1 minute"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "High database connections"
          description: "Database connections are above 80% of maximum"

      - alert: DatabaseSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Database slow queries detected"
          description: "Query efficiency is below 10%"

      - alert: DatabaseReplicationLag
        expr: pg_replication_lag_seconds > 60
        for: 2m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Database replication lag"
          description: "Replication lag is above 60 seconds"

  # Redis Alerts
  - name: redis_health
    rules:
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis has been unreachable for more than 1 minute"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis memory usage high"
          description: "Redis memory usage is above 90%"

      - alert: RedisConnectionsHigh
        expr: redis_connected_clients > 1000
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High Redis connections"
          description: "Redis has more than 1000 connected clients"

  # Business Logic Alerts
  - name: business_critical
    rules:
      - alert: HighFraudDetectionRate
        expr: rate(fraud_detections_total{status="flagged"}[5m]) / rate(fraud_detections_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: fraud_detection
        annotations:
          summary: "High fraud detection rate"
          description: "Fraud detection rate is above 10% for the last 5 minutes"

      - alert: IdentityVerificationFailures
        expr: rate(identity_verifications_total{status="failed"}[5m]) > 5
        for: 5m
        labels:
          severity: warning
          service: identity_verification
        annotations:
          summary: "High identity verification failures"
          description: "Identity verification failures exceed 5 per second"

      - alert: PaymentProcessingDown
        expr: rate(payment_transactions_total[5m]) == 0
        for: 2m
        labels:
          severity: critical
          service: payments
        annotations:
          summary: "Payment processing stopped"
          description: "No payment transactions processed for 2 minutes"

      - alert: UnusualTransactionVolume
        expr: rate(payment_transactions_total[5m]) > 100 or rate(payment_transactions_total[5m]) < 1
        for: 10m
        labels:
          severity: warning
          service: payments
        annotations:
          summary: "Unusual transaction volume"
          description: "Transaction volume is outside normal range"

  # Security Alerts
  - name: security
    rules:
      - alert: SuspiciousActivity
        expr: rate(http_requests_total{status="401"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High authentication failures"
          description: "Authentication failure rate exceeds 10 per second"

      - alert: DDoSAttack
        expr: rate(http_requests_total[1m]) > 1000
        for: 1m
        labels:
          severity: critical
          service: security
        annotations:
          summary: "Potential DDoS attack"
          description: "Request rate exceeds 1000 requests per second"

      - alert: UnauthorizedAccess
        expr: rate(http_requests_total{status="403"}[5m]) > 5
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High unauthorized access attempts"
          description: "Unauthorized access attempts exceed 5 per second"

  # Load Balancer Alerts
  - name: load_balancer
    rules:
      - alert: LoadBalancerDown
        expr: up{job="haproxy"} == 0
        for: 1m
        labels:
          severity: critical
          service: load_balancer
        annotations:
          summary: "Load balancer is down"
          description: "HAProxy load balancer is unreachable"

      - alert: BackendServerDown
        expr: haproxy_server_up == 0
        for: 1m
        labels:
          severity: critical
          service: load_balancer
        annotations:
          summary: "Backend server is down"
          description: "Backend server {{ $labels.server }} is down"

      - alert: HighLoadBalancerLatency
        expr: haproxy_server_response_time_average_seconds > 1
        for: 5m
        labels:
          severity: warning
          service: load_balancer
        annotations:
          summary: "High load balancer latency"
          description: "Average response time is above 1 second"
