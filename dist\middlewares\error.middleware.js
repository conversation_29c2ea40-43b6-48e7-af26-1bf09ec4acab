"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppError = void 0;
// jscpd:ignore-file
class AppError extends Error {
    constructor(message, statusCode, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.default = AppError;
exports.AppError = AppError;
