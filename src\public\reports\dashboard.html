<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AmazingPay Reports Dashboard</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .sidebar {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 100;
        padding: 48px 0 0;
        box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
      }
      .sidebar-sticky {
        position: relative;
        top: 0;
        height: calc(100vh - 48px);
        padding-top: 0.5rem;
        overflow-x: hidden;
        overflow-y: auto;
      }
      .main-content {
        padding-top: 48px;
      }
      .card-dashboard {
        transition: transform 0.3s;
      }
      .card-dashboard:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
      .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-dark fixed-top bg-dark flex-md-nowrap p-0 shadow">
      <a class="navbar-brand col-md-3 col-lg-2 mr-0 px-3" href="#">AmazingPay Reports</a>
      <button
        class="navbar-toggler position-absolute d-md-none collapsed"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#sidebarMenu"
        aria-controls="sidebarMenu"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <input
        class="form-control form-control-dark w-100"
        type="text"
        placeholder="Search"
        aria-label="Search"
      />
      <div class="navbar-nav">
        <div class="nav-item text-nowrap">
          <a class="nav-link px-3" href="#">Sign out</a>
        </div>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row">
        <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
          <div class="sidebar-sticky pt-3">
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link active" href="#">
                  <i class="bi bi-speedometer2"></i>
                  Dashboard
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" id="generate-report-link">
                  <i class="bi bi-file-earmark-text"></i>
                  Generate Report
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" id="scheduled-reports-link">
                  <i class="bi bi-calendar-check"></i>
                  Scheduled Reports
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" id="saved-reports-link">
                  <i class="bi bi-archive"></i>
                  Saved Reports
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" id="templates-link">
                  <i class="bi bi-file-earmark-ruled"></i>
                  Report Templates
                </a>
              </li>
            </ul>

            <h6
              class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted"
            >
              <span>Report Categories</span>
            </h6>
            <ul class="nav flex-column mb-2">
              <li class="nav-item">
                <a class="nav-link" href="#" data-report-type="TRANSACTION">
                  <i class="bi bi-credit-card"></i>
                  Transactions
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-report-type="CUSTOMER">
                  <i class="bi bi-people"></i>
                  Customers
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-report-type="PAYMENT_METHOD">
                  <i class="bi bi-wallet2"></i>
                  Payment Methods
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-report-type="SUBSCRIPTION">
                  <i class="bi bi-arrow-repeat"></i>
                  Subscriptions
                </a>
              </li>
            </ul>
          </div>
        </nav>

        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 main-content">
          <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
          >
            <h1 class="h2">Dashboard</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <div class="btn-group mr-2">
                <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
                <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
              </div>
              <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle">
                <i class="bi bi-calendar"></i>
                This week
              </button>
            </div>
          </div>

          <!-- Dashboard Overview -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="card card-dashboard text-white bg-primary">
                <div class="card-body">
                  <h5 class="card-title">Transactions</h5>
                  <h2 class="card-text">1,234</h2>
                  <p class="card-text"><small>Last 30 days</small></p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card card-dashboard text-white bg-success">
                <div class="card-body">
                  <h5 class="card-title">Revenue</h5>
                  <h2 class="card-text">$45,678</h2>
                  <p class="card-text"><small>Last 30 days</small></p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card card-dashboard text-white bg-info">
                <div class="card-body">
                  <h5 class="card-title">Customers</h5>
                  <h2 class="card-text">567</h2>
                  <p class="card-text"><small>Total active</small></p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card card-dashboard text-white bg-warning">
                <div class="card-body">
                  <h5 class="card-title">Subscriptions</h5>
                  <h2 class="card-text">89</h2>
                  <p class="card-text"><small>Active subscriptions</small></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Charts -->
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">Transaction Volume</div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="transactionChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">Revenue by Payment Method</div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="paymentMethodChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Reports -->
          <h2>Recent Reports</h2>
          <div class="table-responsive">
            <table class="table table-striped table-sm">
              <thead>
                <tr>
                  <th>Report Name</th>
                  <th>Type</th>
                  <th>Format</th>
                  <th>Generated</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="recent-reports-table">
                <tr>
                  <td>Monthly Transaction Report</td>
                  <td>TRANSACTION</td>
                  <td>PDF</td>
                  <td>2023-05-01</td>
                  <td>
                    <button class="btn btn-sm btn-primary">View</button>
                    <button class="btn btn-sm btn-secondary">Download</button>
                  </td>
                </tr>
                <tr>
                  <td>Customer Activity Report</td>
                  <td>CUSTOMER</td>
                  <td>CSV</td>
                  <td>2023-04-28</td>
                  <td>
                    <button class="btn btn-sm btn-primary">View</button>
                    <button class="btn btn-sm btn-secondary">Download</button>
                  </td>
                </tr>
                <tr>
                  <td>Payment Method Analysis</td>
                  <td>PAYMENT_METHOD</td>
                  <td>EXCEL</td>
                  <td>2023-04-25</td>
                  <td>
                    <button class="btn btn-sm btn-primary">View</button>
                    <button class="btn btn-sm btn-secondary">Download</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </main>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // API Base URL
      const API_BASE = '/api';

      // Authentication token (should be retrieved from localStorage or session)
      let authToken = localStorage.getItem('authToken');

      // API Helper function
      async function apiCall(endpoint, options = {}) {
        const response = await fetch(`${API_BASE}${endpoint}`, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${authToken}`,
            ...options.headers,
          },
          ...options,
        });

        if (!response.ok) {
          throw new Error(`API call failed: ${response.statusText}`);
        }

        return response.json();
      }

      // Load dashboard data
      async function loadDashboardData() {
        try {
          // Load recent reports
          const reportsResponse = await apiCall('/advanced-reports/saved');
          updateRecentReportsTable(reportsResponse.data);

          // Load dashboard statistics
          await loadDashboardStats();

          // Initialize charts with real data
          await initializeCharts();
        } catch (error) {
          console.error('Error loading dashboard data:', error);
          showNotification('Error loading dashboard data', 'error');
        }
      }

      // Load dashboard statistics
      async function loadDashboardStats() {
        try {
          // This would typically come from a dashboard stats endpoint
          // For now, we'll use placeholder data
          updateStatsCards({
            transactions: 1234,
            revenue: 45678,
            customers: 567,
            subscriptions: 89,
          });
        } catch (error) {
          console.error('Error loading dashboard stats:', error);
        }
      }

      // Update statistics cards
      function updateStatsCards(stats) {
        document.querySelector('.bg-primary .card-text').textContent =
          stats.transactions.toLocaleString();
        document.querySelector(
          '.bg-success .card-text'
        ).textContent = `$${stats.revenue.toLocaleString()}`;
        document.querySelector('.bg-info .card-text').textContent =
          stats.customers.toLocaleString();
        document.querySelector('.bg-warning .card-text').textContent =
          stats.subscriptions.toLocaleString();
      }

      // Update recent reports table
      function updateRecentReportsTable(reports) {
        const tbody = document.getElementById('recent-reports-table');
        tbody.innerHTML = '';

        reports.slice(0, 5).forEach((report) => {
          const row = document.createElement('tr');
          row.innerHTML = `
          <td>${report.name || 'Unnamed Report'}</td>
          <td>${report.type || 'N/A'}</td>
          <td>${report.format || 'N/A'}</td>
          <td>${new Date(report.createdAt).toLocaleDateString()}</td>
          <td>
            <button class="btn btn-sm btn-primary" onclick="viewReport('${
              report.id
            }')">View</button>
            <button class="btn btn-sm btn-secondary" onclick="downloadReport('${
              report.id
            }')">Download</button>
          </td>
        `;
          tbody.appendChild(row);
        });
      }

      // Initialize charts with real data
      async function initializeCharts() {
        // Transaction Volume Chart
        const transactionCtx = document.getElementById('transactionChart').getContext('2d');
        const transactionChart = new Chart(transactionCtx, {
          type: 'line',
          data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
              {
                label: 'Transaction Volume',
                data: [65, 59, 80, 81, 56, 55],
                fill: false,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              title: {
                display: true,
                text: 'Transaction Volume Over Time',
              },
            },
          },
        });

        // Payment Method Chart
        const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
        const paymentMethodChart = new Chart(paymentMethodCtx, {
          type: 'doughnut',
          data: {
            labels: [
              'Credit Card',
              'Debit Card',
              'Bank Transfer',
              'Digital Wallet',
              'Cryptocurrency',
            ],
            datasets: [
              {
                label: 'Revenue by Payment Method',
                data: [12, 19, 3, 5, 2],
                backgroundColor: [
                  'rgba(255, 99, 132, 0.7)',
                  'rgba(54, 162, 235, 0.7)',
                  'rgba(255, 206, 86, 0.7)',
                  'rgba(75, 192, 192, 0.7)',
                  'rgba(153, 102, 255, 0.7)',
                ],
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              title: {
                display: true,
                text: 'Revenue Distribution by Payment Method',
              },
            },
          },
        });
      }

      // Report actions
      async function viewReport(reportId) {
        try {
          const response = await apiCall(`/advanced-reports/saved/${reportId}`);
          showReportModal(response.data);
        } catch (error) {
          console.error('Error viewing report:', error);
          showNotification('Error viewing report', 'error');
        }
      }

      async function downloadReport(reportId) {
        try {
          const response = await fetch(`${API_BASE}/advanced-reports/saved/${reportId}/download`, {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          });

          if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `report_${reportId}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            showNotification('Report downloaded successfully', 'success');
          } else {
            throw new Error('Download failed');
          }
        } catch (error) {
          console.error('Error downloading report:', error);
          showNotification('Error downloading report', 'error');
        }
      }

      // Show notification
      function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${
          type === 'error' ? 'danger' : type
        } alert-dismissible fade show position-fixed`;
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;
        document.body.appendChild(notification);

        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 5000);
      }

      // Show report modal
      function showReportModal(report) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Report Details</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <h6>Report Information</h6>
              <p><strong>Name:</strong> ${report.name || 'Unnamed Report'}</p>
              <p><strong>Type:</strong> ${report.type || 'N/A'}</p>
              <p><strong>Format:</strong> ${report.format || 'N/A'}</p>
              <p><strong>Created:</strong> ${new Date(report.createdAt).toLocaleString()}</p>
              <p><strong>File Size:</strong> ${report.fileSize || 'N/A'}</p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="downloadReport('${
                report.id
              }')">Download</button>
            </div>
          </div>
        </div>
      `;
        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
          document.body.removeChild(modal);
        });
      }

      // Navigation handlers
      document.addEventListener('DOMContentLoaded', function () {
        // Load dashboard data
        loadDashboardData();

        // Add event listeners for navigation
        document.getElementById('generate-report-link').addEventListener('click', (e) => {
          e.preventDefault();
          showGenerateReportModal();
        });

        document.getElementById('scheduled-reports-link').addEventListener('click', (e) => {
          e.preventDefault();
          loadScheduledReports();
        });

        document.getElementById('saved-reports-link').addEventListener('click', (e) => {
          e.preventDefault();
          loadSavedReports();
        });

        document.getElementById('templates-link').addEventListener('click', (e) => {
          e.preventDefault();
          loadReportTemplates();
        });

        // Report type links
        document.querySelectorAll('[data-report-type]').forEach((link) => {
          link.addEventListener('click', (e) => {
            e.preventDefault();
            const reportType = e.target.getAttribute('data-report-type');
            showGenerateReportModal(reportType);
          });
        });
      });

      // Show generate report modal
      function showGenerateReportModal(reportType = '') {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Generate Report</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="generate-report-form">
                <div class="mb-3">
                  <label for="report-type" class="form-label">Report Type</label>
                  <select class="form-select" id="report-type" required>
                    <option value="">Select report type</option>
                    <option value="TRANSACTION" ${
                      reportType === 'TRANSACTION' ? 'selected' : ''
                    }>Transaction Report</option>
                    <option value="CUSTOMER" ${
                      reportType === 'CUSTOMER' ? 'selected' : ''
                    }>Customer Report</option>
                    <option value="PAYMENT_METHOD" ${
                      reportType === 'PAYMENT_METHOD' ? 'selected' : ''
                    }>Payment Method Report</option>
                    <option value="SUBSCRIPTION" ${
                      reportType === 'SUBSCRIPTION' ? 'selected' : ''
                    }>Subscription Report</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="report-format" class="form-label">Format</label>
                  <select class="form-select" id="report-format" required>
                    <option value="CSV">CSV</option>
                    <option value="PDF">PDF</option>
                    <option value="EXCEL">Excel</option>
                    <option value="JSON">JSON</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="start-date" class="form-label">Start Date</label>
                  <input type="date" class="form-control" id="start-date">
                </div>
                <div class="mb-3">
                  <label for="end-date" class="form-label">End Date</label>
                  <input type="date" class="form-control" id="end-date">
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" onclick="generateReport()">Generate Report</button>
            </div>
          </div>
        </div>
      `;
        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
          document.body.removeChild(modal);
        });
      }

      // Generate report
      async function generateReport() {
        try {
          const form = document.getElementById('generate-report-form');
          const formData = new FormData(form);

          const reportData = {
            type: document.getElementById('report-type').value,
            format: document.getElementById('report-format').value,
            startDate: document.getElementById('start-date').value,
            endDate: document.getElementById('end-date').value,
          };

          const response = await apiCall('/advanced-reports/generate', {
            method: 'POST',
            body: JSON.stringify(reportData),
          });

          showNotification('Report generated successfully', 'success');

          // Close modal
          const modal = document.querySelector('.modal.show');
          if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
          }

          // Refresh reports list
          loadDashboardData();
        } catch (error) {
          console.error('Error generating report:', error);
          showNotification('Error generating report', 'error');
        }
      }

      // Load scheduled reports
      async function loadScheduledReports() {
        try {
          const response = await apiCall('/advanced-reports/scheduled');
          // Update UI with scheduled reports
          showNotification('Scheduled reports loaded', 'info');
        } catch (error) {
          console.error('Error loading scheduled reports:', error);
          showNotification('Error loading scheduled reports', 'error');
        }
      }

      // Load saved reports
      async function loadSavedReports() {
        try {
          const response = await apiCall('/advanced-reports/saved');
          // Update UI with saved reports
          showNotification('Saved reports loaded', 'info');
        } catch (error) {
          console.error('Error loading saved reports:', error);
          showNotification('Error loading saved reports', 'error');
        }
      }

      // Load report templates
      async function loadReportTemplates() {
        try {
          const response = await apiCall('/advanced-reports/templates');
          // Update UI with report templates
          showNotification('Report templates loaded', 'info');
        } catch (error) {
          console.error('Error loading report templates:', error);
          showNotification('Error loading report templates', 'error');
        }
      }
    </script>
  </body>
</html>
