#!/usr/bin/env node
/**
 * 🔒 AUTOMATED DATABASE SETUP SCRIPT
 * This script automatically creates the secure database user and grants permissions
 */

const { Pool } = require('pg');
const readline = require('readline');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function setupDatabase() {
  log('blue', '🔒 AMAZINGPAY DATABASE SECURITY SETUP');
  log('blue', '=====================================');
  
  try {
    // Get PostgreSQL connection details
    log('yellow', '\n📋 Please provide your PostgreSQL connection details:');
    
    const host = await askQuestion('Host (default: localhost): ') || 'localhost';
    const port = await askQuestion('Port (default: 5432): ') || '5432';
    const database = await askQuestion('Database name (default: amazingpay): ') || 'amazingpay';
    const adminUser = await askQuestion('Admin username (default: postgres): ') || 'postgres';
    const adminPassword = await askQuestion('Admin password: ');
    
    if (!adminPassword) {
      log('red', '❌ Admin password is required!');
      process.exit(1);
    }
    
    log('blue', '\n🔄 Connecting to PostgreSQL...');
    
    // Create connection pool with admin credentials
    const adminPool = new Pool({
      user: adminUser,
      host: host,
      database: database,
      password: adminPassword,
      port: parseInt(port),
    });
    
    // Test connection
    await adminPool.query('SELECT NOW()');
    log('green', '✅ Connected to PostgreSQL successfully!');
    
    log('blue', '\n🔐 Creating secure application user...');
    
    // Check if user already exists
    const userExists = await adminPool.query(
      "SELECT 1 FROM pg_roles WHERE rolname = 'amazingpay_app'"
    );
    
    if (userExists.rows.length > 0) {
      log('yellow', '⚠️  User amazingpay_app already exists. Dropping and recreating...');
      await adminPool.query('DROP USER amazingpay_app');
    }
    
    // Create new secure user
    await adminPool.query(`
      CREATE USER amazingpay_app WITH PASSWORD 'AzP4y_S3cur3_2024_Db_P4ssw0rd'
    `);
    log('green', '✅ Created user: amazingpay_app');
    
    // Grant database privileges
    await adminPool.query(`GRANT ALL PRIVILEGES ON DATABASE ${database} TO amazingpay_app`);
    log('green', '✅ Granted database privileges');
    
    // Grant schema privileges
    await adminPool.query(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO amazingpay_app`);
    await adminPool.query(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO amazingpay_app`);
    await adminPool.query(`GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO amazingpay_app`);
    await adminPool.query(`GRANT USAGE ON SCHEMA public TO amazingpay_app`);
    log('green', '✅ Granted schema privileges');
    
    // Grant default privileges for future objects
    await adminPool.query(`ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO amazingpay_app`);
    await adminPool.query(`ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO amazingpay_app`);
    await adminPool.query(`ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO amazingpay_app`);
    log('green', '✅ Granted default privileges for future objects');
    
    // Verify setup
    log('blue', '\n🔍 Verifying setup...');
    
    const users = await adminPool.query("SELECT usename FROM pg_user WHERE usename = 'amazingpay_app'");
    if (users.rows.length > 0) {
      log('green', '✅ User verification successful');
    }
    
    const privileges = await adminPool.query(`
      SELECT grantee, privilege_type 
      FROM information_schema.role_table_grants 
      WHERE table_schema='public' AND grantee='amazingpay_app'
      LIMIT 5
    `);
    
    if (privileges.rows.length > 0) {
      log('green', '✅ Privileges verification successful');
      log('blue', `   Found ${privileges.rows.length} privilege grants`);
    }
    
    await adminPool.end();
    
    log('blue', '\n🧪 Testing new user connection...');
    
    // Test connection with new user
    const appPool = new Pool({
      user: 'amazingpay_app',
      host: host,
      database: database,
      password: 'AzP4y_S3cur3_2024_Db_P4ssw0rd',
      port: parseInt(port),
    });
    
    await appPool.query('SELECT NOW()');
    log('green', '✅ New user connection test successful!');
    await appPool.end();
    
    log('green', '\n🎉 DATABASE SETUP COMPLETED SUCCESSFULLY!');
    log('blue', '\n📋 Summary:');
    log('blue', '   • User: amazingpay_app');
    log('blue', '   • Password: AzP4y_S3cur3_2024_Db_P4ssw0rd');
    log('blue', '   • Database: ' + database);
    log('blue', '   • Host: ' + host);
    log('blue', '   • Port: ' + port);
    
    log('yellow', '\n🔄 Your .env file is already configured with these credentials!');
    log('green', '\n✅ You can now start your application with: npm start');
    
  } catch (error) {
    log('red', '\n❌ Database setup failed:');
    log('red', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      log('yellow', '\n💡 Troubleshooting tips:');
      log('yellow', '   • Make sure PostgreSQL is running');
      log('yellow', '   • Check if the host and port are correct');
      log('yellow', '   • Verify firewall settings');
    } else if (error.code === '28P01') {
      log('yellow', '\n💡 Authentication failed:');
      log('yellow', '   • Check your admin username and password');
      log('yellow', '   • Make sure the user has sufficient privileges');
    } else if (error.code === '3D000') {
      log('yellow', '\n💡 Database not found:');
      log('yellow', '   • Create the database first: CREATE DATABASE amazingpay;');
      log('yellow', '   • Or use an existing database name');
    }
    
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the setup
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
