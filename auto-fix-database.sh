#!/bin/bash

# 🔧 AUTOMATIC DATABASE ENVIRONMENT FIX
# Fixes DATABASE_URL and environment issues automatically
# Perfect for first-time deployment

set -e

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 🎯 CONFIGURATION
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"
DB_USER="postgres"

echo -e "${CYAN}🔧 AUTOMATIC DATABASE ENVIRONMENT FIX${NC}"
echo "====================================="
echo ""

# 🔍 STEP 1: CHECK CURRENT STATE
log "Checking current environment state..."

if [ -f ".env" ]; then
    info ".env file exists"
    if grep -q "DATABASE_URL" .env; then
        success "DATABASE_URL found in .env"
    else
        warning "DATABASE_URL missing from .env"
    fi
else
    warning ".env file does not exist"
fi

# 📝 STEP 2: CREATE/FIX .env FILE
log "Creating/fixing .env file..."

cat > .env << EOF
# 🚀 AMAZINGPAY PRODUCTION ENVIRONMENT
# Auto-generated by auto-fix-database.sh

# Server Configuration
NODE_ENV=production
PORT=3002
HOST=0.0.0.0
API_PREFIX=/api

# Database Configuration
DATABASE_URL=postgresql://postgres:CepWrkdzE5TL@localhost:5432/Amazingpay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=CepWrkdzE5TL
DB_NAME=Amazingpay
DB_SSL=false

# Application URLs
FRONTEND_URL=https://amazingpayme.com
API_URL=https://amazingpayme.com/api
DOMAIN=amazingpayme.com
VPS_IP=************

# JWT Configuration
JWT_SECRET=AzP4y_Pr0d_JWT_S3cr3t_2024_V3ry_L0ng_4nd_S3cur3_K3y_F0r_VPS_D3pl0ym3nt
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d
JWT_ALGORITHM=HS256
JWT_ISSUER=amazingpayme.com
JWT_AUDIENCE=amazingpayme.com

# Security Configuration
BCRYPT_SALT_ROUNDS=12
CSRF_ENABLED=true
CSRF_SECRET=AzP4y_Pr0d_CSRF_S3cr3t_2024_Pr0t3ct_4g41nst_4tt4cks
XSS_PROTECTION=true
CONTENT_SECURITY_POLICY=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
AUTH_RATE_LIMIT_WINDOW_MS=3600000
AUTH_RATE_LIMIT_MAX=5

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined
EOF

success ".env file created with all required variables"

# 🗄️ STEP 3: SETUP DATABASE
log "Setting up PostgreSQL database..."

# Check if PostgreSQL is running
if ! systemctl is-active --quiet postgresql; then
    log "Starting PostgreSQL..."
    systemctl start postgresql
    systemctl enable postgresql
fi

# Create database and set password
log "Configuring database..."
sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
sudo -u postgres psql -c "CREATE DATABASE \"$DB_NAME\";" 2>/dev/null || echo "Database already exists"

success "Database configured"

# 🔄 STEP 4: LOAD ENVIRONMENT AND TEST
log "Loading environment variables..."

# Export all variables from .env
set -a
source .env
set +a

# Verify DATABASE_URL is loaded
if [ -z "$DATABASE_URL" ]; then
    error "DATABASE_URL still not loaded"
else
    success "DATABASE_URL loaded: ${DATABASE_URL:0:30}..."
fi

# 📦 STEP 5: GENERATE PRISMA CLIENT
log "Generating Prisma client..."

npx prisma generate

success "Prisma client generated"

# 🔄 STEP 6: RUN DATABASE MIGRATIONS
log "Running database migrations..."

npx prisma migrate deploy

success "Database migrations completed"

# 🧪 STEP 7: TEST DATABASE CONNECTION
log "Testing database connection..."

# Test with Prisma
if npx prisma db pull --preview-feature &>/dev/null; then
    success "Database connection test passed"
else
    warning "Database connection test failed, but continuing..."
fi

# 📊 STEP 8: DISPLAY STATUS
echo ""
echo -e "${CYAN}📊 DATABASE SETUP STATUS${NC}"
echo "========================"
echo ""
success "✅ .env file created with DATABASE_URL"
success "✅ PostgreSQL database configured"
success "✅ Prisma client generated"
success "✅ Database migrations completed"
echo ""

info "🔗 Database URL: postgresql://postgres:***@localhost:5432/Amazingpay"
info "🗄️ Database Name: $DB_NAME"
info "👤 Database User: $DB_USER"
echo ""

echo -e "${GREEN}🎉 DATABASE ENVIRONMENT FIX COMPLETED!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "  1. Build application: npm run build"
echo "  2. Start with PM2: pm2 start ecosystem.config.js --env production"
echo "  3. Check status: pm2 status"
echo ""

# 🚀 STEP 9: CONTINUE DEPLOYMENT (OPTIONAL)
read -p "Do you want to continue with full deployment? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log "Continuing with full deployment..."
    
    # Build application
    log "Building application..."
    npm run build
    
    # Start with PM2
    log "Starting application with PM2..."
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true
    pm2 start ecosystem.config.js --env production
    pm2 save
    
    success "🎉 Full deployment completed!"
    echo ""
    echo -e "${CYAN}📊 APPLICATION STATUS:${NC}"
    pm2 status
    echo ""
    echo -e "${GREEN}🌐 Your application is now running!${NC}"
    echo "  🔗 Local: http://localhost:3002"
    echo "  🏥 Health: http://localhost:3002/api/health"
    echo "  📊 Status: pm2 status"
    echo "  📝 Logs: pm2 logs"
else
    info "Database fix completed. Run deployment manually when ready."
fi
