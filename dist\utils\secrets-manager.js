"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.secretsManager = void 0;
// jscpd:ignore-file
class SecretsManager {
    constructor() {
        this.secrets = new Map();
    }
    async initialize() {
        this.secrets.set('JWT_SECRET', { value: process.env.JWT_SECRET || 'default-secret' });
    }
    getSecret(key) {
        return this.secrets.get(key);
    }
    getDatabaseUrl() {
        return process.env.DATABASE_URL || '';
    }
}
const secretsManager = new SecretsManager();
exports.secretsManager = secretsManager;
exports.default = secretsManager;
