{"file": "F:\\Amazingpayflow\\src\\tests\\environment-separation.test.ts", "mappings": ";;;AAAA,oBAAoB;AACP,QAAA,iBAAiB,GAAG;IAC7B,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,YAAY;IAC7C,4BAA4B;CAC/B,CAAC;AAEF,kBAAe,yBAAiB,CAAC", "names": [], "sources": ["F:\\Amazingpayflow\\src\\tests\\environment-separation.test.ts"], "sourcesContent": ["// jscpd:ignore-file\nexport const environmentConfig = {\n    nodeEnv: process.env.NODE_ENV ?? 'production',\n    // Environment configuration\n};\n\nexport default environmentConfig;\n"], "version": 3}