# Zero Duplication Developer Guide

This guide provides comprehensive instructions for maintaining zero duplication in the AmazingPay Flow codebase. Following these guidelines will ensure that the codebase remains clean, maintainable, and free of unnecessary duplication.

## Table of Contents

1. [Understanding Code Duplication](#understanding-code-duplication)
2. [Duplication Prevention Strategies](#duplication-prevention-strategies)
3. [Using Shared Modules](#using-shared-modules)
4. [Duplication Checking Tools](#duplication-checking-tools)
5. [Workflow Integration](#workflow-integration)
6. [Common Duplication Patterns](#common-duplication-patterns)
7. [When Duplication is Acceptable](#when-duplication-is-acceptable)
8. [Code Review Guidelines](#code-review-guidelines)

## Understanding Code Duplication

Code duplication occurs when the same or similar code appears in multiple places in a codebase. It can happen at different levels:

- **File-level duplication**: Identical or similar files in different locations
- **Component-level duplication**: Controllers, services, etc. with similar functionality
- **Code-level duplication**: Repeated code patterns within files

Duplication is problematic because:

- It increases maintenance burden (changes must be made in multiple places)
- It increases the risk of bugs (fixes may not be applied consistently)
- It makes the codebase larger and harder to understand
- It can lead to divergent implementations of the same functionality

## Duplication Prevention Strategies

### 1. DRY Principle (Don't Repeat Yourself)

Always look for opportunities to extract common code into reusable functions, classes, or modules.

### 2. Inheritance and Composition

- Use inheritance for "is-a" relationships (e.g., a MerchantController is a CrudController)
- Use composition for "has-a" relationships (e.g., a service has a logger)

### 3. Abstraction

Create abstract base classes or interfaces that define common behavior, then implement specific variations.

### 4. Parameterization

Use parameters to handle variations in behavior rather than duplicating code with slight differences.

### 5. Code Generation

For truly repetitive code, consider using code generation tools rather than manual duplication.

## Using Shared Modules

Our codebase includes a comprehensive set of shared modules to prevent duplication:

### Base Controllers

Located in `src/shared/modules/controllers`:

- **BaseController**: Provides common controller functionality
- **CrudController**: Extends BaseController with CRUD operations

Example usage:

```typescript
import { CrudController } from '../shared/modules/controllers';

export class MerchantController extends CrudController {
  constructor(private merchantService: MerchantService) {
    super();
  }
  
  async getAllMerchants(req: Request, res: Response) {
    return this.getAll(req, res, this.merchantService, 'Merchants retrieved successfully');
  }
  
  // Other methods using base class functionality
}
```

### Base Services

Located in `src/shared/modules/services`:

- **BaseService**: Provides common service functionality

Example usage:

```typescript
import { BaseService } from '../shared/modules/services';

export class MerchantService extends BaseService {
  constructor(private merchantRepository: MerchantRepository) {
    super(merchantRepository);
  }
  
  // Add merchant-specific methods here
}
```

### Utility Functions

Located in `src/shared/modules/utils`:

- **responseUtils**: Functions for handling API responses
- **asyncHandler**: Utility for handling async controller methods

Example usage:

```typescript
import { sendSuccess, sendError } from '../shared/modules/utils/responseUtils';
import { asyncHandler } from '../shared/modules/utils/asyncHandler';

// Using response utilities
router.get('/merchants', asyncHandler(async (req, res) => {
  const merchants = await merchantService.findAll();
  return sendSuccess(res, merchants, 'Merchants retrieved successfully');
}));
```

### Shared Types

Located in `src/shared/modules/types`:

- **common**: Common type definitions used throughout the application

Example usage:

```typescript
import { ApiResponse, PaginationOptions } from '../shared/modules/types/common';

// Using shared types
const response: ApiResponse<Merchant[]> = {
  success: true,
  message: 'Merchants retrieved successfully',
  data: merchants
};
```

## Duplication Checking Tools

We use several tools to detect and prevent duplication:

### 1. JSCPD

[JSCPD](https://github.com/kucherenko/jscpd) is our primary tool for detecting code duplication. It's configured in `.jscpd.json` and can be run with:

```bash
npm run check:duplication       # Check for duplication
npm run check:duplication:strict # Check with 0% threshold
npm run check:duplication:report # Generate detailed report
npm run check:duplication:fix    # Get help fixing duplication
```

### 2. Pre-commit Hook

A pre-commit hook automatically checks for duplication before allowing commits:

```bash
npm run check:duplication:precommit
```

### 3. GitHub Actions

A GitHub Actions workflow checks for duplication on pull requests and pushes to main branches.

## Workflow Integration

To maintain zero duplication, integrate these practices into your workflow:

### 1. Before Writing Code

- Check if similar functionality already exists
- Look for opportunities to use or extend shared modules
- Consider if a new shared module would be beneficial

### 2. During Development

- Regularly run duplication checks
- Refactor as you go to eliminate duplication
- Use the shared modules for common functionality

### 3. Before Committing

- Run the duplication check
- Fix any duplication issues
- Ensure your code follows the DRY principle

### 4. During Code Review

- Look for potential duplication
- Suggest using shared modules where appropriate
- Verify that the duplication check passes

## Common Duplication Patterns

Watch for these common duplication patterns:

### 1. Controller Duplication

- Similar CRUD operations across controllers
- Duplicated response handling
- Repeated validation logic

Solution: Extend the BaseController or CrudController

### 2. Service Duplication

- Similar data access patterns
- Duplicated business logic
- Repeated error handling

Solution: Extend the BaseService or extract common logic into shared utilities

### 3. Utility Duplication

- Duplicated helper functions
- Similar formatting or conversion functions
- Repeated validation utilities

Solution: Move to shared utility modules

## When Duplication is Acceptable

Some minimal duplication may be acceptable in certain cases:

1. **When abstraction would be more complex** than the duplication itself
2. **When performance is critical** and duplication provides measurable benefits
3. **When the duplicated code is expected to diverge** significantly in the future
4. **When the duplication is in test code** and improves test clarity

In these cases, add a comment explaining why the duplication is necessary.

## Code Review Guidelines

When reviewing code, check for these duplication-related issues:

1. **Similar functionality** in multiple places
2. **Copy-pasted code** with minor modifications
3. **Repeated patterns** that could be abstracted
4. **Missed opportunities** to use shared modules
5. **Unnecessary reimplementation** of existing functionality

Suggest refactoring to eliminate duplication when found.

---

By following this guide, you'll help maintain a clean, duplication-free codebase that is easier to maintain, extend, and understand.

For more detailed information, see the [Duplication Strategy Document](./duplication-strategy.md).
