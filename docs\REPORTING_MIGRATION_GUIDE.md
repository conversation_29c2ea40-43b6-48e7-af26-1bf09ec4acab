# Reporting System Migration Guide

## Overview

This guide explains the migration from the basic reporting system to the new Advanced Reporting system in AmazingPay Flow.

## System Comparison

### Old Basic Reporting System

**Location**: `services/report.service.js`
**Routes**: 
- `POST /api/reports/transactions` - Generate transaction reports
- `GET /api/reports` - Get user's reports
- `GET /api/reports/download/:fileName` - Download reports

**Features**:
- Basic transaction reporting only
- CSV and PDF export formats
- Simple file-based storage
- No scheduling or templates

### New Advanced Reporting System

**Location**: `src/services/advanced-report.service.ts`
**Routes**: 
- `POST /api/advanced-reports/generate` - Generate any type of report
- `GET /api/advanced-reports/templates` - Manage report templates
- `GET /api/advanced-reports/scheduled` - Manage scheduled reports
- `GET /api/advanced-reports/saved` - Manage saved reports
- `GET /api/dashboards` - Dashboard management

**Features**:
- Multiple report types (Transaction, Customer, Payment Method, Subscription)
- Multiple export formats (CSV, PDF, Excel, JSON)
- Report templates for reusability
- Scheduled reports with email notifications
- Interactive dashboards
- Performance optimization for large datasets
- Advanced filtering and grouping

## Migration Strategy

### Phase 1: Parallel Operation (Current State)

Both systems operate in parallel:
- Old system: `/api/reports/*`
- New system: `/api/advanced-reports/*`

This allows for gradual migration without breaking existing integrations.

### Phase 2: Data Migration (Optional)

If you want to migrate existing reports to the new system:

```sql
-- Migrate existing reports to saved reports
INSERT INTO "SavedReport" (
  id,
  name,
  type,
  format,
  "filePath",
  "fileSize",
  parameters,
  "createdById",
  "createdAt",
  "updatedAt"
)
SELECT 
  gen_random_uuid(),
  CONCAT(type, ' Report - ', DATE(created_at)),
  type,
  format,
  "filePath",
  0, -- File size not tracked in old system
  filters,
  "userId",
  "createdAt",
  "updatedAt"
FROM "Report"
WHERE type = 'TRANSACTION';
```

### Phase 3: Client Migration

Update client applications to use new endpoints:

#### Old API Calls
```javascript
// Generate transaction report
POST /api/reports/transactions
{
  "format": "csv",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "status": "COMPLETED"
}

// Get reports
GET /api/reports

// Download report
GET /api/reports/download/report_file.csv
```

#### New API Calls
```javascript
// Generate any type of report
POST /api/advanced-reports/generate
{
  "type": "TRANSACTION",
  "format": "CSV",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "status": "COMPLETED"
}

// Get saved reports
GET /api/advanced-reports/saved

// Download report
GET /api/advanced-reports/saved/{reportId}/download
```

### Phase 4: Deprecation (Future)

Once all clients have migrated:
1. Mark old endpoints as deprecated
2. Add deprecation warnings to responses
3. Set sunset date for old API
4. Remove old system after sunset period

## API Mapping

### Report Generation

| Old Endpoint | New Endpoint | Changes |
|--------------|--------------|---------|
| `POST /api/reports/transactions` | `POST /api/advanced-reports/generate` | - Add `type: "TRANSACTION"` parameter<br>- Response format changed |

### Report Retrieval

| Old Endpoint | New Endpoint | Changes |
|--------------|--------------|---------|
| `GET /api/reports` | `GET /api/advanced-reports/saved` | - Response structure changed<br>- Additional metadata included |

### Report Download

| Old Endpoint | New Endpoint | Changes |
|--------------|--------------|---------|
| `GET /api/reports/download/:fileName` | `GET /api/advanced-reports/saved/:id/download` | - Use report ID instead of filename<br>- Enhanced security checks |

## Response Format Changes

### Old Format
```json
{
  "status": "success",
  "data": {
    "reportId": "transaction_report_20231201",
    "fileName": "transaction_report_20231201.csv",
    "recordCount": 1234,
    "format": "csv",
    "downloadUrl": "/api/reports/download/transaction_report_20231201.csv"
  }
}
```

### New Format
```json
{
  "success": true,
  "data": {
    "id": "uuid-report-id",
    "filePath": "/path/to/report.csv",
    "rowCount": 1234,
    "format": "CSV",
    "fileSize": 102400
  }
}
```

## New Features Available

### 1. Report Templates
Create reusable report configurations:

```javascript
POST /api/advanced-reports/templates
{
  "name": "Monthly Transaction Summary",
  "description": "Summary of all transactions for the month",
  "type": "TRANSACTION",
  "config": {
    "columns": ["reference", "amount", "currency", "status"],
    "groupBy": ["status"],
    "sortBy": "createdAt",
    "sortDirection": "desc"
  }
}
```

### 2. Scheduled Reports
Automate report generation:

```javascript
POST /api/advanced-reports/scheduled
{
  "name": "Weekly Transaction Report",
  "templateId": "template-uuid",
  "schedule": "0 0 * * 1", // Every Monday at midnight
  "isActive": true,
  "emailRecipients": ["<EMAIL>"],
  "parameters": {
    "format": "PDF",
    "status": "COMPLETED"
  }
}
```

### 3. Multiple Report Types
Generate reports for different data types:

- `TRANSACTION` - Transaction data
- `CUSTOMER` - Customer information
- `PAYMENT_METHOD` - Payment method analytics
- `SUBSCRIPTION` - Subscription data

### 4. Enhanced Export Formats
Support for additional formats:

- `CSV` - Comma-separated values
- `PDF` - Portable document format
- `EXCEL` - Microsoft Excel format
- `JSON` - JavaScript Object Notation

### 5. Interactive Dashboards
Access the dashboard interface:

```
GET /dashboard/reports/page
```

## Performance Improvements

### Large Dataset Handling
The new system automatically uses streaming for large reports:

- Automatic detection of large datasets (>100MB)
- Batch processing with configurable batch sizes
- Memory-efficient streaming export
- Progress tracking for long-running reports

### Caching and Optimization
- Report template caching
- Query optimization
- Efficient database indexing
- Asynchronous processing for scheduled reports

## Security Enhancements

### Enhanced Access Control
- Role-based access control
- Data isolation between merchants
- Secure file storage
- Audit trail for report access

### File Security
- Reports stored in secure directory structure
- Access validation for downloads
- Automatic cleanup of old reports
- Encrypted file storage (optional)

## Troubleshooting

### Common Migration Issues

1. **Authentication Errors**
   - Ensure JWT tokens are valid
   - Check user permissions for new endpoints

2. **Report Not Found**
   - Old report IDs are not compatible with new system
   - Use report listing endpoints to get new IDs

3. **Format Differences**
   - Update client code to handle new response formats
   - Check for case sensitivity in format parameters

4. **Performance Issues**
   - Large reports now use streaming automatically
   - Monitor memory usage during migration

### Support

For migration support:
1. Check the API documentation: `/docs/api/advanced-reporting.md`
2. Review the implementation guide: `/docs/ADVANCED_REPORTING_IMPLEMENTATION.md`
3. Test endpoints using the interactive dashboard: `/dashboard/reports/page`

## Timeline

### Recommended Migration Timeline

- **Week 1-2**: Review new API documentation and test endpoints
- **Week 3-4**: Update client applications to use new endpoints
- **Week 5-6**: Migrate existing integrations
- **Week 7-8**: Test thoroughly in staging environment
- **Week 9**: Deploy to production
- **Week 10+**: Monitor and optimize

### Deprecation Schedule

- **Month 1**: New system available alongside old system
- **Month 2-3**: Migration period with support for both systems
- **Month 4**: Old system marked as deprecated
- **Month 6**: Old system sunset and removal

This gradual approach ensures minimal disruption while providing time for thorough testing and migration.
