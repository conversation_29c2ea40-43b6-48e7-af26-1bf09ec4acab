{"security": {"passed": 9, "failed": 1, "warnings": 5, "issues": [{"timestamp": "2025-05-25T19:58:13.462Z", "message": "No obvious hardcoded secrets in .env", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.462Z", "message": "JWT secret in .env has adequate length", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.463Z", "message": "No obvious hardcoded secrets in .env.production", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.463Z", "message": "JWT secret in .env.production has adequate length", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.463Z", "message": "No obvious hardcoded secrets in .env.vps.production", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.464Z", "message": "JWT secret in .env.vps.production has adequate length", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.464Z", "message": "No obvious hardcoded secrets in .env.example", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.464Z", "message": "JWT secret in .env.example is too short (< 32 chars)", "severity": "error"}, {"timestamp": "2025-05-25T19:58:13.468Z", "message": "axios may be vulnerable: SSRF vulnerability", "severity": "warning"}, {"timestamp": "2025-05-25T19:58:13.468Z", "message": "jsonwebtoken may be vulnerable: Algorithm confusion", "severity": "warning"}, {"timestamp": "2025-05-25T19:58:13.469Z", "message": "Dependency vulnerability check completed", "severity": "info"}, {"timestamp": "2025-05-25T19:58:13.475Z", "message": "Dynamic environment variable access in src\\config\\index.ts", "severity": "warning"}, {"timestamp": "2025-05-25T19:58:13.580Z", "message": "Dynamic environment variable access in src\\utils\\service-config.ts", "severity": "warning"}, {"timestamp": "2025-05-25T19:58:13.581Z", "message": "Dynamic environment variable access in src\\utils\\test-connection.ts", "severity": "warning"}, {"timestamp": "2025-05-25T19:58:13.582Z", "message": "Code security scan completed", "severity": "info"}]}, "quality": {"passed": 0, "failed": 1, "warnings": 1, "issues": [{"timestamp": "2025-05-25T19:58:17.818Z", "message": "TypeScript compilation has errors", "severity": "error"}, {"timestamp": "2025-05-25T19:58:17.944Z", "message": "Found 2 TODOs and 0 FIXMEs", "severity": "warning"}]}, "functionality": {"passed": 0, "failed": 6, "warnings": 0, "issues": [{"timestamp": "2025-05-25T19:58:17.944Z", "message": "Payment file missing: src/services/payment/PaymentService.ts", "severity": "error"}, {"timestamp": "2025-05-25T19:58:17.944Z", "message": "Payment file missing: src/controllers/payment/PaymentController.ts", "severity": "error"}, {"timestamp": "2025-05-25T19:58:17.945Z", "message": "Payment file missing: src/services/payment/methods/CryptoPaymentService.ts", "severity": "error"}, {"timestamp": "2025-05-25T19:58:17.945Z", "message": "Payment file missing: src/services/payment/methods/BankTransferService.ts", "severity": "error"}, {"timestamp": "2025-05-25T19:58:17.945Z", "message": "Verification file missing: src/services/identity-verification/IdentityVerificationService.ts", "severity": "error"}, {"timestamp": "2025-05-25T19:58:17.946Z", "message": "No verification methods in src/controllers/identity-verification/IdentityVerificationController.ts", "severity": "error"}]}, "deployment": {"passed": 9, "failed": 0, "warnings": 0, "issues": [{"timestamp": "2025-05-25T19:58:17.946Z", "message": "Required file present: package.json", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.946Z", "message": "Required file present: tsconfig.json", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.947Z", "message": "Required file present: prisma/schema.prisma", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.947Z", "message": "Required file present: .env.production", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.947Z", "message": "Required file present: ecosystem.config.js", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.947Z", "message": "Required file present: Dockerfile", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.948Z", "message": "Required file present: docker-compose.yml", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.948Z", "message": "Build directory exists", "severity": "info"}, {"timestamp": "2025-05-25T19:58:17.948Z", "message": "Dependencies installed", "severity": "info"}]}}