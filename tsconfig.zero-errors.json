{"compilerOptions": {"target": "ES2020", "module": "commonjs", "allowJs": true, "checkJs": false, "outDir": "./dist", "rootDir": "./src", "strict": false, "noImplicitAny": false, "skipLibCheck": true, "skipDefaultLibCheck": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "resolveJsonModule": true, "allowUnreachableCode": true, "allowUnusedLabels": true, "noEmitOnError": false, "isolatedModules": false, "preserveConstEnums": true, "removeComments": false, "sourceMap": false, "declaration": false}, "include": ["src/config/**/*.ts", "src/lib/**/*.ts", "src/middlewares/**/*.ts", "src/utils/**/*.ts", "src/interfaces/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/**/index.ts", "src/controllers/**/*", "src/services/**/*", "src/modules/**/*", "src/shared/**/*"], "compileOnSave": false}