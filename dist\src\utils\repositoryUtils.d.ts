/**
 * Repository Utilities
 *
 * This file contains utility functions for repositories.
 */
/**
 * Create a where clause from filters
 * @param filters Filters
 * @returns Where clause
 */
export declare function createWhereClause(filters?: Record<string, unknown>): Record<string, unknown>;
/**
 * Create an order by clause
 * @param sort Sort field
 * @param order Sort order
 * @returns Order by clause
 */
export declare function createOrderByClause(sort?: string, order?: 'asc' | 'desc'): Record<string, unknown> | undefined;
/**
 * Execute a repository method with error handling
 * @param method Repository method
 * @param args Method arguments
 * @returns Method result
 */
export declare function executeRepositoryMethod<T>(method: (...args: any[]) => ): any;
/**
 * Execute a transaction
 * @param prisma Prisma client
 * @param callback Transaction callback
 * @returns Transaction result
 */
export declare function executeTransaction<T>(prisma: any, callback: (tx: any) => ): any;
//# sourceMappingURL=repositoryUtils.d.ts.map