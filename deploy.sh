#!/bin/bash

# 🚀 AMAZINGPAY AUTOMATED PRODUCTION DEPLOYMENT
# Complete automated setup for VPS deployment with aaPanel
#
# Usage: curl -sSL https://raw.githubusercontent.com/Amazingteam-eg/Amazingpayflow/main/deploy.sh | bash
# Or: ./deploy.sh
#
# VPS: 159.65.92.74 | Domain: amazingpayme.com

set -e

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 🎯 CONFIGURATION
ROOT_DIR="/www/wwwroot/Amazingpayflow"
DOMAIN="amazingpayme.com"
DB_NAME="Amazingpay"
DB_PASSWORD="CepWrkdzE5TL"
NODE_PORT="3002"
GITHUB_REPO="https://github.com/Amazingteam-eg/Amazingpayflow.git"

# 📝 LOGGING FUNCTIONS
log() { echo -e "${CYAN}[$(date +'%H:%M:%S')] $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; exit 1; }
info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
step() { echo -e "${PURPLE}🔸 $1${NC}"; }

# 🎉 WELCOME MESSAGE
clear
echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                                                              ║${NC}"
echo -e "${CYAN}║           🚀 AMAZINGPAY AUTOMATED DEPLOYMENT 🚀             ║${NC}"
echo -e "${CYAN}║                                                              ║${NC}"
echo -e "${CYAN}║  Complete automated production setup for aaPanel VPS        ║${NC}"
echo -e "${CYAN}║                                                              ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""
info "🎯 Target: $ROOT_DIR"
info "🌐 Domain: $DOMAIN"
info "🗄️ Database: $DB_NAME"
info "🔌 Port: $NODE_PORT"
echo ""

# 🔍 STEP 1: ENVIRONMENT VALIDATION
step "STEP 1: Environment Validation"
log "Checking system requirements..."

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    warning "Not running as root, some operations may require sudo"
fi

# Check required commands
for cmd in git node npm; do
    if ! command -v $cmd >/dev/null 2>&1; then
        error "$cmd is not installed. Please install it first."
    fi
done

NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
success "Environment validated - Node.js $NODE_VERSION, npm $NPM_VERSION"
echo ""

# 🗑️ STEP 2: CLEANUP PREVIOUS INSTALLATION
step "STEP 2: Cleanup Previous Installation"
log "Stopping existing services..."

# Stop PM2 processes
pm2 stop all 2>/dev/null || true
pm2 delete all 2>/dev/null || true

# Kill any processes using port 3002
lsof -ti:$NODE_PORT | xargs kill -9 2>/dev/null || true

# Remove existing directory
if [ -d "$ROOT_DIR" ]; then
    log "Removing existing installation..."
    rm -rf "$ROOT_DIR"
fi

success "Previous installation cleaned"
echo ""

# 📁 STEP 3: PROJECT SETUP
log "Setting up project..."

mkdir -p "$ROOT_DIR"
cd "$ROOT_DIR"

git clone "$GITHUB_REPO" .
git config --global --add safe.directory "$ROOT_DIR"

# Set proper permissions
chown -R www-data:www-data "$ROOT_DIR" 2>/dev/null || chown -R root:root "$ROOT_DIR"
chmod -R 755 "$ROOT_DIR"

success "Project setup completed"

# 📦 STEP 4: DEPENDENCIES
log "Installing dependencies..."

export HUSKY=0
export NODE_ENV=production
npm install --omit=dev --ignore-scripts --no-audit --no-fund

# Install PM2 globally
npm install -g pm2

success "Dependencies installed"

# 🗄️ STEP 5: DATABASE SETUP
log "Setting up database..."

systemctl start postgresql 2>/dev/null || true
systemctl enable postgresql 2>/dev/null || true

sudo -u postgres psql -c "ALTER USER postgres PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
sudo -u postgres psql -c "CREATE DATABASE \"$DB_NAME\";" 2>/dev/null || true

success "Database configured"

# 📝 STEP 6: ENVIRONMENT CONFIGURATION
log "Creating environment configuration..."

cat > .env << EOF
NODE_ENV=production
PORT=$NODE_PORT
HOST=0.0.0.0
DATABASE_URL=postgresql://postgres:$DB_PASSWORD@localhost:5432/$DB_NAME
FRONTEND_URL=https://$DOMAIN
API_URL=https://$DOMAIN/api
DOMAIN=$DOMAIN
JWT_SECRET=AzP4y_Pr0d_JWT_S3cr3t_2024_V3ry_L0ng_4nd_S3cur3_K3y
JWT_EXPIRES_IN=1d
EOF

export $(cat .env | xargs)

success "Environment configured"

# 🔨 STEP 7: BUILD APPLICATION
log "Building application..."

npx prisma generate
npx prisma migrate deploy
npm run build

success "Application built"

# 🚀 STEP 8: START APPLICATION
log "Starting application..."

pm2 start dist/src/index.js --name "amazingpay" --instances 1 --env production
pm2 save
pm2 startup

success "Application started"

# 🌐 STEP 9: NGINX CONFIGURATION
log "Configuring Nginx..."

mkdir -p /www/server/panel/vhost/nginx

cat > /www/server/panel/vhost/nginx/$DOMAIN.conf << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location / {
        proxy_pass http://127.0.0.1:$NODE_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    location ~ /\\.(env|git) {
        deny all;
        return 404;
    }
    
    access_log /www/wwwlogs/$DOMAIN.log;
    error_log /www/wwwlogs/$DOMAIN.error.log;
}
EOF

nginx -t && systemctl reload nginx

success "Nginx configured"

# 🧪 STEP 10: DEPLOYMENT VALIDATION
log "Validating deployment..."

sleep 10

if curl -f http://localhost:$NODE_PORT/api/health >/dev/null 2>&1; then
    success "Health check passed"
else
    warning "Health check failed - application may still be starting"
fi

# 📊 FINAL STATUS
echo ""
echo -e "${CYAN}🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!${NC}"
echo "=================================="
echo ""
echo -e "${GREEN}✅ DEPLOYMENT SUMMARY:${NC}"
echo "  📁 Root: $ROOT_DIR"
echo "  🗄️ Database: $DB_NAME"
echo "  🚀 Application: Running on PM2"
echo "  🌐 Domain: $DOMAIN"
echo ""
echo -e "${CYAN}🔗 URLS:${NC}"
echo "  🏠 Application: http://$DOMAIN"
echo "  🏥 Health Check: http://$DOMAIN/api/health"
echo "  📊 API Docs: http://$DOMAIN/api/docs"
echo ""
echo -e "${CYAN}📊 MANAGEMENT:${NC}"
echo "  Status: pm2 status"
echo "  Logs: pm2 logs"
echo "  Restart: pm2 restart amazingpay"
echo ""

pm2 status

success "🎉 AmazingPay is now live at: http://$DOMAIN"
