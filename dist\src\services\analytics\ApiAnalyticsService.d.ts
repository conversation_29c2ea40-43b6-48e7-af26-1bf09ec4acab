import { Request, Response } from 'express';
/**
 * API analytics service
 * This service tracks API usage
 */
export declare class ApiAnalyticsService {
    private static instance;
    private eventBuffer;
    private bufferSize;
    private flushInterval;
    /**
     * Create a new API analytics service
     */
    private constructor();
    /**
     * Get the API analytics service instance
     * @returns API analytics service instance
     */
    static getInstance(): ApiAnalyticsService;
    /**
     * Track API request
     * @param req Express request
     * @param res Express response
     * @param responseTime Response time in milliseconds
     */
    trackRequest(req: Request, res: Response, responseTime: number): void;
    /**
     * Flush event buffer
     */
    private flushBuffer;
}
//# sourceMappingURL=ApiAnalyticsService.d.ts.map