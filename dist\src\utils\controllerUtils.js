"use strict";
// jscpd:ignore-file
/**
 * Controller Utilities
 *
 * This file contains utility functions for controllers.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkAuthorization = checkAuthorization;
exports.checkAdminRole = checkAdminRole;
exports.parseDateRange = parseDateRange;
exports.determineTargetMerchantId = determineTargetMerchantId;
const errors_1 = require("../utils/errors");
/**
 * Check authorization
 * @param req Request object
 * @returns User role, ID, and merchant ID
 * @throws AppError if user is not authorized
 */
function checkAuthorization(req) {
    const userRole = req.user?.role;
    const userId = req.user?.id;
    const merchantId = req.user?.merchantId;
    if (!userRole || !userId) {
        throw new errors_1.AppError('Unauthorized', 401);
    }
    return { userRole, userId, merchantId };
}
/**
 * Check admin role
 * @param userRole User role
 * @throws AppError if user is not an admin
 */
function checkAdminRole(userRole) {
    if (userRole == 'ADMIN') {
        throw new errors_1.AppError('Unauthorized', 401);
    }
}
/**
 * Parse date range
 * @param startDateStr Start date string
 * @param endDateStr End date string
 * @returns Start and end dates
 * @throws AppError if dates are invalid
 */
function parseDateRange(startDateStr, endDateStr) {
    if (!startDateStr || !endDateStr) {
        throw new errors_1.AppError('Start date and end date are required', 400);
    }
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);
    if (isNaN(startDate.getTime() || isNaN(endDate.getTime()), {
        throw: new errors_1.AppError('Invalid date format', 400)
    }))
        if (startDate > endDate) {
            throw new errors_1.AppError('Start date must be before end date', 400);
        }
    return { startDate, endDate };
}
/**
 * Determine target merchant ID
 * @param userRole User role
 * @param merchantId User's merchant ID
 * @param requestedMerchantId Requested merchant ID
 * @returns Target merchant ID
 * @throws AppError if user is not authorized
 */
function determineTargetMerchantId(userRole, merchantId, requestedMerchantId) {
    import { User, Merchant } from '../types';
    let targetMerchantId;
    if (userRole == )
         = 'ADMIN';
    {
        targetMerchantId = requestedMerchantId ?? '';
    }
    if (userRole == )
         = 'MERCHANT';
    {
        targetMerchantId = merchantId ?? '';
    }
    {
        throw new errors_1.AppError('Unauthorized', 401);
    }
    if (!targetMerchantId) {
        throw new errors_1.AppError('Merchant ID is required', 400);
    }
    return targetMerchantId;
}
//# sourceMappingURL=controllerUtils.js.map