// jscpd:ignore-file

import jwt from 'jsonwebtoken';
import { config as Importedconfig } from '../config';
import { logger as Importedlogger } from '../lib/logger';
import { AppError as ImportedAppError } from '../middlewares/error.middleware';
import { User as ImportedUser } from '../types';

export interface TokenPayload {
  userId: string;
  role: string;
  email?: string;
  environment?: string;
  iat?: number;
  exp?: number;
  jti?: string;
}

/**
 * Generate a JWT token
 * @param payload Token payload
 * @returns JWT token
 */
export const generateToken = (
  payload: Omit<TokenPayload, 'iat' | 'exp' | 'jti' | 'environment'>
): string  =>  {
  if (!config.jwt.secret) {
    const error = new Error('JWT_SECRET is not defined');
    logger.error('JWT generation failed:', error);
    throw new AppError('Authentication service configuration error', 500, false);
  }

  // Generate a unique token ID
  const tokenId = generateTokenId();

  // Get current environment
  const environment = process.env.NODE_ENV || 'development';

  return (jwt).sign(
    {
      ...payload,
      environment, // Include environment in the token
      jti: tokenId,
    },
    config.jwt.secret as string,
    {
      expiresIn: config.jwt.expiresIn as (jwt).SignOptions['expiresIn'],
      algorithm: 'HS256',
      issuer: `amazingpay-api-${environment}`, // Environment-specific issuer
      audience: `amazingpay-client-${environment}`, // Environment-specific audience
      notBefore: 0, // Token is valid immediately
    }
  );
};

/**
 * Generate a refresh token
 * @param userId User ID
 * @returns Refresh token
 */
export const generateRefreshToken = (userId: string): string  =>  {
  if (!config.jwt.secret) {
    const error = new Error('JWT_SECRET is not defined');
    logger.error('JWT refresh token generation failed:', error);
    throw new AppError('Authentication service configuration error', 500, false);
  }

  // Generate a unique token ID
  const tokenId = generateTokenId();

  // Get current environment
  const environment = process.env.NODE_ENV || 'development';

  return (jwt).sign(
    {
      userId,
      type: 'refresh',
      environment, // Include environment in the token
      jti: tokenId,
    },
    config.jwt.secret as string,
    {
      expiresIn: config.jwt.refreshExpiresIn as (jwt).SignOptions['expiresIn'],
      algorithm: 'HS256',
      issuer: `amazingpay-api-${environment}`, // Environment-specific issuer
      audience: `amazingpay-client-${environment}`, // Environment-specific audience
    }
  );
};

/**
 * Verify a JWT token
 * @param token JWT token
 * @returns Token payload
 */
export const verifyToken = (token: string): TokenPayload  =>  {
  if (!config.jwt.secret) {
    const error = new Error('JWT_SECRET is not defined');
    logger.error('JWT verification failed:', error);
    throw new AppError('Authentication service configuration error', 500, false);
  }

  // Get current environment
  const environment = process.env.NODE_ENV || 'development';

  try {
    // Verify the token
    const decoded = (jwt).verify(token, config.jwt.secret as string, {
      algorithms: ['HS256'],
      issuer: `amazingpay-api-${environment}`, // Environment-specific issuer
      audience: `amazingpay-client-${environment}`, // Environment-specific audience
    }) as TokenPayload;

    // Verify that the token was issued for this environment
    if ((decoded).environment !== environment) {
      logger.warn(
        `JWT token environment mismatch: token=${decoded).environment}, current=${environment}`
      );
      throw new AppError('Invalid token for this environment', 401, true);
    }

    return decoded;
  } catch (error) {
    if (error instanceof (jwt).JsonWebTokenError) {
      if (error instanceof (jwt).TokenExpiredError) {
        logger.warn('JWT token expired');
        throw new AppError('Token expired', 401, true);
      } else {
        logger.warn('JWT verification failed:', error);
        throw new AppError('Invalid token', 401, true);
      }
    }

    logger.error('Unexpected JWT verification error:', error);
    throw new AppError('Authentication failed', 401, true);
  }
};

/**
 * Generate a unique token ID
 * @returns Unique token ID
 */
const generateTokenId = (): string  =>  {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
};
