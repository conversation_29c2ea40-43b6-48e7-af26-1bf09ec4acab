# Prometheus Configuration for AmazingPay Flow Production
# Advanced monitoring with custom business metrics and alerting

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'amazingpay-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load alerting rules
rule_files:
  - "alert_rules.yml"
  - "business_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # AmazingPay Flow API instances
  - job_name: 'amazingpay-api'
    static_configs:
      - targets: 
          - 'amazingpay-api-1:3000'
          - 'amazingpay-api-2:3000'
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    params:
      format: ['prometheus']

  # Database monitoring
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis monitoring
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: 
          - 'node-exporter-1:9100'
          - 'node-exporter-2:9100'
    scrape_interval: 30s

  # HAProxy load balancer
  - job_name: 'haproxy'
    static_configs:
      - targets: ['haproxy:8404']
    metrics_path: '/stats/prometheus'
    scrape_interval: 30s

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # Custom business metrics
  - job_name: 'business-metrics'
    static_configs:
      - targets: 
          - 'amazingpay-api-1:3000'
          - 'amazingpay-api-2:3000'
    metrics_path: '/metrics/business'
    scrape_interval: 30s

  # Fraud detection metrics
  - job_name: 'fraud-detection'
    static_configs:
      - targets: 
          - 'amazingpay-api-1:3000'
          - 'amazingpay-api-2:3000'
    metrics_path: '/metrics/fraud'
    scrape_interval: 15s

  # Identity verification metrics
  - job_name: 'identity-verification'
    static_configs:
      - targets: 
          - 'amazingpay-api-1:3000'
          - 'amazingpay-api-2:3000'
    metrics_path: '/metrics/identity'
    scrape_interval: 30s

  # Payment processing metrics
  - job_name: 'payment-processing'
    static_configs:
      - targets: 
          - 'amazingpay-api-1:3000'
          - 'amazingpay-api-2:3000'
    metrics_path: '/metrics/payments'
    scrape_interval: 10s

# Remote write configuration for long-term storage
remote_write:
  - url: "https://prometheus-remote-write.amazingpay.com/api/v1/write"
    basic_auth:
      username: "amazingpay"
      password_file: "/etc/prometheus/remote_write_password"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Remote read configuration
remote_read:
  - url: "https://prometheus-remote-read.amazingpay.com/api/v1/read"
    basic_auth:
      username: "amazingpay"
      password_file: "/etc/prometheus/remote_read_password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true
