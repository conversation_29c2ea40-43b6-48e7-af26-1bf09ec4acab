{"version": 3, "file": "error-handler.js", "sourceRoot": "", "sources": ["../../../src/utils/error-handler.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;;GAKG;;;AA0BH;;;;;;GAMG;AACI,MAAM,qBAAqB,GAAI,CACpC,GAAqB,EACrB,GAAY,EACZ,GAAa,EACb,IAAkB,EACpB,EADoB,CACpB,AADoB,CACnB;AALY,QAAA,qBAAqB,yBAKjC;AACM,AAAF,GAAK;IACR,YAAY;IACZ,MAAM,EAAA,EAAA,CAAC,KAAK,CAAC,mBAAmB,EAAE;QAChC,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,KAAK,EAAE,GAAG,CAAC,OAAO;QAClB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,SAAS,EAAE,GAAG,CAAC,EAAE;KAClB,CAAC;IAEF,wBAAwB;IACxB,KAAK,EAAC,aAAa,EAAE,aAAa,GAAG;QACnC,MAAM,EAAE,OAAO;QACf,UAAU,EAAE,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAA,CAAA,CAAA,EAAC,EAAA,CAAC,UAAU,EAAG,GAAG,EAAA;QAC3D,OAAO,EAAE,GAAG,CAAC,OAAO,IAAQ,uBAAuB;QACnD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,WAAW,IAAQ,GAAG,CAAC,GAAG;KACrC;IAED,8BAA8B;IAC9B,EAAE,CAAE,GAAG,IAAA,CAAC,AAAD,EAAA,EAAA,CAAC,EAAE;CAAC,CAAA;AAAC,CAAC;IACX,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;AACnC,CAAC;AAED,8BAA8B;AAC9B,IAAI,GAAG,YAAY,QAAQ,IAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;IAC5C,aAAa,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AAChC,CAAC;AAED,iCAAiC;AACjC,IAAI,GAAG,YAAY,QAAQ,IAAQ,GAAG,CAAC,OAAO,EAAE,CAAC;IAC/C,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AACtC,CAAC;AAED,iCAAiC;AACjC,IAAI,CAAC,YAAY,EAAE,EAAC,CAAC;IACnB,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AAClC,CAAC;AAED,sBAAsB;AACtB,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1D,CAAC;AACK,MAAM,kBAAkB,GAAI,CACjC,GAAqB,EACrB,WAAmB,EACnB,UAAkB,EAClB,MAAgB,EAClB,EADkB,CAClB,AADkB,CACjB;AALY,QAAA,kBAAkB,sBAK9B;AACM,AAAF,GAAK;IACR,YAAY;IACZ,MAAM,EAAA,EAAA,CAAC,KAAK,CAAC,oBAAoB,WAAW,IAAI,UAAU,GAAG,EAAE;QAC7D,KAAK,EAAE,GAAG,CAAC,OAAO;QAClB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAG,SAAS,CAAA;YACpD,CADoD;gBACpD,AADoD,RAAA,HACnD,CAAC;IAEF,mBAAmB;IACnB,EAAE,CAAE,GAAG,IAAC,CAAC,AAAF,EAAC,UAAU,EAAC,QAAQ;CAAC,CAAA;AAAC,CAAC;IAC5B,MAAM,GAAG,CAAC;AACZ,CAAC;AAED,gCAAgC;AAChC,MAAM,IAAI,QAAQ,CAChB,GAAG,CAAC,OAAO,IAAQ,uBAAuB,EAC1C,GAAG,EACH,uBAAuB,EACvB,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,CACxD,CAAC;AACH,CAAC;AAEF;;;;;;GAMG;AACI,MAAM,kBAAkB,GAAI,CACjC,GAAqB,EACrB,GAAY,EACZ,GAAa,EACb,IAAkB,EACpB,EADoB,CACpB,AADoB,CACnB;AALY,QAAA,kBAAkB,sBAK9B;AACM,AAAF,GAAK;IACR,YAAY;IACZ,MAAM,EAAA,EAAA,CAAC,KAAK,CAAC,uBAAuB,EAAE;QACpC,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,KAAK,EAAE,GAAG,CAAC,OAAO;QAClB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,SAAS,EAAE,GAAG,CAAC,EAAE;KAClB,CAAC;IAEF,wBAAwB;IACxB,KAAK,EAAC,aAAa,EAAE,aAAa,GAAG;QACnC,MAAM,EAAE,OAAO;QACf,UAAU,EAAE,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAA,CAAA,CAAA,EAAC,EAAA,CAAC,UAAU,EAAG,GAAG,EAAA;QAC3D,OAAO,EAAE,GAAG,CAAC,OAAO,IAAQ,uBAAuB;QACnD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,WAAW,IAAQ,GAAG,CAAC,GAAG;KACrC;IAED,8BAA8B;IAC9B,EAAE,CAAE,GAAG,IAAA,CAAC,AAAD,EAAA,EAAA,CAAC,EAAE;CAAC,CAAA;AAAC,CAAC;IACX,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;AACnC,CAAC;AAED,8BAA8B;AAC9B,IAAI,GAAG,YAAY,QAAQ,IAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;IAC5C,aAAa,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AAChC,CAAC;AAED,iCAAiC;AACjC,IAAI,GAAG,YAAY,QAAQ,IAAQ,GAAG,CAAC,OAAO,EAAE,CAAC;IAC/C,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AACtC,CAAC;AAED,iCAAiC;AACjC,IAAI,CAAC,YAAY,EAAE,EAAC,CAAC;IACnB,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IAChC,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;AACjC,CAAC;AAED,sBAAsB;AACtB,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1D,CAAC;AACK,MAAM,cAAc,GAAI,CAC7B,GAAY,EACZ,GAAa,EACb,IAAkB,EACpB,EADoB,CACpB,AADoB,CACnB;AAJY,QAAA,cAAc,kBAI1B;AACM,AAAF,GAAK;IACR,wBAAwB;IACxB,KAAK,EAAC,aAAa,EAAE,aAAa,GAAG;QACnC,MAAM,EAAE,OAAO;QACf,UAAU,EAAE,GAAG;QACf,OAAO,EAAE,oBAAoB,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,IAAQ,GAAG,CAAC,GAAG,EAAE;QAC3E,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,WAAW,IAAQ,GAAG,CAAC,GAAG;KACrC;IAED,8BAA8B;IAC9B,EAAE,CAAE,GAAG,IAAA,CAAC,AAAD,EAAA,EAAA,CAAC,EAAE;CAAC,CAAA;AAAC,CAAC;IACX,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC;AACnC,CAAC;AAED,YAAY;AACZ,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;IAC9B,IAAI,EAAE,GAAG,CAAC,IAAI;IACd,MAAM,EAAE,GAAG,CAAC,MAAM;IAClB,SAAS,EAAE,GAAG,CAAC,EAAE;CAClB,CAAC,CAAC;AAEH,sBAAsB;AACtB,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACnC,CAAC;AAEF,kBAAe;IACb,qBAAqB,EAArB,6BAAqB;IACrB,kBAAkB,EAAlB,0BAAkB;IAClB,kBAAkB,EAAlB,0BAAkB;IAClB,cAAc,EAAd,sBAAc;CACf,CAAC"}