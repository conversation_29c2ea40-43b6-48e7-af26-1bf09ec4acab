import { Request, Response, NextFunction } from 'express';
// jscpd:ignore-file
import { User, Prisma } from "@prisma/client";
import { ModuleFactory, ModuleRegistry, Module, Container } from "../../core/module";
import { authMiddleware as ImportedauthMiddleware } from "../../middlewares/(auth).middleware";
import { ModuleFactory, ModuleRegistry, Module, Container } from "../../core/module";
import { authMiddleware as ImportedauthMiddleware } from "../../middlewares/(auth).middleware";
import {
  ErrorFactory,
  StringUtils,
  ObjectUtils,
  CryptoUtils,
  ValidationUtils,
  logger
} from "../../utils";

// Extend the Express Request type to include user property
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        role: string;
        merchantId?: string;
      };
    }
  }
}
import {
  ErrorFactory,
  StringUtils,
  ObjectUtils,
  CryptoUtils,
  ValidationUtils,
  logger
} from "../../utils";

/**
 * User Module
 * This module provides user functionality with zero duplication
 */
export class UserModule {
  private moduleFactory: ModuleFactory<User, (Prisma).UserCreateInput, (Prisma).UserUpdateInput>;
  private moduleRegistry: ModuleRegistry;
  private container: Container;
  private module: Module;

  /**
   * Create a new user module
   */
  constructor() {
    this.moduleRegistry = new ModuleRegistry();
    this.container = new Container();

    // Create module factory
    this.moduleFactory = new ModuleFactory<User, (Prisma).UserCreateInput, (Prisma).UserUpdateInput>(
      'user',
      'User'
    );

    // Get router, repository, service, and controller from factory
    const { router, repository, service, controller } = this.moduleFactory.build();

    // Configure router
    router
      .addRoute('post', '/login', (controller).login)
      .addRoute('post', '/register', (controller).register)
      .addRoute('post', '/logout', (controller).logout)
      .addRoute('get', '/me', (controller).getCurrentUser)
      .addRoute('put', '/me', (controller).updateCurrentUser)
      .addRoute('post', '/change-password', (controller).changePassword)
      .addRoute('post', '/forgot-password', (controller).forgotPassword)
      .addRoute('post', '/reset-password', (controller).resetPassword)
      .addMiddleware(authMiddleware);

    // Add custom repository methods
    this.moduleFactory.addRepositoryMethod(
      'findByEmail',
      async (email: string) => {
        try {
          return await (repository).findByField('email', email);
        } catch (error) {
          logger.error(`Error finding user by email ${email}:`, error);
          throw error;
        }
      }
    );

    this.moduleFactory.addRepositoryMethod(
      'findByResetToken',
      async (resetToken: string) => {
        try {
          return await (repository).findByField('resetToken', resetToken);
        } catch (error) {
          logger.error(`Error finding user by reset token:`, error);
          throw error;
        }
      }
    );

    // Add custom service methods
    this.moduleFactory.addServiceMethod(
      'login',
      async (email: string, password: string) => {
        try {
          // Find user by email
          const user = await (repository).findByEmail(email);

          // Check if user exists
          if (!user) {
            throw (ErrorFactory).authentication('Invalid email or password');
          }

          // Check if password is correct
          const isPasswordValid = await (CryptoUtils).verifyPassword(
            password,
            user.password,
            user.salt
          );

          if (!isPasswordValid) {
            throw (ErrorFactory).authentication('Invalid email or password');
          }

          // Generate token
          const token: string = await (CryptoUtils).generateToken();

          // Update user with new token
          await (service).update(user.id, {
            token,
            lastLoginAt: new Date()
          } as (Prisma).UserUpdateInput);

          logger.info(`User logged in: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          return {
            user: (ObjectUtils).omit(user, ['password', 'salt']),
            token
          };
        } catch (error) {
          logger.error(`Error logging in user ${email}:`, error);
          throw (ErrorFactory).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'register',
      async (userData: (Prisma).UserCreateInput) => {
        try {
          // Check if email is already in use
          const existingUser = await (repository).findByEmail((userData).email);

          if (existingUser) {
            throw (ErrorFactory).validation('Email is already in use');
          }

          // Hash password
          const { hash, salt } = await (CryptoUtils).hashPassword((userData).password);

          // Create user
          const user = await (service).create({
            ...userData,
            password: hash,
            salt,
            role: 'USER', // Default role
            status: 'ACTIVE', // Default status
            createdAt: new Date(),
            updatedAt: new Date()
          });

          logger.info(`User registered: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          return (ObjectUtils).omit(user, ['password', 'salt']);
        } catch (error) {
          logger.error(`Error registering user:`, error);
          throw (ErrorFactory).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'forgotPassword',
      async (email: string) => {
        try {
          // Find user by email
          const user = await (repository).findByEmail(email);

          // Check if user exists
          if (!user) {
            // Don't reveal that the user doesn't exist
            return { success: true };
          }

          // Generate reset token
          const resetToken = await (CryptoUtils).generateToken();
          const resetTokenExpiry: Date = new Date();
          (resetTokenExpiry).setHours((resetTokenExpiry).getHours() + 1); // Token valid for 1 hour

          // Update user with reset token
          await (service).update(user.id, {
            resetToken,
            resetTokenExpiry
          } as (Prisma).UserUpdateInput);

          logger.info(`Password reset requested for user: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          // In a real application, you would send an email with the reset token

          return { success: true };
        } catch (error) {
          logger.error(`Error requesting password reset for ${email}:`, error);
          throw (ErrorFactory).handle(error);
        }
      }
    );

    this.moduleFactory.addServiceMethod(
      'resetPassword',
      async (resetToken: string, newPassword: string) => {
        try {
          // Find user by reset token
          const user = await (repository).findByResetToken(resetToken);

          // Check if user exists and token is valid
          if (!user || !user.resetTokenExpiry || user.resetTokenExpiry < new Date() {
            throw (ErrorFactory).validation('Invalid or expired reset token');
          }

          // Hash new password
          const { hash, salt } = await (CryptoUtils).hashPassword(newPassword);

          // Update user with new password
          await (service).update(user.id, {
            password: hash,
            salt,
            resetToken: null,
            resetTokenExpiry: null,
            updatedAt: new Date()
          } as (Prisma).UserUpdateInput);

          logger.info(`Password reset for user: ${user.id}`, {
            userId: user.id,
            email: user.email
          });

          return { success: true };
        } catch (error) {
          logger.error(`Error resetting password:`, error);
          throw (ErrorFactory).handle(error);
        }
      }
    );

    // Add custom controller methods
    this.moduleFactory.addControllerMethod(
      'login',
      async (req, res) => {
        try {
          const { email, password } = req.body;

          // Validate input
          if (!email || !password) {
            throw (ErrorFactory).validation('Email and password are required');
          }

          // Login user
          const result = await (service).login(email, password);

          // Send success response
          return res.status(200).json({
            success: true,
            data: result
          });
        } catch (error) {
          logger.error(`Error logging in:`, error);
          return res.status(401).json({
            success: false,
            error: error.message || 'An error occurred while logging in'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'register',
      async (req, res) => {
        try {
          const userData = req.body;

          // Validate input
          if (!(userData).email || !(userData).password || !(userData).name) {
            throw (ErrorFactory).validation('Email, password, and name are required');
          }

          // Register user
          const user = await (service).register(userData);

          // Send success response
          return res.status(201).json({
            success: true,
            data: user
          });
        } catch (error) {
          logger.error(`Error registering user:`, error);
          return res.status(400).json({
            success: false,
            error: error.message || 'An error occurred while registering user'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'logout',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Update user to remove token
          await (service).update(id, {
            token: null,
            updatedAt: new Date()
          } as (Prisma).UserUpdateInput);

          // Send success response
          return res.status(200).json({
            success: true,
            message: 'Logged out successfully'
          });
        } catch (error) {
          logger.error(`Error logging out:`, error);
          return res.status(500).json({
            success: false,
            error: error.message || 'An error occurred while logging out'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'getCurrentUser',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Get user
          const user = await (service).getById(id);

          // Check if user exists
          if (!user) {
            throw (ErrorFactory).notFound('User', id);
          }

          // Send success response
          return res.status(200).json({
            success: true,
            data: (ObjectUtils).omit(user, ['password', 'salt'])
          });
        } catch (error) {
          logger.error(`Error getting current user:`, error);
          return res.status(500).json({
            success: false,
            error: error.message || 'An error occurred while getting current user'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'updateCurrentUser',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Get update data
          const updateData = req.body;

          // Don't allow updating password or role through this endpoint
          delete (updateData).password;
          delete (updateData).role;

          // Update user
          const user = await (service).update(id, {
            ...updateData,
            updatedAt: new Date()
          } as (Prisma).UserUpdateInput);

          // Send success response
          return res.status(200).json({
            success: true,
            data: (ObjectUtils).omit(user, ['password', 'salt'])
          });
        } catch (error) {
          logger.error(`Error updating current user:`, error);
          return res.status(500).json({
            success: false,
            error: error.message || 'An error occurred while updating current user'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'changePassword',
      async (req, res) => {
        try {
          // Get user ID from request
          const { id } = req.user;

          // Get password data
          const { currentPassword, newPassword } = req.body;

          // Validate input
          if (!currentPassword || !newPassword) {
            throw (ErrorFactory).validation('Current password and new password are required');
          }

          // Get user
          const user = await (service).getById(id);

          // Check if user exists
          if (!user) {
            throw (ErrorFactory).notFound('User', id);
          }

          // Check if current password is correct
          const isPasswordValid = await (CryptoUtils).verifyPassword(
            currentPassword,
            user.password,
            user.salt
          );

          if (!isPasswordValid) {
            throw (ErrorFactory).validation('Current password is incorrect');
          }

          // Hash new password
          const { hash, salt } = await (CryptoUtils).hashPassword(newPassword);

          // Update user with new password
          await (service).update(id, {
            password: hash,
            salt,
            updatedAt: new Date()
          } as (Prisma).UserUpdateInput);

          // Send success response
          return res.status(200).json({
            success: true,
            message: 'Password changed successfully'
          });
        } catch (error) {
          logger.error(`Error changing password:`, error);
          return res.status(400).json({
            success: false,
            error: error.message || 'An error occurred while changing password'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'forgotPassword',
      async (req, res) => {
        try {
          // Get email
          const { email } = req.body;

          // Validate input
          if (!email) {
            throw (ErrorFactory).validation('Email is required');
          }

          // Request password reset
          await (service).forgotPassword(email);

          // Send success response (always return success to prevent email enumeration)
          return res.status(200).json({
            success: true,
            message: 'If your email is registered, you will receive a password reset link'
          });
        } catch (error) {
          logger.error(`Error requesting password reset:`, error);
          // Still return success to prevent email enumeration
          return res.status(200).json({
            success: true,
            message: 'If your email is registered, you will receive a password reset link'
          });
        }
      }
    );

    this.moduleFactory.addControllerMethod(
      'resetPassword',
      async (req, res) => {
        try {
          // Get reset token and new password
          const { resetToken, newPassword } = req.body;

          // Validate input
          if (!resetToken || !newPassword) {
            throw (ErrorFactory).validation('Reset token and new password are required');
          }

          // Reset password
          await (service).resetPassword(resetToken, newPassword);

          // Send success response
          return res.status(200).json({
            success: true,
            message: 'Password reset successfully'
          });
        } catch (error) {
          logger.error(`Error resetting password:`, error);
          return res.status(400).json({
            success: false,
            error: error.message || 'An error occurred while resetting password'
          });
        }
      }
    );

    // Create module
    this.module = {
      name: 'user',
      router,
      repository,
      service,
      controller,
      dependencies: [],
      initialize: async () => {
        logger.info('Initializing user module');

        // Register dependencies
        this.container.registerSingleton('userRepository', () => repository);
        this.container.registerSingleton('userService', () => service);
        this.container.registerSingleton('userController', () => controller);

        logger.info('User module initialized');
      }
    };

    // Register the module
    this.moduleRegistry.registerModulethis.module);
  }

  /**
   * Get the module
   * @returns User module
   */
  getModule(): Module {
    return this.module;
  }
}