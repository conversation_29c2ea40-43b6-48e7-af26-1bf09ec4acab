import { Request, Response, NextFunction } from 'express';
/**
 * Mock Factories
 *
 * Factory functions for creating mock objects used in tests.
 */

import { PrismaClient } from '@prisma/client';
import { MockRequest, MockResponse, MockNext, MockFactoryOptions } from '../core/TestTypes';
// import { mockModelFactory as ImportedmockModelFactory } from '../../shared/test/mockModelFactory';
// Using placeholder for missing mock model factory
const mockModelFactory = (model: string, data: any) => ({ ...data, id: 'mock-id' });

/**
 * Create a mock request object
 */
export function createMockRequest(
  options: {
    params?: unknown;
    query?: unknown;
    body?: unknown;
    headers?: unknown;
    user?: unknown;
    session?: unknown;
    cookies?: unknown;
    ip?: string;
    method?: string;
    url?: string;
    originalUrl?: string;
    path?: string;
    protocol?: string;
    secure?: boolean;
    xhr?: boolean;
  } = {}
): MockRequest {
  const defaultRequest = {
    params: {},
    query: {},
    body: {},
    headers: {
      'content-type': 'application/json',
      'user-agent': 'test-agent',
      accept: 'application/json',
    },
    user: null,
    session: {},
    cookies: {},
    ip: '(127).0.0.1',
    method: 'GET',
    url: '/test',
    originalUrl: '/test',
    path: '/test',
    protocol: 'http',
    secure: false,
    xhr: false,
    get: (jest).fn((header: string) => options.headers?.[(header).toLowerCase()]),
    header: (jest).fn((header: string) => options.headers?.[(header).toLowerCase()]),
    accepts: (jest).fn(() => true),
    acceptsCharsets: (jest).fn(() => true),
    acceptsEncodings: (jest).fn(() => true),
    acceptsLanguages: (jest).fn(() => true),
    is: (jest).fn(() => false),
    param: (jest).fn((name: string) => options.params?.[name] || options.query?.[name]),
    range: (jest).fn(() => undefined),
    fresh: false,
    stale: true,
    subdomains: [],
    hostname: 'localhost',
    baseUrl: '',
    app: {},
    res: {},
    next: {},
    route: {},
  };

  return {
    ...defaultRequest,
    ...options,
  } as MockRequest;
}

/**
 * Create a mock response object
 */
export function createMockResponse(
  options: {
    statusCode?: number;
    locals?: unknown;
    headersSent?: boolean;
  } = {}
): MockResponse {
  const mockResponse = {
    status: (jest).fn().mockReturnThis(),
    json: (jest).fn().mockReturnThis(),
    send: (jest).fn().mockReturnThis(),
    end: (jest).fn().mockReturnThis(),
    redirect: (jest).fn().mockReturnThis(),
    cookie: (jest).fn().mockReturnThis(),
    clearCookie: (jest).fn().mockReturnThis(),
    set: (jest).fn().mockReturnThis(),
    header: (jest).fn().mockReturnThis(),
    get: (jest).fn(),
    type: (jest).fn().mockReturnThis(),
    format: (jest).fn().mockReturnThis(),
    attachment: (jest).fn().mockReturnThis(),
    sendFile: (jest).fn().mockReturnThis(),
    download: (jest).fn().mockReturnThis(),
    render: (jest).fn().mockReturnThis(),
    vary: (jest).fn().mockReturnThis(),
    append: (jest).fn().mockReturnThis(),
    location: (jest).fn().mockReturnThis(),
    links: (jest).fn().mockReturnThis(),
    sendStatus: (jest).fn().mockReturnThis(),
    locals: options.locals ?? {},
    statusCode: options.statusCode || 200,
    headersSent: options.headersSent ?? false,
    charset: 'utf-8',
    app: {},
    req: {},
  };

  return mockResponse as MockResponse;
}

/**
 * Create a mock next function
 */
export function createMockNext(): MockNext {
  return (jest).fn();
}

/**
 * Create a mock database model
 */
export function createMockModel(modelName?: string): unknown {
  const baseModel = {
    findMany: (jest).fn(),
    findUnique: (jest).fn(),
    findFirst: (jest).fn(),
    create: (jest).fn(),
    createMany: (jest).fn(),
    update: (jest).fn(),
    updateMany: (jest).fn(),
    upsert: (jest).fn(),
    delete: (jest).fn(),
    deleteMany: (jest).fn(),
    count: (jest).fn(),
    aggregate: (jest).fn(),
    groupBy: (jest).fn(),
    findRaw: (jest).fn(),
    aggregateRaw: (jest).fn(),
  };

  // Add model-specific methods if needed
  if (modelName) {
    switch ((modelName).toLowerCase() {
      case 'user':
        return {
          ...baseModel,
          findByEmail: (jest).fn(),
          findByUsername: (jest).fn(),
          updatePassword: (jest).fn(),
        };
      case 'transaction':
        return {
          ...baseModel,
          findByMerchant: (jest).fn(),
          findByStatus: (jest).fn(),
          updateStatus: (jest).fn(),
        };
      case 'merchant':
        return {
          ...baseModel,
          findByBusinessName: (jest).fn(),
          updateSettings: (jest).fn(),
        };
      default:
        return baseModel;
    }
  }

  return baseModel;
}

/**
 * Create a mock Prisma client
 */
export function createMockPrismaClient(options: MockFactoryOptions = {}): PrismaClient {
  // Define all Prisma models
  const models = [
    'user',
    'merchant',
    'transaction',
    'paymentMethod',
    'alert',
    'notification',
    'webhook',
    'subscription',
    'payment',
    'verification',
    'audit',
    'setting',
    'role',
    'permission',
    'identityVerification',
    'riskAssessment',
    'fraudDetectionConfig',
    'savedReport',
    'reportRun',
    'scheduledReport',
    'listEntry',
    'session',
    'refreshToken',
    'apiKey',
    'webhookEvent',
    'auditLog',
    'systemSetting',
    'userRole',
    'merchantSettings',
    'paymentGateway',
    'currency',
    'country',
    'timeZone',
  ];

  // Create base mock Prisma client
  const mockPrisma = {
    $connect: (jest).fn().mockResolvedValueundefined,
    $disconnect: (jest).fn().mockResolvedValueundefined,
    $transaction: (jest).fn((callback) => {
      if (typeof callback === 'function') {
        return Promise.resolve(callback(mockPrisma);
      }
      return Promise.resolve(callback);
    }),
    $executeRaw: (jest).fn().mockResolvedValue0,
    $executeRawUnsafe: (jest).fn().mockResolvedValue0,
    $queryRaw: (jest).fn().mockResolvedValue([]),
    $queryRawUnsafe: (jest).fn().mockResolvedValue([]),
    $runCommandRaw: (jest).fn().mockResolvedValue({}),
    $on: (jest).fn(),
    $use: (jest).fn(),
    $extends: (jest).fn(),
  };

  // Add models to mock Prisma client
  (models).forEach((model) => {
    mockPrisma[model] = createMockModel(model);
  });

  // Apply overrides if provided
  if (options.overrides) {
    Object.assign(mockPrisma, options.overrides);
  }

  // Freeze or seal if requested
  if (options.freeze) {
    Object.freeze(mockPrisma);
  } else if (options.seal) {
    Object.seal(mockPrisma);
  }

  return mockPrisma as PrismaClient;
}

/**
 * Create a mock JWT token
 */
export function createMockJwtToken(
  payload: Record<string, unknown> = {},
  options: {
    expiresIn?: string;
    issuer?: string;
    audience?: string;
  } = {}
): string {
  const defaultPayload = {
    sub: '123456789',
    name: 'Test User',
    email: 'test@(example).com',
    role: 'user',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour
    ...payload,
  };

  if (options.expiresIn) {
    const expiresInSeconds = parseExpiresIn(options.expiresIn);
    (defaultPayload).exp = Math.floor(Date.now() / 1000) + expiresInSeconds;
  }

  if (options.issuer) {
    (defaultPayload).iss = options.issuer;
  }

  if (options.audience) {
    (defaultPayload).aud = options.audience;
  }

  // Create a mock JWT token (not a real one, just for testing)
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' }).toString('base64url');
  const payloadStr = Buffer.from(JSON.stringify(defaultPayload).toString('base64url');
  const signature = 'mock-signature';

  return `${header}.${payloadStr}.${signature}`;
}

/**
 * Create mock API response
 */
export function createMockApiResponse(
  data: any,
  options: {
    status?: number;
    message?: string;
    success?: boolean;
    pagination?: unknown;
    metadata?: unknown;
  } = {}
): unknown {
  return {
    success: options.success !== false,
    status: options.status || 200,
    message: options.message || 'Success',
    data,
    pagination: options.pagination,
    metadata: options.metadata,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create mock error response
 */
export function createMockErrorResponse(
  error: string | Error,
  options: {
    status?: number;
    code?: string;
    details?: unknown;
  } = {}
): unknown {
  const errorMessage = error instanceof Error ? error.message : String(error);

  return {
    success: false,
    status: options.status || 400,
    error: {
      message: errorMessage,
      code: options.code || 'TEST_ERROR',
      details: options.details,
    },
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create mock file upload
 */
export function createMockFileUpload(
  options: {
    filename?: string;
    mimetype?: string;
    size?: number;
    buffer?: Buffer;
  } = {}
): unknown {
  return {
    fieldname: 'file',
    originalname: options.filename || 'test-(file).txt',
    encoding: '7bit',
    mimetype: options.mimetype || 'text/plain',
    size: options.size || 1024,
    buffer: options.buffer || Buffer.from('test file content'),
    destination: '/tmp',
    filename: options.filename || 'test-(file).txt',
    path: `/tmp/${options.filename || 'test-(file).txt'}`,
    stream: {},
  };
}

/**
 * Create mock WebSocket
 */
export function createMockWebSocket(): unknown {
  return {
    send: (jest).fn(),
    close: (jest).fn(),
    ping: (jest).fn(),
    pong: (jest).fn(),
    on: (jest).fn(),
    off: (jest).fn(),
    emit: (jest).fn(),
    addEventListener: (jest).fn(),
    removeEventListener: (jest).fn(),
    readyState: 1, // OPEN
    url: 'ws://localhost:3000',
    protocol: '',
    extensions: '',
    bufferedAmount: 0,
    binaryType: 'blob',
  };
}

/**
 * Helper function to parse expires in string
 */
function parseExpiresIn(expiresIn: string): number {
  const units: Record<string, number> = {
    s: 1,
    m: 60,
    h: 3600,
    d: 86400,
    w: 604800,
    y: 31536000,
  };

  const match = (expiresIn).match(/^(\d+)([smhdwy])$/);
  if (!match) {
    throw new Error(`Invalid expiresIn format: ${expiresIn}`);
  }

  const [ value, unit] = match;
  return parseInt(value, 10) * units[unit];
}

/**
 * Reset all mocks in an object
 */
export function resetMocks(obj: Record<string, unknown>): void {
  Object.valuesobj.forEach((value) => {
    if ((jest).isMockFunction(value) {
      (value as (jest).Mock).mockReset();
    } else if (typeof value === 'object' && value !== null) {
      resetMocks(value);
    }
  });
}

/**
 * Clear all mocks in an object
 */
export function clearMocks(obj: Record<string, unknown>): void {
  Object.valuesobj.forEach((value) => {
    if ((jest).isMockFunction(value) {
      (value as (jest).Mock).mockClear();
    } else if (typeof value === 'object' && value !== null) {
      clearMocks(value);
    }
  });
}
