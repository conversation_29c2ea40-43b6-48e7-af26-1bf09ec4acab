#!/bin/bash

# PRE-LAUNCH SECURITY VALIDATION SCRIPT
# Critical Financial Application - MANDATORY SECURITY CHECK
# Usage: ./pre-launch-security-validation.sh https://yourdomain.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="${1:-https://amazingpay.com}"
FAILED_CHECKS=0
TOTAL_CHECKS=0

echo -e "${RED}🚨 PRE-LAUNCH SECURITY VALIDATION${NC}"
echo -e "${RED}=================================${NC}"
echo -e "${YELLOW}⚠️  CRITICAL: This is a FINANCIAL APPLICATION${NC}"
echo -e "${YELLOW}⚠️  ALL security checks MUST pass before launch${NC}"
echo ""
echo -e "Target Domain: ${BLUE}$DOMAIN${NC}"
echo -e "Validation Time: ${BLUE}$(date)${NC}"
echo ""

# Function to run security check
security_check() {
    local description="$1"
    local command="$2"
    local critical="${3:-true}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "Checking $description... "
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        return 0
    else
        if [ "$critical" = "true" ]; then
            echo -e "${RED}❌ CRITICAL FAILURE${NC}"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        else
            echo -e "${YELLOW}⚠️  WARNING${NC}"
        fi
        return 1
    fi
}

# Function to test file access
test_file_access() {
    local url="$1"
    local description="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "Testing $description... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$url" 2>/dev/null || echo "000")
    
    if [[ "$response" == "403" || "$response" == "404" ]]; then
        echo -e "${GREEN}✅ SECURE (HTTP $response)${NC}"
        return 0
    else
        echo -e "${RED}❌ VULNERABLE (HTTP $response)${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        echo "   🚨 CRITICAL: Sensitive file accessible - DO NOT LAUNCH"
        return 1
    fi
}

echo -e "${PURPLE}1. CRITICAL FILE ACCESS PROTECTION${NC}"
echo "=================================="

# Test critical environment files
test_file_access "$DOMAIN/.env" "Environment file (.env)"
test_file_access "$DOMAIN/.env.production" "Production environment"
test_file_access "$DOMAIN/.env.local" "Local environment"

# Test configuration files
test_file_access "$DOMAIN/package.json" "Package configuration"
test_file_access "$DOMAIN/tsconfig.json" "TypeScript configuration"
test_file_access "$DOMAIN/webpack.config.js" "Webpack configuration"

# Test source code files
test_file_access "$DOMAIN/src/app.ts" "Source code files"
test_file_access "$DOMAIN/app.js" "Application files"
test_file_access "$DOMAIN/server.js" "Server files"

# Test version control
test_file_access "$DOMAIN/.git/config" "Git configuration"
test_file_access "$DOMAIN/.gitignore" "Git ignore file"

# Test dependencies
test_file_access "$DOMAIN/node_modules/package.json" "Node modules"

echo ""
echo -e "${PURPLE}2. DIRECTORY LISTING PROTECTION${NC}"
echo "==============================="

# Test directory listing
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "Testing root directory listing... "
response=$(curl -s --connect-timeout 10 "$DOMAIN/" 2>/dev/null)
if [[ "$response" == *"Index of"* || "$response" == *"Directory listing"* ]]; then
    echo -e "${RED}❌ DIRECTORY LISTING ENABLED${NC}"
    echo "   🚨 CRITICAL: Directory browsing enabled - DO NOT LAUNCH"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
else
    echo -e "${GREEN}✅ PROTECTED${NC}"
fi

# Test sensitive directories
test_file_access "$DOMAIN/config/" "Config directory"
test_file_access "$DOMAIN/src/" "Source directory"
test_file_access "$DOMAIN/storage/" "Storage directory"

echo ""
echo -e "${PURPLE}3. SECURITY HEADERS VALIDATION${NC}"
echo "=============================="

# Get headers
headers=$(curl -I -s --connect-timeout 10 "$DOMAIN" 2>/dev/null || echo "")

# Check critical security headers
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "X-Frame-Options header... "
if echo "$headers" | grep -qi "X-Frame-Options"; then
    echo -e "${GREEN}✅ PRESENT${NC}"
else
    echo -e "${RED}❌ MISSING${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "X-Content-Type-Options header... "
if echo "$headers" | grep -qi "X-Content-Type-Options"; then
    echo -e "${GREEN}✅ PRESENT${NC}"
else
    echo -e "${RED}❌ MISSING${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "Strict-Transport-Security header... "
if echo "$headers" | grep -qi "Strict-Transport-Security"; then
    echo -e "${GREEN}✅ PRESENT${NC}"
else
    echo -e "${RED}❌ MISSING${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "Content-Security-Policy header... "
if echo "$headers" | grep -qi "Content-Security-Policy"; then
    echo -e "${GREEN}✅ PRESENT${NC}"
else
    echo -e "${YELLOW}⚠️  MISSING (Recommended)${NC}"
fi

echo ""
echo -e "${PURPLE}4. SSL/TLS SECURITY VALIDATION${NC}"
echo "=============================="

# Test HTTPS redirect
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "HTTPS redirect... "
http_response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "${DOMAIN/https:/http:}" 2>/dev/null || echo "000")
if [[ "$http_response" == "301" || "$http_response" == "302" ]]; then
    echo -e "${GREEN}✅ REDIRECTS TO HTTPS${NC}"
else
    echo -e "${RED}❌ NO HTTPS REDIRECT${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

# Test SSL certificate
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "SSL certificate validity... "
if echo | openssl s_client -connect "${DOMAIN#https://}:443" -servername "${DOMAIN#https://}" 2>/dev/null | openssl x509 -noout -dates >/dev/null 2>&1; then
    echo -e "${GREEN}✅ VALID${NC}"
else
    echo -e "${RED}❌ INVALID${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

echo ""
echo -e "${PURPLE}5. SERVER INFORMATION DISCLOSURE${NC}"
echo "================================="

# Check for server information disclosure
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "Server header disclosure... "
if echo "$headers" | grep -qi "Server:"; then
    server_header=$(echo "$headers" | grep -i "Server:" | head -1)
    echo -e "${YELLOW}⚠️  DISCLOSED: $server_header${NC}"
else
    echo -e "${GREEN}✅ HIDDEN${NC}"
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
echo -n "X-Powered-By header disclosure... "
if echo "$headers" | grep -qi "X-Powered-By"; then
    powered_by=$(echo "$headers" | grep -i "X-Powered-By" | head -1)
    echo -e "${YELLOW}⚠️  DISCLOSED: $powered_by${NC}"
else
    echo -e "${GREEN}✅ HIDDEN${NC}"
fi

echo ""
echo -e "${PURPLE}6. COMPREHENSIVE SECURITY TEST${NC}"
echo "=============================="

# Run the comprehensive security file test if available
if [ -f "scripts/security-file-test.sh" ]; then
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "Running comprehensive file access test... "
    if bash scripts/security-file-test.sh "$DOMAIN" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ ALL TESTS PASSED${NC}"
    else
        echo -e "${RED}❌ SECURITY VULNERABILITIES FOUND${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        echo "   🚨 CRITICAL: Run 'bash scripts/security-file-test.sh $DOMAIN' for details"
    fi
else
    echo -e "${YELLOW}⚠️  Comprehensive test script not found${NC}"
fi

echo ""
echo -e "${BLUE}=================================${NC}"
echo -e "${BLUE}SECURITY VALIDATION RESULTS${NC}"
echo -e "${BLUE}=================================${NC}"

echo -e "Total Checks: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "Failed Checks: ${RED}$FAILED_CHECKS${NC}"
echo -e "Passed Checks: ${GREEN}$((TOTAL_CHECKS - FAILED_CHECKS))${NC}"

echo ""

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🏆 SECURITY VALIDATION PASSED${NC}"
    echo -e "${GREEN}✅ APPLICATION APPROVED FOR LAUNCH${NC}"
    echo ""
    echo -e "${GREEN}🔒 Your financial application meets security requirements!${NC}"
    echo -e "${GREEN}🚀 You may proceed with deployment.${NC}"
    echo ""
    echo -e "${BLUE}📋 Post-launch recommendations:${NC}"
    echo "1. Monitor security logs daily"
    echo "2. Run weekly security audits"
    echo "3. Keep security configurations updated"
    echo "4. Maintain incident response procedures"
    
    exit 0
else
    echo -e "${RED}🚨 SECURITY VALIDATION FAILED${NC}"
    echo -e "${RED}❌ APPLICATION NOT APPROVED FOR LAUNCH${NC}"
    echo ""
    echo -e "${RED}🛑 CRITICAL SECURITY ISSUES FOUND!${NC}"
    echo -e "${RED}   DO NOT LAUNCH UNTIL ALL ISSUES ARE RESOLVED${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Required actions:${NC}"
    echo "1. Fix all failed security checks"
    echo "2. Implement proper web server configuration"
    echo "3. Set correct file permissions"
    echo "4. Configure security headers"
    echo "5. Re-run this validation script"
    echo ""
    echo -e "${YELLOW}📚 Resources:${NC}"
    echo "- Review: docs/web-security-deployment-checklist.md"
    echo "- Implement: deployment/nginx-security.conf or deployment/apache-security.htaccess"
    echo "- Follow: CRITICAL-SECURITY-IMPLEMENTATION-PLAN.md"
    echo ""
    echo -e "${RED}⚠️  This is a FINANCIAL APPLICATION - Security is MANDATORY!${NC}"
    
    exit 1
fi
