#!/usr/bin/env node

/**
 * 🔗 AMAZINGPAY GITHUB WEBHOOK HANDLER
 * Handles GitHub webhooks for automated deployment
 * VPS: 159.65.92.74 | Domain: amazingpayme.com
 */

const express = require('express');
const crypto = require('crypto');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 🎯 CONFIGURATION
const CONFIG = {
    port: process.env.WEBHOOK_PORT || 9000,
    secret: process.env.WEBHOOK_SECRET || 'your-webhook-secret-here',
    appDir: '/www/wwwroot/amazingpayme.com',
    logDir: '/var/log/amazingpay',
    allowedBranches: ['main', 'production'],
    allowedEvents: ['push', 'workflow_run']
};

// 📝 LOGGING
const log = (message, level = 'info') => {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    
    console.log(logMessage);
    
    // Write to log file
    const logFile = path.join(CONFIG.logDir, 'webhook.log');
    fs.appendFileSync(logFile, logMessage + '\n');
};

// 🔐 VERIFY GITHUB SIGNATURE
const verifySignature = (payload, signature) => {
    if (!signature) {
        return false;
    }
    
    const expectedSignature = crypto
        .createHmac('sha256', CONFIG.secret)
        .update(payload)
        .digest('hex');
    
    const providedSignature = signature.replace('sha256=', '');
    
    return crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(providedSignature, 'hex')
    );
};

// 🚀 DEPLOY APPLICATION
const deployApplication = async (payload) => {
    const { ref, repository, head_commit } = payload;
    const branch = ref.replace('refs/heads/', '');
    
    log(`Starting deployment for branch: ${branch}`);
    log(`Commit: ${head_commit?.id || 'unknown'}`);
    log(`Message: ${head_commit?.message || 'No message'}`);
    
    try {
        // Change to application directory
        process.chdir(CONFIG.appDir);
        
        // Execute deployment script
        const deployScript = path.join(CONFIG.appDir, 'scripts', 'vps-update.sh');
        
        if (!fs.existsSync(deployScript)) {
            throw new Error('Deployment script not found');
        }
        
        log('Executing deployment script...');
        const output = execSync(`bash ${deployScript} ${branch}`, {
            encoding: 'utf8',
            timeout: 300000, // 5 minutes timeout
            maxBuffer: 1024 * 1024 // 1MB buffer
        });
        
        log('Deployment output:');
        log(output);
        
        log('✅ Deployment completed successfully');
        
        return {
            success: true,
            message: 'Deployment completed successfully',
            output: output
        };
        
    } catch (error) {
        log(`❌ Deployment failed: ${error.message}`, 'error');
        log(`Error output: ${error.stdout || ''}`, 'error');
        
        return {
            success: false,
            message: `Deployment failed: ${error.message}`,
            error: error.message
        };
    }
};

// 📊 GET DEPLOYMENT STATUS
const getDeploymentStatus = () => {
    try {
        // Check if application is running
        const pm2Status = execSync('pm2 jlist', { encoding: 'utf8' });
        const processes = JSON.parse(pm2Status);
        const amazingpayProcess = processes.find(p => p.name === 'amazingpay-main');
        
        // Get current commit
        process.chdir(CONFIG.appDir);
        const currentCommit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
        const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
        
        // Get last deployment time
        const logFile = path.join(CONFIG.logDir, 'deployment.log');
        let lastDeployment = 'Never';
        if (fs.existsSync(logFile)) {
            const logs = fs.readFileSync(logFile, 'utf8');
            const deploymentLogs = logs.split('\n').filter(line => line.includes('Deployment completed successfully'));
            if (deploymentLogs.length > 0) {
                const lastLog = deploymentLogs[deploymentLogs.length - 1];
                const match = lastLog.match(/\[(.*?)\]/);
                if (match) {
                    lastDeployment = match[1];
                }
            }
        }
        
        return {
            status: amazingpayProcess ? amazingpayProcess.pm2_env.status : 'stopped',
            commit: currentCommit,
            branch: currentBranch,
            lastDeployment: lastDeployment,
            uptime: amazingpayProcess ? amazingpayProcess.pm2_env.pm_uptime : null,
            memory: amazingpayProcess ? amazingpayProcess.monit.memory : null,
            cpu: amazingpayProcess ? amazingpayProcess.monit.cpu : null
        };
        
    } catch (error) {
        log(`Error getting deployment status: ${error.message}`, 'error');
        return {
            status: 'error',
            error: error.message
        };
    }
};

// 🌐 EXPRESS APP
const app = express();

// Middleware to capture raw body
app.use('/webhook', express.raw({ type: 'application/json' }));
app.use(express.json());

// 🏥 HEALTH CHECK
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'amazingpay-webhook-handler'
    });
});

// 📊 STATUS ENDPOINT
app.get('/status', (req, res) => {
    const status = getDeploymentStatus();
    res.json({
        webhook: 'active',
        deployment: status,
        timestamp: new Date().toISOString()
    });
});

// 🔗 GITHUB WEBHOOK ENDPOINT
app.post('/webhook/deploy', async (req, res) => {
    try {
        const signature = req.get('X-Hub-Signature-256');
        const event = req.get('X-GitHub-Event');
        const payload = req.body;
        
        log(`Received webhook: ${event}`);
        
        // Verify signature
        if (!verifySignature(payload, signature)) {
            log('Invalid signature', 'error');
            return res.status(401).json({ error: 'Invalid signature' });
        }
        
        // Parse payload
        const webhookPayload = JSON.parse(payload.toString());
        
        // Check if event is allowed
        if (!CONFIG.allowedEvents.includes(event)) {
            log(`Event ${event} not allowed`, 'warn');
            return res.status(200).json({ message: 'Event not processed' });
        }
        
        // Handle push events
        if (event === 'push') {
            const branch = webhookPayload.ref.replace('refs/heads/', '');
            
            // Check if branch is allowed
            if (!CONFIG.allowedBranches.includes(branch)) {
                log(`Branch ${branch} not allowed for deployment`, 'warn');
                return res.status(200).json({ message: 'Branch not allowed for deployment' });
            }
            
            // Deploy application
            const result = await deployApplication(webhookPayload);
            
            if (result.success) {
                res.status(200).json({
                    message: 'Deployment successful',
                    branch: branch,
                    commit: webhookPayload.head_commit?.id
                });
            } else {
                res.status(500).json({
                    message: 'Deployment failed',
                    error: result.message
                });
            }
        }
        
        // Handle workflow_run events (GitHub Actions)
        else if (event === 'workflow_run') {
            const { workflow_run } = webhookPayload;
            
            if (workflow_run.conclusion === 'success' && workflow_run.head_branch === 'main') {
                log('GitHub Actions workflow completed successfully, triggering deployment');
                
                const deployPayload = {
                    ref: `refs/heads/${workflow_run.head_branch}`,
                    repository: webhookPayload.repository,
                    head_commit: {
                        id: workflow_run.head_sha,
                        message: 'Automated deployment from GitHub Actions'
                    }
                };
                
                const result = await deployApplication(deployPayload);
                
                res.status(200).json({
                    message: result.success ? 'Deployment triggered' : 'Deployment failed',
                    workflow: workflow_run.name,
                    conclusion: workflow_run.conclusion
                });
            } else {
                res.status(200).json({ message: 'Workflow not eligible for deployment' });
            }
        }
        
    } catch (error) {
        log(`Webhook error: ${error.message}`, 'error');
        res.status(500).json({ error: 'Internal server error' });
    }
});

// 🔄 MANUAL DEPLOYMENT ENDPOINT
app.post('/deploy/:branch', async (req, res) => {
    const { branch } = req.params;
    
    if (!CONFIG.allowedBranches.includes(branch)) {
        return res.status(400).json({ error: 'Branch not allowed' });
    }
    
    log(`Manual deployment triggered for branch: ${branch}`);
    
    const payload = {
        ref: `refs/heads/${branch}`,
        repository: { name: 'amazingpay-flow' },
        head_commit: {
            id: 'manual',
            message: 'Manual deployment trigger'
        }
    };
    
    const result = await deployApplication(payload);
    
    if (result.success) {
        res.status(200).json({ message: 'Manual deployment successful', branch });
    } else {
        res.status(500).json({ message: 'Manual deployment failed', error: result.message });
    }
});

// 🚀 START SERVER
const server = app.listen(CONFIG.port, () => {
    log(`🔗 GitHub Webhook Handler started on port ${CONFIG.port}`);
    log(`📁 Application directory: ${CONFIG.appDir}`);
    log(`📝 Log directory: ${CONFIG.logDir}`);
    log(`🌿 Allowed branches: ${CONFIG.allowedBranches.join(', ')}`);
    log(`📡 Webhook endpoint: http://localhost:${CONFIG.port}/webhook/deploy`);
});

// 🛑 GRACEFUL SHUTDOWN
process.on('SIGTERM', () => {
    log('Received SIGTERM, shutting down gracefully');
    server.close(() => {
        log('Webhook handler stopped');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    log('Received SIGINT, shutting down gracefully');
    server.close(() => {
        log('Webhook handler stopped');
        process.exit(0);
    });
});

// 🚨 ERROR HANDLING
process.on('uncaughtException', (error) => {
    log(`Uncaught exception: ${error.message}`, 'error');
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    log(`Unhandled rejection at: ${promise}, reason: ${reason}`, 'error');
});

module.exports = app;
