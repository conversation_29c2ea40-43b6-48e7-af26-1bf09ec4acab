#!/bin/bash

# SECURITY FILE ACCESS TESTING SCRIPT
# Critical Financial Application File Protection Validation
# Usage: ./security-file-test.sh https://yourdomain.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="${1:-https://amazingpay.com}"
TIMEOUT=10
USER_AGENT="Security-Test-Bot/1.0"
FAILED_TESTS=0
TOTAL_TESTS=0

echo -e "${BLUE}🔒 SECURITY FILE ACCESS TESTING${NC}"
echo -e "${BLUE}=================================${NC}"
echo -e "Target Domain: ${YELLOW}$DOMAIN${NC}"
echo -e "Timestamp: ${YELLOW}$(date)${NC}"
echo ""

# Function to test file access
test_file_access() {
    local url="$1"
    local description="$2"
    local expected_codes="${3:-403,404}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing $description... "
    
    # Make request and capture response code
    response=$(curl -s -o /dev/null -w "%{http_code}" \
        --connect-timeout $TIMEOUT \
        --max-time $TIMEOUT \
        -A "$USER_AGENT" \
        "$url" 2>/dev/null || echo "000")
    
    # Check if response code is in expected codes
    if [[ ",$expected_codes," == *",$response,"* ]]; then
        echo -e "${GREEN}✅ SECURE${NC} (HTTP $response)"
    else
        echo -e "${RED}❌ VULNERABLE${NC} (HTTP $response)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        # Log vulnerable URL for further investigation
        echo "$url - HTTP $response" >> vulnerable-files.log
    fi
}

# Function to test directory listing
test_directory_listing() {
    local url="$1"
    local description="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing $description... "
    
    # Make request and capture both response code and content
    response=$(curl -s -w "%{http_code}" \
        --connect-timeout $TIMEOUT \
        --max-time $TIMEOUT \
        -A "$USER_AGENT" \
        "$url" 2>/dev/null)
    
    # Extract HTTP code (last 3 characters)
    http_code="${response: -3}"
    content="${response%???}"
    
    # Check for directory listing indicators
    if [[ "$http_code" == "403" || "$http_code" == "404" ]]; then
        echo -e "${GREEN}✅ SECURE${NC} (HTTP $http_code)"
    elif [[ "$content" == *"Index of"* || "$content" == *"Directory listing"* || "$content" == *"<title>Index of"* ]]; then
        echo -e "${RED}❌ DIRECTORY LISTING ENABLED${NC} (HTTP $http_code)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        echo "$url - Directory listing enabled" >> vulnerable-files.log
    else
        echo -e "${GREEN}✅ SECURE${NC} (HTTP $http_code - No listing)"
    fi
}

# Initialize log file
> vulnerable-files.log

echo -e "${YELLOW}1. TESTING ENVIRONMENT FILES${NC}"
echo "=============================="
test_file_access "$DOMAIN/.env" "Environment file (.env)"
test_file_access "$DOMAIN/.env.local" "Local environment (.env.local)"
test_file_access "$DOMAIN/.env.production" "Production environment (.env.production)"
test_file_access "$DOMAIN/.env.staging" "Staging environment (.env.staging)"
test_file_access "$DOMAIN/.env.development" "Development environment (.env.development)"
test_file_access "$DOMAIN/.env.example" "Example environment (.env.example)"
test_file_access "$DOMAIN/config.env" "Config environment (config.env)"

echo ""
echo -e "${YELLOW}2. TESTING CONFIGURATION FILES${NC}"
echo "==============================="
test_file_access "$DOMAIN/tsconfig.json" "TypeScript config"
test_file_access "$DOMAIN/package.json" "Package file"
test_file_access "$DOMAIN/package-lock.json" "Package lock file"
test_file_access "$DOMAIN/yarn.lock" "Yarn lock file"
test_file_access "$DOMAIN/composer.json" "Composer file"
test_file_access "$DOMAIN/composer.lock" "Composer lock"
test_file_access "$DOMAIN/webpack.config.js" "Webpack config"
test_file_access "$DOMAIN/vite.config.js" "Vite config"
test_file_access "$DOMAIN/next.config.js" "Next.js config"
test_file_access "$DOMAIN/nuxt.config.js" "Nuxt.js config"

echo ""
echo -e "${YELLOW}3. TESTING SOURCE CODE FILES${NC}"
echo "============================="
test_file_access "$DOMAIN/src/app.ts" "Main application file"
test_file_access "$DOMAIN/src/index.ts" "Index TypeScript file"
test_file_access "$DOMAIN/src/main.js" "Main JavaScript file"
test_file_access "$DOMAIN/app.js" "Root app file"
test_file_access "$DOMAIN/index.js" "Root index file"
test_file_access "$DOMAIN/server.js" "Server file"
test_file_access "$DOMAIN/config/database.config.ts" "Database config"
test_file_access "$DOMAIN/config/auth.ts" "Auth config"

echo ""
echo -e "${YELLOW}4. TESTING VERSION CONTROL FILES${NC}"
echo "================================="
test_file_access "$DOMAIN/.git/config" "Git config"
test_file_access "$DOMAIN/.git/HEAD" "Git HEAD"
test_file_access "$DOMAIN/.gitignore" "Git ignore file"
test_file_access "$DOMAIN/.gitmodules" "Git modules"
test_file_access "$DOMAIN/.svn/entries" "SVN entries"
test_file_access "$DOMAIN/.hg/hgrc" "Mercurial config"

echo ""
echo -e "${YELLOW}5. TESTING BACKUP AND TEMPORARY FILES${NC}"
echo "======================================"
test_file_access "$DOMAIN/.env.bak" "Environment backup"
test_file_access "$DOMAIN/.env.backup" "Environment backup (alt)"
test_file_access "$DOMAIN/config.bak" "Config backup"
test_file_access "$DOMAIN/database.sql" "Database dump"
test_file_access "$DOMAIN/backup.sql" "Backup SQL file"
test_file_access "$DOMAIN/.DS_Store" "macOS metadata"
test_file_access "$DOMAIN/Thumbs.db" "Windows metadata"
test_file_access "$DOMAIN/.htaccess.bak" "Apache config backup"
test_file_access "$DOMAIN/web.config.bak" "IIS config backup"

echo ""
echo -e "${YELLOW}6. TESTING LOG FILES${NC}"
echo "==================="
test_file_access "$DOMAIN/logs/app.log" "Application log"
test_file_access "$DOMAIN/logs/error.log" "Error log"
test_file_access "$DOMAIN/logs/access.log" "Access log"
test_file_access "$DOMAIN/storage/logs/laravel.log" "Laravel log"
test_file_access "$DOMAIN/var/log/app.log" "System app log"
test_file_access "$DOMAIN/debug.log" "Debug log"
test_file_access "$DOMAIN/error_log" "PHP error log"

echo ""
echo -e "${YELLOW}7. TESTING DEPENDENCY DIRECTORIES${NC}"
echo "=================================="
test_file_access "$DOMAIN/node_modules/package.json" "Node modules"
test_file_access "$DOMAIN/vendor/autoload.php" "Composer vendor"
test_file_access "$DOMAIN/bower_components/bower.json" "Bower components"

echo ""
echo -e "${YELLOW}8. TESTING DIRECTORY LISTING${NC}"
echo "============================="
test_directory_listing "$DOMAIN/" "Root directory"
test_directory_listing "$DOMAIN/config/" "Config directory"
test_directory_listing "$DOMAIN/src/" "Source directory"
test_directory_listing "$DOMAIN/app/" "App directory"
test_directory_listing "$DOMAIN/storage/" "Storage directory"
test_directory_listing "$DOMAIN/logs/" "Logs directory"
test_directory_listing "$DOMAIN/uploads/" "Uploads directory"
test_directory_listing "$DOMAIN/assets/" "Assets directory"

echo ""
echo -e "${YELLOW}9. TESTING COMMON SENSITIVE PATHS${NC}"
echo "=================================="
test_file_access "$DOMAIN/admin/" "Admin panel" "403,404,401"
test_file_access "$DOMAIN/phpmyadmin/" "phpMyAdmin" "403,404"
test_file_access "$DOMAIN/wp-admin/" "WordPress admin" "403,404"
test_file_access "$DOMAIN/wp-config.php" "WordPress config"
test_file_access "$DOMAIN/database.php" "Database config file"
test_file_access "$DOMAIN/config.php" "PHP config file"
test_file_access "$DOMAIN/settings.php" "Settings file"

echo ""
echo -e "${YELLOW}10. TESTING DEVELOPMENT FILES${NC}"
echo "=============================="
test_file_access "$DOMAIN/README.md" "README file"
test_file_access "$DOMAIN/CHANGELOG.md" "Changelog"
test_file_access "$DOMAIN/LICENSE" "License file"
test_file_access "$DOMAIN/TODO.txt" "TODO file"
test_file_access "$DOMAIN/INSTALL.txt" "Install instructions"
test_file_access "$DOMAIN/docker-compose.yml" "Docker compose"
test_file_access "$DOMAIN/Dockerfile" "Dockerfile"
test_file_access "$DOMAIN/.dockerignore" "Docker ignore"

echo ""
echo -e "${BLUE}=================================${NC}"
echo -e "${BLUE}SECURITY TEST RESULTS${NC}"
echo -e "${BLUE}=================================${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🏆 ALL TESTS PASSED${NC}"
    echo -e "${GREEN}✅ Total Tests: $TOTAL_TESTS${NC}"
    echo -e "${GREEN}✅ Failed Tests: $FAILED_TESTS${NC}"
    echo -e "${GREEN}✅ Security Status: SECURE${NC}"
    echo ""
    echo -e "${GREEN}🔒 Your application is properly protected against file exposure!${NC}"
    
    # Clean up log file if no vulnerabilities
    rm -f vulnerable-files.log
    
    exit 0
else
    echo -e "${RED}⚠️  SECURITY ISSUES FOUND${NC}"
    echo -e "${RED}❌ Total Tests: $TOTAL_TESTS${NC}"
    echo -e "${RED}❌ Failed Tests: $FAILED_TESTS${NC}"
    echo -e "${RED}❌ Security Status: VULNERABLE${NC}"
    echo ""
    echo -e "${RED}🚨 IMMEDIATE ACTION REQUIRED!${NC}"
    echo ""
    echo -e "${YELLOW}Vulnerable files found:${NC}"
    if [ -f vulnerable-files.log ]; then
        cat vulnerable-files.log
        echo ""
        echo -e "${YELLOW}📄 Full vulnerability report saved to: vulnerable-files.log${NC}"
    fi
    echo ""
    echo -e "${YELLOW}🔧 Recommended actions:${NC}"
    echo "1. Review web server configuration"
    echo "2. Check file permissions"
    echo "3. Verify directory structure"
    echo "4. Update .htaccess or nginx.conf"
    echo "5. Re-run this test after fixes"
    
    exit 1
fi
