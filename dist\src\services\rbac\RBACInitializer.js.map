{"version": 3, "file": "RBACInitializer.js", "sourceRoot": "", "sources": ["../../../../src/services/rbac/RBACInitializer.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;;GAIG;;;AAGH,mEAA+E;AAU/E;;GAEG;AACH,MAAa,eAAe;IAGxB;;;;KAIC;IACD,YAAY,MAAoB;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;KAEC;IACM,KAAK,CAAC,UAAU;QACnB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,IAAI,CAAC;YACD,qDAAqD;YACrD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAExD,IAAI,aAAa,EAAE,CAAC;gBAChB,yBAAyB;gBACzB,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAEnC,mBAAmB;gBACnB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAE7B,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;gBAChF,MAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;YACzF,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IAED;;KAEC;IACO,KAAK,CAAC,oBAAoB;QAC9B,IAAI,CAAC;YACD,0DAA0D;YAC1D,MAAM,kBAAkB,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACpD,MAAM,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAC7C,MAAM,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YAC5D,MAAM,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YAEhD,MAAM,cAAc,GAAG,kBAAkB,IAAQ,YAAY,IAAQ,sBAAsB,IAAQ,gBAAgB,CAAC;YAEpH,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBACpE,IAAI,CAAC,kBAAkB;oBAAE,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBACtE,IAAI,CAAC,YAAY;oBAAE,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBAC/D,IAAI,CAAC,sBAAsB;oBAAE,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAC9E,IAAI,CAAC,gBAAgB;oBAAE,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACtE,CAAC;YAED,OAAO,cAAc,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;KAEC;IACO,KAAK,CAAC,qBAAqB;QAC/B,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,IAAI,CAAC;YACD,6CAA6C;YAC7C,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,UAAU,EAAG,EAAE,CAAE,AAAF,GAAK;gBAC5D,KAAK,EAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;gBAEhD,MAAM,EAAC;oBACH,QAAQ;oBACR,MAAM;oBACN,WAAW,EAAE,GAAG,MAAM,IAAI,QAAQ,EAAE;iBACvC;aACJ,CAAC,CAAC;YAEH,uCAAuC;YACvC,IAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACxB,iCAAiC;gBACjC,KAAK,MAAM,UAAU,IAAI,cAAc,EAAE,CAAC;oBACtC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;wBAChC,KAAK,EAAE,EAAE,eAAe,EAAE;gCAClB,QAAQ,EAAE,UAAU,CAAC,QAAQ;gCAC7B,MAAM,EAAE,UAAU,CAAC,MAAM;6BAC5B;yBACJ;wBACD,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,CAAC,WAAW;yBAC5C;wBACD,MAAM,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ;4BACnC,MAAM,EAAE,UAAU,CAAC,MAAM;4BACzB,WAAW,EAAE,UAAU,CAAC,WAAW;yBACtC;qBACJ,CAAC,CAAC;gBACP,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,eAAe,cAAc,CAAC,CAAC,MAAM,CAAA;YAAA,CAAC;YAAC,WAAW,CAAA;;;;;;;;;;;;;;;;;;;;;;;;6BAwBjD,CAAA;YAAA,WAAW,CAAA;YAAC,CAAC,CAAA;YAAA,CAAC;gBAAA,MAAM,CAAC,IAAI,CAAC,8BAAc,CAAC,MAAM,CAAA,CAAA;YAAA,CAAC;YAAC,KAAK,CAAA;;;;;;;;;;;;;;;;;;;6BAmBtD,CAAA;YAAA,SAAS,CAAA;YAAC,GAAG,CAAA;YAAC,KAAK,IAAI,MAAM,CAAA;YAAC,MAAM,CAAE,QAAQ,CAAA;YAAC,IAAI,CAAA;YAAC,QAAQ,CAAA;YAAC,KAAI,CAAC,EAAA,EAAC,QAAQ,EAAC,EAAA,CAAC,IAAI,EAAC,CAAC,CAAA;;;;;;;;;;;;;;;;;;;;;6BAqBnF,EAAA,UAAU;gBAAC,KAAK,CAAA;YAAC,GAAG,CAAA;YAAC,KAAK,IAAI,MAAM,CAAA;YAAC,MAAM,CAAE,QAAQ,CAAA;YAAC,UAAU,CAAA;YAAC,UAAU,CAAA;YAAC,KAAI,IAAI,EAAC,CAAC,EAAA,EAAC,QAAQ,EAAC,EAAA,CAAC,IAAI,EAAC,CAAC,CAAA;;;;;;;;;;;;;;;;;;6BAkBvG;gBAAA,cAAc,CAAA;YAAC,KAAK,CAAA;YAAC,GAAG,CAAA;YAAC,KAAK,IAAI,MAAM,CAAA;YAAC,MAAM,CAAE,QAAQ,CAAA;YAAC,UAAU,CAAA;YAAC,UAAU,CAAA;YAAC,KAAI,IAAI,EAAC,CAAC,EAAA,EAAC,QAAQ,EAAC,EAAA,CAAC,IAAI,EAAC,CAAC,CAAA;;;;;;;;;;;;;;;;;;;yBAmB/G;gBAAA,OAAO,GAAC,OAAO,CAAA;YAAC,IAAI,EAAE,CAAC,CAAA;YAAA,CAAC;gBAAA,QAAQ,CAAA;gBAAE,IAAI,CAAA;YAAA,CAAC;YAAC,MAAK,CAAC,EAAA,CAAC;gBAAA,WAAW,CAAA;gBAAE,MAAM,CAAA;YAAA,CAAC;YAAC,WAAW,CAAA;;0BAE9E,CAAA;YAAA,KAAK,CAAA;YAAC,QAAQ,GAAC,QAAQ,CAAA;YAAC,IAAI,CAAA;YAAC,CAAC,CAAA;YAAA,CAAC;gBAAA,QAAQ,CAAA;gBAAE,IAAI,CAAA;YAAA,CAAC;YAAC;;;;;;;;;;;;;;yBAchD,CAAA;YAAA,QAAQ,CAAA;YAAC,KAAK,CAAC,KAAK,CAAA;YAAC,IAAI,EAAE,CAAC,CAAA;YAAA,CAAC;gBAAA,KAAK,CAAA;YAAA,CAAC;YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yBA8DnC,CAAA;YAAA,KAAK,CAAA;YAAC,KAAK,CAAA;YAAC,IAAI,CAAA;YAAC,OAAO,EAAE,CAAC,CAAA;YAAA,CAAC;gBAAA,KAAK,CAAA;YAAA,CAAC;YAAA;;;;;;;;;;;;;;;;;;;YAmB3D,CAAA;QAAA,CAAC,AAAD;gBAAA,CAAC,CAAD,CAAC,AAAD;IAAA,CAAC,AAAD;CAAA;AA/SA,0CA+SA"}