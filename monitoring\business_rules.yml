# Business Metrics and KPI Alert Rules for AmazingPay Flow
# Custom business logic monitoring and alerting

groups:
  # Revenue and Transaction Metrics
  - name: revenue_metrics
    rules:
      - alert: LowDailyRevenue
        expr: sum(increase(payment_amount_total[24h])) < 10000
        for: 1h
        labels:
          severity: warning
          category: business
          kpi: revenue
        annotations:
          summary: "Daily revenue below threshold"
          description: "Daily revenue is below $10,000 for the last 24 hours"

      - alert: RevenueDropSignificant
        expr: (sum(rate(payment_amount_total[1h])) - sum(rate(payment_amount_total[1h] offset 24h))) / sum(rate(payment_amount_total[1h] offset 24h)) < -0.3
        for: 30m
        labels:
          severity: critical
          category: business
          kpi: revenue
        annotations:
          summary: "Significant revenue drop detected"
          description: "Revenue has dropped by more than 30% compared to same time yesterday"

      - alert: HighValueTransactionAnomaly
        expr: rate(payment_transactions_total{amount_range="high"}[5m]) > 2 * rate(payment_transactions_total{amount_range="high"}[5m] offset 1h)
        for: 10m
        labels:
          severity: warning
          category: business
          kpi: transactions
        annotations:
          summary: "Unusual high-value transaction activity"
          description: "High-value transactions are 2x higher than usual"

  # Customer Experience Metrics
  - name: customer_experience
    rules:
      - alert: HighCustomerDropoffRate
        expr: rate(checkout_abandoned_total[5m]) / rate(checkout_started_total[5m]) > 0.7
        for: 10m
        labels:
          severity: warning
          category: business
          kpi: conversion
        annotations:
          summary: "High checkout abandonment rate"
          description: "Checkout abandonment rate is above 70%"

      - alert: LowCustomerSatisfaction
        expr: avg(customer_satisfaction_score) < 3.5
        for: 30m
        labels:
          severity: warning
          category: business
          kpi: satisfaction
        annotations:
          summary: "Low customer satisfaction score"
          description: "Average customer satisfaction is below 3.5/5"

      - alert: HighSupportTicketVolume
        expr: rate(support_tickets_created_total[1h]) > 50
        for: 30m
        labels:
          severity: warning
          category: business
          kpi: support
        annotations:
          summary: "High support ticket volume"
          description: "Support ticket creation rate exceeds 50 per hour"

  # Fraud and Risk Metrics
  - name: fraud_risk_metrics
    rules:
      - alert: FraudRateSpike
        expr: rate(fraud_detections_total{status="confirmed_fraud"}[5m]) > 3 * rate(fraud_detections_total{status="confirmed_fraud"}[5m] offset 1h)
        for: 5m
        labels:
          severity: critical
          category: security
          kpi: fraud
        annotations:
          summary: "Fraud rate spike detected"
          description: "Confirmed fraud rate is 3x higher than usual"

      - alert: SuspiciousGeographicActivity
        expr: count by (country) (rate(payment_transactions_total[5m])) > 5 and on (country) fraud_risk_score_by_country > 0.8
        for: 10m
        labels:
          severity: warning
          category: security
          kpi: geographic_risk
        annotations:
          summary: "Suspicious activity from high-risk country"
          description: "High transaction volume from country {{ $labels.country }} with fraud risk score > 0.8"

      - alert: IdentityVerificationBacklog
        expr: identity_verification_queue_size > 1000
        for: 15m
        labels:
          severity: warning
          category: operations
          kpi: verification
        annotations:
          summary: "Identity verification backlog"
          description: "Identity verification queue has more than 1000 pending items"

  # Operational Efficiency Metrics
  - name: operational_efficiency
    rules:
      - alert: LowAPISuccessRate
        expr: rate(api_requests_total{status=~"2.."}[5m]) / rate(api_requests_total[5m]) < 0.95
        for: 10m
        labels:
          severity: warning
          category: operations
          kpi: api_success
        annotations:
          summary: "Low API success rate"
          description: "API success rate is below 95%"

      - alert: DatabaseQueryPerformanceDegraded
        expr: histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m])) > 1
        for: 10m
        labels:
          severity: warning
          category: operations
          kpi: database_performance
        annotations:
          summary: "Database query performance degraded"
          description: "95th percentile database query time exceeds 1 second"

      - alert: CacheHitRateLow
        expr: rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m])) < 0.8
        for: 15m
        labels:
          severity: warning
          category: operations
          kpi: cache_efficiency
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is below 80%"

  # Compliance and Regulatory Metrics
  - name: compliance_metrics
    rules:
      - alert: KYCComplianceRate
        expr: rate(kyc_verifications_total{status="approved"}[1h]) / rate(kyc_verifications_total[1h]) < 0.9
        for: 30m
        labels:
          severity: warning
          category: compliance
          kpi: kyc
        annotations:
          summary: "Low KYC approval rate"
          description: "KYC approval rate is below 90%"

      - alert: AMLFlagsHigh
        expr: rate(aml_flags_total[1h]) > 10
        for: 30m
        labels:
          severity: warning
          category: compliance
          kpi: aml
        annotations:
          summary: "High AML flag rate"
          description: "AML flags exceed 10 per hour"

      - alert: RegulatoryReportingDelay
        expr: time() - regulatory_last_report_timestamp > 86400
        for: 1h
        labels:
          severity: critical
          category: compliance
          kpi: reporting
        annotations:
          summary: "Regulatory reporting delay"
          description: "Last regulatory report was more than 24 hours ago"

  # Partner and Integration Metrics
  - name: partner_integration
    rules:
      - alert: PaymentProviderDown
        expr: payment_provider_availability < 0.95
        for: 5m
        labels:
          severity: critical
          category: integration
          kpi: payment_provider
        annotations:
          summary: "Payment provider availability low"
          description: "Payment provider {{ $labels.provider }} availability is below 95%"

      - alert: ThirdPartyAPILatencyHigh
        expr: histogram_quantile(0.95, rate(third_party_api_duration_seconds_bucket[5m])) > 5
        for: 10m
        labels:
          severity: warning
          category: integration
          kpi: api_latency
        annotations:
          summary: "High third-party API latency"
          description: "95th percentile third-party API latency exceeds 5 seconds"

      - alert: WebhookDeliveryFailures
        expr: rate(webhook_delivery_failures_total[5m]) > 5
        for: 10m
        labels:
          severity: warning
          category: integration
          kpi: webhooks
        annotations:
          summary: "High webhook delivery failure rate"
          description: "Webhook delivery failures exceed 5 per second"

  # Business Growth Metrics
  - name: growth_metrics
    rules:
      - alert: NewUserRegistrationDrop
        expr: rate(user_registrations_total[1h]) < 0.5 * rate(user_registrations_total[1h] offset 24h)
        for: 2h
        labels:
          severity: warning
          category: growth
          kpi: user_acquisition
        annotations:
          summary: "New user registration drop"
          description: "New user registrations are 50% below yesterday's rate"

      - alert: MerchantOnboardingStalled
        expr: rate(merchant_onboarding_completed_total[24h]) == 0
        for: 4h
        labels:
          severity: warning
          category: growth
          kpi: merchant_acquisition
        annotations:
          summary: "Merchant onboarding stalled"
          description: "No merchant onboarding completed in the last 24 hours"

      - alert: TransactionVolumeGrowthNegative
        expr: (rate(payment_transactions_total[7d]) - rate(payment_transactions_total[7d] offset 7d)) / rate(payment_transactions_total[7d] offset 7d) < -0.1
        for: 1d
        labels:
          severity: warning
          category: growth
          kpi: transaction_growth
        annotations:
          summary: "Negative transaction volume growth"
          description: "Weekly transaction volume has decreased by more than 10%"
