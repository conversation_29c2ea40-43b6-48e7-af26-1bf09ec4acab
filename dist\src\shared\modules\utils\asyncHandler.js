"use strict";
/**
 * Async Handler
 *
 * This module provides a utility function for handling async controller methods.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.functionName = void 0;
/**
 * Wrap an async function to catch errors and pass them to the next middleware
 */
exports.functionName = asyncHandler = (fn =  >  > (Promise)) => ;
 >  > {
    return: async (req, res, next) => 
} >  > {
    try: {
        await
    }, catch(error) {
        next(error);
    }
};
;
//# sourceMappingURL=asyncHandler.js.map