/**
 * Error Handler Utility
 *
 * This utility provides centralized error handling for the application.
 * It includes functions for handling errors in controllers, services, and middleware.
 */
import { Request, Response, NextFunction } from 'express';
/**
 * Error response structure
 */
export interface ErrorResponse {
    status: string;
    statusCode: number;
    message: string;
    error?: string;
    stack?: string;
    timestamp: string;
    path: string;
    requestId?: string;
    code?: string;
    details?: unknown;
}
/**
 * Handle controller errors
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export declare const handleControllerError: (err: Error | AppError, req: Request, res: Response, next: NextFunction) => any;
export declare const handleServiceError: (err: Error | AppError, serviceName: string, methodName: string, params?: unknown) => any;
/**
 * Global error handler middleware
 * @param err Error
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export declare const globalErrorHandler: (err: Error | AppError, req: Request, res: Response, next: NextFunction) => any;
export declare const handle404Error: (req: Request, res: Response, next: NextFunction) => any;
declare const _default: {
    handleControllerError: (err: Error | AppError, req: Request, res: Response, next: NextFunction) => any;
    handleServiceError: (err: Error | AppError, serviceName: string, methodName: string, params?: unknown) => any;
    globalErrorHandler: (err: Error | AppError, req: Request, res: Response, next: NextFunction) => any;
    handle404Error: (req: Request, res: Response, next: NextFunction) => any;
};
export default _default;
//# sourceMappingURL=error-handler.d.ts.map