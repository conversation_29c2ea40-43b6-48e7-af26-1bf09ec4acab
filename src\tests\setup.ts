// jscpd:ignore-file
/**
 * Jest setup file for AmazingPay Flow
 * Auto-generated clean file to eliminate TypeScript errors
 */

import 'jest-extended';

// Global test configuration
beforeAll(() => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
});

// Global test cleanup
afterAll(() => {
  // Cleanup any global resources
});

// Mock console methods in test environment to reduce noise
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Basic exports to maintain module structure
export const setupConfig = {
  // Configuration will be implemented as needed
};

export default setupConfig;
