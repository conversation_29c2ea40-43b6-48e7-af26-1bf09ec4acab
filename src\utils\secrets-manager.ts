// jscpd:ignore-file
class SecretsManager {
    private secrets: Map<string, { value: string }> = new Map();

    async initialize(): Promise<void> {
        this.secrets.set('JWT_SECRET', { value: process.env.JWT_SECRET || 'default-secret' });
    }

    getSecret(key: string): { value: string } | undefined {
        return this.secrets.get(key);
    }

    getDatabaseUrl(): string {
        return process.env.DATABASE_URL || '';
    }
}

const secretsManager = new SecretsManager();
export default secretsManager;
export { secretsManager };
