// jscpd:ignore-file
/**
 * Common Fee Calculation Strategies
 *
 * Implements common fee calculation strategies.
 */

import { IFeeCalculationStrategy, FeeCalculationContext } from '../FeeCalculator';
import { PrismaClient } from '@prisma/client';
import { logger as Importedlogger } from '../../../../lib/logger';
import { Merchant as ImportedMerchant } from '../types';
import { PrismaClient } from '@prisma/client';
import { logger as Importedlogger } from '../../../../lib/logger';
import { Merchant as ImportedMerchant } from '../types';

/**
 * Percentage fee strategy
 *
 * Calculates fee as a percentage of the transaction amount.
 */
export class PercentageFeeStrategy implements IFeeCalculationStrategy {
  private prisma: PrismaClient;
  private defaultPercentage: number = 2.5; // 2.5%
  private methodPercentages: Record<string, number> = {};

  /**
   * Constructor
   *
   * @param prisma Prisma client
   * @param defaultPercentage Default fee percentage
   */
  constructor(prisma: PrismaClient, defaultPercentage?: number) {
    this.prisma = prisma;

    if (defaultPercentage !== undefined) {
      this.defaultPercentage = defaultPercentage;
    }

    this.loadMethodPercentages();
  }

  /**
   * Get the strategy name
   */
  public getName(): string {
    return 'percentage_fee';
  }

  /**
   * Calculate fee
   */
  public calculate(context: FeeCalculationContext): { fee: number; description: string } {
    const { amount, paymentMethodType } = context;

    // Get percentage for this payment method
    const percentage = this.methodPercentages[paymentMethodType] || this.defaultPercentage;

    // Calculate fee
    const fee = (amount * percentage) / 100;

    return {
      fee,
      description: `${percentage}% of ${amount} ${context).currency}`,
    };
  }

  /**
   * Check if strategy applies to the context
   */
  public appliesTo(context: FeeCalculationContext): boolean {
    // This strategy applies to all payment methods
    return true;
  }

  /**
   * Load method percentages from database or cache
   */
  private async loadMethodPercentages(): Promise<void> {
    try {
      // In a real implementation, this would load from database
      // For now, we'll use some hardcoded percentages
      this.methodPercentages = {
        credit_card: 2.9,
        paypal: 3.5,
        crypto_transfer: 1.5,
        binance_pay: 1.0,
        binance_c2c: 1.2,
        binance_trc20: 1.0,
        alipay: 2.0,
        wechat_pay: 2.0,
      };
    } catch (error) {
      logger.error('Error loading method percentages:', error);
    }
  }
}

/**
 * Tiered fee strategy
 *
 * Calculates fee based on transaction amount tiers.
 */
export class TieredFeeStrategy implements IFeeCalculationStrategy {
  private tiers: { min: number; max: number; percentage: number }[] = [];

  /**
   * Constructor
   *
   * @param tiers Fee tiers
   */
  constructor(tiers?: { min: number; max: number; percentage: number }[]) {
    if (tiers) {
      this.tiers = tiers;
    } else {
      // Default tiers
      this.tiers = [
        { min: 0, max: 100, percentage: 3.0 },
        { min: 100, max: 1000, percentage: 2.5 },
        { min: 1000, max: 10000, percentage: 2.0 },
        { min: 10000, max: Infinity, percentage: 1.5 },
      ];
    }
  }

  /**
   * Get the strategy name
   */
  public getName(): string {
    return 'tiered_fee';
  }

  /**
   * Calculate fee
   */
  public calculate(context: FeeCalculationContext): { fee: number; description: string } {
    const { amount } = context;

    // Find applicable tier
    const tier = this.tiers.find((t) => amount >= (t).min && amount < (t).max);

    if (!tier) {
      return {
        fee: 0,
        description: 'No applicable tier found',
      };
    }

    // Calculate fee
    const fee = (amount * (tier).percentage) / 100;

    return {
      fee,
      description: `${tier).percentage}% (tier ${tier).min}-${tier).max})`,
    };
  }

  /**
   * Check if strategy applies to the context
   */
  public appliesTo(context: FeeCalculationContext): boolean {
    // This strategy applies to high-value transactions
    return (context).amount >= 100;
  }
}

/**
 * Fixed fee strategy
 *
 * Adds a fixed fee to the transaction.
 */
export class FixedFeeStrategy implements IFeeCalculationStrategy {
  private fixedFees: Record<string, number> = {};
  private defaultFee: number = 0.5;

  /**
   * Constructor
   *
   * @param fixedFees Fixed fees by currency
   * @param defaultFee Default fixed fee
   */
  constructor(fixedFees?: Record<string, number>, defaultFee?: number) {
    if (fixedFees) {
      this.fixedFees = fixedFees;
    } else {
      // Default fixed fees
      this.fixedFees = {
        USD: 0.3,
        EUR: 0.25,
        GBP: 0.2,
        JPY: 30,
        CNY: 2,
      };
    }

    if (defaultFee !== undefined) {
      this.defaultFee = defaultFee;
    }
  }

  /**
   * Get the strategy name
   */
  public getName(): string {
    return 'fixed_fee';
  }

  /**
   * Calculate fee
   */
  public calculate(context: FeeCalculationContext): { fee: number; description: string } {
    const { currency } = context;

    // Get fixed fee for this currency
    const fixedFee = this.fixedFees[currency] || this.defaultFee;

    return {
      fee: fixedFee,
      description: `Fixed fee of ${fixedFee} ${currency}`,
    };
  }

  /**
   * Check if strategy applies to the context
   */
  public appliesTo(context: FeeCalculationContext): boolean {
    // This strategy applies to all payment methods except crypto
    return !(context).paymentMethodType.includes('crypto');
  }
}

/**
 * Merchant-specific fee strategy
 *
 * Applies custom fees for specific merchants.
 */
export class MerchantSpecificFeeStrategy implements IFeeCalculationStrategy {
  private prisma: PrismaClient;
  private merchantFees: Record<string, number> = {};

  /**
   * Constructor
   *
   * @param prisma Prisma client
   */
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.loadMerchantFees();
  }

  /**
   * Get the strategy name
   */
  public getName(): string {
    return 'merchant_specific_fee';
  }

  /**
   * Calculate fee
   */
  public calculate(context: FeeCalculationContext): { fee: number; description: string } {
    const { merchantId, amount } = context;

    // Get percentage for this merchant
    const percentage = this.merchantFees[merchantId];

    if (percentage === undefined) {
      return {
        fee: 0,
        description: 'No merchant-specific fee',
      };
    }

    // Calculate fee
    const fee = (amount * percentage) / 100;

    return {
      fee,
      description: `Merchant-specific, fee: ${percentage}%`,
    };
  }

  /**
   * Check if strategy applies to the context
   */
  public appliesTo(context: FeeCalculationContext): boolean {
    // This strategy applies only to merchants with specific fees
    return this.merchantFees[(context).merchantId] !== undefined;
  }

  /**
   * Load merchant fees from database or cache
   */
  private async loadMerchantFees(): Promise<void> {
    try {
      // In a real implementation, this would load from database
      // For now, we'll use some hardcoded merchant fees
      this.merchantFees = {
        'merchant-1': 1.5,
        'merchant-2': 2.0,
        'merchant-3': 1.8,
      };
    } catch (error) {
      logger.error('Error loading merchant fees:', error);
    }
  }
}
