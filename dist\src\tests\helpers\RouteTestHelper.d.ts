import { Request, Response, NextFunction } from 'express';
/**
 * Route test helper
 * This class helps with testing routes
 */
export declare class RouteTestHelper {
    private app;
    private routeRegistry;
    private routeVersionManager;
    private routeMonitor;
    /**
     * Create a new route test helper
     * @param app Express application
     */
    constructor(app: Application);
    /**
     * Create a mock router
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockRouter(key: string, path: string, handler?: (req: Request, res: Response) => ): Router;
    /**
     * Create a mock versioned router
     * @param version Version
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockVersionedRouter(version: string, key: string, path: string, handler?: (req: Request, res: Response) => ): Router;
    /**
     * Create a mock middleware
     * @param name Middleware name
     * @param handler Middleware handler
     * @returns Express middleware
     */
    createMockMiddleware(name: string, handler?: (req: Request, res: Response, next: NextFunction) => ): (req: Request, res: Response, next: NextFunction) => ;
}
export default RouteTestHelper;
//# sourceMappingURL=RouteTestHelper.d.ts.map