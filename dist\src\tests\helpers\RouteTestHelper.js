"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteTestHelper = void 0;
const supertest_1 = __importDefault(require("supertest"));
/**
 * Route test helper
 * This class helps with testing routes
 */
class RouteTestHelper {
    /**
     * Create a new route test helper
     * @param app Express application
     */
    constructor(app) {
        this.app = app;
        this.routeRegistry = RouteRegistry.getInstance();
        this.routeVersionManager = RouteVersionManager.getInstance();
        this.routeMonitor = RouteMonitor.getInstance();
    }
    /**
     * Create a mock router
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockRouter(key, path, handler =  >  > void ) {
        const router = Router();
        // Add handler
        router.get("/", handler);
        // Register router
        this.routeRegistry.register(key, router, { path });
        return router;
    }
    /**
     * Create a mock versioned router
     * @param version Version
     * @param key Router key
     * @param path Router path
     * @param handler Request handler
     * @returns Express router
     */
    createMockVersionedRouter(version, key, path, handler =  >  > void ) {
        const router = Router();
        // Add handler
        router.get("/", handler);
        // Register router
        this.routeVersionManager.registerVersionedRoute(version, key, router, { path });
        return router;
    }
}
exports.RouteTestHelper = RouteTestHelper;
 >  > void {} >  > {
    logger, : .debug(`Mock middleware ${name} called`)
};
async;
testRoute(method, "get" | "post" | "put" | "delete" | "patch", path, string, expectedStatus, number, expectedBody ?  : unknown, token ?  : string, body ?  : unknown);
Promise < supertest_1.default.Response > {
    // Create request
    let, req: Request = requestthis.app,
    // Add token if provided
    if(token) {
        req = req.set("Authorization", `Bearer ${token}`);
    }
    // Add body if provided
    ,
    // Add body if provided
    if(body) {
        req = req.send(body);
    }
    // Send request
    ,
    // Send request
    const: res, Response: express_1.Response = await req,
    // Check status
    expect(res) { }, : .status, : .toBe(expectedStatus),
    // Check body if provided
    if(expectedBody) {
        expect(res.body).toMatchObject(expectedBody);
    },
    return: res
};
async;
testAuthenticatedRoute(method, "get" | "post" | "put" | "delete" | "patch", path, string, token, string, expectedStatus, number, expectedBody ?  : unknown, body ?  : unknown);
Promise < supertest_1.default.Response > {
    return: this.testRoute(method, path, expectedStatus, expectedBody, token, body)
};
async;
testUnauthenticatedRoute(method, "get" | "post" | "put" | "delete" | "patch", path, string, expectedStatus, number, expectedBody ?  : unknown, body ?  : unknown);
Promise < supertest_1.default.Response > {
    return: this.testRoute(method, path, expectedStatus, expectedBody, undefined, body)
};
resetRouteMetrics();
void {
    this: .routeMonitor.resetAllMetrics()
};
exports.default = RouteTestHelper;
//# sourceMappingURL=RouteTestHelper.js.map