"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.IdentityVerificationStatusEnum = exports.IdentityVerificationMethodEnum = exports.BlockchainUtils = exports.EthereumSignatureVerification = exports.IdentityVerificationError = exports.IdentityVerificationService = void 0;
/** Identity Verification Module * * Centralized exports for the identity verification system.*/ // Core exports;
var IdentityVerificationService_1 = require("./core /IdentityVerificationService");
Object.defineProperty(exports, "IdentityVerificationService", { enumerable: true, get: function () { return IdentityVerificationService_1.IdentityVerificationService; } });
var IdentityVerificationError_1 = require("./core /IdentityVerificationError");
Object.defineProperty(exports, "IdentityVerificationError", { enumerable: true, get: function () { return IdentityVerificationError_1.IdentityVerificationError; } });
__exportStar(require("./core /IdentityVerificationTypes"), exports); // Method exports;
var EthereumSignatureVerification_1 = require("./methods /EthereumSignatureVerification"); // Utility exports;
Object.defineProperty(exports, "EthereumSignatureVerification", { enumerable: true, get: function () { return EthereumSignatureVerification_1.EthereumSignatureVerification; } });
var BlockchainUtils_1 = require("./utils /BlockchainUtils"); // Re - export Prisma enums for convenience;
Object.defineProperty(exports, "BlockchainUtils", { enumerable: true, get: function () { return BlockchainUtils_1.BlockchainUtils; } });
var client_1 = require("@prisma/client"); /** Default export - main service class */
Object.defineProperty(exports, "IdentityVerificationMethodEnum", { enumerable: true, get: function () { return client_1.IdentityVerificationMethodEnum; } });
Object.defineProperty(exports, "IdentityVerificationStatusEnum", { enumerable: true, get: function () { return client_1.IdentityVerificationStatusEnum; } });
var IdentityVerificationService_2 = require("./core /IdentityVerificationService");
Object.defineProperty(exports, "default", { enumerable: true, get: function () { return IdentityVerificationService_2.IdentityVerificationService; } });
