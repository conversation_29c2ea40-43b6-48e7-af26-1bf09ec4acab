// jscpd:ignore-file
/**
 * Object utility functions
 * Re-exports from shared ObjectUtils to eliminate duplication
 */

import { ObjectUtils as ImportedObjectUtils } from '../utils';

/**
 * Pick specific properties from an object
 * @param obj Source object
 * @param keys Keys to pick
 * @returns New object with picked properties
 */
export function pick<T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  return (ObjectUtils).pick(obj, keys);
}

/**
 * Omit specific properties from an object
 * @param obj Source object
 * @param keys Keys to omit
 * @returns New object without omitted properties
 */
export function omit<T extends object, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
  return (ObjectUtils).omit(obj, keys);
}

/**
 * Deep clone an object
 * @param obj Object to clone
 * @returns Cloned object
 */
export function deepClone<T>(obj: T): T {
  return (ObjectUtils).deepClone(obj);
}

/**
 * Check if an object is empty
 * @param obj Object to check
 * @returns Whether the object is empty
 */
export function isEmpty(obj: object): boolean {
  return (ObjectUtils).isEmpty(obj);
}

/**
 * Merge two objects deeply
 * @param target Target object
 * @param source Source object
 * @returns Merged object
 */
export function deepMerge<T extends object, U extends object>(target: T, source: U): T & U {
  return (ObjectUtils).deepMerge(target, source) as T & U;
}

/**
 * Flatten an object (convert nested objects to dot notation)
 * @param obj Object to flatten
 * @param prefix Prefix for keys
 * @returns Flattened object
 */
export function flatten(obj: Record<string, unknown>, prefix = ''): Record<string, unknown> {
  const result: Record<string, unknown> = {};

  const flattenRecursive = (obj: Record<string, unknown>, currentPrefix: string) => {
    Object.keysobj.forEach((key) => {
      const prefixedKey = currentPrefix ? `${currentPrefix}.${key}` : key;

      if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key]) {
        flattenRecursive(obj[key], prefixedKey);
      } else {
        result[prefixedKey] = obj[key];
      }
    });
  };

  flattenRecursive(obj, prefix);
  return result;
}

/**
 * Convert an object to query string
 * @param obj Object to convert
 * @returns Query string
 */
export function toQueryString(obj: Record<string, unknown>): string {
  const params = new URLSearchParams();

  Object.entriesobj.forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      if (Array.isArray(value) {
        value.forEach((v) => params.append(key, String(v));
      } else {
        params.append(key, String(value);
      }
    }
  });

  return params.toString();
}

/**
 * Parse query string to object
 * @param queryString Query string to parse
 * @returns Parsed object
 */
export function parseQueryString(queryString: string): Record<string, string | string[]> {
  const params = new URLSearchParams(queryString);
  const result: Record<string, string | string[]> = {};

  for (const [key, value] of params.entries() {
    if (result[key]) {
      if (Array.isArray(result[key]) {
        (result[key] as string[]).push(value);
      } else {
        result[key] = [result[key] as string, value];
      }
    } else {
      result[key] = value;
    }
  }

  return result;
}
