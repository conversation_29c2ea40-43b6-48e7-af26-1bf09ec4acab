{"version": 3, "file": "db-optimization.js", "sourceRoot": "", "sources": ["../../../src/utils/db-optimization.ts"], "names": [], "mappings": ";AAAA,oBAAoB;AACpB;;;GAGG;;;;;;AAKH,2DAAmC;AAcnC;;;GAGG;AACI,MAAM,YAAY,GAAI,CAAC,OAAqB,EAAE,EAAD,CAAC,AAAC,CAAA;AAAzC,QAAA,YAAY,gBAA6B;AAAC,AAAD,GAAI,AAAF,GAAK;IACzD,aAAa,EAAA,EAAA,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;CAC9C,CAAC;AAEF;;;GAGG;AACU,QAAA,oBAAoB,GAAG,CAAC,CAAC,GAAG,AAAD,GAAI,AAAF,GAAK;IAC3C,MAAM,EAAC,aAAa,CAAC,oBAAoB,EAAoB;CAChE,CAAC;AAEF;;GAEG;AACU,QAAA,sBAAsB,GAAG,CAAC,CAAC,GAAG,AAAD,GAAI,AAAF,GAAK;IAC7C,aAAa,EAAA,EAAA,CAAC,sBAAsB,EAAE;CACzC,CAAC;AAEF;;;GAGG;AACI,MAAM,+BAA+B,GAAI,CAAC,eAA6B,gBAAM,EAAE,EAAD,CAAC,AAAC,CAAA;AAA1E,QAAA,+BAA+B,mCAA2C;AAAC,AAAD,GAAI,AAAF,GAAK;IAC1F,aAAa,EAAA,EAAA,CAAC,+BAA+B,CAAC,YAAY,EAAE,MAAM,CAAC;CACtE,CAAC;AAEF;;;;;;;GAOG;AACI,MAAM,qBAAqB,GAAI,CAClC,KAAa,EACjB,EADkB,CAClB,AADkB,CACjB;AAFY,QAAA,qBAAqB,yBAEjC;AACG,IAAI,GAAG,EAAE;IACT,eAAe,CAAA;AAAE,MAAM,GAAG,EAAE;IAC5B,WAAW,CAAA;AAAE,MAAM,GAAG,GAAG,CAAA;AACzB,AAAD,GAAI,AAAF,GAAK;IACN,MAAM,EAAC,aAAa,CAAC,qBAAqB,CAAC,KAAK,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,CAAC;CAChG,CAAC;AAEF;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,KAAK,CAChD,OAAO,EAAE,CAAC,CAAC,GAAG,AAAD,GAAI,AAAF,IAAK,OAAa,CAAA,EACjC,OAAO,EAAE,MAAM,GAAG,IAAI,EACtB,OAAO,EAAE,MAAM,GAAG,CAAC,CAAA,CACtB;AACE,CAAA,OAAa,IAAK,AAAD,GAAK;IACrB,MAAM,EAAC,aAAa,CAAC,+BAA+B,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;CAC1F,CAAC;AAEF;;GAEG;AACU,QAAA,8BAA8B,GAAG,CAAC,CAAC,GAAG,AAAD,GAAI,AAAF,GAAK;IACrD,aAAa,EAAA,EAAA,CAAC,8BAA8B,CAAC,gBAAM,EAAE,MAAM,CAAC;CAC/D,CAAC;AAEF,kBAAe;IACX,YAAY,EAAZ,oBAAY;IACZ,oBAAoB,EAApB,4BAAoB;IACpB,sBAAsB,EAAtB,8BAAsB;IACtB,+BAA+B,EAA/B,uCAA+B;IAC/B,qBAAqB,EAArB,6BAAqB;IACrB,+BAA+B,EAA/B,uCAA+B;IAC/B,8BAA8B,EAA9B,sCAA8B;CACjC,CAAC"}