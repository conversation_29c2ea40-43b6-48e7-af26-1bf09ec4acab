/**
 * 🎨 AMAZINGPAY PAYMENT GATEWAY ANIMATIONS CSS
 * Professional CSS animations for payment processing visualization
 */

/* ===== KEYFRAME ANIMATIONS ===== */

@keyframes paymentPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes securityScan {
  0% { transform: translateX(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

@keyframes dataFlow {
  0% { transform: translateX(-50px) scale(0); opacity: 0; }
  10% { transform: translateX(-30px) scale(1); opacity: 1; }
  90% { transform: translateX(calc(100vw + 30px)) scale(1); opacity: 1; }
  100% { transform: translateX(calc(100vw + 50px)) scale(0); opacity: 0; }
}

@keyframes currencyFlip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(180deg); }
}

@keyframes networkPulse {
  0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
  50% { transform: scale(1.1); box-shadow: 0 0 0 20px rgba(59, 130, 246, 0); }
}

@keyframes encryptionGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(16, 185, 129, 0.5); }
  50% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.8), 0 0 30px rgba(16, 185, 129, 0.6); }
}

@keyframes liquidFill {
  0% { height: 0%; }
  100% { height: 100%; }
}

@keyframes cardSlide {
  0% { transform: translateX(-100%) rotateY(-15deg); opacity: 0; }
  50% { transform: translateX(0%) rotateY(0deg); opacity: 1; }
  100% { transform: translateX(10%) rotateY(5deg); opacity: 0.9; }
}

@keyframes processingDots {
  0%, 20% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 1; }
  80%, 100% { transform: scale(1); opacity: 0.3; }
}

@keyframes successCheckmark {
  0% { transform: scale(0) rotate(-45deg); opacity: 0; }
  50% { transform: scale(1.2) rotate(-45deg); opacity: 1; }
  100% { transform: scale(1) rotate(-45deg); opacity: 1; }
}

/* ===== PAYMENT FLOW COMPONENTS ===== */

.payment-flow-container {
  position: relative;
  width: 100%;
  height: 300px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-card {
  width: 200px;
  height: 120px;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius: 12px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: cardSlide 3s ease-in-out infinite;
}

.payment-card::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  width: 40px;
  height: 30px;
  background: #fbbf24;
  border-radius: 4px;
}

.payment-card::after {
  content: '•••• •••• •••• 1234';
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  letter-spacing: 2px;
}

.processing-indicator {
  position: absolute;
  top: 50%;
  right: 20%;
  transform: translateY(-50%);
  display: flex;
  gap: 8px;
}

.processing-dot {
  width: 12px;
  height: 12px;
  background: #10b981;
  border-radius: 50%;
  animation: processingDots 1.5s ease-in-out infinite;
}

.processing-dot:nth-child(2) { animation-delay: 0.2s; }
.processing-dot:nth-child(3) { animation-delay: 0.4s; }

.success-indicator {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  border: 3px solid #10b981;
  border-radius: 50%;
  opacity: 0;
}

.success-indicator::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 10px;
  width: 6px;
  height: 12px;
  border: solid #10b981;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
  animation: successCheckmark 0.5s ease-in-out forwards;
}

/* ===== SECURITY COMPONENTS ===== */

.security-shield {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.shield-icon {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  position: relative;
  z-index: 2;
}

.shield-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: encryptionGlow 2s ease-in-out infinite;
}

.security-scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, #10b981, transparent);
  animation: securityScan 2s linear infinite;
}

/* ===== DATA FLOW COMPONENTS ===== */

.data-stream {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(59, 130, 246, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin: 10px 0;
}

.data-particle {
  position: absolute;
  width: 20px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  border-radius: 2px;
  animation: dataFlow 3s linear infinite;
}

.data-particle:nth-child(2) { animation-delay: 0.5s; }
.data-particle:nth-child(3) { animation-delay: 1s; }
.data-particle:nth-child(4) { animation-delay: 1.5s; }

/* ===== CURRENCY EXCHANGE COMPONENTS ===== */

.currency-exchange {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.currency {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 18px;
  position: relative;
  overflow: hidden;
}

.currency.usd {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.currency.eur {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.currency.btc {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.exchange-arrow {
  font-size: 24px;
  color: #6b7280;
  transition: all 0.3s ease;
}

.currency-exchange:hover .exchange-arrow {
  transform: translateX(5px);
  color: #3b82f6;
}

/* ===== NETWORK COMPONENTS ===== */

.global-network {
  position: relative;
  width: 100%;
  height: 300px;
  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  border-radius: 20px;
  overflow: hidden;
}

.network-node {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
}

.network-node.active {
  animation: networkPulse 2s infinite;
}

.network-connection {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, rgba(59, 130, 246, 0.3), #3b82f6);
  transform-origin: left center;
}

/* ===== MOBILE PAYMENT COMPONENTS ===== */

.mobile-payment {
  display: flex;
  align-items: center;
  gap: 30px;
  padding: 30px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.phone {
  width: 80px;
  height: 140px;
  background: #1f2937;
  border-radius: 15px;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.phone::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background: #3b82f6;
  border-radius: 8px;
}

.nfc-wave {
  position: absolute;
  top: 50%;
  right: -20px;
  width: 30px;
  height: 30px;
  border: 2px solid #10b981;
  border-radius: 50%;
  opacity: 0;
}

.payment-terminal {
  width: 100px;
  height: 60px;
  background: #374151;
  border-radius: 10px;
  position: relative;
}

.terminal-light {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 12px;
  height: 12px;
  background: #ef4444;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

/* ===== ENCRYPTION COMPONENTS ===== */

.encryption-demo {
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.plain-text {
  font-family: 'Courier New', monospace;
  color: #ef4444;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.encrypted-text {
  font-family: 'Courier New', monospace;
  color: #10b981;
  opacity: 0;
  transform: scale(0.8);
}

.lock-icon {
  font-size: 30px;
  color: #3b82f6;
  margin: 20px 0;
  transition: all 0.3s ease;
}

/* ===== PERFORMANCE METRICS ===== */

.performance-metric {
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.metric-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
}

.progress-container {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 4px;
  width: 0%;
  transition: width 2s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.percentage {
  font-weight: bold;
  color: #3b82f6;
  margin-top: 5px;
}

/* ===== LIQUID LOADING ===== */

.liquid-loading {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #e5e7eb;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

.liquid {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0%;
  background: linear-gradient(0deg, #3b82f6, #10b981);
  border-radius: 50%;
  animation: liquidFill 3s ease-in-out forwards;
}

.liquid::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 0;
  width: 100%;
  height: 20px;
  background: inherit;
  border-radius: 50%;
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: translateX(-50%) rotate(0deg); }
  50% { transform: translateX(-50%) rotate(180deg); }
}

.loading-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  color: white;
  z-index: 2;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .payment-flow-container {
    height: 200px;
  }
  
  .payment-card {
    width: 150px;
    height: 90px;
  }
  
  .mobile-payment {
    flex-direction: column;
    gap: 20px;
  }
  
  .currency-exchange {
    flex-direction: column;
    gap: 15px;
  }
  
  .global-network {
    height: 200px;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== DARK MODE SUPPORT ===== */

@media (prefers-color-scheme: dark) {
  .performance-metric,
  .encryption-demo,
  .mobile-payment,
  .currency-exchange {
    background: #1f2937;
    color: white;
  }
  
  .metric-label {
    color: #e5e7eb;
  }
  
  .progress-container {
    background: #374151;
  }
}
