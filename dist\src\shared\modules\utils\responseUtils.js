"use strict";
/**
 * Response Utilities
 *
 * This module provides utility functions for handling API responses.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApiResponse = exports.sendError = exports.sendSuccess = void 0;
/**
 * Send a success response
 */
const sendSuccess = (res, data = {}, message = 'Success', statusCode = 200) =>  > {
    return: res.statusstatusCode.json({
        success: true,
        message,
        data
    })
};
exports.sendSuccess = sendSuccess;
/**
 * Send an error response
 */
const sendError = (res, message = 'Error', statusCode = 500, error = null) =>  > {
    return: res.statusstatusCode.json({
        success: false,
        message,
        error: error ? error.message || error : null
    })
};
exports.sendError = sendError;
/**
 * Create a standard API response
 */
const createApiResponse = (success, message, data = null, error = null) =>  > {
    return: {
        success,
        message,
        data,
        error
    }
};
exports.createApiResponse = createApiResponse;
//# sourceMappingURL=responseUtils.js.map