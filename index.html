<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AmazingPay Flow</title>
  <!-- Chart.js for analytics -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- Date picker for filtering -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #333;
      margin-top: 0;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    .response {
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-top: 15px;
      white-space: pre-wrap;
      font-family: monospace;
      max-height: 300px;
      overflow-y: auto;
    }
    .tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #ddd;
      flex-wrap: wrap;
    }
    .tab {
      padding: 10px 15px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
    }
    .tab.active {
      border-bottom: 2px solid #4CAF50;
      font-weight: bold;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .chart-container {
      position: relative;
      height: 300px;
      margin-bottom: 20px;
    }
    .stats-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }
    .stat-card {
      flex: 1;
      min-width: 200px;
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      text-align: center;
    }
    .stat-card h3 {
      margin-top: 0;
      color: #666;
      font-size: 14px;
    }
    .stat-card .value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }
    .filters {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }
    .filter {
      flex: 1;
      min-width: 150px;
    }
    .transaction-list {
      margin-top: 20px;
    }
    .transaction-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .transaction-item:hover {
      background-color: #f5f5f5;
    }
    .status-badge {
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
    }
    .status-pending {
      background-color: #FFF3CD;
      color: #856404;
    }
    .status-processing {
      background-color: #D1ECF1;
      color: #0C5460;
    }
    .status-completed {
      background-color: #D4EDDA;
      color: #155724;
    }
    .status-failed {
      background-color: #F8D7DA;
      color: #721C24;
    }
    .status-refunded {
      background-color: #E2E3E5;
      color: #383D41;
    }
    .status-cancelled {
      background-color: #E2E3E5;
      color: #383D41;
    }
    .chart-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }
    .chart-col {
      flex: 1;
      min-width: 300px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>AmazingPay Flow API Tester</h1>

    <div class="tabs">
      <div class="tab active" data-tab="login">Login</div>
      <div class="tab" data-tab="register">Register</div>
      <div class="tab" data-tab="profile">My Profile</div>
      <div class="tab" data-tab="dashboard">Dashboard</div>
      <div class="tab" data-tab="transactions">Transactions</div>
      <div class="tab" data-tab="create-transaction">Create Transaction</div>
      <div class="tab" data-tab="payment-methods">Payment Methods</div>
      <div class="tab" data-tab="analytics">Analytics</div>
      <div class="tab" data-tab="merchant-profile">Merchant Profile</div>
    </div>

    <div id="login" class="tab-content active">
      <div class="card">
        <h2>Login</h2>
        <div class="form-group">
          <label for="login-email">Email</label>
          <input type="email" id="login-email" placeholder="Enter your email">
        </div>
        <div class="form-group">
          <label for="login-password">Password</label>
          <input type="password" id="login-password" placeholder="Enter your password">
        </div>
        <button onclick="login()">Login</button>
        <div id="login-response" class="response"></div>
      </div>
    </div>

    <div id="register" class="tab-content">
      <div class="card">
        <h2>Register</h2>
        <div class="form-group">
          <label for="register-email">Email</label>
          <input type="email" id="register-email" placeholder="Enter your email">
        </div>
        <div class="form-group">
          <label for="register-password">Password</label>
          <input type="password" id="register-password" placeholder="Enter your password">
        </div>
        <div class="form-group">
          <label for="register-firstName">First Name</label>
          <input type="text" id="register-firstName" placeholder="Enter your first name">
        </div>
        <div class="form-group">
          <label for="register-lastName">Last Name</label>
          <input type="text" id="register-lastName" placeholder="Enter your last name">
        </div>
        <button onclick="register()">Register</button>
        <div id="register-response" class="response"></div>
      </div>
    </div>

    <div id="profile" class="tab-content">
      <div class="card">
        <h2>My Profile</h2>
        <button onclick="getProfile()">Get Profile</button>
        <div id="profile-response" class="response"></div>
      </div>
    </div>

    <div id="transactions" class="tab-content">
      <div class="card">
        <h2>Transactions</h2>

        <div class="filters">
          <div class="filter">
            <label for="transactions-status">Status</label>
            <select id="transactions-status">
              <option value="">All Statuses</option>
              <option value="PENDING">Pending</option>
              <option value="PROCESSING">Processing</option>
              <option value="COMPLETED">Completed</option>
              <option value="FAILED">Failed</option>
              <option value="REFUNDED">Refunded</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>
          <div class="filter">
            <label for="transactions-startDate">Start Date</label>
            <input type="text" id="transactions-startDate" placeholder="Select start date">
          </div>
          <div class="filter">
            <label for="transactions-endDate">End Date</label>
            <input type="text" id="transactions-endDate" placeholder="Select end date">
          </div>
          <div class="filter">
            <label for="transactions-page">Page</label>
            <input type="number" id="transactions-page" value="1" min="1">
          </div>
          <div class="filter">
            <label for="transactions-limit">Per Page</label>
            <select id="transactions-limit">
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50" selected>50</option>
              <option value="100">100</option>
            </select>
          </div>
          <div class="filter" style="align-self: flex-end;">
            <button onclick="getTransactions()">Search Transactions</button>
          </div>
        </div>

        <div id="transactions-list" class="transaction-list">
          <!-- Transactions will be displayed here -->
        </div>

        <div id="transactions-pagination" style="margin-top: 20px; text-align: center; display: none;">
          <button id="prev-page" onclick="prevPage()">Previous</button>
          <span id="page-info">Page 1 of 1</span>
          <button id="next-page" onclick="nextPage()">Next</button>
        </div>

        <h3>Transaction Details</h3>
        <div id="transaction-details" style="display: none;">
          <div class="card">
            <h3>Transaction Information</h3>
            <div id="transaction-info"></div>

            <h3>Status History</h3>
            <div id="status-history"></div>

            <h3>Update Status</h3>
            <div class="form-group">
              <label for="update-status">New Status</label>
              <select id="update-status">
                <option value="PENDING">Pending</option>
                <option value="PROCESSING">Processing</option>
                <option value="COMPLETED">Completed</option>
                <option value="FAILED">Failed</option>
                <option value="REFUNDED">Refunded</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
            </div>
            <div class="form-group">
              <label for="status-note">Note</label>
              <input type="text" id="status-note" placeholder="Add a note about this status change">
            </div>
            <button id="update-status-btn" onclick="updateTransactionStatus()">Update Status</button>
          </div>
        </div>

        <h3>Raw Response</h3>
        <div id="transactions-response" class="response"></div>
      </div>
    </div>

    <div id="create-transaction" class="tab-content">
      <div class="card">
        <h2>Create Transaction</h2>
        <div class="form-group">
          <label for="transaction-merchantId">Merchant ID</label>
          <input type="text" id="transaction-merchantId" placeholder="Enter merchant ID">
        </div>
        <div class="form-group">
          <label for="transaction-amount">Amount</label>
          <input type="number" id="transaction-amount" placeholder="Enter amount">
        </div>
        <div class="form-group">
          <label for="transaction-currency">Currency</label>
          <select id="transaction-currency">
            <option value="USD">USD</option>
            <option value="EUR">EUR</option>
            <option value="GBP">GBP</option>
            <option value="BTC">BTC</option>
            <option value="ETH">ETH</option>
          </select>
        </div>
        <div class="form-group">
          <label for="transaction-paymentMethod">Payment Method</label>
          <select id="transaction-paymentMethod">
            <option value="CREDIT_CARD">Credit Card</option>
            <option value="DEBIT_CARD">Debit Card</option>
            <option value="BANK_TRANSFER">Bank Transfer</option>
            <option value="CRYPTO">Cryptocurrency</option>
          </select>
        </div>
        <div class="form-group">
          <label for="transaction-description">Description</label>
          <input type="text" id="transaction-description" placeholder="Enter description">
        </div>
        <button onclick="createTransaction()">Create Transaction</button>
        <div id="create-transaction-response" class="response"></div>
      </div>
    </div>

    <div id="dashboard" class="tab-content">
      <div class="card">
        <h2>Dashboard</h2>
        <button onclick="getDashboard()">Get Dashboard Stats</button>
        <div id="dashboard-response" class="response"></div>
      </div>
    </div>

    <div id="payment-methods" class="tab-content">
      <div class="card">
        <h2>Payment Methods</h2>
        <button onclick="getPaymentMethods()">Get My Payment Methods</button>
        <div id="payment-methods-response" class="response"></div>

        <h3>Add Payment Method</h3>
        <div class="form-group">
          <label for="payment-type">Type</label>
          <select id="payment-type">
            <option value="CREDIT_CARD">Credit Card</option>
            <option value="DEBIT_CARD">Debit Card</option>
            <option value="BANK_TRANSFER">Bank Transfer</option>
            <option value="CRYPTO">Cryptocurrency</option>
            <option value="PAYPAL">PayPal</option>
          </select>
        </div>
        <div class="form-group">
          <label for="payment-name">Name</label>
          <input type="text" id="payment-name" placeholder="Enter payment method name">
        </div>
        <div class="form-group">
          <label for="payment-default">Set as Default</label>
          <input type="checkbox" id="payment-default">
        </div>
        <button onclick="addPaymentMethod()">Add Payment Method</button>
        <div id="add-payment-method-response" class="response"></div>
      </div>
    </div>

    <div id="analytics" class="tab-content">
      <div class="card">
        <h2>Transaction Analytics</h2>

        <div class="filters">
          <div class="filter">
            <label for="analytics-period">Period</label>
            <select id="analytics-period">
              <option value="day">Last 24 Hours</option>
              <option value="week" selected>Last Week</option>
              <option value="month">Last Month</option>
              <option value="year">Last Year</option>
            </select>
          </div>
          <div class="filter">
            <label for="analytics-groupBy">Group By</label>
            <select id="analytics-groupBy">
              <option value="hour">Hour</option>
              <option value="day" selected>Day</option>
              <option value="week">Week</option>
              <option value="month">Month</option>
            </select>
          </div>
          <div class="filter">
            <label for="analytics-startDate">Start Date</label>
            <input type="text" id="analytics-startDate" placeholder="Select start date">
          </div>
          <div class="filter">
            <label for="analytics-endDate">End Date</label>
            <input type="text" id="analytics-endDate" placeholder="Select end date">
          </div>
          <div class="filter" style="align-self: flex-end;">
            <button onclick="getAnalytics()">Update Analytics</button>
          </div>
        </div>

        <div class="stats-container">
          <div class="stat-card">
            <h3>Total Transactions</h3>
            <div id="total-transactions" class="value">0</div>
          </div>
          <div class="stat-card">
            <h3>Total Amount</h3>
            <div id="total-amount" class="value">$0.00</div>
          </div>
          <div class="stat-card">
            <h3>Average Transaction</h3>
            <div id="avg-transaction" class="value">$0.00</div>
          </div>
          <div class="stat-card">
            <h3>Success Rate</h3>
            <div id="success-rate" class="value">0%</div>
          </div>
        </div>

        <div class="chart-row">
          <div class="chart-col">
            <h3>Transaction Volume Over Time</h3>
            <div class="chart-container">
              <canvas id="transactions-chart"></canvas>
            </div>
          </div>
          <div class="chart-col">
            <h3>Transaction Amount Over Time</h3>
            <div class="chart-container">
              <canvas id="amount-chart"></canvas>
            </div>
          </div>
        </div>

        <div class="chart-row">
          <div class="chart-col">
            <h3>Transaction Status Distribution</h3>
            <div class="chart-container">
              <canvas id="status-chart"></canvas>
            </div>
          </div>
          <div class="chart-col">
            <h3>Payment Method Distribution</h3>
            <div class="chart-container">
              <canvas id="payment-method-chart"></canvas>
            </div>
          </div>
        </div>

        <h3>Raw Data</h3>
        <div id="analytics-response" class="response"></div>
      </div>
    </div>

    <div id="merchant-profile" class="tab-content">
      <div class="card">
        <h2>Create Merchant Profile</h2>
        <div class="form-group">
          <label for="merchant-businessName">Business Name</label>
          <input type="text" id="merchant-businessName" placeholder="Enter business name">
        </div>
        <div class="form-group">
          <label for="merchant-businessType">Business Type</label>
          <select id="merchant-businessType">
            <option value="RETAIL">Retail</option>
            <option value="ECOMMERCE">E-Commerce</option>
            <option value="SERVICES">Services</option>
            <option value="FOOD">Food & Beverage</option>
            <option value="TRAVEL">Travel</option>
            <option value="OTHER">Other</option>
          </select>
        </div>
        <div class="form-group">
          <label for="merchant-contactEmail">Contact Email</label>
          <input type="email" id="merchant-contactEmail" placeholder="Enter contact email">
        </div>
        <div class="form-group">
          <label for="merchant-contactPhone">Contact Phone</label>
          <input type="text" id="merchant-contactPhone" placeholder="Enter contact phone">
        </div>
        <div class="form-group">
          <label for="merchant-website">Website</label>
          <input type="text" id="merchant-website" placeholder="Enter website">
        </div>
        <div class="form-group">
          <label for="merchant-country">Country</label>
          <input type="text" id="merchant-country" placeholder="Enter country">
        </div>
        <div class="form-group">
          <label for="merchant-address">Address</label>
          <input type="text" id="merchant-address" placeholder="Enter address">
        </div>
        <div class="form-group">
          <label for="merchant-city">City</label>
          <input type="text" id="merchant-city" placeholder="Enter city">
        </div>
        <div class="form-group">
          <label for="merchant-state">State/Province</label>
          <input type="text" id="merchant-state" placeholder="Enter state/province">
        </div>
        <div class="form-group">
          <label for="merchant-postalCode">Postal Code</label>
          <input type="text" id="merchant-postalCode" placeholder="Enter postal code">
        </div>
        <button onclick="createMerchantProfile()">Create Merchant Profile</button>
        <div id="merchant-profile-response" class="response"></div>
      </div>
    </div>
  </div>

  <script>
    // API base URL
    const API_URL = 'http://localhost:3002';

    // Store token in localStorage
    let token = localStorage.getItem('token');

    // Tab switching
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

        tab.classList.add('active');
        document.getElementById(tab.dataset.tab).classList.add('active');
      });
    });

    // Login function
    async function login() {
      const email = document.getElementById('login-email').value;
      const password = document.getElementById('login-password').value;

      if (!email || !password) {
        document.getElementById('login-response').textContent = 'Please enter email and password';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email, password })
        });

        const data = await response.json();
        document.getElementById('login-response').textContent = JSON.stringify(data, null, 2);

        if (data.status === 'success') {
          token = data.data.token;
          localStorage.setItem('token', token);
          alert('Login successful!');
        }
      } catch (error) {
        document.getElementById('login-response').textContent = error.message;
      }
    }

    // Register function
    async function register() {
      const email = document.getElementById('register-email').value;
      const password = document.getElementById('register-password').value;
      const firstName = document.getElementById('register-firstName').value;
      const lastName = document.getElementById('register-lastName').value;

      if (!email || !password) {
        document.getElementById('register-response').textContent = 'Please enter email and password';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email, password, firstName, lastName })
        });

        const data = await response.json();
        document.getElementById('register-response').textContent = JSON.stringify(data, null, 2);

        if (data.status === 'success') {
          token = data.data.token;
          localStorage.setItem('token', token);
          alert('Registration successful!');
        }
      } catch (error) {
        document.getElementById('register-response').textContent = error.message;
      }
    }

    // Get profile function
    async function getProfile() {
      if (!token) {
        document.getElementById('profile-response').textContent = 'Please login first';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/auth/me`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();
        document.getElementById('profile-response').textContent = JSON.stringify(data, null, 2);
      } catch (error) {
        document.getElementById('profile-response').textContent = error.message;
      }
    }

    // Get dashboard function
    async function getDashboard() {
      if (!token) {
        document.getElementById('dashboard-response').textContent = 'Please login first';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/dashboard`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();
        document.getElementById('dashboard-response').textContent = JSON.stringify(data, null, 2);
      } catch (error) {
        document.getElementById('dashboard-response').textContent = error.message;
      }
    }

    // Initialize date pickers
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize date pickers for transactions
      flatpickr('#transactions-startDate', {
        dateFormat: 'Y-m-d',
        maxDate: 'today'
      });

      flatpickr('#transactions-endDate', {
        dateFormat: 'Y-m-d',
        maxDate: 'today'
      });

      // Initialize date pickers for analytics
      flatpickr('#analytics-startDate', {
        dateFormat: 'Y-m-d',
        maxDate: 'today'
      });

      flatpickr('#analytics-endDate', {
        dateFormat: 'Y-m-d',
        maxDate: 'today'
      });
    });

    // Current page for pagination
    let currentPage = 1;
    let totalPages = 1;
    let currentTransactionId = null;

    // Get transactions function
    async function getTransactions() {
      if (!token) {
        document.getElementById('transactions-response').textContent = 'Please login first';
        return;
      }

      // Get filter values
      const status = document.getElementById('transactions-status').value;
      const startDate = document.getElementById('transactions-startDate').value;
      const endDate = document.getElementById('transactions-endDate').value;
      const page = document.getElementById('transactions-page').value;
      const limit = document.getElementById('transactions-limit').value;

      // Update current page
      currentPage = parseInt(page);

      // Build query string
      let queryParams = new URLSearchParams();
      if (status) queryParams.append('status', status);
      if (startDate) queryParams.append('startDate', startDate);
      if (endDate) queryParams.append('endDate', endDate);
      queryParams.append('page', page);
      queryParams.append('limit', limit);

      try {
        const response = await fetch(`${API_URL}/api/transactions?${queryParams.toString()}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();
        document.getElementById('transactions-response').textContent = JSON.stringify(data, null, 2);

        if (data.status === 'success') {
          // Update pagination
          totalPages = data.data.pagination.totalPages;
          updatePagination();

          // Render transactions
          renderTransactions(data.data.transactions);
        }
      } catch (error) {
        document.getElementById('transactions-response').textContent = error.message;
      }
    }

    // Render transactions in a list
    function renderTransactions(transactions) {
      const transactionsList = document.getElementById('transactions-list');
      transactionsList.innerHTML = '';

      if (transactions.length === 0) {
        transactionsList.innerHTML = '<p>No transactions found.</p>';
        return;
      }

      transactions.forEach(transaction => {
        const item = document.createElement('div');
        item.className = 'transaction-item';
        item.innerHTML = `
          <div>
            <strong>${transaction.reference}</strong><br>
            ${new Date(transaction.createdAt).toLocaleString()}<br>
            ${transaction.merchant ? transaction.merchant.businessName : 'Unknown Merchant'}
          </div>
          <div>
            ${transaction.currency} ${transaction.amount.toFixed(2)}<br>
            ${transaction.paymentMethod.replace('_', ' ')}
          </div>
          <div>
            <span class="status-badge status-${transaction.status.toLowerCase()}">${transaction.status}</span>
          </div>
          <div>
            <button onclick="viewTransaction('${transaction.id}')">View Details</button>
          </div>
        `;
        transactionsList.appendChild(item);
      });
    }

    // Update pagination controls
    function updatePagination() {
      const paginationDiv = document.getElementById('transactions-pagination');
      const pageInfo = document.getElementById('page-info');
      const prevButton = document.getElementById('prev-page');
      const nextButton = document.getElementById('next-page');

      pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
      prevButton.disabled = currentPage <= 1;
      nextButton.disabled = currentPage >= totalPages;

      paginationDiv.style.display = totalPages > 1 ? 'block' : 'none';
    }

    // Go to previous page
    function prevPage() {
      if (currentPage > 1) {
        document.getElementById('transactions-page').value = currentPage - 1;
        getTransactions();
      }
    }

    // Go to next page
    function nextPage() {
      if (currentPage < totalPages) {
        document.getElementById('transactions-page').value = currentPage + 1;
        getTransactions();
      }
    }

    // View transaction details
    async function viewTransaction(id) {
      if (!token) {
        alert('Please login first');
        return;
      }

      currentTransactionId = id;

      try {
        const response = await fetch(`${API_URL}/api/transactions/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();

        if (data.status === 'success') {
          // Show transaction details
          document.getElementById('transaction-details').style.display = 'block';
          renderTransactionDetails(data.data);
        } else {
          alert(data.message || 'Failed to fetch transaction details');
        }
      } catch (error) {
        alert(error.message);
      }
    }

    // Render transaction details
    function renderTransactionDetails(transaction) {
      const infoDiv = document.getElementById('transaction-info');
      const historyDiv = document.getElementById('status-history');

      // Format transaction info
      infoDiv.innerHTML = `
        <p><strong>Reference:</strong> ${transaction.reference}</p>
        <p><strong>Amount:</strong> ${transaction.currency} ${transaction.amount.toFixed(2)}</p>
        <p><strong>Status:</strong> <span class="status-badge status-${transaction.status.toLowerCase()}">${transaction.status}</span></p>
        <p><strong>Payment Method:</strong> ${transaction.paymentMethod.replace('_', ' ')}</p>
        <p><strong>Created:</strong> ${new Date(transaction.createdAt).toLocaleString()}</p>
        <p><strong>Last Updated:</strong> ${new Date(transaction.updatedAt).toLocaleString()}</p>
        <p><strong>Merchant:</strong> ${transaction.merchant.businessName}</p>
        <p><strong>Description:</strong> ${transaction.description || 'N/A'}</p>
      `;

      // Format status history
      historyDiv.innerHTML = '';
      if (transaction.statusHistory && transaction.statusHistory.length > 0) {
        const historyList = document.createElement('ul');
        transaction.statusHistory.forEach(history => {
          const item = document.createElement('li');
          item.innerHTML = `
            <strong>${history.status}</strong> -
            ${new Date(history.createdAt).toLocaleString()} -
            ${history.note || 'No note'} -
            By: ${history.user ? history.user.email : 'System'}
          `;
          historyList.appendChild(item);
        });
        historyDiv.appendChild(historyList);
      } else {
        historyDiv.innerHTML = '<p>No status history available.</p>';
      }

      // Set current status in dropdown
      document.getElementById('update-status').value = transaction.status;
    }

    // Update transaction status
    async function updateTransactionStatus() {
      if (!token || !currentTransactionId) {
        alert('Please select a transaction first');
        return;
      }

      const status = document.getElementById('update-status').value;
      const note = document.getElementById('status-note').value;

      try {
        const response = await fetch(`${API_URL}/api/transactions/${currentTransactionId}/status`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            status,
            note
          })
        });

        const data = await response.json();

        if (data.status === 'success') {
          alert('Transaction status updated successfully');
          viewTransaction(currentTransactionId); // Refresh details
          getTransactions(); // Refresh list
        } else {
          alert(data.message || 'Failed to update transaction status');
        }
      } catch (error) {
        alert(error.message);
      }
    }

    // Create transaction function
    async function createTransaction() {
      if (!token) {
        document.getElementById('create-transaction-response').textContent = 'Please login first';
        return;
      }

      const merchantId = document.getElementById('transaction-merchantId').value;
      const amount = document.getElementById('transaction-amount').value;
      const currency = document.getElementById('transaction-currency').value;
      const paymentMethod = document.getElementById('transaction-paymentMethod').value;
      const description = document.getElementById('transaction-description').value;

      if (!merchantId || !amount || !currency || !paymentMethod) {
        document.getElementById('create-transaction-response').textContent = 'Please fill all required fields';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/transactions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            merchantId,
            amount,
            currency,
            paymentMethod,
            description
          })
        });

        const data = await response.json();
        document.getElementById('create-transaction-response').textContent = JSON.stringify(data, null, 2);

        if (data.status === 'success') {
          alert('Transaction created successfully!');
        }
      } catch (error) {
        document.getElementById('create-transaction-response').textContent = error.message;
      }
    }

    // Get payment methods function
    async function getPaymentMethods() {
      if (!token) {
        document.getElementById('payment-methods-response').textContent = 'Please login first';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/merchants/payment-methods`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();
        document.getElementById('payment-methods-response').textContent = JSON.stringify(data, null, 2);
      } catch (error) {
        document.getElementById('payment-methods-response').textContent = error.message;
      }
    }

    // Add payment method function
    async function addPaymentMethod() {
      if (!token) {
        document.getElementById('add-payment-method-response').textContent = 'Please login first';
        return;
      }

      const type = document.getElementById('payment-type').value;
      const name = document.getElementById('payment-name').value;
      const isDefault = document.getElementById('payment-default').checked;

      if (!type || !name) {
        document.getElementById('add-payment-method-response').textContent = 'Please fill all required fields';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/payment-methods`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            type,
            name,
            isDefault,
            details: {}
          })
        });

        const data = await response.json();
        document.getElementById('add-payment-method-response').textContent = JSON.stringify(data, null, 2);

        if (data.status === 'success') {
          alert('Payment method added successfully!');
          getPaymentMethods(); // Refresh the list
        }
      } catch (error) {
        document.getElementById('add-payment-method-response').textContent = error.message;
      }
    }

    // Charts objects
    let transactionsChart = null;
    let amountChart = null;
    let statusChart = null;
    let paymentMethodChart = null;

    // Get analytics function
    async function getAnalytics() {
      if (!token) {
        document.getElementById('analytics-response').textContent = 'Please login first';
        return;
      }

      const period = document.getElementById('analytics-period').value;
      const groupBy = document.getElementById('analytics-groupBy').value;
      const startDate = document.getElementById('analytics-startDate').value;
      const endDate = document.getElementById('analytics-endDate').value;

      // Build query string
      let queryParams = new URLSearchParams();
      queryParams.append('period', period);
      queryParams.append('groupBy', groupBy);
      if (startDate) queryParams.append('startDate', startDate);
      if (endDate) queryParams.append('endDate', endDate);

      try {
        const response = await fetch(`${API_URL}/api/analytics/transactions?${queryParams.toString()}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = await response.json();
        document.getElementById('analytics-response').textContent = JSON.stringify(data, null, 2);

        if (data.status === 'success') {
          updateAnalyticsStats(data.data);
          renderAnalyticsCharts(data.data);
        }
      } catch (error) {
        document.getElementById('analytics-response').textContent = error.message;
      }
    }

    // Update analytics stats
    function updateAnalyticsStats(data) {
      document.getElementById('total-transactions').textContent = data.totalTransactions;
      document.getElementById('total-amount').textContent = `$${data.totalAmount.toFixed(2)}`;

      // Calculate average transaction amount
      const avgAmount = data.totalTransactions > 0 ? data.totalAmount / data.totalTransactions : 0;
      document.getElementById('avg-transaction').textContent = `$${avgAmount.toFixed(2)}`;

      // Calculate success rate
      let successCount = 0;
      if (data.statusData) {
        const completedStatus = data.statusData.find(s => s.status === 'COMPLETED');
        if (completedStatus) {
          successCount = completedStatus.count;
        }
      }
      const successRate = data.totalTransactions > 0 ? (successCount / data.totalTransactions) * 100 : 0;
      document.getElementById('success-rate').textContent = `${successRate.toFixed(1)}%`;
    }

    // Render analytics charts
    function renderAnalyticsCharts(data) {
      // Prepare data for time series charts
      const timeLabels = data.timeSeriesData.map(item => item.date);
      const transactionCounts = data.timeSeriesData.map(item => item.count);
      const transactionAmounts = data.timeSeriesData.map(item => item.amount);

      // Prepare data for status chart
      const statusLabels = data.statusData.map(item => item.status);
      const statusCounts = data.statusData.map(item => item.count);

      // Prepare data for payment method chart
      const paymentMethodLabels = data.paymentMethodData.map(item => item.paymentMethod);
      const paymentMethodCounts = data.paymentMethodData.map(item => item.count);

      // Render transaction count chart
      renderTransactionsChart(timeLabels, transactionCounts);

      // Render transaction amount chart
      renderAmountChart(timeLabels, transactionAmounts);

      // Render status distribution chart
      renderStatusChart(statusLabels, statusCounts);

      // Render payment method distribution chart
      renderPaymentMethodChart(paymentMethodLabels, paymentMethodCounts);
    }

    // Render transactions chart
    function renderTransactionsChart(labels, data) {
      const ctx = document.getElementById('transactions-chart').getContext('2d');

      // Destroy existing chart if it exists
      if (transactionsChart) {
        transactionsChart.destroy();
      }

      transactionsChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: 'Transaction Count',
            data: data,
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 2,
            tension: 0.1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                precision: 0
              }
            }
          }
        }
      });
    }

    // Render amount chart
    function renderAmountChart(labels, data) {
      const ctx = document.getElementById('amount-chart').getContext('2d');

      // Destroy existing chart if it exists
      if (amountChart) {
        amountChart.destroy();
      }

      amountChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Transaction Amount',
            data: data,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    }

    // Render status chart
    function renderStatusChart(labels, data) {
      const ctx = document.getElementById('status-chart').getContext('2d');

      // Define colors for each status
      const backgroundColors = labels.map(status => {
        switch(status) {
          case 'PENDING': return 'rgba(255, 206, 86, 0.6)';
          case 'PROCESSING': return 'rgba(54, 162, 235, 0.6)';
          case 'COMPLETED': return 'rgba(75, 192, 192, 0.6)';
          case 'FAILED': return 'rgba(255, 99, 132, 0.6)';
          case 'REFUNDED': return 'rgba(153, 102, 255, 0.6)';
          case 'CANCELLED': return 'rgba(201, 203, 207, 0.6)';
          default: return 'rgba(201, 203, 207, 0.6)';
        }
      });

      // Destroy existing chart if it exists
      if (statusChart) {
        statusChart.destroy();
      }

      statusChart = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: backgroundColors,
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      });
    }

    // Render payment method chart
    function renderPaymentMethodChart(labels, data) {
      const ctx = document.getElementById('payment-method-chart').getContext('2d');

      // Define colors for each payment method
      const backgroundColors = [
        'rgba(255, 99, 132, 0.6)',
        'rgba(54, 162, 235, 0.6)',
        'rgba(255, 206, 86, 0.6)',
        'rgba(75, 192, 192, 0.6)',
        'rgba(153, 102, 255, 0.6)'
      ];

      // Destroy existing chart if it exists
      if (paymentMethodChart) {
        paymentMethodChart.destroy();
      }

      paymentMethodChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels.map(method => method.replace('_', ' ')),
          datasets: [{
            data: data,
            backgroundColor: backgroundColors,
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      });
    }

    // Create merchant profile function
    async function createMerchantProfile() {
      if (!token) {
        document.getElementById('merchant-profile-response').textContent = 'Please login first';
        return;
      }

      const businessName = document.getElementById('merchant-businessName').value;
      const businessType = document.getElementById('merchant-businessType').value;
      const contactEmail = document.getElementById('merchant-contactEmail').value;
      const contactPhone = document.getElementById('merchant-contactPhone').value;
      const website = document.getElementById('merchant-website').value;
      const country = document.getElementById('merchant-country').value;
      const address = document.getElementById('merchant-address').value;
      const city = document.getElementById('merchant-city').value;
      const state = document.getElementById('merchant-state').value;
      const postalCode = document.getElementById('merchant-postalCode').value;

      if (!businessName || !businessType || !contactEmail || !country) {
        document.getElementById('merchant-profile-response').textContent = 'Please fill all required fields';
        return;
      }

      try {
        const response = await fetch(`${API_URL}/api/merchants`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            businessName,
            businessType,
            contactEmail,
            contactPhone,
            website,
            country,
            address,
            city,
            state,
            postalCode
          })
        });

        const data = await response.json();
        document.getElementById('merchant-profile-response').textContent = JSON.stringify(data, null, 2);

        if (data.status === 'success') {
          alert('Merchant profile created successfully!');
        }
      } catch (error) {
        document.getElementById('merchant-profile-response').textContent = error.message;
      }
    }
  </script>
</body>
</html>
