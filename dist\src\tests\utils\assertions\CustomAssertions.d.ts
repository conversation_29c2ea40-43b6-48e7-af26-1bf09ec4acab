/**
 * Custom Assertions
 *
 * Custom Jest matchers for common test assertions.
 */
/**
 * Custom Jest matchers
 */
export declare const customMatchers: TestAssertions;
/**
 * Additional assertion helpers
 */
export declare class AssertionHelpers {
    /**
     * Assert that an array contains unique elements
     */
    static assertUniqueArray<T>(array: T[], keyExtractor?: (item: T) => ): any;
}
//# sourceMappingURL=CustomAssertions.d.ts.map