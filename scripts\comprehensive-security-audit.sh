#!/bin/bash

# COMPREHENSIVE SECURITY AUDIT SCRIPT
# Critical Financial Application Security Validation
# Usage: ./comprehensive-security-audit.sh https://yourdomain.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="${1:-https://amazingpay.com}"
REPORT_DIR="security-audit-$(date +%Y%m%d-%H%M%S)"
TIMEOUT=30

echo -e "${BLUE}🔍 COMPREHENSIVE SECURITY AUDIT${NC}"
echo -e "${BLUE}===============================${NC}"
echo -e "Target Domain: ${YELLOW}$DOMAIN${NC}"
echo -e "Report Directory: ${YELLOW}$REPORT_DIR${NC}"
echo -e "Timestamp: ${YELLOW}$(date)${NC}"
echo ""

# Create report directory
mkdir -p "$REPORT_DIR"

# Function to run command with status
run_audit() {
    local description="$1"
    local command="$2"
    local output_file="$3"
    
    echo -e "${YELLOW}Running: $description${NC}"
    echo -n "Progress: "
    
    if eval "$command" > "$REPORT_DIR/$output_file" 2>&1; then
        echo -e "${GREEN}✅ COMPLETED${NC}"
    else
        echo -e "${RED}❌ FAILED${NC}"
    fi
    echo ""
}

# 1. File Access Protection Test
echo -e "${PURPLE}1. FILE ACCESS PROTECTION TEST${NC}"
echo "==============================="
run_audit "Testing file access protection" \
    "bash scripts/security-file-test.sh $DOMAIN" \
    "file-access-test.txt"

# 2. SSL/TLS Security Test
echo -e "${PURPLE}2. SSL/TLS SECURITY TEST${NC}"
echo "========================"
if command -v sslscan &> /dev/null; then
    run_audit "SSL/TLS configuration scan" \
        "sslscan $DOMAIN" \
        "ssl-scan.txt"
else
    echo -e "${YELLOW}⚠️  sslscan not installed, using openssl instead${NC}"
    run_audit "SSL/TLS basic check" \
        "echo | openssl s_client -connect ${DOMAIN#https://}:443 -servername ${DOMAIN#https://} 2>/dev/null | openssl x509 -noout -text" \
        "ssl-basic-check.txt"
fi

# 3. Security Headers Test
echo -e "${PURPLE}3. SECURITY HEADERS TEST${NC}"
echo "========================"
run_audit "Security headers analysis" \
    "curl -I -s --connect-timeout $TIMEOUT $DOMAIN" \
    "security-headers.txt"

# 4. Directory and File Discovery
echo -e "${PURPLE}4. DIRECTORY AND FILE DISCOVERY${NC}"
echo "==============================="

# Create custom wordlist for sensitive files
cat > "$REPORT_DIR/sensitive-files-wordlist.txt" << EOF
.env
.env.local
.env.production
.env.staging
.env.development
.env.example
config.env
tsconfig.json
package.json
package-lock.json
yarn.lock
composer.json
composer.lock
webpack.config.js
vite.config.js
next.config.js
.gitignore
.git/config
.git/HEAD
.htaccess
web.config
database.sql
backup.sql
config.bak
.env.bak
README.md
CHANGELOG.md
LICENSE
docker-compose.yml
Dockerfile
src/
config/
app/
storage/
logs/
node_modules/
vendor/
admin/
phpmyadmin/
wp-admin/
wp-config.php
EOF

if command -v gobuster &> /dev/null; then
    run_audit "Directory and file discovery (gobuster)" \
        "gobuster dir -u $DOMAIN -w $REPORT_DIR/sensitive-files-wordlist.txt -q --timeout ${TIMEOUT}s" \
        "directory-scan.txt"
elif command -v dirb &> /dev/null; then
    run_audit "Directory and file discovery (dirb)" \
        "dirb $DOMAIN $REPORT_DIR/sensitive-files-wordlist.txt -w" \
        "directory-scan.txt"
else
    echo -e "${YELLOW}⚠️  No directory scanner available (gobuster/dirb)${NC}"
    echo "Manual directory scanning recommended" > "$REPORT_DIR/directory-scan.txt"
fi

# 5. Web Vulnerability Scan
echo -e "${PURPLE}5. WEB VULNERABILITY SCAN${NC}"
echo "========================="
if command -v nikto &> /dev/null; then
    run_audit "Web vulnerability scan (nikto)" \
        "nikto -h $DOMAIN -C all -timeout $TIMEOUT -Format txt" \
        "vulnerability-scan.txt"
else
    echo -e "${YELLOW}⚠️  Nikto not installed, performing basic checks${NC}"
    run_audit "Basic vulnerability checks" \
        "curl -s --connect-timeout $TIMEOUT $DOMAIN | grep -i 'server\\|x-powered-by\\|generator' || echo 'No obvious server information disclosed'" \
        "basic-vuln-check.txt"
fi

# 6. HTTP Methods Test
echo -e "${PURPLE}6. HTTP METHODS TEST${NC}"
echo "===================="
run_audit "HTTP methods enumeration" \
    "curl -s -X OPTIONS -I --connect-timeout $TIMEOUT $DOMAIN" \
    "http-methods.txt"

# 7. Robots.txt and Sitemap Analysis
echo -e "${PURPLE}7. ROBOTS.TXT AND SITEMAP ANALYSIS${NC}"
echo "=================================="
run_audit "Robots.txt analysis" \
    "curl -s --connect-timeout $TIMEOUT $DOMAIN/robots.txt || echo 'No robots.txt found'" \
    "robots-txt.txt"

run_audit "Sitemap analysis" \
    "curl -s --connect-timeout $TIMEOUT $DOMAIN/sitemap.xml || echo 'No sitemap.xml found'" \
    "sitemap.txt"

# 8. Error Page Analysis
echo -e "${PURPLE}8. ERROR PAGE ANALYSIS${NC}"
echo "======================"
run_audit "404 error page analysis" \
    "curl -s --connect-timeout $TIMEOUT $DOMAIN/nonexistent-page-12345" \
    "404-error-page.txt"

run_audit "403 error page analysis" \
    "curl -s --connect-timeout $TIMEOUT $DOMAIN/.env" \
    "403-error-page.txt"

# 9. Security Configuration Analysis
echo -e "${PURPLE}9. SECURITY CONFIGURATION ANALYSIS${NC}"
echo "==================================="

# Create security analysis script
cat > "$REPORT_DIR/analyze-security.sh" << 'EOF'
#!/bin/bash
echo "SECURITY CONFIGURATION ANALYSIS"
echo "==============================="
echo ""

echo "1. SECURITY HEADERS ANALYSIS:"
echo "-----------------------------"
if grep -q "X-Frame-Options" security-headers.txt; then
    echo "✅ X-Frame-Options header present"
else
    echo "❌ X-Frame-Options header missing"
fi

if grep -q "X-Content-Type-Options" security-headers.txt; then
    echo "✅ X-Content-Type-Options header present"
else
    echo "❌ X-Content-Type-Options header missing"
fi

if grep -q "X-XSS-Protection" security-headers.txt; then
    echo "✅ X-XSS-Protection header present"
else
    echo "❌ X-XSS-Protection header missing"
fi

if grep -q "Strict-Transport-Security" security-headers.txt; then
    echo "✅ HSTS header present"
else
    echo "❌ HSTS header missing"
fi

if grep -q "Content-Security-Policy" security-headers.txt; then
    echo "✅ CSP header present"
else
    echo "❌ CSP header missing"
fi

echo ""
echo "2. SERVER INFORMATION DISCLOSURE:"
echo "---------------------------------"
if grep -q "Server:" security-headers.txt; then
    echo "⚠️  Server header disclosed: $(grep "Server:" security-headers.txt)"
else
    echo "✅ Server header not disclosed"
fi

if grep -q "X-Powered-By:" security-headers.txt; then
    echo "⚠️  X-Powered-By header disclosed: $(grep "X-Powered-By:" security-headers.txt)"
else
    echo "✅ X-Powered-By header not disclosed"
fi

echo ""
echo "3. SSL/TLS CONFIGURATION:"
echo "------------------------"
if [ -f ssl-scan.txt ]; then
    if grep -q "TLSv1.3" ssl-scan.txt; then
        echo "✅ TLS 1.3 supported"
    else
        echo "⚠️  TLS 1.3 not detected"
    fi
    
    if grep -q "TLSv1.0\|SSLv" ssl-scan.txt; then
        echo "❌ Weak SSL/TLS versions detected"
    else
        echo "✅ No weak SSL/TLS versions detected"
    fi
fi

echo ""
echo "4. FILE ACCESS PROTECTION:"
echo "--------------------------"
if [ -f file-access-test.txt ]; then
    if grep -q "ALL TESTS PASSED" file-access-test.txt; then
        echo "✅ File access protection: SECURE"
    else
        echo "❌ File access protection: VULNERABLE"
        echo "   Check file-access-test.txt for details"
    fi
fi

echo ""
echo "5. DIRECTORY LISTING:"
echo "--------------------"
if [ -f directory-scan.txt ]; then
    if grep -q "403\|404" directory-scan.txt; then
        echo "✅ Directory access properly restricted"
    else
        echo "⚠️  Check directory-scan.txt for potential issues"
    fi
fi
EOF

chmod +x "$REPORT_DIR/analyze-security.sh"
run_audit "Security configuration analysis" \
    "cd $REPORT_DIR && bash analyze-security.sh" \
    "security-analysis.txt"

# 10. Generate Summary Report
echo -e "${PURPLE}10. GENERATING SUMMARY REPORT${NC}"
echo "============================="

cat > "$REPORT_DIR/SECURITY-AUDIT-SUMMARY.md" << EOF
# SECURITY AUDIT SUMMARY REPORT

**Target Domain:** $DOMAIN  
**Audit Date:** $(date)  
**Report Directory:** $REPORT_DIR  

## AUDIT SCOPE

This comprehensive security audit tested the following areas:

1. ✅ File Access Protection
2. ✅ SSL/TLS Configuration
3. ✅ Security Headers
4. ✅ Directory and File Discovery
5. ✅ Web Vulnerability Scanning
6. ✅ HTTP Methods Testing
7. ✅ Information Disclosure
8. ✅ Error Page Analysis
9. ✅ Security Configuration
10. ✅ Summary Analysis

## CRITICAL FINDINGS

### File Access Protection
$(if grep -q "ALL TESTS PASSED" "$REPORT_DIR/file-access-test.txt"; then echo "✅ SECURE - All sensitive files properly protected"; else echo "❌ VULNERABLE - Sensitive files accessible (see file-access-test.txt)"; fi)

### SSL/TLS Security
$(if [ -f "$REPORT_DIR/ssl-scan.txt" ]; then echo "✅ TESTED - See ssl-scan.txt for details"; else echo "⚠️  BASIC CHECK - See ssl-basic-check.txt"; fi)

### Security Headers
$(if grep -q "X-Frame-Options\|X-Content-Type-Options" "$REPORT_DIR/security-headers.txt"; then echo "✅ PRESENT - Security headers detected"; else echo "❌ MISSING - Critical security headers not found"; fi)

### Directory Discovery
$(if [ -f "$REPORT_DIR/directory-scan.txt" ]; then echo "✅ TESTED - See directory-scan.txt for results"; else echo "⚠️  MANUAL TESTING REQUIRED"; fi)

## RECOMMENDATIONS

1. **Review file-access-test.txt** for any failed tests
2. **Check security-headers.txt** for missing security headers
3. **Analyze ssl-scan.txt** for SSL/TLS improvements
4. **Review vulnerability-scan.txt** for security issues
5. **Implement missing security controls** identified in the audit

## FILES GENERATED

- \`file-access-test.txt\` - File access protection results
- \`security-headers.txt\` - HTTP security headers
- \`ssl-scan.txt\` - SSL/TLS configuration analysis
- \`directory-scan.txt\` - Directory and file discovery
- \`vulnerability-scan.txt\` - Web vulnerability scan results
- \`security-analysis.txt\` - Comprehensive security analysis
- \`http-methods.txt\` - Supported HTTP methods
- \`robots-txt.txt\` - Robots.txt analysis
- \`404-error-page.txt\` - Error page analysis

## NEXT STEPS

1. Address any critical vulnerabilities found
2. Implement missing security headers
3. Fix file access protection issues
4. Re-run audit after implementing fixes
5. Schedule regular security audits

---
**Audit completed at:** $(date)
EOF

echo -e "${GREEN}✅ Summary report generated${NC}"
echo ""

# Final Results
echo -e "${BLUE}=================================${NC}"
echo -e "${BLUE}AUDIT COMPLETED${NC}"
echo -e "${BLUE}=================================${NC}"
echo ""
echo -e "${GREEN}📁 All reports saved to: ${YELLOW}$REPORT_DIR${NC}"
echo -e "${GREEN}📄 Summary report: ${YELLOW}$REPORT_DIR/SECURITY-AUDIT-SUMMARY.md${NC}"
echo ""
echo -e "${YELLOW}🔍 Key files to review:${NC}"
echo "1. file-access-test.txt - File protection status"
echo "2. security-analysis.txt - Overall security analysis"
echo "3. SECURITY-AUDIT-SUMMARY.md - Executive summary"
echo ""

# Check for critical issues
if [ -f "$REPORT_DIR/file-access-test.txt" ] && grep -q "SECURITY ISSUES FOUND" "$REPORT_DIR/file-access-test.txt"; then
    echo -e "${RED}🚨 CRITICAL: File access vulnerabilities detected!${NC}"
    echo -e "${RED}   Immediate action required - review file-access-test.txt${NC}"
    exit 1
else
    echo -e "${GREEN}🏆 File access protection appears secure${NC}"
    echo -e "${GREEN}   Review detailed reports for additional improvements${NC}"
    exit 0
fi
