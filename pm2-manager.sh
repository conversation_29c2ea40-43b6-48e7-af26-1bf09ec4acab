#!/bin/bash

# 🔧 PM2 MANAGER SCRIPT
# Handles PM2 commands regardless of installation method

# Find PM2 and execute command
execute_pm2() {
    local cmd="$*"
    
    # Method 1: Try direct pm2 command
    if command -v pm2 >/dev/null 2>&1; then
        pm2 $cmd
        return $?
    fi
    
    # Method 2: Try /usr/local/bin/pm2
    if [ -f "/usr/local/bin/pm2" ]; then
        /usr/local/bin/pm2 $cmd
        return $?
    fi
    
    # Method 3: Try /usr/bin/pm2
    if [ -f "/usr/bin/pm2" ]; then
        /usr/bin/pm2 $cmd
        return $?
    fi
    
    # Method 4: Use npx
    npx pm2 $cmd
    return $?
}

# Execute PM2 with all arguments
execute_pm2 "$@"
