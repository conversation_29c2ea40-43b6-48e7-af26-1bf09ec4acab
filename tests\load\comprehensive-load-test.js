/**
 * Comprehensive Load Testing Suite for AmazingPay Flow
 * 
 * Tests various scenarios including:
 * - Payment processing under load
 * - Identity verification stress testing
 * - Fraud detection performance
 * - API endpoint load testing
 * - Database performance under stress
 */

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { randomString, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
const paymentSuccessRate = new Rate('payment_success_rate');
const identityVerificationRate = new Rate('identity_verification_success_rate');
const fraudDetectionLatency = new Trend('fraud_detection_latency');
const apiResponseTime = new Trend('api_response_time');
const errorCount = new Counter('error_count');

// Test configuration
export const options = {
  scenarios: {
    // Baseline load test
    baseline_load: {
      executor: 'constant-vus',
      vus: 50,
      duration: '10m',
      tags: { test_type: 'baseline' },
    },
    
    // Stress test - gradually increase load
    stress_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 100 },
        { duration: '5m', target: 200 },
        { duration: '2m', target: 300 },
        { duration: '5m', target: 300 },
        { duration: '2m', target: 400 },
        { duration: '1m', target: 0 },
      ],
      tags: { test_type: 'stress' },
    },
    
    // Spike test - sudden load increase
    spike_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '10s', target: 100 },
        { duration: '1m', target: 100 },
        { duration: '10s', target: 1000 }, // Spike
        { duration: '3m', target: 1000 },
        { duration: '10s', target: 100 },
        { duration: '3m', target: 100 },
        { duration: '10s', target: 0 },
      ],
      tags: { test_type: 'spike' },
    },
    
    // Soak test - extended duration
    soak_test: {
      executor: 'constant-vus',
      vus: 100,
      duration: '1h',
      tags: { test_type: 'soak' },
    },
    
    // Payment processing focused test
    payment_focused: {
      executor: 'constant-arrival-rate',
      rate: 100, // 100 requests per second
      timeUnit: '1s',
      duration: '10m',
      preAllocatedVUs: 50,
      maxVUs: 200,
      tags: { test_type: 'payment_focused' },
      exec: 'paymentProcessingTest',
    },
    
    // Identity verification focused test
    identity_focused: {
      executor: 'constant-arrival-rate',
      rate: 50, // 50 requests per second
      timeUnit: '1s',
      duration: '10m',
      preAllocatedVUs: 25,
      maxVUs: 100,
      tags: { test_type: 'identity_focused' },
      exec: 'identityVerificationTest',
    },
  },
  
  thresholds: {
    // Overall performance thresholds
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.05'], // Error rate under 5%
    
    // Custom metric thresholds
    payment_success_rate: ['rate>0.95'], // 95% payment success rate
    identity_verification_success_rate: ['rate>0.90'], // 90% identity verification success
    fraud_detection_latency: ['p(95)<1000'], // Fraud detection under 1s
    api_response_time: ['p(99)<3000'], // 99% of API calls under 3s
    
    // Scenario-specific thresholds
    'http_req_duration{test_type:baseline}': ['p(95)<1500'],
    'http_req_duration{test_type:stress}': ['p(95)<3000'],
    'http_req_duration{test_type:spike}': ['p(95)<5000'],
    'http_req_duration{test_type:soak}': ['p(95)<2000'],
  },
};

// Test data generators
function generatePaymentData() {
  return {
    amount: randomIntBetween(100, 10000), // $1 to $100
    currency: 'USD',
    merchantId: `merchant_${randomIntBetween(1, 1000)}`,
    customerId: `customer_${randomIntBetween(1, 10000)}`,
    paymentMethod: {
      type: 'card',
      cardNumber: '****************',
      expiryMonth: '12',
      expiryYear: '2025',
      cvv: '123',
    },
    metadata: {
      orderId: `order_${randomString(10)}`,
      description: `Test payment ${randomString(5)}`,
    },
  };
}

function generateIdentityData() {
  return {
    userId: `user_${randomIntBetween(1, 10000)}`,
    merchantId: `merchant_${randomIntBetween(1, 1000)}`,
    address: `0x${randomString(40)}`,
    message: `Verify identity for AmazingPay ${randomString(10)}`,
    signature: `0x${randomString(130)}`,
  };
}

function generateUserData() {
  return {
    email: `test${randomIntBetween(1, 100000)}@example.com`,
    password: 'TestPassword123!',
    firstName: `FirstName${randomIntBetween(1, 1000)}`,
    lastName: `LastName${randomIntBetween(1, 1000)}`,
    phoneNumber: `+1${randomIntBetween(1000000000, 9999999999)}`,
  };
}

// Base URL configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_VERSION = '/api/v1';

// Authentication helper
function authenticate() {
  const loginData = {
    email: '<EMAIL>',
    password: 'LoadTest123!',
  };
  
  const response = http.post(`${BASE_URL}${API_VERSION}/auth/login`, JSON.stringify(loginData), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  if (response.status === 200) {
    const body = JSON.parse(response.body);
    return body.token;
  }
  
  return null;
}

// Main test function
export default function() {
  const token = authenticate();
  
  if (!token) {
    errorCount.add(1);
    return;
  }
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  
  group('API Health Check', () => {
    const response = http.get(`${BASE_URL}/health`);
    check(response, {
      'health check status is 200': (r) => r.status === 200,
      'health check response time < 500ms': (r) => r.timings.duration < 500,
    });
    apiResponseTime.add(response.timings.duration);
  });
  
  group('Payment Processing', () => {
    const paymentData = generatePaymentData();
    const startTime = Date.now();
    
    const response = http.post(
      `${BASE_URL}${API_VERSION}/payments/process`,
      JSON.stringify(paymentData),
      { headers }
    );
    
    const success = check(response, {
      'payment status is 200 or 201': (r) => [200, 201].includes(r.status),
      'payment response time < 3000ms': (r) => r.timings.duration < 3000,
      'payment response has transaction id': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.transactionId !== undefined;
        } catch {
          return false;
        }
      },
    });
    
    paymentSuccessRate.add(success);
    apiResponseTime.add(response.timings.duration);
    
    if (!success) {
      errorCount.add(1);
    }
  });
  
  group('Identity Verification', () => {
    const identityData = generateIdentityData();
    const startTime = Date.now();
    
    const response = http.post(
      `${BASE_URL}${API_VERSION}/identity/verify/ethereum`,
      JSON.stringify(identityData),
      { headers }
    );
    
    const success = check(response, {
      'identity verification status is 200': (r) => r.status === 200,
      'identity verification response time < 2000ms': (r) => r.timings.duration < 2000,
      'identity verification has result': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.success !== undefined;
        } catch {
          return false;
        }
      },
    });
    
    identityVerificationRate.add(success);
    apiResponseTime.add(response.timings.duration);
    
    if (!success) {
      errorCount.add(1);
    }
  });
  
  group('Fraud Detection', () => {
    const fraudCheckData = {
      transactionId: `txn_${randomString(10)}`,
      amount: randomIntBetween(100, 50000),
      customerId: `customer_${randomIntBetween(1, 10000)}`,
      merchantId: `merchant_${randomIntBetween(1, 1000)}`,
      ipAddress: `192.168.1.${randomIntBetween(1, 255)}`,
      userAgent: 'Mozilla/5.0 (compatible; LoadTest)',
    };
    
    const startTime = Date.now();
    
    const response = http.post(
      `${BASE_URL}${API_VERSION}/fraud/check`,
      JSON.stringify(fraudCheckData),
      { headers }
    );
    
    const latency = Date.now() - startTime;
    fraudDetectionLatency.add(latency);
    
    check(response, {
      'fraud check status is 200': (r) => r.status === 200,
      'fraud check response time < 1000ms': (r) => r.timings.duration < 1000,
      'fraud check has risk score': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body.riskScore !== undefined;
        } catch {
          return false;
        }
      },
    });
    
    apiResponseTime.add(response.timings.duration);
  });
  
  group('User Management', () => {
    const userData = generateUserData();
    
    const response = http.post(
      `${BASE_URL}${API_VERSION}/users/register`,
      JSON.stringify(userData),
      { headers }
    );
    
    check(response, {
      'user registration status is 201': (r) => r.status === 201,
      'user registration response time < 1500ms': (r) => r.timings.duration < 1500,
    });
    
    apiResponseTime.add(response.timings.duration);
  });
  
  // Random sleep between 1-3 seconds to simulate real user behavior
  sleep(randomIntBetween(1, 3));
}

// Specialized test functions
export function paymentProcessingTest() {
  const token = authenticate();
  if (!token) return;
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  
  const paymentData = generatePaymentData();
  const response = http.post(
    `${BASE_URL}${API_VERSION}/payments/process`,
    JSON.stringify(paymentData),
    { headers }
  );
  
  const success = check(response, {
    'payment processing success': (r) => [200, 201].includes(r.status),
  });
  
  paymentSuccessRate.add(success);
  sleep(0.5);
}

export function identityVerificationTest() {
  const token = authenticate();
  if (!token) return;
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
  
  const identityData = generateIdentityData();
  const response = http.post(
    `${BASE_URL}${API_VERSION}/identity/verify/ethereum`,
    JSON.stringify(identityData),
    { headers }
  );
  
  const success = check(response, {
    'identity verification success': (r) => r.status === 200,
  });
  
  identityVerificationRate.add(success);
  sleep(1);
}

// Test teardown
export function teardown(data) {
  console.log('Load test completed');
  console.log(`Total errors: ${errorCount.count}`);
}
