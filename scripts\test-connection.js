// Database connection test script
const { Client } = require('pg');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function testConnection() {
  console.log('🔍 Testing PostgreSQL connection...');
  
  // Get connection details from environment variables
  const host = process.env.DB_HOST ?? 'localhost';
  const port = process.env.DB_PORT ?? '5432';
  const user = process.env.DB_USERNAME ?? 'postgres';
  const password = process.env.DB_PASSWORD ?? 'Amz12344321';
  const database = process.env.DB_NAME ?? 'amazingpay';
  
  console.log(`📊 Connection details:`);
  console.log(`🏠 Host: ${host}`);
  console.log(`🔢 Port: ${port}`);
  console.log(`👤 User: ${user}`);
  console.log(`🔑 Password: ${'*'.repeat(password.length)}`);
  console.log(`📚 Database: ${database}`);
  
  // Create a new client
  const client = new Client({
    host,
    port,
    user,
    password,
    database,
  });
  
  try {
    console.log('\n🔌 Connecting to PostgreSQL...');
    await client.connect();
    console.log('✅ Connection successful!');
    
    // Test query
    console.log('\n🔍 Testing query execution...');
    const result = await client.query('SELECT version()');
    console.log(`✅ Query executed successfully!`);
    console.log(`📋 PostgreSQL version: ${result.rows[0].version}`);
    
    // Check if the database exists
    console.log('\n🔍 Checking if database exists...');
    const dbResult = await client.query(`
      SELECT EXISTS (
        SELECT 1 FROM pg_database WHERE datname = $1
      )
    `, [database]);
    
    if (dbResult.rows[0].exists) {
      console.log(`✅ Database '${database}' exists!`);
    } else {
      console.log(`❌ Database '${database}' does not exist!`);
      console.log(`⚠️ You need to create the database first.`);
    }
    
    // Check if we can create a table
    console.log('\n🔍 Testing table creation permission...');
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS connection_test (
          id SERIAL PRIMARY KEY,
          test_timestamp TIMESTAMP DEFAULT NOW()
        )
      `);
      console.log('✅ Table creation successful!');
      
      // Clean up the test table
      await client.query('DROP TABLE connection_test');
      console.log('✅ Test table cleaned up.');
    } catch (error) {
      console.error('❌ Table creation failed:', error.message);
      console.log('⚠️ You may not have the necessary permissions.');
    }
    
    console.log('\n✅ Database connection test completed successfully!');
    console.log('🎉 Your PostgreSQL connection is working properly.');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.log('\n⚠️ Troubleshooting tips:');
    console.log('1. Make sure PostgreSQL is running');
    console.log('2. Check your database credentials');
    console.log('3. Make sure the database exists');
    console.log('4. Check your network connection');
    console.log('5. Check PostgreSQL logs for more information');
  } finally {
    try {
      await client.end();
      console.log('🔌 Connection closed.');
    } catch (e) {
      // Ignore errors on disconnect
    }
  }
}

// Run the test
testConnection().catch(console.error);
