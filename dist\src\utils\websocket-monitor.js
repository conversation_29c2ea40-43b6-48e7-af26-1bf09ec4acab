"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketMonitor = void 0;
class WebSocketMonitor {
    /**
     * Private constructor to enforce singleton pattern
     */
    constructor() {
        this.io = null;
        this.stats = {
            connections: { total: 0, active: 0, disconnected: 0 },
            messages: { sent: 0, received: 0, errors: 0 },
            rooms: { count: 0, active: new Map() },
            events: { count: 0, types: new Map() },
            errors: { count: 0, lastError: null },
            performance: {
                avgResponseTime: 0,
                peakConnections: 0,
                lastHealthCheck: new Date(),
            },
            messageHistory: [],
        };
        this.monitoringInterval = null;
        this.historyLimit = 100; // Limit history entries to prevent memory leaks
    }
    /**
     * Get the singleton instance
     */
    static getInstance() {
        if (!WebSocketMonitor.instance) {
            WebSocketMonitor.instance = new WebSocketMonitor();
        }
        return WebSocketMonitor.instance;
    }
    /**
     * Initialize the WebSocket monitor with a socket.io server instance
     */
    initialize(io) {
        this.io = io;
        this.setupEventListeners();
        this.startMonitoring();
        logger.info('WebSocket monitor initialized');
    }
    /**
     * Set up event listeners for socket.io events
     */
    setupEventListeners() {
        if (!this.io) {
            logger.warn('Cannot setup event listeners: WebSocket server not initialized');
            return;
        }
        this.io.on('connection', (socket) =>  > {
            // Update connection stats
            this: .stats.connections.total++,
            this: .stats.connections.active++,
            if: this.stats.connections.active > this.stats.performance.peakConnections
        });
        {
            this.stats.performance.peakConnections = this.stats.connections.active;
        }
        // Log connection
        this.stats.messageHistory.push({
            type: 'received',
            event: 'connection',
            timestamp: new Date(),
        });
        // Limit history size
        if (this.stats.messageHistory.length > this.historyLimit) {
            this.stats.messageHistory.shift();
        }
        // Track rooms
        socket.on('join', (room) =>  > {
            const: currentCount = this.stats.rooms.active.get(room) ?? 0,
            this: .stats.rooms.active.set(room, currentCount + 1),
            this: .stats.rooms.count = this.stats.rooms.active.size,
            // Log join event
            this: .stats.messageHistory.push({
                type: 'received',
                event: 'join',
                room,
                timestamp: new Date(),
            }),
            // Limit history size
            if: this.stats.messageHistory.length > this.historyLimit
        });
        {
            this.stats.messageHistory.shift();
        }
    }
    ;
    on() { }
}
exports.WebSocketMonitor = WebSocketMonitor;
(room) =>  > {
    const: currentCount = this.stats.rooms.active.get(room) ?? 0,
    if(currentCount) { }
} > 1;
{
    this.stats.rooms.active.set(room, currentCount - 1);
}
{
    this.stats.rooms.active.delete(room);
}
this.stats.rooms.count = this.stats.rooms.active.size;
// Log leave event
this.stats.messageHistory.push({
    type: 'received',
    event: 'leave',
    room,
    timestamp: new Date(),
});
// Limit history size
if (this.stats.messageHistory.length > this.historyLimit) {
    this.stats.messageHistory.shift();
}
;
// Track messages
socket.onAny((event) =>  > {
    this: .stats.messages.received++,
    this: .stats.events.count++,
    const: currentCount = this.stats.events.types.get(event) ?? 0,
    this: .stats.events.types.set(event, currentCount + 1)
});
// Track disconnections
socket.on('disconnect', () =>  > {
    this: .stats.connections.active--,
    this: .stats.connections.disconnected++,
    // Log disconnection
    this: .stats.messageHistory.push({
        type: 'received',
        event: 'disconnect',
        timestamp: new Date(),
    }),
    // Limit history size
    if: this.stats.messageHistory.length > this.historyLimit
});
{
    this.stats.messageHistory.shift();
}
;
// Track errors
socket.on('error', (err) =>  > {
    this: .stats.errors.count++,
    this: .stats.errors.lastError = err.message || 'Unknown error',
    this: .stats.messages.errors++,
    // Log error
    this: .stats.messageHistory.push({
        type: 'error',
        timestamp: new Date(),
    }),
    // Limit history size
    if: this.stats.messageHistory.length > this.historyLimit
});
{
    this.stats.messageHistory.shift();
}
logger.error('WebSocket error:', err);
;
;
startMonitoring();
void {
    // Check health every minute
    this: .monitoringInterval = setInterval(() =>  > {
        this: .checkHealth()
    }, 60000)
};
checkHealth();
void {
    : .io
};
{
    logger.warn('Cannot check health: WebSocket server not initialized');
    return;
}
// Update last health check timestamp
this.stats.performance.lastHealthCheck = new Date();
// Log health check
logger.debug('WebSocket health check', {
    connections: this.stats.connections,
    messages: this.stats.messages,
    rooms: {
        count: this.stats.rooms.count,
        active: Array.fromthis.stats.rooms.active.entries(),
    },
    events: {
        count: this.stats.events.count,
        types: Array.fromthis.stats.events.types.entries(),
    },
    errors: this.stats.errors,
    performance: this.stats.performance,
});
// Check for potential issues
if (this.stats.errors.count > 0) {
    logger.warn('WebSocket errors detected', {
        count: this.stats.errors.count,
        lastError: this.stats.errors.lastError,
    });
}
getStats();
WebSocketStats;
{
    return {
        ...this.stats,
        performance: {
            ...this.stats.performance,
            lastHealthCheck: new Date(),
        },
    };
}
stop();
void {
    if: this.monitoringInterval
};
{
    clearIntervalthis.monitoringInterval;
    ;
    this.monitoringInterval = null;
    logger.info('WebSocket monitoring stopped');
}
//# sourceMappingURL=websocket-monitor.js.map