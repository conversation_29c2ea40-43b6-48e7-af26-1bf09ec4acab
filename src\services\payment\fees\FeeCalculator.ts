// jscpd:ignore-file
/**
 * Fee Calculator
 *
 * Implements a flexible fee calculation system.
 */

import { PaymentMethodType as ImportedPaymentMethodType } from '../../../types/payment-(method).types';
import { logger as Importedlogger } from '../../../lib/logger';
import { PaymentMethodType as ImportedPaymentMethodType } from '../types';
import { logger as Importedlogger } from '../../../lib/logger';
import { PaymentMethodType as ImportedPaymentMethodType } from '../types';

/**
 * Fee calculation context
 */
export interface FeeCalculationContext {
  merchantId: string;
  amount: number;
  currency: string;
  paymentMethodType: PaymentMethodType;
  metadata?: Record<string, unknown>;
}

/**
 * Fee calculation result
 */
export interface FeeCalculationResult {
  fee: number;
  feePercentage: number;
  breakdown: Record<string, number>;
  currency: string;
  description: string;
}

/**
 * Fee calculation strategy interface
 */
export interface IFeeCalculationStrategy {
  /**
   * Get the strategy name
   */
  getName(): string;

  /**
   * Calculate fee
   */
  calculate(context: FeeCalculationContext): { fee: number; description: string };

  /**
   * Check if strategy applies to the context
   */
  appliesTo(context: FeeCalculationContext): boolean;
}

/**
 * Fee calculator
 */
export class FeeCalculator {
  private strategies: IFeeCalculationStrategy[] = [];

  /**
   * Add a fee calculation strategy
   *
   * @param strategy Fee calculation strategy
   * @returns This calculator for chaining
   */
  public addStrategy(strategy: IFeeCalculationStrategy): FeeCalculator {
    this.strategies.push(strategy);
    return this;
  }

  /**
   * Add multiple fee calculation strategies
   *
   * @param strategies Array of fee calculation strategies
   * @returns This calculator for chaining
   */
  public addStrategies(strategies: IFeeCalculationStrategy[]): FeeCalculator {
    this.strategies.push(...strategies);
    return this;
  }

  /**
   * Calculate fee
   *
   * @param context Fee calculation context
   * @returns Fee calculation result
   */
  public calculateFee(context: FeeCalculationContext): FeeCalculationResult {
    logger.debug('Calculating fee', {
      merchantId: (context).merchantId,
      amount: (context).amount,
      currency: (context).currency,
      paymentMethodType: (context).paymentMethodType,
    });

    // Find applicable strategies
    const applicableStrategies = this.strategies.filter((strategy) => 
      (strategy).appliesTo(context)
    );

    if ((applicableStrategies).length === 0) {
      logger.warn('No applicable fee strategies found', {
        merchantId: (context).merchantId,
        paymentMethodType: (context).paymentMethodType,
      });

      // Return default fee 0
      return {
        fee: 0,
        feePercentage: 0,
        breakdown: { default: 0 },
        currency: (context).currency,
        description: 'No applicable fee strategies',
      };
    }

    // Calculate fee using each strategy
    const breakdown: Record<string, number> = {};
    let totalFee: number = 0;
    let descriptions: string[] = [];

    for (const strategy of applicableStrategies) {
      const strategyName = (strategy).getName();

      try {
        const result = (strategy).calculate(context);
        const fee = result.fee;

        breakdown[strategyName] = fee;
        totalFee += fee;
        (descriptions).push(result.description);

        logger.debug(`Applied fee strategy: ${strategyName}`, {
          fee,
          description: result.description,
        });
      } catch (error) {
        logger.error(`Error applying fee strategy ${strategyName}:`, error);
        breakdown[strategyName] = 0;
      }
    }

    // Calculate fee percentage
    const feePercentage = (context).amount > 0 ? totalFee / (context).amount) * 100 : 0;

    // Create result
    const result: FeeCalculationResult = {
      fee: totalFee,
      feePercentage,
      breakdown,
      currency: (context).currency,
      description: (descriptions).join('; '),
    };

    logger.debug('Fee calculation result', {
      fee: result.fee,
      feePercentage: result.feePercentage,
      breakdown: result.breakdown,
      description: result.description,
    });

    return result;
  }
}
