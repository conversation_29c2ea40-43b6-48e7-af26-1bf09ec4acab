# 🚀 AmazingPay VPS Deployment Guide

## 📋 **DEPLOYMENT INFORMATION**

- **VPS IP:** ************
- **Domain:** amazingpayme.com
- **Database:** Amazingpay
- **Database Password:** CepWrkdzE5TL
- **Application Port:** 3002
- **Node.js Version:** 20.x LTS

## 🎯 **QUICK DEPLOYMENT STEPS**

### **1. 📁 Upload Files to VPS**

```bash
# Upload your project files to VPS
scp -r ./amazingpay-flow root@************:/www/wwwroot/amazingpayme.com/
```

### **2. 🔧 Run Automated Deployment**

```bash
# SSH to your VPS
ssh root@************

# Navigate to project directory
cd /www/wwwroot/amazingpayme.com

# Make deployment script executable
chmod +x scripts/vps-deployment.sh

# Run deployment script
./scripts/vps-deployment.sh
```

## 📝 **MANUAL DEPLOYMENT STEPS**

### **Step 1: 🔧 System Setup**

```bash
# Update system
apt update && apt upgrade -y

# Install dependencies
apt install -y curl wget git nginx postgresql postgresql-contrib \
               nodejs npm build-essential python3-certbot-nginx \
               ufw fail2ban htop tree zip unzip

# Install PM2 globally
npm install -g pm2
```

### **Step 2: 🗄️ Database Setup**

```bash
# Start PostgreSQL
systemctl start postgresql
systemctl enable postgresql

# Set postgres password
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'CepWrkdzE5TL';"

# Create database
sudo -u postgres psql -c "CREATE DATABASE \"Amazingpay\";"

# Run database setup script
cd /www/wwwroot/amazingpayme.com
chmod +x scripts/setup-production-database.sh
./scripts/setup-production-database.sh
```

### **Step 3: 🌐 Nginx Configuration**

```bash
# Copy Nginx configuration
cp deployment/nginx-amazingpay.conf /etc/nginx/sites-available/amazingpayme.com

# Enable site
ln -sf /etc/nginx/sites-available/amazingpayme.com /etc/nginx/sites-enabled/

# Remove default site
rm -f /etc/nginx/sites-enabled/default

# Test configuration
nginx -t

# Start Nginx
systemctl start nginx
systemctl enable nginx
```

### **Step 4: 🔐 SSL Certificate**

```bash
# Install SSL certificate
certbot --nginx -d amazingpayme.com -d www.amazingpayme.com \
        --non-interactive --agree-tos --email <EMAIL>

# Setup auto-renewal
crontab -l | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet"; } | crontab -
```

### **Step 5: 🚀 Application Deployment**

```bash
# Navigate to app directory
cd /www/wwwroot/amazingpayme.com

# Copy production environment
cp .env.production .env

# Install dependencies
npm ci --production

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# Build application
npm run build

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### **Step 6: 🔥 Firewall Setup**

```bash
# Configure UFW
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3002/tcp
ufw --force enable
```

## 🔧 **CONFIGURATION FILES UPDATED**

### **✅ Environment Configuration**

- **`.env`** - Updated with VPS settings
- **`.env.production`** - Production environment file
- **Database URL:** `postgresql://postgres:CepWrkdzE5TL@localhost:5432/Amazingpay`

### **✅ Deployment Scripts**

- **`scripts/vps-deployment.sh`** - Complete automated deployment
- **`scripts/setup-production-database.sh`** - Database setup with VPS credentials
- **`ecosystem.config.js`** - PM2 configuration for production

### **✅ Server Configuration**

- **`deployment/nginx-amazingpay.conf`** - Nginx configuration with SSL and security
- **Firewall rules** - UFW configuration for security

## 🎯 **POST-DEPLOYMENT VERIFICATION**

### **1. 🏥 Health Checks**

```bash
# Check application status
pm2 status

# Check application logs
pm2 logs amazingpay-main

# Test health endpoint
curl https://amazingpayme.com/api/health
```

### **2. 🗄️ Database Verification**

```bash
# Test database connection
sudo -u postgres psql -d Amazingpay -c "SELECT 1;"

# Check database size
sudo -u postgres psql -d Amazingpay -c "SELECT pg_size_pretty(pg_database_size('Amazingpay'));"
```

### **3. 🌐 Web Server Tests**

```bash
# Test Nginx configuration
nginx -t

# Check SSL certificate
curl -I https://amazingpayme.com

# Test API endpoints
curl https://amazingpayme.com/api/health
```

## 📊 **MONITORING & MAINTENANCE**

### **🔄 Application Management**

```bash
# Restart application
pm2 restart amazingpay-main

# View logs
pm2 logs amazingpay-main --lines 100

# Monitor resources
pm2 monit
```

### **🗄️ Database Backups**

```bash
# Manual backup
/usr/local/bin/backup-amazingpay-db.sh

# View backup files
ls -la /var/backups/amazingpay/database/
```

### **🔐 Security Monitoring**

```bash
# Check fail2ban status
fail2ban-client status

# View firewall status
ufw status verbose

# Check SSL certificate expiry
certbot certificates
```

## 🚨 **TROUBLESHOOTING**

### **Application Issues**

```bash
# Check PM2 status
pm2 status

# View error logs
pm2 logs amazingpay-main --err

# Restart application
pm2 restart amazingpay-main
```

### **Database Issues**

```bash
# Check PostgreSQL status
systemctl status postgresql

# Test database connection
sudo -u postgres psql -d Amazingpay -c "SELECT version();"

# View database logs
tail -f /var/log/postgresql/postgresql-*.log
```

### **Web Server Issues**

```bash
# Check Nginx status
systemctl status nginx

# Test configuration
nginx -t

# View access logs
tail -f /var/log/nginx/amazingpay-access.log

# View error logs
tail -f /var/log/nginx/amazingpay-error.log
```

## 🎉 **SUCCESS VERIFICATION**

After deployment, verify these URLs work:

- **🌐 Main Site:** https://amazingpayme.com
- **🔗 API Health:** https://amazingpayme.com/api/health
- **📊 API Docs:** https://amazingpayme.com/api/docs
- **🔐 Admin Panel:** https://amazingpayme.com/admin

## 📞 **SUPPORT**

If you encounter any issues:

1. **Check logs:** `pm2 logs amazingpay-main`
2. **Verify configuration:** Review `.env.production`
3. **Test database:** Run database connection test
4. **Check services:** Ensure all services are running

## 🔄 **UPDATES & MAINTENANCE**

### **Application Updates**

```bash
# Pull latest changes
git pull origin main

# Install dependencies
npm ci --production

# Run migrations
npx prisma migrate deploy

# Build application
npm run build

# Restart application
pm2 restart amazingpay-main
```

### **System Updates**

```bash
# Update system packages
apt update && apt upgrade -y

# Update Node.js (if needed)
npm install -g npm@latest

# Update PM2
npm install -g pm2@latest
```

---

**🎯 Your AmazingPay application is now ready for production on VPS ************!**
