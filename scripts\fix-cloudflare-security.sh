#!/bin/bash

# 🌐 CLOUDFLARE SECURITY CONFIGURATION FIX
# Fixes security headers when using Cloudflare for AmazingPay
# Domain: amazingpayme.com | VPS: 159.65.92.74

set -e

# 🎯 CONFIGURATION
DOMAIN="amazingpayme.com"

# 🎨 COLORS
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 📝 LOGGING
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 🔧 CREATE APPLICATION-LEVEL SECURITY HEADERS
create_app_security_middleware() {
    log "Creating application-level security headers middleware..."
    
    local middleware_file="/www/wwwroot/amazingpayme.com/src/middlewares/security-headers.ts"
    
    # Create middleware directory if it doesn't exist
    mkdir -p "$(dirname "$middleware_file")"
    
    cat > "$middleware_file" << 'EOF'
import { Request, Response, NextFunction } from 'express';

/**
 * 🔒 SECURITY HEADERS MIDDLEWARE
 * Ensures all security headers are set for financial application
 * Overrides any missing headers from Cloudflare/Nginx
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
    // 🛡️ HSTS - Force HTTPS for 1 year
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    
    // 🚫 X-Frame-Options - Prevent clickjacking
    res.setHeader('X-Frame-Options', 'DENY');
    
    // 🔒 X-Content-Type-Options - Prevent MIME sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // 🛡️ X-XSS-Protection - Enable XSS filtering
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // 🔐 Content Security Policy - Strict CSP for financial app
    const csp = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net",
        "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com",
        "img-src 'self' data: https:",
        "font-src 'self' data: https://fonts.gstatic.com",
        "connect-src 'self' https:",
        "frame-ancestors 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "upgrade-insecure-requests"
    ].join('; ');
    res.setHeader('Content-Security-Policy', csp);
    
    // 🚫 Referrer Policy - Strict referrer policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // 🔒 Permissions Policy - Disable dangerous features
    const permissionsPolicy = [
        'geolocation=()',
        'microphone=()',
        'camera=()',
        'payment=()',
        'usb=()',
        'magnetometer=()',
        'gyroscope=()',
        'speaker=()',
        'fullscreen=(self)',
        'sync-xhr=()'
    ].join(', ');
    res.setHeader('Permissions-Policy', permissionsPolicy);
    
    // 🌐 Cross-Origin Policies
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
    res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
    
    // 🚫 Hide server information
    res.removeHeader('X-Powered-By');
    res.setHeader('Server', 'AmazingPay-Secure');
    
    // 📱 Additional security headers for financial applications
    res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
    res.setHeader('X-Download-Options', 'noopen');
    res.setHeader('X-DNS-Prefetch-Control', 'off');
    
    // 💰 Financial application specific headers
    res.setHeader('X-Financial-Security', 'enabled');
    res.setHeader('X-Payment-Security', 'maximum');
    
    next();
};

/**
 * 🔒 API SECURITY HEADERS
 * Additional security for API endpoints
 */
export const apiSecurityHeaders = (req: Request, res: Response, next: NextFunction): void => {
    // Prevent caching of sensitive API responses
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    // API-specific security
    res.setHeader('X-API-Security', 'enabled');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    next();
};

/**
 * 🔐 AUTH SECURITY HEADERS
 * Maximum security for authentication endpoints
 */
export const authSecurityHeaders = (req: Request, res: Response, next: NextFunction): void => {
    // Maximum security for auth endpoints
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private, max-age=0');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('X-Auth-Security', 'maximum');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    next();
};
EOF
    
    success "Security headers middleware created"
}

# 🔧 UPDATE MAIN APPLICATION
update_main_app() {
    log "Updating main application to use security headers..."
    
    local app_file="/www/wwwroot/amazingpayme.com/src/index.ts"
    
    if [ ! -f "$app_file" ]; then
        warning "Main application file not found, creating basic structure..."
        return
    fi
    
    # Check if security headers are already imported
    if grep -q "securityHeaders" "$app_file"; then
        info "Security headers already configured in main app"
        return
    fi
    
    # Create backup
    cp "$app_file" "$app_file.backup.$(date +%Y%m%d-%H%M%S)"
    
    # Add security headers import and middleware
    local temp_file=$(mktemp)
    
    # Add import at the top (after other imports)
    sed '/^import.*express/a\
import { securityHeaders, apiSecurityHeaders, authSecurityHeaders } from '\''./middlewares/security-headers'\'';' "$app_file" > "$temp_file"
    
    # Add middleware usage (after app creation)
    sed '/const app = express()/a\
\
// 🔒 SECURITY HEADERS MIDDLEWARE - FINANCIAL APPLICATION\
app.use(securityHeaders);\
\
// 🔐 API SECURITY HEADERS\
app.use('\''/api'\'', apiSecurityHeaders);\
\
// 🔒 AUTH SECURITY HEADERS\
app.use('\''/api/auth'\'', authSecurityHeaders);' "$temp_file" > "$app_file"
    
    rm "$temp_file"
    
    success "Main application updated with security headers"
}

# 🔄 REBUILD APPLICATION
rebuild_application() {
    log "Rebuilding application with security updates..."
    
    cd /www/wwwroot/amazingpayme.com
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        npm ci --production
    fi
    
    # Build application
    npm run build
    
    success "Application rebuilt successfully"
}

# 🚀 RESTART APPLICATION
restart_application() {
    log "Restarting application..."
    
    # Restart PM2 application
    if command -v pm2 &> /dev/null; then
        pm2 restart amazingpay-main || pm2 restart all
        success "Application restarted with PM2"
    else
        warning "PM2 not found, manual restart may be required"
    fi
}

# 🧪 TEST SECURITY HEADERS
test_security_headers() {
    log "Testing security headers after application update..."
    
    sleep 10  # Wait for application to fully restart
    
    local test_url="https://$DOMAIN/api/health"
    
    echo "Testing security headers for: $test_url"
    
    # Test with curl
    local headers=$(curl -I -s "$test_url" 2>/dev/null || echo "")
    
    if [ -z "$headers" ]; then
        warning "Could not retrieve headers, application may still be starting"
        return
    fi
    
    echo "Response headers:"
    echo "$headers"
    echo ""
    
    # Check specific headers
    if echo "$headers" | grep -i "x-frame-options" > /dev/null; then
        success "✅ X-Frame-Options header present"
    else
        error "❌ X-Frame-Options header missing"
    fi
    
    if echo "$headers" | grep -i "strict-transport-security" > /dev/null; then
        success "✅ HSTS header present"
    else
        error "❌ HSTS header missing"
    fi
    
    if echo "$headers" | grep -i "x-content-type-options" > /dev/null; then
        success "✅ X-Content-Type-Options header present"
    else
        error "❌ X-Content-Type-Options header missing"
    fi
    
    if echo "$headers" | grep -i "content-security-policy" > /dev/null; then
        success "✅ Content-Security-Policy header present"
    else
        warning "⚠️ Content-Security-Policy header missing"
    fi
}

# 📋 DISPLAY CLOUDFLARE INSTRUCTIONS
display_cloudflare_instructions() {
    log "📋 Cloudflare Configuration Instructions"
    
    echo ""
    info "🌐 CLOUDFLARE DASHBOARD CONFIGURATION:"
    echo ""
    echo "1. Login to Cloudflare Dashboard: https://dash.cloudflare.com"
    echo "2. Select your domain: $DOMAIN"
    echo "3. Go to 'Security' → 'Settings'"
    echo "4. Configure these settings:"
    echo ""
    echo "   🔒 Security Level: High"
    echo "   🛡️ Browser Integrity Check: ON"
    echo "   🚫 Challenge Passage: 30 minutes"
    echo ""
    echo "5. Go to 'Speed' → 'Optimization'"
    echo "6. Configure these settings:"
    echo ""
    echo "   ⚡ Auto Minify: CSS, HTML, JavaScript"
    echo "   🗜️ Brotli: ON"
    echo ""
    echo "7. Go to 'SSL/TLS' → 'Edge Certificates'"
    echo "8. Configure these settings:"
    echo ""
    echo "   🔐 SSL/TLS encryption mode: Full (strict)"
    echo "   🔒 Always Use HTTPS: ON"
    echo "   📜 HSTS: Enable with these settings:"
    echo "       - Max Age Header: 12 months"
    echo "       - Include Subdomains: ON"
    echo "       - Preload: ON"
    echo ""
    echo "9. Go to 'Security' → 'WAF'"
    echo "10. Enable these rules:"
    echo ""
    echo "    🛡️ OWASP Core Rule Set: ON"
    echo "    🚫 Rate Limiting: Configure for API endpoints"
    echo ""
    success "Cloudflare configuration instructions provided"
}

# 🎯 MAIN FUNCTION
main() {
    log "🌐 Starting Cloudflare Security Configuration Fix"
    log "Domain: $DOMAIN"
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
    fi
    
    create_app_security_middleware
    update_main_app
    rebuild_application
    restart_application
    test_security_headers
    display_cloudflare_instructions
    
    success "🎉 Cloudflare security configuration completed!"
    info "Run the security validation script again to verify all issues are resolved"
    info "Command: ./scripts/pre-launch-security-validation.sh https://amazingpayme.com"
}

# Run main function
main "$@"
