/**
 * SECURE CONFIGURATION UTILITIES
 * Critical financial application security helpers
 */

import { logger } from '../lib/logger';

/**
 * Securely retrieves environment variable with validation
 * NEVER logs the actual value for security
 */
export function getSecureEnvVar(
  key: string, 
  defaultValue?: string,
  required: boolean = false
): string {
  const value = process.env[key] ?? defaultValue;
  
  if (required && !value) {
    logger.error(`SECURITY: Required environment variable ${key} is not set`);
    throw new Error(`Required environment variable ${key} is not set`);
  }
  
  // Log only the status, never the value
  if (value) {
    logger.info(`Environment variable ${key}: configured`);
  } else {
    logger.warn(`Environment variable ${key}: not set, using default`);
  }
  
  return value ?? '';
}

/**
 * Validates that sensitive environment variables are properly configured
 * Returns only status information, never actual values
 */
export function validateSecurityConfig(): Record<string, boolean> {
  const criticalVars = [
    'JWT_SECRET',
    'DB_PASSWORD',
    'DATABASE_URL'
  ];
  
  const sensitiveVars = [
    'BINANCE_API_KEY',
    'BINANCE_API_SECRET',
    'STRIPE_SECRET_KEY',
    'PAYPAL_CLIENT_SECRET'
  ];
  
  const status: Record<string, boolean> = {};
  
  // Check critical variables
  for (const varName of criticalVars) {
    const isSet = Boolean(process.env[varName]);
    status[varName] = isSet;
    
    if (!isSet) {
      logger.error(`SECURITY: Critical variable ${varName} is not configured`);
    } else {
      logger.info(`SECURITY: Critical variable ${varName} is properly configured`);
    }
  }
  
  // Check sensitive variables
  for (const varName of sensitiveVars) {
    const isSet = Boolean(process.env[varName]);
    status[varName] = isSet;
    
    if (isSet) {
      logger.info(`SECURITY: Sensitive variable ${varName} is configured`);
    } else {
      logger.warn(`SECURITY: Sensitive variable ${varName} is not configured`);
    }
  }
  
  return status;
}

/**
 * Masks sensitive values for safe logging/display
 * Shows only first and last 2 characters with asterisks in between
 */
export function maskSensitiveValue(value: string, showLength: number = 2): string {
  if (!value || value.length <= showLength * 2) {
    return '***';
  }
  
  const start = value.substring(0, showLength);
  const end = value.substring(value.length - showLength);
  const middle = '*'.repeat(Math.max(4, value.length - showLength * 2));
  
  return `${start}${middle}${end}`;
}

/**
 * Safely displays configuration status for admin interfaces
 * NEVER exposes actual values
 */
export function getConfigurationStatus(): Record<string, string> {
  return {
    database: process.env.DATABASE_URL ? 'configured' : 'not configured',
    jwt: process.env.JWT_SECRET ? 'configured' : 'not configured',
    binance: process.env.BINANCE_API_KEY ? 'configured' : 'not configured',
    stripe: process.env.STRIPE_SECRET_KEY ? 'configured' : 'not configured',
    email: process.env.EMAIL_PASSWORD ? 'configured' : 'not configured',
    sms: process.env.TWILIO_AUTH_TOKEN ? 'configured' : 'not configured'
  };
}

/**
 * Validates environment variable format without exposing values
 */
export function validateEnvVarFormat(key: string, pattern: RegExp): boolean {
  const value = process.env[key];
  
  if (!value) {
    logger.warn(`SECURITY: Environment variable ${key} is not set`);
    return false;
  }
  
  const isValid = pattern.test(value);
  
  if (isValid) {
    logger.info(`SECURITY: Environment variable ${key} format is valid`);
  } else {
    logger.error(`SECURITY: Environment variable ${key} format is invalid`);
  }
  
  return isValid;
}

/**
 * Secure database URL validation
 */
export function validateDatabaseUrl(): boolean {
  const dbUrl = process.env.DATABASE_URL;
  
  if (!dbUrl) {
    logger.error('SECURITY: DATABASE_URL is not configured');
    return false;
  }
  
  // Check if it contains password (should be masked in logs)
  const hasPassword = dbUrl.includes(':') && dbUrl.includes('@');
  
  if (hasPassword) {
    logger.info('SECURITY: DATABASE_URL is properly formatted with credentials');
  } else {
    logger.warn('SECURITY: DATABASE_URL may be missing credentials');
  }
  
  return hasPassword;
}

/**
 * JWT secret strength validation
 */
export function validateJwtSecret(): boolean {
  const jwtSecret = process.env.JWT_SECRET;
  
  if (!jwtSecret) {
    logger.error('SECURITY: JWT_SECRET is not configured');
    return false;
  }
  
  const isStrong = jwtSecret.length >= 32;
  
  if (isStrong) {
    logger.info('SECURITY: JWT_SECRET meets minimum length requirements');
  } else {
    logger.error('SECURITY: JWT_SECRET is too short (minimum 32 characters required)');
  }
  
  return isStrong;
}

/**
 * Comprehensive security validation
 */
export function performSecurityValidation(): boolean {
  logger.info('SECURITY: Starting comprehensive security validation');
  
  const validationResults = [
    validateDatabaseUrl(),
    validateJwtSecret(),
    validateEnvVarFormat('BINANCE_API_KEY', /^[A-Za-z0-9]{64}$/),
    validateEnvVarFormat('STRIPE_SECRET_KEY', /^sk_(test_|live_)[A-Za-z0-9]{99}$/)
  ];
  
  const allValid = validationResults.every(result => result);
  
  if (allValid) {
    logger.info('SECURITY: All security validations passed');
  } else {
    logger.error('SECURITY: Some security validations failed');
  }
  
  return allValid;
}

export default {
  getSecureEnvVar,
  validateSecurityConfig,
  maskSensitiveValue,
  getConfigurationStatus,
  validateEnvVarFormat,
  validateDatabaseUrl,
  validateJwtSecret,
  performSecurityValidation
};
