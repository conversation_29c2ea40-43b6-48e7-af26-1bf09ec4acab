#!/usr/bin/env node

/**
 * COMPREHENSIVE SECURITY AUDIT
 * Critical financial application security validation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 COMPREHENSIVE SECURITY AUDIT');
console.log('================================');
console.log('🚨 CRITICAL: Financial application security validation');
console.log('📋 Checking: Environment variables, secrets, logging, exposure');
console.log('');

const securityResults = {
  envVarLogging: { violations: [], count: 0 },
  hardcodedSecrets: { violations: [], count: 0 },
  unsafeOperators: { violations: [], count: 0 },
  gitignoreCheck: { violations: [], count: 0 },
  configSeparation: { violations: [], count: 0 },
  adminExposure: { violations: [], count: 0 },
  debuggingIssues: { violations: [], count: 0 },
};

const SENSITIVE_PATTERNS = [
  'JWT_SECRET',
  'DB_PASSWORD',
  'DATABASE_URL',
  'API_KEY',
  'SECRET_KEY',
  'PRIVATE_KEY',
  'TOKEN',
  'BINANCE_',
  'PAYMENT_',
  'STRIPE_',
  'PAYPAL_',
];

function scanFileForSecurityViolations(filePath) {
  if (!fs.existsSync(filePath) || (!filePath.endsWith('.ts') && !filePath.endsWith('.js'))) {
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      const lineNum = index + 1;

      // 1. Check for environment variable logging
      if (line.includes('console.log') && line.includes('process.env')) {
        securityResults.envVarLogging.violations.push({
          file: filePath,
          line: lineNum,
          content: line.trim(),
          severity: 'CRITICAL',
        });
      }

      // 2. Check for hardcoded sensitive values
      const hardcodedPatterns = [
        /['"`]sk_[a-zA-Z0-9_]{20,}['"`]/g, // Stripe secret keys
        /['"`]pk_[a-zA-Z0-9_]{20,}['"`]/g, // Stripe public keys
        /['"`][A-Za-z0-9+/]{32,}={0,2}['"`]/g, // Base64 encoded secrets
        /password\s*[:=]\s*['"`][^'"`\s]{8,}['"`]/gi,
        /secret\s*[:=]\s*['"`][^'"`\s]{8,}['"`]/gi,
      ];

      hardcodedPatterns.forEach((pattern) => {
        if (pattern.test(line)) {
          securityResults.hardcodedSecrets.violations.push({
            file: filePath,
            line: lineNum,
            content: line.trim(),
            severity: 'CRITICAL',
          });
        }
      });

      // 3. Check for unsafe logical OR operators with env vars
      if (line.includes('process.env') && line.includes('||') && !line.includes('??')) {
        securityResults.unsafeOperators.violations.push({
          file: filePath,
          line: lineNum,
          content: line.trim(),
          severity: 'HIGH',
        });
      }

      // 4. Check for potential admin exposure
      if (
        (line.includes('process.env') && line.includes('res.json')) ||
        (line.includes('process.env') && line.includes('response')) ||
        (line.includes('process.env') && line.includes('send'))
      ) {
        securityResults.adminExposure.violations.push({
          file: filePath,
          line: lineNum,
          content: line.trim(),
          severity: 'CRITICAL',
        });
      }

      // 5. Check for debugging issues
      if (
        line.includes('console.') &&
        SENSITIVE_PATTERNS.some((pattern) => line.toUpperCase().includes(pattern))
      ) {
        securityResults.debuggingIssues.violations.push({
          file: filePath,
          line: lineNum,
          content: line.trim(),
          severity: 'HIGH',
        });
      }
    });
  } catch (error) {
    console.warn(`Warning: Could not scan ${filePath}: ${error.message}`);
  }
}

function scanDirectoryForSecurity(dir) {
  try {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !['node_modules', '.git', 'dist'].includes(item)) {
        scanDirectoryForSecurity(fullPath);
      } else if (
        (item.endsWith('.ts') || item.endsWith('.js')) &&
        !item.includes('security-audit.js')
      ) {
        scanFileForSecurityViolations(fullPath);
      }
    }
  } catch (error) {
    console.warn(`Warning: Could not scan directory ${dir}: ${error.message}`);
  }
}

function checkGitignoreCompliance() {
  console.log('🔧 1. .gitignore Security Compliance');
  console.log('=====================================');

  const gitignorePath = '.gitignore';
  const requiredEntries = [
    '.env',
    '.env.local',
    '.env.production',
    '.env.staging',
    '*.env',
    'secrets/',
    'config/secrets.json',
  ];

  if (!fs.existsSync(gitignorePath)) {
    securityResults.gitignoreCheck.violations.push({
      file: gitignorePath,
      issue: 'Missing .gitignore file',
      severity: 'CRITICAL',
    });
    console.log('❌ CRITICAL: .gitignore file missing');
    return false;
  }

  const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
  let missingEntries = [];

  for (const entry of requiredEntries) {
    if (!gitignoreContent.includes(entry)) {
      missingEntries.push(entry);
    }
  }

  if (missingEntries.length > 0) {
    securityResults.gitignoreCheck.violations.push({
      file: gitignorePath,
      issue: `Missing entries: ${missingEntries.join(', ')}`,
      severity: 'HIGH',
    });
    console.log(`⚠️  Missing .gitignore entries: ${missingEntries.join(', ')}`);
    return false;
  }

  console.log('✅ .gitignore properly configured');
  return true;
}

function checkConfigSeparation() {
  console.log('\n🔧 2. Configuration Separation Check');
  console.log('====================================');

  const requiredConfigFiles = ['src/config/database.config.ts', 'src/config/auth.ts'];

  const optionalConfigFiles = [
    'src/config/binance.config.ts',
    'src/config/payment.config.ts',
    'src/config/stripe.config.ts',
  ];

  let allPresent = true;

  for (const configFile of requiredConfigFiles) {
    if (!fs.existsSync(configFile)) {
      securityResults.configSeparation.violations.push({
        file: configFile,
        issue: 'Required config file missing',
        severity: 'HIGH',
      });
      console.log(`❌ Missing required config: ${configFile}`);
      allPresent = false;
    } else {
      console.log(`✅ Found: ${configFile}`);
    }
  }

  for (const configFile of optionalConfigFiles) {
    if (fs.existsSync(configFile)) {
      console.log(`✅ Found optional: ${configFile}`);
    }
  }

  return allPresent;
}

function performSecurityScan() {
  console.log('\n🔧 3. Source Code Security Scan');
  console.log('================================');

  scanDirectoryForSecurity('./src');
  scanDirectoryForSecurity('./scripts');

  // Count violations
  securityResults.envVarLogging.count = securityResults.envVarLogging.violations.length;
  securityResults.hardcodedSecrets.count = securityResults.hardcodedSecrets.violations.length;
  securityResults.unsafeOperators.count = securityResults.unsafeOperators.violations.length;
  securityResults.adminExposure.count = securityResults.adminExposure.violations.length;
  securityResults.debuggingIssues.count = securityResults.debuggingIssues.violations.length;

  console.log(`📊 Environment variable logging violations: ${securityResults.envVarLogging.count}`);
  console.log(`📊 Hardcoded secrets violations: ${securityResults.hardcodedSecrets.count}`);
  console.log(`📊 Unsafe operator violations: ${securityResults.unsafeOperators.count}`);
  console.log(`📊 Admin exposure violations: ${securityResults.adminExposure.count}`);
  console.log(`📊 Debugging violations: ${securityResults.debuggingIssues.count}`);
}

function generateSecurityReport() {
  console.log('\n🔒 SECURITY AUDIT REPORT');
  console.log('========================');

  const totalViolations =
    securityResults.envVarLogging.count +
    securityResults.hardcodedSecrets.count +
    securityResults.unsafeOperators.count +
    securityResults.gitignoreCheck.count +
    securityResults.configSeparation.count +
    securityResults.adminExposure.count +
    securityResults.debuggingIssues.count;

  const isSecure = totalViolations === 0;

  console.log(`\n🎯 SECURITY STATUS: ${isSecure ? '🔒 SECURE' : '🚨 CRITICAL ISSUES FOUND'}`);
  console.log('================================================');
  console.log(`📊 Total Security Violations: ${totalViolations}`);
  console.log('');
  console.log('📋 DETAILED BREAKDOWN:');
  console.log(`   🚨 Environment Variable Logging: ${securityResults.envVarLogging.count}`);
  console.log(`   🔑 Hardcoded Secrets: ${securityResults.hardcodedSecrets.count}`);
  console.log(`   ⚠️  Unsafe Operators: ${securityResults.unsafeOperators.count}`);
  console.log(`   📁 .gitignore Issues: ${securityResults.gitignoreCheck.count}`);
  console.log(`   🗂️  Config Separation: ${securityResults.configSeparation.count}`);
  console.log(`   🌐 Admin Exposure: ${securityResults.adminExposure.count}`);
  console.log(`   🐛 Debugging Issues: ${securityResults.debuggingIssues.count}`);

  if (isSecure) {
    console.log('\n🏆 SECURITY AUDIT PASSED!');
    console.log('==========================');
    console.log('✅ No environment variable logging');
    console.log('✅ No hardcoded secrets');
    console.log('✅ Safe operator usage');
    console.log('✅ Proper .gitignore configuration');
    console.log('✅ Good config separation');
    console.log('✅ No admin exposure risks');
    console.log('✅ No debugging security issues');
    console.log('');
    console.log('🚀 APPLICATION IS SECURE FOR FINANCIAL OPERATIONS');
  } else {
    console.log('\n🚨 CRITICAL SECURITY ISSUES FOUND!');
    console.log('===================================');

    if (securityResults.envVarLogging.count > 0) {
      console.log('\n🚨 CRITICAL: Environment Variable Logging Detected:');
      securityResults.envVarLogging.violations.forEach((v) => {
        console.log(`   ${v.file}:${v.line} - ${v.content}`);
      });
    }

    if (securityResults.hardcodedSecrets.count > 0) {
      console.log('\n🔑 CRITICAL: Hardcoded Secrets Detected:');
      securityResults.hardcodedSecrets.violations.forEach((v) => {
        console.log(`   ${v.file}:${v.line} - [REDACTED FOR SECURITY]`);
      });
    }

    if (securityResults.adminExposure.count > 0) {
      console.log('\n🌐 CRITICAL: Admin Exposure Risk:');
      securityResults.adminExposure.violations.forEach((v) => {
        console.log(`   ${v.file}:${v.line} - ${v.content}`);
      });
    }

    console.log('\n⚠️  IMMEDIATE ACTION REQUIRED!');
    console.log('==============================');
    console.log('🔴 Fix all CRITICAL issues before deployment');
    console.log('🟠 Review and fix HIGH severity issues');
    console.log('🔒 This application handles sensitive financial data');
  }

  // Save security report
  const reportPath = 'security-audit-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(securityResults, null, 2), 'utf8');
  console.log(`\n📄 Security report saved to: ${reportPath}`);

  return isSecure;
}

async function main() {
  console.log('🚀 Starting comprehensive security audit...\n');

  // Run all security checks
  checkGitignoreCompliance();
  checkConfigSeparation();
  performSecurityScan();

  // Generate final security report
  const isSecure = generateSecurityReport();

  console.log('\n🔒 SECURITY AUDIT COMPLETE!');
  console.log('===========================');

  if (isSecure) {
    console.log('🏆 SECURITY VALIDATION PASSED - SAFE FOR FINANCIAL OPERATIONS!');
    process.exit(0);
  } else {
    console.log('🚨 CRITICAL SECURITY ISSUES - IMMEDIATE ACTION REQUIRED!');
    process.exit(1);
  }
}

main().catch(console.error);
