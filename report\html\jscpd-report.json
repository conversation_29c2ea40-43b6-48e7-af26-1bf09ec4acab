{"statistics": {"detectionDate": "2025-05-25T04:38:05.325Z", "formats": {"typescript": {"sources": {"src/services/payment/routing/rules/CommonRoutingRules.ts": {"lines": 275, "tokens": 1825, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment/fees/strategies/CommonFeeStrategies.ts": {"lines": 305, "tokens": 1828, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/suites/TestSuiteBuilders.ts": {"lines": 505, "tokens": 3770, "sources": 1, "clones": 14, "duplicatedLines": 276, "duplicatedTokens": 1970, "percentage": 54.65, "percentageTokens": 52.25, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/runners/TestRunners.ts": {"lines": 507, "tokens": 3801, "sources": 1, "clones": 14, "duplicatedLines": 220, "duplicatedTokens": 1638, "percentage": 43.39, "percentageTokens": 43.09, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/generators/DataGenerators.ts": {"lines": 434, "tokens": 3507, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/factories/MockFactories.ts": {"lines": 449, "tokens": 3594, "sources": 1, "clones": 2, "duplicatedLines": 25, "duplicatedTokens": 190, "percentage": 5.57, "percentageTokens": 5.29, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/core/TestTypes.ts": {"lines": 402, "tokens": 2925, "sources": 1, "clones": 1, "duplicatedLines": 20, "duplicatedTokens": 125, "percentage": 4.98, "percentageTokens": 4.27, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/assertions/CustomAssertions.ts": {"lines": 427, "tokens": 3212, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/shared/modules/utils/responseUtils.ts": {"lines": 40, "tokens": 297, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/shared/modules/utils/asyncHandler.ts": {"lines": 19, "tokens": 144, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/shared/modules/types/common.ts": {"lines": 35, "tokens": 156, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/shared/modules/services/BaseService.ts": {"lines": 57, "tokens": 288, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/shared/modules/controllers/CrudController.ts": {"lines": 101, "tokens": 940, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/shared/modules/controllers/BaseController.ts": {"lines": 54, "tokens": 422, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/strategies/BinanceTRC20VerificationStrategy.ts": {"lines": 272, "tokens": 1735, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/processors/VerificationPreProcessor.ts": {"lines": 22, "tokens": 62, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/processors/VerificationPostProcessor.ts": {"lines": 25, "tokens": 98, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/processors/NotificationPostProcessor.ts": {"lines": 91, "tokens": 651, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/processors/LoggingPreProcessor.ts": {"lines": 40, "tokens": 223, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/policy/VerificationPolicyManager.ts": {"lines": 127, "tokens": 714, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/policy/VerificationPolicy.ts": {"lines": 179, "tokens": 771, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/reporting/scheduling/ReportScheduler.ts": {"lines": 360, "tokens": 2655, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/reporting/generators/TransactionReportGenerator.ts": {"lines": 297, "tokens": 2396, "sources": 1, "clones": 1, "duplicatedLines": 6, "duplicatedTokens": 82, "percentage": 2.02, "percentageTokens": 3.42, "newDuplicatedLines": 0, "newClones": 0}, "src/services/reporting/export/CSVExporter.ts": {"lines": 299, "tokens": 2247, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/reporting/core/ReportTypes.ts": {"lines": 285, "tokens": 1499, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/reporting/core/ReportService.ts": {"lines": 313, "tokens": 2268, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment/routing/PaymentRouter.ts": {"lines": 166, "tokens": 1077, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment/gateways/BinanceGateway.ts": {"lines": 309, "tokens": 2023, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment/fees/FeeCalculator.ts": {"lines": 165, "tokens": 973, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/identity-verification/utils/BlockchainUtils.ts": {"lines": 187, "tokens": 1127, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/identity-verification/methods/EthereumSignatureVerification.ts": {"lines": 193, "tokens": 1250, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/identity-verification/core/IdentityVerificationTypes.ts": {"lines": 168, "tokens": 807, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/identity-verification/core/IdentityVerificationService.ts": {"lines": 256, "tokens": 1846, "sources": 1, "clones": 2, "duplicatedLines": 14, "duplicatedTokens": 158, "percentage": 5.47, "percentageTokens": 8.56, "newDuplicatedLines": 0, "newClones": 0}, "src/services/identity-verification/core/IdentityVerificationError.ts": {"lines": 146, "tokens": 737, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fraud-detection/rules/RiskRuleEngine.ts": {"lines": 392, "tokens": 2811, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fraud-detection/detectors/VelocityRiskDetector.ts": {"lines": 382, "tokens": 2815, "sources": 1, "clones": 4, "duplicatedLines": 52, "duplicatedTokens": 494, "percentage": 13.61, "percentageTokens": 17.55, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fraud-detection/detectors/AmountRiskDetector.ts": {"lines": 301, "tokens": 2218, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fraud-detection/core/FraudDetectionTypes.ts": {"lines": 348, "tokens": 1592, "sources": 1, "clones": 2, "duplicatedLines": 83, "duplicatedTokens": 268, "percentage": 23.85, "percentageTokens": 16.83, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fraud-detection/core/FraudDetectionService.ts": {"lines": 458, "tokens": 3400, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/identity-verification/types/IdentityVerificationControllerTypes.ts": {"lines": 400, "tokens": 1902, "sources": 1, "clones": 10, "duplicatedLines": 361, "duplicatedTokens": 1742, "percentage": 90.25, "percentageTokens": 91.59, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/identity-verification/services/IdentityVerificationValidationService.ts": {"lines": 557, "tokens": 4975, "sources": 1, "clones": 29, "duplicatedLines": 456, "duplicatedTokens": 3880, "percentage": 81.87, "percentageTokens": 77.99, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/identity-verification/services/IdentityVerificationAuthService.ts": {"lines": 355, "tokens": 2434, "sources": 1, "clones": 8, "duplicatedLines": 257, "duplicatedTokens": 1549, "percentage": 72.39, "percentageTokens": 63.64, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/identity-verification/mappers/IdentityVerificationResponseMapper.ts": {"lines": 368, "tokens": 2479, "sources": 1, "clones": 12, "duplicatedLines": 509, "duplicatedTokens": 3492, "percentage": 138.32, "percentageTokens": 140.86, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/validators/TransactionRiskValidator.ts": {"lines": 80, "tokens": 687, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/validators/FraudConfigValidator.ts": {"lines": 164, "tokens": 1694, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/validators/BaseValidator.ts": {"lines": 176, "tokens": 1328, "sources": 1, "clones": 2, "duplicatedLines": 20, "duplicatedTokens": 205, "percentage": 11.36, "percentageTokens": 15.44, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/types/FraudDetectionControllerTypes.ts": {"lines": 381, "tokens": 1982, "sources": 1, "clones": 4, "duplicatedLines": 139, "duplicatedTokens": 665, "percentage": 36.48, "percentageTokens": 33.55, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/services/FraudDetectionBusinessService.ts": {"lines": 485, "tokens": 4012, "sources": 1, "clones": 3, "duplicatedLines": 43, "duplicatedTokens": 366, "percentage": 8.87, "percentageTokens": 9.12, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/services/FraudDetectionAuthService.ts": {"lines": 372, "tokens": 2539, "sources": 1, "clones": 12, "duplicatedLines": 375, "duplicatedTokens": 2265, "percentage": 100.81, "percentageTokens": 89.21, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/mappers/FraudDetectionResponseMapper.ts": {"lines": 357, "tokens": 2343, "sources": 1, "clones": 4, "duplicatedLines": 158, "duplicatedTokens": 1060, "percentage": 44.26, "percentageTokens": 45.24, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/alert-aggregation/types/AlertAggregationTypes.ts": {"lines": 327, "tokens": 1715, "sources": 1, "clones": 6, "duplicatedLines": 210, "duplicatedTokens": 975, "percentage": 64.22, "percentageTokens": 56.85, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/alert-aggregation/services/ValidationService.ts": {"lines": 399, "tokens": 3747, "sources": 1, "clones": 6, "duplicatedLines": 110, "duplicatedTokens": 833, "percentage": 27.57, "percentageTokens": 22.23, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/alert-aggregation/services/AuthorizationService.ts": {"lines": 302, "tokens": 1960, "sources": 1, "clones": 8, "duplicatedLines": 253, "duplicatedTokens": 1567, "percentage": 83.77, "percentageTokens": 79.95, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/alert-aggregation/services/AlertAggregationBusinessService.ts": {"lines": 403, "tokens": 3176, "sources": 1, "clones": 5, "duplicatedLines": 101, "duplicatedTokens": 906, "percentage": 25.06, "percentageTokens": 28.53, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/alert-aggregation/mappers/ResponseMapper.ts": {"lines": 311, "tokens": 2047, "sources": 1, "clones": 3, "duplicatedLines": 190, "duplicatedTokens": 1283, "percentage": 61.09, "percentageTokens": 62.68, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/admin/types/AdminControllerTypes.ts": {"lines": 407, "tokens": 2009, "sources": 1, "clones": 4, "duplicatedLines": 148, "duplicatedTokens": 700, "percentage": 36.36, "percentageTokens": 34.84, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/admin/services/AdminValidationService.ts": {"lines": 426, "tokens": 4175, "sources": 1, "clones": 19, "duplicatedLines": 256, "duplicatedTokens": 2604, "percentage": 60.09, "percentageTokens": 62.37, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/admin/services/AdminBusinessService.ts": {"lines": 456, "tokens": 3514, "sources": 1, "clones": 3, "duplicatedLines": 50, "duplicatedTokens": 352, "percentage": 10.96, "percentageTokens": 10.02, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/admin/services/AdminAuthorizationService.ts": {"lines": 389, "tokens": 2626, "sources": 1, "clones": 6, "duplicatedLines": 171, "duplicatedTokens": 1103, "percentage": 43.96, "percentageTokens": 42, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/admin/mappers/AdminResponseMapper.ts": {"lines": 334, "tokens": 2245, "sources": 1, "clones": 3, "duplicatedLines": 126, "duplicatedTokens": 893, "percentage": 37.72, "percentageTokens": 39.78, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/validation/RequestValidator.ts": {"lines": 96, "tokens": 664, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/response/ResponseFactory.ts": {"lines": 195, "tokens": 998, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/errors/ServiceError.ts": {"lines": 130, "tokens": 796, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/errors/index.ts": {"lines": 5, "tokens": 27, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/errors/ErrorFactory.ts": {"lines": 425, "tokens": 2585, "sources": 1, "clones": 2, "duplicatedLines": 26, "duplicatedTokens": 212, "percentage": 6.12, "percentageTokens": 8.2, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/errors/AppError.ts": {"lines": 308, "tokens": 1999, "sources": 1, "clones": 6, "duplicatedLines": 66, "duplicatedTokens": 522, "percentage": 21.43, "percentageTokens": 26.11, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/TestUtility.ts": {"lines": 820, "tokens": 5835, "sources": 1, "clones": 23, "duplicatedLines": 337, "duplicatedTokens": 2391, "percentage": 41.1, "percentageTokens": 40.98, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/utils/index.ts": {"lines": 210, "tokens": 1436, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/suites/RouteTestSuite.ts": {"lines": 217, "tokens": 1447, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/runners/RouteTestRunner.ts": {"lines": 76, "tokens": 420, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/mocks/prisma.mock.ts": {"lines": 41, "tokens": 225, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/helpers/RouteTestHelper.ts": {"lines": 198, "tokens": 1193, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/shared/modules/index.ts": {"lines": 9, "tokens": 37, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/websocket/verificationWebSocketService.ts": {"lines": 258, "tokens": 1879, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/VerificationService.ts": {"lines": 295, "tokens": 2176, "sources": 1, "clones": 2, "duplicatedLines": 30, "duplicatedTokens": 286, "percentage": 10.17, "percentageTokens": 13.14, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/VerificationChain.ts": {"lines": 332, "tokens": 2154, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/unified-verification.service.ts": {"lines": 759, "tokens": 4926, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/blockchain-verification.service.ts": {"lines": 263, "tokens": 1691, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification/binance-verification.service.ts": {"lines": 222, "tokens": 1392, "sources": 1, "clones": 2, "duplicatedLines": 72, "duplicatedTokens": 504, "percentage": 32.43, "percentageTokens": 36.21, "newDuplicatedLines": 0, "newClones": 0}, "src/services/validation/paymentMethodValidator.ts": {"lines": 223, "tokens": 1667, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/system/SystemInitializer.ts": {"lines": 314, "tokens": 2146, "sources": 1, "clones": 2, "duplicatedLines": 36, "duplicatedTokens": 400, "percentage": 11.46, "percentageTokens": 18.64, "newDuplicatedLines": 0, "newClones": 0}, "src/services/system/OperationalModeService.ts": {"lines": 414, "tokens": 2527, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/reporting/index.ts": {"lines": 20, "tokens": 93, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/rbac/RBACInitializer.ts": {"lines": 323, "tokens": 2177, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment/EnhancedPaymentService.ts": {"lines": 269, "tokens": 1945, "sources": 1, "clones": 2, "duplicatedLines": 26, "duplicatedTokens": 242, "percentage": 9.67, "percentageTokens": 12.44, "newDuplicatedLines": 0, "newClones": 0}, "src/services/optimization/verification-optimization.service.ts": {"lines": 446, "tokens": 2582, "sources": 1, "clones": 1, "duplicatedLines": 7, "duplicatedTokens": 77, "percentage": 1.57, "percentageTokens": 2.98, "newDuplicatedLines": 0, "newClones": 0}, "src/services/notification/alert-notification.service.ts": {"lines": 400, "tokens": 2781, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/monitoring/verification-monitoring.service.ts": {"lines": 283, "tokens": 2076, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/monitoring/verification-alert.service.ts": {"lines": 418, "tokens": 3422, "sources": 1, "clones": 1, "duplicatedLines": 7, "duplicatedTokens": 77, "percentage": 1.67, "percentageTokens": 2.25, "newDuplicatedLines": 0, "newClones": 0}, "src/services/monitoring/payment-monitoring.service.ts": {"lines": 496, "tokens": 3386, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/identity-verification/index.ts": {"lines": 23, "tokens": 109, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/identity-verification/blockchain-identity.service.ts": {"lines": 270, "tokens": 1800, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fraud-detection/index.ts": {"lines": 24, "tokens": 108, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/cache/analytics-cache.service.ts": {"lines": 233, "tokens": 1564, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/blockchain/blockchain-api.service.ts": {"lines": 390, "tokens": 2253, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/blockchain/binance-api.service.ts": {"lines": 701, "tokens": 4152, "sources": 1, "clones": 2, "duplicatedLines": 34, "duplicatedTokens": 246, "percentage": 4.85, "percentageTokens": 5.92, "newDuplicatedLines": 0, "newClones": 0}, "src/services/base/BaseService.ts": {"lines": 145, "tokens": 697, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/analytics/payment-analytics.service.ts": {"lines": 631, "tokens": 4426, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/analytics/ApiAnalyticsService.ts": {"lines": 350, "tokens": 2549, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/v1/payment-method.routes.ts": {"lines": 15, "tokens": 204, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/v1/merchant.routes.ts": {"lines": 17, "tokens": 228, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/v1/index.ts": {"lines": 22, "tokens": 136, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/webhook/webhook.module.ts": {"lines": 216, "tokens": 1639, "sources": 1, "clones": 6, "duplicatedLines": 66, "duplicatedTokens": 613, "percentage": 30.56, "percentageTokens": 37.4, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/users/users.module.ts": {"lines": 48, "tokens": 312, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/user/user.module.ts": {"lines": 565, "tokens": 4102, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/transactions/transactions.module.ts": {"lines": 48, "tokens": 312, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/payments/payments.module.ts": {"lines": 53, "tokens": 368, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/payment/payment.module.ts": {"lines": 249, "tokens": 1909, "sources": 1, "clones": 4, "duplicatedLines": 50, "duplicatedTokens": 423, "percentage": 20.08, "percentageTokens": 22.16, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/merchants/merchants.module.ts": {"lines": 48, "tokens": 312, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/merchant/merchant.module.ts": {"lines": 463, "tokens": 3467, "sources": 1, "clones": 1, "duplicatedLines": 5, "duplicatedTokens": 80, "percentage": 1.08, "percentageTokens": 2.31, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/example/example.module.ts": {"lines": 493, "tokens": 4032, "sources": 1, "clones": 5, "duplicatedLines": 47, "duplicatedTokens": 506, "percentage": 9.53, "percentageTokens": 12.55, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/auth/auth.module.ts": {"lines": 46, "tokens": 280, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/shared/BaseResponseMapper.ts": {"lines": 285, "tokens": 1949, "sources": 1, "clones": 2, "duplicatedLines": 35, "duplicatedTokens": 256, "percentage": 12.28, "percentageTokens": 13.13, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/optimization/verification-optimization.controller.ts": {"lines": 128, "tokens": 831, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/monitoring/verification-monitoring.controller.ts": {"lines": 247, "tokens": 2091, "sources": 1, "clones": 3, "duplicatedLines": 60, "duplicatedTokens": 688, "percentage": 24.29, "percentageTokens": 32.9, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/merchant/MerchantController.ts": {"lines": 257, "tokens": 1677, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 196, "percentage": 9.34, "percentageTokens": 11.69, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/identity-verification/index.ts": {"lines": 20, "tokens": 93, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/identity-verification/IdentityVerificationController.ts": {"lines": 373, "tokens": 2571, "sources": 1, "clones": 12, "duplicatedLines": 168, "duplicatedTokens": 1392, "percentage": 45.04, "percentageTokens": 54.14, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/index.ts": {"lines": 21, "tokens": 106, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fraud-detection/FraudDetectionController.ts": {"lines": 263, "tokens": 2032, "sources": 1, "clones": 2, "duplicatedLines": 20, "duplicatedTokens": 168, "percentage": 7.6, "percentageTokens": 8.27, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/base/CrudController.ts": {"lines": 228, "tokens": 1369, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/base/BaseController.ts": {"lines": 217, "tokens": 1308, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/alert-aggregation/index.ts": {"lines": 21, "tokens": 106, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/alert-aggregation/AlertAggregationController.ts": {"lines": 293, "tokens": 2211, "sources": 1, "clones": 11, "duplicatedLines": 116, "duplicatedTokens": 989, "percentage": 39.59, "percentageTokens": 44.73, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/admin/index.ts": {"lines": 21, "tokens": 106, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/admin/AdminController.ts": {"lines": 312, "tokens": 2388, "sources": 1, "clones": 7, "duplicatedLines": 84, "duplicatedTokens": 671, "percentage": 26.92, "percentageTokens": 28.1, "newDuplicatedLines": 0, "newClones": 0}, "src/config/verification/PredefinedVerificationPolicies.ts": {"lines": 99, "tokens": 474, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/security/production.security.config.ts": {"lines": 310, "tokens": 1611, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/rbac/RoleTemplates.ts": {"lines": 226, "tokens": 1159, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/rbac/PermissionGroups.ts": {"lines": 294, "tokens": 1151, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/environment/production.config.ts": {"lines": 129, "tokens": 846, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/database/production.config.ts": {"lines": 147, "tokens": 582, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/api/production.api.config.ts": {"lines": 169, "tokens": 690, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "tests/setup/jest.setup.ts": {"lines": 312, "tokens": 2734, "sources": 1, "clones": 8, "duplicatedLines": 62, "duplicatedTokens": 592, "percentage": 19.87, "percentageTokens": 21.65, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/websocket-monitor.ts": {"lines": 276, "tokens": 2142, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/verification-utils.ts": {"lines": 326, "tokens": 2540, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/verification-error-handler.ts": {"lines": 169, "tokens": 1115, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/validation.ts": {"lines": 188, "tokens": 977, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/UtilityRegistry.ts": {"lines": 53, "tokens": 295, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/types.ts": {"lines": 78, "tokens": 569, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/test-db-connection.ts": {"lines": 61, "tokens": 498, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 237, "percentage": 39.34, "percentageTokens": 47.59, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/test-connection.ts": {"lines": 72, "tokens": 594, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 237, "percentage": 33.33, "percentageTokens": 39.9, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/string.ts": {"lines": 116, "tokens": 727, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/storage-path.ts": {"lines": 119, "tokens": 674, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/service-config.ts": {"lines": 154, "tokens": 1125, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/secrets-manager.ts": {"lines": 136, "tokens": 1665, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/response-formatter.ts": {"lines": 174, "tokens": 966, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/repositoryUtils.ts": {"lines": 99, "tokens": 730, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/object.ts": {"lines": 124, "tokens": 911, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/monitoring.ts": {"lines": 199, "tokens": 1498, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/migration-manager.ts": {"lines": 258, "tokens": 1764, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/memory-store.ts": {"lines": 183, "tokens": 1259, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/logger.ts": {"lines": 41, "tokens": 279, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/log-rotation.ts": {"lines": 131, "tokens": 934, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/jwt.utils.ts": {"lines": 144, "tokens": 1125, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/jwt.ts": {"lines": 138, "tokens": 1036, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/index.ts": {"lines": 11, "tokens": 57, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/health-monitor.ts": {"lines": 255, "tokens": 1764, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/formatting.ts": {"lines": 139, "tokens": 782, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/feature-flags.ts": {"lines": 131, "tokens": 672, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/errorHandling.ts": {"lines": 28, "tokens": 142, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/error-handler.ts": {"lines": 203, "tokens": 1366, "sources": 1, "clones": 2, "duplicatedLines": 70, "duplicatedTokens": 556, "percentage": 34.48, "percentageTokens": 40.7, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/environment-validator.ts": {"lines": 133, "tokens": 539, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/encryption.ts": {"lines": 142, "tokens": 1071, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/domain.ts": {"lines": 66, "tokens": 316, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/db-optimization.ts": {"lines": 101, "tokens": 519, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/date.ts": {"lines": 35, "tokens": 546, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/database-verifier.ts": {"lines": 374, "tokens": 2651, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/database-initializer.ts": {"lines": 417, "tokens": 2604, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/csrf.ts": {"lines": 127, "tokens": 838, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/CryptoUtils.ts": {"lines": 311, "tokens": 1678, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/cookie.ts": {"lines": 163, "tokens": 899, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/controllerUtils.ts": {"lines": 121, "tokens": 753, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/controller.utils.ts": {"lines": 230, "tokens": 1491, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/controller-utils.ts": {"lines": 200, "tokens": 1312, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/common.ts": {"lines": 100, "tokens": 571, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/cache.ts": {"lines": 493, "tokens": 3628, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/cache-manager.ts": {"lines": 82, "tokens": 445, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/asyncHandler.ts": {"lines": 11, "tokens": 119, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/array.ts": {"lines": 138, "tokens": 779, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/appError.ts": {"lines": 4, "tokens": 21, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/app-error.ts": {"lines": 61, "tokens": 636, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/utils/alerting.ts": {"lines": 311, "tokens": 1826, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/verification.ts": {"lines": 59, "tokens": 296, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/token-payload.ts": {"lines": 18, "tokens": 89, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/prisma.d.ts": {"lines": 73, "tokens": 629, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/paymentMethodTypes.ts": {"lines": 118, "tokens": 616, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/index.ts": {"lines": 336, "tokens": 2247, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/global.d.ts": {"lines": 10, "tokens": 73, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/express.ts": {"lines": 54, "tokens": 576, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/express.d.ts": {"lines": 20, "tokens": 103, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/database.ts": {"lines": 111, "tokens": 832, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/blockchain.ts": {"lines": 52, "tokens": 284, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/binanceTypes.ts": {"lines": 117, "tokens": 270, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/types/alert.types.ts": {"lines": 230, "tokens": 1216, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/setup.ts": {"lines": 41, "tokens": 255, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/security-test.ts": {"lines": 198, "tokens": 1488, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/run-tests.ts": {"lines": 162, "tokens": 1182, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/route-tests.ts": {"lines": 27, "tokens": 173, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/tests/load-test.ts": {"lines": 172, "tokens": 1868, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/webhook.service.ts": {"lines": 321, "tokens": 2205, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification.service.ts": {"lines": 509, "tokens": 3574, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/verification-method.service.ts": {"lines": 500, "tokens": 3841, "sources": 1, "clones": 3, "duplicatedLines": 36, "duplicatedTokens": 264, "percentage": 7.2, "percentageTokens": 6.87, "newDuplicatedLines": 0, "newClones": 0}, "src/services/user.service.ts": {"lines": 273, "tokens": 1870, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/two-factor-auth.service.ts": {"lines": 287, "tokens": 1977, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 246, "percentage": 8.36, "percentageTokens": 12.44, "newDuplicatedLines": 0, "newClones": 0}, "src/services/transaction.service.ts": {"lines": 225, "tokens": 1584, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/telegram.service.ts": {"lines": 344, "tokens": 2188, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/system.service.ts": {"lines": 60, "tokens": 426, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/subscription.service.ts": {"lines": 173, "tokens": 1349, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/sms.service.ts": {"lines": 213, "tokens": 1493, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/report-optimization.service.ts": {"lines": 498, "tokens": 4182, "sources": 1, "clones": 15, "duplicatedLines": 130, "duplicatedTokens": 1410, "percentage": 26.1, "percentageTokens": 33.72, "newDuplicatedLines": 0, "newClones": 0}, "src/services/report-monitoring.service.ts": {"lines": 414, "tokens": 3261, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/rbac.service.ts": {"lines": 224, "tokens": 1725, "sources": 1, "clones": 2, "duplicatedLines": 26, "duplicatedTokens": 240, "percentage": 11.61, "percentageTokens": 13.91, "newDuplicatedLines": 0, "newClones": 0}, "src/services/push-notification.service.ts": {"lines": 327, "tokens": 2094, "sources": 1, "clones": 2, "duplicatedLines": 50, "duplicatedTokens": 332, "percentage": 15.29, "percentageTokens": 15.85, "newDuplicatedLines": 0, "newClones": 0}, "src/services/paymentVerificationService.ts": {"lines": 190, "tokens": 1233, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment.service.ts": {"lines": 159, "tokens": 1385, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment-verification.service.ts": {"lines": 352, "tokens": 2131, "sources": 1, "clones": 2, "duplicatedLines": 18, "duplicatedTokens": 170, "percentage": 5.11, "percentageTokens": 7.98, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment-routing.service.ts": {"lines": 617, "tokens": 3662, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment-recommendation.service.ts": {"lines": 677, "tokens": 4729, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment-page.service.ts": {"lines": 449, "tokens": 3627, "sources": 1, "clones": 6, "duplicatedLines": 76, "duplicatedTokens": 576, "percentage": 16.93, "percentageTokens": 15.88, "newDuplicatedLines": 0, "newClones": 0}, "src/services/payment-method.service.ts": {"lines": 357, "tokens": 2192, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/notification.service.ts": {"lines": 763, "tokens": 4949, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/notification-events.service.ts": {"lines": 126, "tokens": 719, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/multi-factor-auth.service.ts": {"lines": 237, "tokens": 1627, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/monitoring.service.ts": {"lines": 731, "tokens": 5480, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/merchant.service.ts": {"lines": 315, "tokens": 2177, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/merchant-self-service.service.ts": {"lines": 464, "tokens": 3020, "sources": 1, "clones": 2, "duplicatedLines": 22, "duplicatedTokens": 182, "percentage": 4.74, "percentageTokens": 6.03, "newDuplicatedLines": 0, "newClones": 0}, "src/services/merchant-segmentation.service.ts": {"lines": 583, "tokens": 4096, "sources": 1, "clones": 2, "duplicatedLines": 22, "duplicatedTokens": 174, "percentage": 3.77, "percentageTokens": 4.25, "newDuplicatedLines": 0, "newClones": 0}, "src/services/merchant-relationship.service.ts": {"lines": 613, "tokens": 4132, "sources": 1, "clones": 4, "duplicatedLines": 44, "duplicatedTokens": 348, "percentage": 7.18, "percentageTokens": 8.42, "newDuplicatedLines": 0, "newClones": 0}, "src/services/merchant-events.service.ts": {"lines": 105, "tokens": 614, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/log.service.ts": {"lines": 33, "tokens": 287, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/index.ts": {"lines": 15, "tokens": 96, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fraud-detection.service.ts": {"lines": 845, "tokens": 5255, "sources": 1, "clones": 2, "duplicatedLines": 83, "duplicatedTokens": 268, "percentage": 9.82, "percentageTokens": 5.1, "newDuplicatedLines": 0, "newClones": 0}, "src/services/fee-management.service.ts": {"lines": 626, "tokens": 3649, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/example.service.ts": {"lines": 251, "tokens": 1641, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/enhanced-subscription.service.ts": {"lines": 297, "tokens": 1906, "sources": 1, "clones": 2, "duplicatedLines": 42, "duplicatedTokens": 326, "percentage": 14.14, "percentageTokens": 17.1, "newDuplicatedLines": 0, "newClones": 0}, "src/services/enhanced-risk-engine.service.ts": {"lines": 452, "tokens": 2102, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/enhanced-risk-engine.service.implementation.ts": {"lines": 535, "tokens": 4327, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/email.service.ts": {"lines": 238, "tokens": 1653, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/blockchain-verification.service.ts": {"lines": 427, "tokens": 2695, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/blockchain-api.service.ts": {"lines": 409, "tokens": 2669, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/binanceApiService.ts": {"lines": 293, "tokens": 2155, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/binance.service.ts": {"lines": 139, "tokens": 722, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/binance-pay.service.ts": {"lines": 191, "tokens": 1647, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/binance-api.service.ts": {"lines": 300, "tokens": 2205, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/base.service.ts": {"lines": 379, "tokens": 2056, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/auth.service.ts": {"lines": 298, "tokens": 2291, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/audit.service.ts": {"lines": 151, "tokens": 1043, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/alert.service.ts": {"lines": 558, "tokens": 4055, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/alert-analytics.service.ts": {"lines": 501, "tokens": 3436, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/services/alert-aggregation.service.ts": {"lines": 733, "tokens": 5573, "sources": 1, "clones": 4, "duplicatedLines": 58, "duplicatedTokens": 388, "percentage": 7.91, "percentageTokens": 6.96, "newDuplicatedLines": 0, "newClones": 0}, "src/services/advanced-report.service.ts": {"lines": 944, "tokens": 7333, "sources": 1, "clones": 10, "duplicatedLines": 132, "duplicatedTokens": 1246, "percentage": 13.98, "percentageTokens": 16.99, "newDuplicatedLines": 0, "newClones": 0}, "src/security/security-audit.ts": {"lines": 428, "tokens": 2785, "sources": 1, "clones": 2, "duplicatedLines": 18, "duplicatedTokens": 160, "percentage": 4.21, "percentageTokens": 5.75, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/websocket-monitoring.routes.ts": {"lines": 98, "tokens": 685, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/webhook.routes.ts": {"lines": 158, "tokens": 1253, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/version.routes.ts": {"lines": 87, "tokens": 438, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/verify.routes.ts": {"lines": 48, "tokens": 329, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/verification.routes.ts": {"lines": 24, "tokens": 206, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/verification-policy.routes.ts": {"lines": 67, "tokens": 463, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/verification-optimization.routes.ts": {"lines": 50, "tokens": 208, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/verification-monitoring.routes.ts": {"lines": 50, "tokens": 208, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/verification-method.routes.ts": {"lines": 36, "tokens": 298, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/user.routes.ts": {"lines": 119, "tokens": 712, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/transaction.routes.ts": {"lines": 38, "tokens": 381, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/telegram.routes.ts": {"lines": 19, "tokens": 186, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/system.routes.ts": {"lines": 53, "tokens": 343, "sources": 1, "clones": 1, "duplicatedLines": 9, "duplicatedTokens": 91, "percentage": 16.98, "percentageTokens": 26.53, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/subscription.routes.ts": {"lines": 122, "tokens": 656, "sources": 1, "clones": 1, "duplicatedLines": 9, "duplicatedTokens": 91, "percentage": 7.38, "percentageTokens": 13.87, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/sms.routes.ts": {"lines": 14, "tokens": 138, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/receipt.routes.ts": {"lines": 32, "tokens": 218, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/push-notification.routes.ts": {"lines": 20, "tokens": 202, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/performance.routes.ts": {"lines": 86, "tokens": 547, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/paymentVerificationRoutes.ts": {"lines": 46, "tokens": 229, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/payment.routes.ts": {"lines": 93, "tokens": 576, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/payment-verification.routes.ts": {"lines": 42, "tokens": 141, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/payment-routing.routes.ts": {"lines": 290, "tokens": 334, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/payment-recommendation.routes.ts": {"lines": 58, "tokens": 246, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/payment-page.routes.ts": {"lines": 39, "tokens": 334, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/payment-method.routes.ts": {"lines": 77, "tokens": 500, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/operational-mode.routes.ts": {"lines": 52, "tokens": 332, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/notification.routes.ts": {"lines": 29, "tokens": 276, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/multi-factor-auth.routes.ts": {"lines": 24, "tokens": 181, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/monitoring.routes.ts": {"lines": 79, "tokens": 226, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/monitoring-dashboard.routes.ts": {"lines": 227, "tokens": 1678, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/merchant.routes.ts": {"lines": 62, "tokens": 469, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/merchant-self-service.routes.ts": {"lines": 127, "tokens": 409, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/merchant-segmentation.routes.ts": {"lines": 116, "tokens": 407, "sources": 1, "clones": 2, "duplicatedLines": 20, "duplicatedTokens": 198, "percentage": 17.24, "percentageTokens": 48.65, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/merchant-relationship.routes.ts": {"lines": 133, "tokens": 410, "sources": 1, "clones": 1, "duplicatedLines": 10, "duplicatedTokens": 99, "percentage": 7.52, "percentageTokens": 24.15, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/log.routes.ts": {"lines": 33, "tokens": 237, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/location.routes.ts": {"lines": 10, "tokens": 74, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/index.ts": {"lines": 45, "tokens": 331, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/identity-verification.routes.ts": {"lines": 108, "tokens": 711, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/health.routes.ts": {"lines": 191, "tokens": 1202, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/fraud-detection.routes.ts": {"lines": 82, "tokens": 309, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/fee-management.routes.ts": {"lines": 440, "tokens": 428, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/fee-management-test.routes.ts": {"lines": 9, "tokens": 70, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/example.routes.ts": {"lines": 42, "tokens": 141, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/enhanced-verification.routes.ts": {"lines": 66, "tokens": 421, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/enhanced-risk-engine.routes.ts": {"lines": 76, "tokens": 272, "sources": 1, "clones": 1, "duplicatedLines": 10, "duplicatedTokens": 99, "percentage": 13.16, "percentageTokens": 36.4, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/enhanced-payment.routes.ts": {"lines": 62, "tokens": 384, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/email.routes.ts": {"lines": 14, "tokens": 138, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/dashboard.routes.ts": {"lines": 33, "tokens": 305, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/cache.routes.ts": {"lines": 55, "tokens": 170, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/blockchain-verification.routes.ts": {"lines": 113, "tokens": 779, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/binance.routes.ts": {"lines": 22, "tokens": 213, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/binance-pay-webhook.routes.ts": {"lines": 9, "tokens": 60, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/auth.routes.ts": {"lines": 112, "tokens": 730, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/api-analytics.routes.ts": {"lines": 52, "tokens": 273, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/analytics.routes.ts": {"lines": 58, "tokens": 200, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/alerting.routes.ts": {"lines": 211, "tokens": 1448, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/alert.routes.ts": {"lines": 21, "tokens": 214, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/alert-notification.routes.ts": {"lines": 55, "tokens": 170, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/alert-analytics.routes.ts": {"lines": 22, "tokens": 245, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/alert-aggregation.routes.ts": {"lines": 37, "tokens": 248, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/advanced-report.routes.ts": {"lines": 34, "tokens": 299, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/routes/admin.routes.ts": {"lines": 137, "tokens": 981, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/modules/index.ts": {"lines": 13, "tokens": 53, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/models/role.model.ts": {"lines": 178, "tokens": 901, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/models/permission.model.ts": {"lines": 191, "tokens": 1062, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/versionMiddleware.ts": {"lines": 80, "tokens": 627, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/validation.middleware.ts": {"lines": 164, "tokens": 1622, "sources": 1, "clones": 2, "duplicatedLines": 34, "duplicatedTokens": 470, "percentage": 20.73, "percentageTokens": 28.98, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/security.middleware.ts": {"lines": 277, "tokens": 1752, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/request-id.middleware.ts": {"lines": 55, "tokens": 305, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/rbac.middleware.ts": {"lines": 178, "tokens": 1471, "sources": 1, "clones": 8, "duplicatedLines": 120, "duplicatedTokens": 820, "percentage": 67.42, "percentageTokens": 55.74, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/rate-limit.middleware.ts": {"lines": 161, "tokens": 1227, "sources": 1, "clones": 2, "duplicatedLines": 12, "duplicatedTokens": 140, "percentage": 7.45, "percentageTokens": 11.41, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/performance-monitor.middleware.ts": {"lines": 231, "tokens": 1800, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/operational-mode.middleware.ts": {"lines": 106, "tokens": 801, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/monitoring.middleware.ts": {"lines": 64, "tokens": 471, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/merchantAccessMiddleware.ts": {"lines": 76, "tokens": 453, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/feature-flags.middleware.ts": {"lines": 103, "tokens": 766, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/errorHandler.ts": {"lines": 150, "tokens": 1067, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/errorHandler.middleware.ts": {"lines": 122, "tokens": 840, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/error.middleware.ts": {"lines": 282, "tokens": 2081, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/environment.middleware.ts": {"lines": 135, "tokens": 893, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/enhanced-rate-limit.middleware.ts": {"lines": 148, "tokens": 1190, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/enhanced-error.middleware.ts": {"lines": 180, "tokens": 1298, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/enhanced-auth.middleware.ts": {"lines": 257, "tokens": 2120, "sources": 1, "clones": 3, "duplicatedLines": 36, "duplicatedTokens": 301, "percentage": 14.01, "percentageTokens": 14.2, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/authorization.middleware.ts": {"lines": 125, "tokens": 956, "sources": 1, "clones": 2, "duplicatedLines": 18, "duplicatedTokens": 162, "percentage": 14.4, "percentageTokens": 16.95, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/auth.ts": {"lines": 165, "tokens": 1182, "sources": 1, "clones": 2, "duplicatedLines": 10, "duplicatedTokens": 158, "percentage": 6.06, "percentageTokens": 13.37, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/auth.middleware.ts": {"lines": 235, "tokens": 1842, "sources": 1, "clones": 6, "duplicatedLines": 48, "duplicatedTokens": 486, "percentage": 20.43, "percentageTokens": 26.38, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/audit.middleware.ts": {"lines": 141, "tokens": 1076, "sources": 1, "clones": 1, "duplicatedLines": 18, "duplicatedTokens": 99, "percentage": 12.77, "percentageTokens": 9.2, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/apiResponseMiddleware.ts": {"lines": 401, "tokens": 2197, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/middlewares/api-response.middleware.ts": {"lines": 46, "tokens": 326, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/redis-manager.ts": {"lines": 316, "tokens": 2327, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/redis-client.ts": {"lines": 61, "tokens": 260, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/prisma.ts": {"lines": 235, "tokens": 1803, "sources": 1, "clones": 2, "duplicatedLines": 34, "duplicatedTokens": 324, "percentage": 14.47, "percentageTokens": 17.97, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/prisma-client.ts": {"lines": 181, "tokens": 1376, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/ModuleRegistry.ts": {"lines": 265, "tokens": 1345, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/mode-specific-db.ts": {"lines": 86, "tokens": 557, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/logger.ts": {"lines": 167, "tokens": 1312, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/EventBus.ts": {"lines": 129, "tokens": 817, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/DIContainer.ts": {"lines": 147, "tokens": 946, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/lib/database-init.ts": {"lines": 59, "tokens": 422, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/docs/api-documentation.ts": {"lines": 706, "tokens": 4854, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/version.controller.ts": {"lines": 217, "tokens": 1638, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/verification.controller.ts": {"lines": 423, "tokens": 2932, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/verification-policy.controller.ts": {"lines": 227, "tokens": 1741, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/verification-method.controller.ts": {"lines": 142, "tokens": 1126, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/transaction-analytics.controller.ts": {"lines": 77, "tokens": 841, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/telegram-webhook.controller.ts": {"lines": 308, "tokens": 2166, "sources": 1, "clones": 9, "duplicatedLines": 126, "duplicatedTokens": 1017, "percentage": 40.91, "percentageTokens": 46.95, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/system.controller.ts": {"lines": 150, "tokens": 1137, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/subscription.controller.ts": {"lines": 67, "tokens": 585, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/subscription-plans.controller.ts": {"lines": 133, "tokens": 1092, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/subscription-history.controller.ts": {"lines": 117, "tokens": 959, "sources": 1, "clones": 2, "duplicatedLines": 28, "duplicatedTokens": 212, "percentage": 23.93, "percentageTokens": 22.11, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/sms.controller.ts": {"lines": 191, "tokens": 1322, "sources": 1, "clones": 3, "duplicatedLines": 42, "duplicatedTokens": 339, "percentage": 21.99, "percentageTokens": 25.64, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/paymentVerificationController.ts": {"lines": 156, "tokens": 1058, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/payment.controller.ts": {"lines": 188, "tokens": 1430, "sources": 1, "clones": 2, "duplicatedLines": 18, "duplicatedTokens": 184, "percentage": 9.57, "percentageTokens": 12.87, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/payment-verification.controller.ts": {"lines": 261, "tokens": 2083, "sources": 1, "clones": 4, "duplicatedLines": 32, "duplicatedTokens": 416, "percentage": 12.26, "percentageTokens": 19.97, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/payment-routing.controller.ts": {"lines": 343, "tokens": 2634, "sources": 1, "clones": 1, "duplicatedLines": 10, "duplicatedTokens": 97, "percentage": 2.92, "percentageTokens": 3.68, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/payment-recommendation.controller.ts": {"lines": 264, "tokens": 2036, "sources": 1, "clones": 2, "duplicatedLines": 28, "duplicatedTokens": 184, "percentage": 10.61, "percentageTokens": 9.04, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/payment-page.controller.ts": {"lines": 196, "tokens": 1385, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/operational-mode.controller.ts": {"lines": 252, "tokens": 1839, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/multi-factor-auth.controller.ts": {"lines": 114, "tokens": 821, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 202, "percentage": 21.05, "percentageTokens": 24.6, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/monitoring.controller.ts": {"lines": 128, "tokens": 1145, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/merchant-subscription.controller.ts": {"lines": 132, "tokens": 1152, "sources": 1, "clones": 2, "duplicatedLines": 14, "duplicatedTokens": 158, "percentage": 10.61, "percentageTokens": 13.72, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/merchant-self-service.controller.ts": {"lines": 271, "tokens": 2075, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/merchant-segmentation.controller.ts": {"lines": 203, "tokens": 1548, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/merchant-relationship.controller.ts": {"lines": 302, "tokens": 2229, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/log.controller.ts": {"lines": 71, "tokens": 624, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/location.controller.ts": {"lines": 100, "tokens": 1562, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/health.controller.ts": {"lines": 302, "tokens": 2559, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/fee-management.controller.ts": {"lines": 430, "tokens": 3074, "sources": 1, "clones": 3, "duplicatedLines": 30, "duplicatedTokens": 269, "percentage": 6.98, "percentageTokens": 8.75, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/example.controller.ts": {"lines": 124, "tokens": 971, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/enhanced-verification.controller.ts": {"lines": 233, "tokens": 1777, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/enhanced-risk-engine.controller.ts": {"lines": 270, "tokens": 2393, "sources": 1, "clones": 1, "duplicatedLines": 6, "duplicatedTokens": 108, "percentage": 2.22, "percentageTokens": 4.51, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/enhanced-payment.controller.ts": {"lines": 216, "tokens": 1676, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/dashboard.controller.ts": {"lines": 232, "tokens": 1903, "sources": 1, "clones": 15, "duplicatedLines": 194, "duplicatedTokens": 2028, "percentage": 83.62, "percentageTokens": 106.57, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/dashboard-widget.controller.ts": {"lines": 315, "tokens": 2835, "sources": 1, "clones": 11, "duplicatedLines": 154, "duplicatedTokens": 1578, "percentage": 48.89, "percentageTokens": 55.66, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/cache.controller.ts": {"lines": 63, "tokens": 563, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/blockchain-verification.controller.ts": {"lines": 317, "tokens": 2805, "sources": 1, "clones": 10, "duplicatedLines": 140, "duplicatedTokens": 1212, "percentage": 44.16, "percentageTokens": 43.21, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/binance.controller.ts": {"lines": 105, "tokens": 861, "sources": 1, "clones": 2, "duplicatedLines": 24, "duplicatedTokens": 218, "percentage": 22.86, "percentageTokens": 25.32, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/binance-pay-webhook.controller.ts": {"lines": 127, "tokens": 1045, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/base.controller.ts": {"lines": 210, "tokens": 1468, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/auth.controller.ts": {"lines": 351, "tokens": 2710, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/analytics.controller.ts": {"lines": 190, "tokens": 1611, "sources": 1, "clones": 6, "duplicatedLines": 102, "duplicatedTokens": 980, "percentage": 53.68, "percentageTokens": 60.83, "newDuplicatedLines": 0, "newClones": 0}, "src/controllers/advanced-report.controller.ts": {"lines": 466, "tokens": 3796, "sources": 1, "clones": 6, "duplicatedLines": 60, "duplicatedTokens": 628, "percentage": 12.88, "percentageTokens": 16.54, "newDuplicatedLines": 0, "newClones": 0}, "src/config/index.ts": {"lines": 194, "tokens": 1851, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/environment.ts": {"lines": 71, "tokens": 491, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/env.config.ts": {"lines": 18, "tokens": 171, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/database.ts": {"lines": 32, "tokens": 184, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/database.config.ts": {"lines": 120, "tokens": 849, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/config/auth.ts": {"lines": 121, "tokens": 987, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "prisma/seeds/report-templates.ts": {"lines": 125, "tokens": 753, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "prisma/seeds/advanced-reporting-seed.ts": {"lines": 303, "tokens": 2286, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "src/index.ts": {"lines": 250, "tokens": 1359, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "prisma/seed.ts": {"lines": 126, "tokens": 958, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 86024, "tokens": 593553, "sources": 405, "clones": 237, "duplicatedLines": 4163, "duplicatedTokens": 31812, "percentage": 4.84, "percentageTokens": 5.36, "newDuplicatedLines": 0, "newClones": 0}}, "javascript": {"sources": {"report/html/js/prism.js": {"lines": 15, "tokens": 4580, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "tests/load/comprehensive-load-test.js": {"lines": 377, "tokens": 3057, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "scripts/verify-advanced-reporting.js": {"lines": 523, "tokens": 3795, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "scripts/test-new-code-duplication.js": {"lines": 116, "tokens": 1014, "sources": 1, "clones": 2, "duplicatedLines": 15, "duplicatedTokens": 258, "percentage": 12.93, "percentageTokens": 25.44, "newDuplicatedLines": 0, "newClones": 0}, "scripts/test-duplication.js": {"lines": 77, "tokens": 669, "sources": 1, "clones": 2, "duplicatedLines": 15, "duplicatedTokens": 258, "percentage": 19.48, "percentageTokens": 38.57, "newDuplicatedLines": 0, "newClones": 0}, "scripts/test-db.js": {"lines": 61, "tokens": 492, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "scripts/test-connection.js": {"lines": 100, "tokens": 743, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "scripts/setup-database.js": {"lines": 80, "tokens": 596, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "scripts/run-comprehensive-tests.js": {"lines": 597, "tokens": 4534, "sources": 1, "clones": 4, "duplicatedLines": 32, "duplicatedTokens": 348, "percentage": 5.36, "percentageTokens": 7.68, "newDuplicatedLines": 0, "newClones": 0}, "scripts/mass-fix-syntax.js": {"lines": 313, "tokens": 2070, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "scripts/generate-duplication-dashboard.js": {"lines": 356, "tokens": 1133, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "scripts/fix-duplication.js": {"lines": 205, "tokens": 1647, "sources": 1, "clones": 2, "duplicatedLines": 14, "duplicatedTokens": 174, "percentage": 6.83, "percentageTokens": 10.56, "newDuplicatedLines": 0, "newClones": 0}, "jest.config.js": {"lines": 129, "tokens": 717, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 2949, "tokens": 25047, "sources": 13, "clones": 5, "duplicatedLines": 38, "duplicatedTokens": 519, "percentage": 1.29, "percentageTokens": 2.07, "newDuplicatedLines": 0, "newClones": 0}}}, "total": {"lines": 88973, "tokens": 618600, "sources": 418, "clones": 242, "duplicatedLines": 4201, "duplicatedTokens": 32331, "percentage": 4.72, "percentageTokens": 5.23, "newDuplicatedLines": 0, "newClones": 0}}, "duplicates": [{"format": "typescript", "lines": 19, "fragment": ";\n\n    // Global setup\n    beforeAll(async () => {\n      if (config.setup) {\n        await config.setup();\n      }\n    });\n\n    // Global teardown\n    afterAll(async () => {\n      if (config.teardown) {\n        await config.teardown();\n      }\n    });\n\n    // Setup before each test\n    beforeEach(async () => {\n      service", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 96, "end": 114, "startLoc": {"line": 96, "column": 12, "position": 684}, "endLoc": {"line": 114, "column": 8, "position": 793}}, "secondFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 30, "end": 48, "startLoc": {"line": 30, "column": 15, "position": 180}, "endLoc": {"line": 48, "column": 11, "position": 289}}}, {"format": "typescript", "lines": 28, "fragment": "if (config.beforeEach) {\n        await config.beforeEach();\n      }\n    });\n\n    // Cleanup after each test\n    afterEach(async () => {\n      if (config.afterEach) {\n        await config.afterEach();\n      }\n    });\n\n    // Create individual test cases\n    Object.entries(tests).forEach(([method, testOptions]) => {\n      const testName = testOptions.description || `should test ${method}`;\n      const timeout = testOptions.timeout || config.timeout || 10000;\n\n      if (testOptions.skip) {\n        it.skip(testName, () => {});\n        return;\n      }\n\n      const testFn = testOptions.only ? it.only : it;\n\n      testFn(\n        testName,\n        async () => {\n          await testService", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 120, "end": 147, "startLoc": {"line": 120, "column": 7, "position": 828}, "endLoc": {"line": 147, "column": 12, "position": 1052}}, "secondFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 50, "end": 77, "startLoc": {"line": 50, "column": 7, "position": 302}, "endLoc": {"line": 77, "column": 15, "position": 526}}}, {"format": "typescript", "lines": 19, "fragment": ";\n\n    // Global setup\n    beforeAll(async () => {\n      if (config.setup) {\n        await config.setup();\n      }\n    });\n\n    // Global teardown\n    afterAll(async () => {\n      if (config.teardown) {\n        await config.teardown();\n      }\n    });\n\n    // Setup before each test\n    beforeEach(async () => {\n      repository", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 165, "end": 183, "startLoc": {"line": 165, "column": 4, "position": 1186}, "endLoc": {"line": 183, "column": 11, "position": 1295}}, "secondFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 30, "end": 48, "startLoc": {"line": 30, "column": 15, "position": 180}, "endLoc": {"line": 48, "column": 11, "position": 289}}}, {"format": "typescript", "lines": 30, "fragment": "();\n\n      if (config.beforeEach) {\n        await config.beforeEach();\n      }\n    });\n\n    // Cleanup after each test\n    afterEach(async () => {\n      if (config.afterEach) {\n        await config.afterEach();\n      }\n    });\n\n    // Create individual test cases\n    Object.entries(tests).forEach(([method, testOptions]) => {\n      const testName = testOptions.description || `should test ${method}`;\n      const timeout = testOptions.timeout || config.timeout || 10000;\n\n      if (testOptions.skip) {\n        it.skip(testName, () => {});\n        return;\n      }\n\n      const testFn = testOptions.only ? it.only : it;\n\n      testFn(\n        testName,\n        async () => {\n          await testRepository", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 183, "end": 212, "startLoc": {"line": 183, "column": 16, "position": 1302}, "endLoc": {"line": 212, "column": 15, "position": 1532}}, "secondFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 48, "end": 77, "startLoc": {"line": 48, "column": 16, "position": 296}, "endLoc": {"line": 77, "column": 15, "position": 526}}}, {"format": "typescript", "lines": 17, "fragment": "// Global setup\n    beforeAll(async () => {\n      if (config.setup) {\n        await config.setup();\n      }\n    });\n\n    // Global teardown\n    afterAll(async () => {\n      if (config.teardown) {\n        await config.teardown();\n      }\n    });\n\n    // Setup before each test\n    beforeEach(async () => {\n      if", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 229, "end": 245, "startLoc": {"line": 229, "column": 5, "position": 1639}, "endLoc": {"line": 245, "column": 3, "position": 1744}}, "secondFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 32, "end": 48, "startLoc": {"line": 32, "column": 5, "position": 184}, "endLoc": {"line": 48, "column": 11, "position": 289}}}, {"format": "typescript", "lines": 16, "fragment": "${name}`, () => {\n    // Global setup\n    beforeAll(async () => {\n      if (config.setup) {\n        await config.setup();\n      }\n    });\n\n    // Global teardown\n    afterAll(async () => {\n      if (config.teardown) {\n        await config.teardown();\n      }\n    });\n\n    // Create performance tests", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 307, "end": 322, "startLoc": {"line": 307, "column": 15, "position": 2123}, "endLoc": {"line": 322, "column": 28, "position": 2227}}, "secondFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 228, "end": 46, "startLoc": {"line": 228, "column": 15, "position": 1625}, "endLoc": {"line": 46, "column": 26, "position": 274}}}, {"format": "typescript", "lines": 16, "fragment": "${name}`, () => {\n    // Global setup\n    beforeAll(async () => {\n      if (config.setup) {\n        await config.setup();\n      }\n    });\n\n    // Global teardown\n    afterAll(async () => {\n      if (config.teardown) {\n        await config.teardown();\n      }\n    });\n\n    // Create API tests", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 381, "end": 396, "startLoc": {"line": 381, "column": 7, "position": 2756}, "endLoc": {"line": 396, "column": 20, "position": 2860}}, "secondFile": {"name": "src\\tests\\utils\\suites\\TestSuiteBuilders.ts", "start": 228, "end": 46, "startLoc": {"line": 228, "column": 15, "position": 1625}, "endLoc": {"line": 46, "column": 26, "position": 274}}}, {"format": "typescript", "lines": 10, "fragment": "// Handle expected errors\n    if (options.expectedError) {\n      if (typeof options.expectedError === 'function') {\n        expect(error).toEqual(expect.objectContaining(options.expectedError()));\n      } else {\n        expect(error).toEqual(options.expectedError);\n      }\n\n      return {\n        success", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 206, "end": 215, "startLoc": {"line": 206, "column": 5, "position": 1470}, "endLoc": {"line": 215, "column": 8, "position": 1552}}, "secondFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 103, "end": 110, "startLoc": {"line": 103, "column": 5, "position": 688}, "endLoc": {"line": 110, "column": 4, "position": 768}}}, {"format": "typescript", "lines": 16, "fragment": "// Validate result if validator provided\n    if (options.validateResult) {\n      await options.validateResult(result);\n    }\n\n    // Assert expected result\n    if (options.expectedResult !== undefined) {\n      if (typeof options.expectedResult === 'function') {\n        expect(result).toEqual(expect.objectContaining(options.expectedResult()));\n      } else {\n        expect(result).toEqual(options.expectedResult);\n      }\n    }\n\n    // Run cleanup hooks\n    if (options.repositoryCleanup", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 289, "end": 304, "startLoc": {"line": 289, "column": 5, "position": 2135}, "endLoc": {"line": 304, "column": 18, "position": 2256}}, "secondFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 169, "end": 184, "startLoc": {"line": 169, "column": 5, "position": 1205}, "endLoc": {"line": 184, "column": 15, "position": 1326}}}, {"format": "typescript", "lines": 18, "fragment": ");\n    }\n\n    if (options.cleanup) {\n      await options.cleanup();\n    }\n\n    if (options.afterEach) {\n      await options.afterEach();\n    }\n\n    const executionTime = Date.now() - startTime;\n\n    return {\n      success: true,\n      result,\n      executionTime,\n      metadata", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 305, "end": 322, "startLoc": {"line": 305, "column": 11, "position": 2269}, "endLoc": {"line": 322, "column": 9, "position": 2366}}, "secondFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 185, "end": 202, "startLoc": {"line": 185, "column": 8, "position": 1339}, "endLoc": {"line": 202, "column": 2, "position": 1436}}}, {"format": "typescript", "lines": 32, "fragment": ",\n    };\n  } catch (error) {\n    const executionTime = Date.now() - startTime;\n\n    // Handle expected errors\n    if (options.expectedError) {\n      if (typeof options.expectedError === 'function') {\n        expect(error).toEqual(expect.objectContaining(options.expectedError()));\n      } else {\n        expect(error).toEqual(options.expectedError);\n      }\n\n      return {\n        success: true,\n        error: error instanceof Error ? error : new Error(String(error)),\n        executionTime,\n      };\n    }\n\n    // Return test failure result\n    return {\n      success: false,\n      error: error instanceof Error ? error : new Error(String(error)),\n      executionTime,\n    };\n  }\n}\n\n/**\n * Test middleware\n */", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 325, "end": 356, "startLoc": {"line": 325, "column": 2, "position": 2389}, "endLoc": {"line": 356, "column": 4, "position": 2609}}, "secondFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 201, "end": 232, "startLoc": {"line": 201, "column": 14, "position": 1433}, "endLoc": {"line": 232, "column": 4, "position": 1653}}}, {"format": "typescript", "lines": 10, "fragment": "};\n  } catch (error) {\n    // Handle expected errors\n    if (options.expectedError) {\n      if (typeof options.expectedError === 'function') {\n        expect(error).toEqual(expect.objectContaining(options.expectedError()));\n      } else {\n        expect(error).toEqual(options.expectedError);\n      }\n      return { req: req as", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 422, "end": 431, "startLoc": {"line": 422, "column": 2, "position": 3126}, "endLoc": {"line": 431, "column": 3, "position": 3226}}, "secondFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 101, "end": 110, "startLoc": {"line": 101, "column": 2, "position": 673}, "endLoc": {"line": 110, "column": 2, "position": 772}}}, {"format": "typescript", "lines": 16, "fragment": "();\n    }\n\n    const executionTime = Date.now() - startTime;\n\n    return {\n      success: true,\n      result,\n      executionTime,\n    };\n  } catch (error) {\n    const executionTime = Date.now() - startTime;\n\n    // Handle expected errors\n    if (options.expectedError) {\n      expect", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 478, "end": 493, "startLoc": {"line": 478, "column": 8, "position": 3588}, "endLoc": {"line": 493, "column": 7, "position": 3684}}, "secondFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 193, "end": 208, "startLoc": {"line": 193, "column": 10, "position": 1388}, "endLoc": {"line": 208, "column": 3, "position": 1484}}}, {"format": "typescript", "lines": 15, "fragment": "return {\n        success: true,\n        error: error instanceof Error ? error : new Error(String(error)),\n        executionTime,\n      };\n    }\n\n    // Return test failure result\n    return {\n      success: false,\n      error: error instanceof Error ? error : new Error(String(error)),\n      executionTime,\n    };\n  }\n}", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 494, "end": 508, "startLoc": {"line": 494, "column": 7, "position": 3698}, "endLoc": {"line": 508, "column": 2, "position": 3801}}, "secondFile": {"name": "src\\tests\\utils\\runners\\TestRunners.ts", "start": 214, "end": 228, "startLoc": {"line": 214, "column": 7, "position": 1547}, "endLoc": {"line": 228, "column": 2, "position": 1650}}}, {"format": "typescript", "lines": 21, "fragment": "{\n  params?: any;\n  query?: any;\n  body?: any;\n  headers?: any;\n  user?: any;\n  session?: any;\n  cookies?: any;\n  ip?: string;\n  method?: string;\n  url?: string;\n  originalUrl?: string;\n  path?: string;\n  protocol?: string;\n  secure?: boolean;\n  xhr?: boolean;\n}\n\n/**\n * Mock response interface\n */", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\core\\TestTypes.ts", "start": 21, "end": 41, "startLoc": {"line": 21, "column": 2, "position": 84}, "endLoc": {"line": 41, "column": 4, "position": 209}}, "secondFile": {"name": "src\\tests\\utils\\factories\\MockFactories.ts", "start": 17, "end": 33, "startLoc": {"line": 17, "column": 2, "position": 93}, "endLoc": {"line": 33, "column": 2, "position": 218}}}, {"format": "typescript", "lines": 8, "fragment": ";\n      if (filters.dateFrom || filters.dateTo) {\n        where.createdAt = {};\n        if (filters.dateFrom) where.createdAt.gte = filters.dateFrom;\n        if (filters.dateTo) where.createdAt.lte = filters.dateTo;\n      }\n\n      const", "tokens": 0, "firstFile": {"name": "src\\services\\identity-verification\\core\\IdentityVerificationService.ts", "start": 153, "end": 160, "startLoc": {"line": 153, "column": 11, "position": 1049}, "endLoc": {"line": 160, "column": 6, "position": 1128}}, "secondFile": {"name": "src\\services\\identity-verification\\core\\IdentityVerificationService.ts", "start": 102, "end": 110, "startLoc": {"line": 102, "column": 7, "position": 642}, "endLoc": {"line": 110, "column": 7, "position": 722}}}, {"format": "typescript", "lines": 12, "fragment": "createdAt: { gte: timeWindow },\n          status: { not: 'FAILED' },\n        },\n        select: {\n          amount: true,\n          createdAt: true,\n        },\n      });\n\n      const currentCount = recentTransactions.length;\n      const currentAmount = recentTransactions.reduce((sum, t) => sum + t.amount, 0);\n      const maxAllowed = 5", "tokens": 0, "firstFile": {"name": "src\\services\\fraud-detection\\detectors\\VelocityRiskDetector.ts", "start": 230, "end": 241, "startLoc": {"line": 230, "column": 11, "position": 1583}, "endLoc": {"line": 241, "column": 2, "position": 1696}}, "secondFile": {"name": "src\\services\\fraud-detection\\detectors\\VelocityRiskDetector.ts", "start": 120, "end": 131, "startLoc": {"line": 120, "column": 11, "position": 770}, "endLoc": {"line": 131, "column": 7, "position": 883}}}, {"format": "typescript", "lines": 16, "fragment": "const maxAmount = config.velocitySettings.maxAmount;\n\n      const countViolation = currentCount > maxAllowed;\n      const amountViolation = currentAmount > maxAmount;\n\n      return {\n        isViolation: countViolation || amountViolation,\n        currentCount,\n        currentAmount,\n        maxAllowed,\n        timeWindow: `${config.velocitySettings.timeWindowMinutes} minutes`,\n        violationType:\n          countViolation && amountViolation ? 'BOTH' : countViolation ? 'COUNT' : 'AMOUNT',\n      };\n    } catch (error) {\n      logger.error('Error checking customer velocity:'", "tokens": 0, "firstFile": {"name": "src\\services\\fraud-detection\\detectors\\VelocityRiskDetector.ts", "start": 242, "end": 257, "startLoc": {"line": 242, "column": 7, "position": 1702}, "endLoc": {"line": 257, "column": 36, "position": 1836}}, "secondFile": {"name": "src\\services\\fraud-detection\\detectors\\VelocityRiskDetector.ts", "start": 132, "end": 147, "startLoc": {"line": 132, "column": 7, "position": 889}, "endLoc": {"line": 147, "column": 36, "position": 1023}}}, {"format": "typescript", "lines": 16, "fragment": ",\n      });\n    }\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    return {\n      address: data.address.toLowerCase(),\n      ein", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 103, "end": 118, "startLoc": {"line": 103, "column": 16, "position": 864}, "endLoc": {"line": 118, "column": 4, "position": 963}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 55, "end": 70, "startLoc": {"line": 55, "column": 10, "position": 424}, "endLoc": {"line": 70, "column": 8, "position": 523}}}, {"format": "typescript", "lines": 14, "fragment": "{\n    const errors: ValidationError[] = [];\n\n    if (!data.address) {\n      errors.push({ field: 'address', message: 'Address is required' });\n    } else if (!this.isValidEthereumAddress(data.address)) {\n      errors.push({\n        field: 'address',\n        message: 'Invalid Ethereum address format',\n        value: data.address,\n      });\n    }\n\n    if (!data.key", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 126, "end": 139, "startLoc": {"line": 126, "column": 2, "position": 1007}, "endLoc": {"line": 139, "column": 4, "position": 1127}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 78, "end": 91, "startLoc": {"line": 78, "column": 2, "position": 567}, "endLoc": {"line": 91, "column": 4, "position": 687}}}, {"format": "typescript", "lines": 15, "fragment": "});\n    }\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    return {\n      address: data.address.toLowerCase(),\n      key", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 148, "end": 162, "startLoc": {"line": 148, "column": 2, "position": 1306}, "endLoc": {"line": 162, "column": 4, "position": 1402}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 56, "end": 70, "startLoc": {"line": 56, "column": 7, "position": 427}, "endLoc": {"line": 70, "column": 8, "position": 523}}}, {"format": "typescript", "lines": 11, "fragment": "if (!data.address) {\n      errors.push({ field: 'address', message: 'Address is required' });\n    } else if (!this.isValidEthereumAddress(data.address)) {\n      errors.push({\n        field: 'address',\n        message: 'Invalid Ethereum address format',\n        value: data.address,\n      });\n    }\n\n    if (errors", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 179, "end": 189, "startLoc": {"line": 179, "column": 5, "position": 1559}, "endLoc": {"line": 189, "column": 7, "position": 1656}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 81, "end": 91, "startLoc": {"line": 81, "column": 5, "position": 587}, "endLoc": {"line": 91, "column": 2, "position": 684}}}, {"format": "typescript", "lines": 7, "fragment": "{\n    const errors: ValidationError[] = [];\n\n    if (!data.address) {\n      errors.push({ field: 'address', message: 'Address is required' });\n    } else if (!this.isValidEthereumAddress(data.address)) {\n      errors.push({ field: 'address', message: 'Invalid address format'", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 207, "end": 213, "startLoc": {"line": 207, "column": 2, "position": 1775}, "endLoc": {"line": 213, "column": 25, "position": 1865}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 78, "end": 86, "startLoc": {"line": 78, "column": 2, "position": 567}, "endLoc": {"line": 86, "column": 34, "position": 659}}}, {"format": "typescript", "lines": 15, "fragment": "});\n    }\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    return {\n      address: data.address.toLowerCase(),\n      proof", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 219, "end": 233, "startLoc": {"line": 219, "column": 2, "position": 1956}, "endLoc": {"line": 233, "column": 6, "position": 2052}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 56, "end": 70, "startLoc": {"line": 56, "column": 7, "position": 427}, "endLoc": {"line": 70, "column": 8, "position": 523}}}, {"format": "typescript", "lines": 10, "fragment": "{\n    const errors: ValidationError[] = [];\n\n    if (!data.address) {\n      errors.push({ field: 'address', message: 'Address is required' });\n    } else if (!this.isValidEthereumAddress(data.address)) {\n      errors.push({ field: 'address', message: 'Invalid address format', value: data.address });\n    }\n\n    if (!data.nullifier", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 240, "end": 249, "startLoc": {"line": 240, "column": 2, "position": 2083}, "endLoc": {"line": 249, "column": 10, "position": 2198}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 78, "end": 216, "startLoc": {"line": 78, "column": 2, "position": 567}, "endLoc": {"line": 216, "column": 6, "position": 1890}}}, {"format": "typescript", "lines": 21, "fragment": "});\n    }\n\n    if (!data.proof) {\n      errors.push({ field: 'proof', message: 'Proof is required' });\n    } else if (typeof data.proof !== 'object') {\n      errors.push({ field: 'proof', message: 'Proof must be an object' });\n    }\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    return {\n      address: data.address.toLowerCase(),\n      nullifier", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 252, "end": 272, "startLoc": {"line": 252, "column": 2, "position": 2280}, "endLoc": {"line": 272, "column": 10, "position": 2457}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 213, "end": 70, "startLoc": {"line": 213, "column": 2, "position": 1875}, "endLoc": {"line": 70, "column": 8, "position": 523}}}, {"format": "typescript", "lines": 10, "fragment": "});\n    }\n\n    if (!data.address) {\n      errors.push({ field: 'address', message: 'Address is required' });\n    } else if (!this.isValidEthereumAddress(data.address)) {\n      errors.push({ field: 'address', message: 'Invalid address format', value: data.address });\n    }\n\n    if (errors", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 286, "end": 295, "startLoc": {"line": 286, "column": 2, "position": 2601}, "endLoc": {"line": 295, "column": 7, "position": 2702}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 176, "end": 216, "startLoc": {"line": 176, "column": 2, "position": 1550}, "endLoc": {"line": 216, "column": 2, "position": 1887}}}, {"format": "typescript", "lines": 19, "fragment": "!this.isValidSignature(data.signature)) {\n      errors.push({\n        field: 'signature',\n        message: 'Invalid signature format',\n        value: data.signature,\n      });\n    }\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    return {\n      requestId", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 371, "end": 389, "startLoc": {"line": 371, "column": 2, "position": 3323}, "endLoc": {"line": 389, "column": 10, "position": 3450}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 51, "end": 69, "startLoc": {"line": 51, "column": 2, "position": 383}, "endLoc": {"line": 69, "column": 8, "position": 510}}}, {"format": "typescript", "lines": 10, "fragment": "});\n    }\n\n    if (!data.value) {\n      errors.push({ field: 'value', message: 'Value is required' });\n    } else if (typeof data.value !== 'string' || data.value.trim().length === 0) {\n      errors.push({ field: 'value', message: 'Value must be a non-empty string' });\n    }\n\n    if (!", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 413, "end": 422, "startLoc": {"line": 413, "column": 2, "position": 3692}, "endLoc": {"line": 422, "column": 2, "position": 3801}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 142, "end": 151, "startLoc": {"line": 142, "column": 2, "position": 1209}, "endLoc": {"line": 151, "column": 7, "position": 1318}}}, {"format": "typescript", "lines": 10, "fragment": "} {\n    const page = query.page ? parseInt(query.page, 10) : 1;\n    const limit = query.limit ? parseInt(query.limit, 10) : 10;\n\n    if (isNaN(page) || page < 1) {\n      throw new AppError({\n        message: 'Page must be a positive integer',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT\n      }", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\validators\\BaseValidator.ts", "start": 97, "end": 106, "startLoc": {"line": 97, "column": 2, "position": 698}, "endLoc": {"line": 106, "column": 2, "position": 813}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 476, "end": 484, "startLoc": {"line": 476, "column": 3, "position": 4230}, "endLoc": {"line": 484, "column": 2, "position": 4343}}}, {"format": "typescript", "lines": 12, "fragment": "});\n      }\n      result.sortBy = query.sortBy;\n    }\n\n    if (query.sortOrder) {\n      if (!['asc', 'desc'].includes(query.sortOrder)) {\n        throw new AppError({\n          message: 'Sort order must be either \"asc\" or \"desc\"',\n          type: ErrorType.VALIDATION,\n          code: ErrorCode.INVALID_INPUT\n        }", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\validators\\BaseValidator.ts", "start": 126, "end": 137, "startLoc": {"line": 126, "column": 9, "position": 1007}, "endLoc": {"line": 137, "column": 2, "position": 1097}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 505, "end": 515, "startLoc": {"line": 505, "column": 9, "position": 4539}, "endLoc": {"line": 515, "column": 2, "position": 4627}}}, {"format": "typescript", "lines": 26, "fragment": ";\n}\n\n/**\n * API response wrapper\n */\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n/**\n * Pagination parameters\n */\nexport interface PaginationParams {\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * Fraud detection filters\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\types\\FraudDetectionControllerTypes.ts", "start": 158, "end": 183, "startLoc": {"line": 158, "column": 7, "position": 822}, "endLoc": {"line": 183, "column": 4, "position": 927}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 176, "end": 201, "startLoc": {"line": 176, "column": 2, "position": 741}, "endLoc": {"line": 201, "column": 4, "position": 846}}}, {"format": "typescript", "lines": 41, "fragment": ";\n}\n\n/**\n * Validation error interface\n */\nexport interface ValidationError {\n  field: string;\n  message: string;\n  value?: any;\n}\n\n/**\n * Controller method options\n */\nexport interface ControllerMethodOptions {\n  requireAuth?: boolean;\n  requiredRole?: string;\n  validateInput?: boolean;\n  logRequest?: boolean;\n}\n\n/**\n * Request context interface\n */\nexport interface RequestContext {\n  user?: {\n    id: string;\n    role: string;\n    merchantId?: string;\n  };\n  requestId: string;\n  timestamp: Date;\n  ip: string;\n  userAgent: string;\n}\n\n/**\n * Service dependencies interface\n */\nexport interface FraudDetectionServiceDependencies", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\types\\FraudDetectionControllerTypes.ts", "start": 192, "end": 232, "startLoc": {"line": 192, "column": 7, "position": 999}, "endLoc": {"line": 232, "column": 34, "position": 1164}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 210, "end": 250, "startLoc": {"line": 210, "column": 7, "position": 930}, "endLoc": {"line": 250, "column": 40, "position": 1095}}}, {"format": "typescript", "lines": 53, "fragment": ";\n}\n\n/**\n * Error response format\n */\nexport interface ErrorResponse {\n  success: false;\n  error: {\n    message: string;\n    code: string;\n    type: string;\n    details?: any;\n  };\n  timestamp: Date;\n  requestId: string;\n}\n\n/**\n * Success response format\n */\nexport interface SuccessResponse<T = any> {\n  success: true;\n  data: T;\n  message?: string;\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n  timestamp: Date;\n  requestId: string;\n}\n\n/**\n * Controller method result\n */\nexport type ControllerResult<T = any> = Promise<SuccessResponse<T> | ErrorResponse>;\n\n/**\n * Middleware function type\n */\nexport type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) => void | Promise<void>;\n\n/**\n * Controller method type\n */\nexport type ControllerMethod<T = any> = (req: AuthenticatedRequest, res: Response) => ControllerResult<T>;\n\n/**\n * Authorization context\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\types\\FraudDetectionControllerTypes.ts", "start": 251, "end": 303, "startLoc": {"line": 251, "column": 7, "position": 1280}, "endLoc": {"line": 303, "column": 4, "position": 1565}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 283, "end": 335, "startLoc": {"line": 283, "column": 7, "position": 1283}, "endLoc": {"line": 335, "column": 4, "position": 1568}}}, {"format": "typescript", "lines": 23, "fragment": "/**\n * Authorization context\n */\nexport interface AuthorizationContext {\n  user: {\n    id: string;\n    role: string;\n    merchantId?: string;\n  };\n  resource: string;\n  action: string;\n  resourceId?: string;\n}\n\n/**\n * Permission check result\n */\nexport interface PermissionResult {\n  allowed: boolean;\n  reason?: string;\n  requiredRole?: string;\n  requiredPermissions?: string[];\n}", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\types\\FraudDetectionControllerTypes.ts", "start": 301, "end": 323, "startLoc": {"line": 301, "column": 1, "position": 1565}, "endLoc": {"line": 323, "column": 2, "position": 1675}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 379, "end": 401, "startLoc": {"line": 379, "column": 1, "position": 1792}, "endLoc": {"line": 401, "column": 2, "position": 1902}}}, {"format": "typescript", "lines": 40, "fragment": "];\n\n  /**\n   * Check if user is authorized for the given action\n   */\n  async checkPermission(context: AuthorizationContext): Promise<PermissionResult> {\n    const { user, resource, action } = context;\n\n    if (!user) {\n      return {\n        allowed: false,\n        reason: 'User not authenticated',\n      };\n    }\n\n    // Check role-based permissions\n    const rolePermission = this.checkRolePermission(user.role, resource, action);\n    if (!rolePermission.allowed) {\n      return rolePermission;\n    }\n\n    // Check resource-specific permissions\n    const resourcePermission = await this.checkResourcePermission(context);\n    if (!resourcePermission.allowed) {\n      return resourcePermission;\n    }\n\n    return { allowed: true };\n  }\n\n  /**\n   * Check role-based permissions\n   */\n  private checkRolePermission(\n    userRole: string,\n    resource: string,\n    action: string\n  ): PermissionResult {\n    switch (resource) {\n      case 'risk_assessment'", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 16, "end": 55, "startLoc": {"line": 16, "column": 14, "position": 106}, "endLoc": {"line": 55, "column": 18, "position": 346}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 20, "end": 59, "startLoc": {"line": 20, "column": 6, "position": 116}, "endLoc": {"line": 59, "column": 15, "position": 356}}}, {"format": "typescript", "lines": 14, "fragment": ")) {\n      throw new AppError({\n        message: 'Admin role required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Require merchant role or higher\n   */\n  requireMerchant(userRole?: string): void {\n    if (!userRole || !this.merchantRoles.includes(userRole)", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 207, "end": 220, "startLoc": {"line": 207, "column": 9, "position": 1367}, "endLoc": {"line": 220, "column": 2, "position": 1453}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 177, "end": 190, "startLoc": {"line": 177, "column": 9, "position": 1176}, "endLoc": {"line": 190, "column": 3, "position": 1263}}}, {"format": "typescript", "lines": 14, "fragment": ")) {\n      throw new AppError({\n        message: 'Merchant role required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Require authenticated user\n   */\n  requireAuthenticated(userRole?: string): void {\n    if (!userRole || !this.userRoles.includes(userRole)", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 220, "end": 233, "startLoc": {"line": 220, "column": 9, "position": 1453}, "endLoc": {"line": 233, "column": 2, "position": 1539}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 190, "end": 203, "startLoc": {"line": 190, "column": 9, "position": 1266}, "endLoc": {"line": 203, "column": 3, "position": 1353}}}, {"format": "typescript", "lines": 15, "fragment": ")) {\n      throw new AppError({\n        message: 'Authentication required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Check if user has specific role\n   */\n  hasRole(userRole: string, requiredRole: string): boolean {\n    const roleHierarchy: Record<string, number> = {\n      USER", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 233, "end": 247, "startLoc": {"line": 233, "column": 9, "position": 1539}, "endLoc": {"line": 247, "column": 5, "position": 1632}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 203, "end": 217, "startLoc": {"line": 203, "column": 9, "position": 1356}, "endLoc": {"line": 217, "column": 2, "position": 1449}}}, {"format": "typescript", "lines": 16, "fragment": ",\n    };\n\n    const userLevel = roleHierarchy[userRole] || 0;\n    const requiredLevel = roleHierarchy[requiredRole] || 0;\n\n    return userLevel >= requiredLevel;\n  }\n\n  /**\n   * Get user permissions for a resource\n   */\n  getUserPermissions(userRole: string, resource: string): string[] {\n    const permissions: string[] = [];\n\n    if", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 250, "end": 265, "startLoc": {"line": 250, "column": 2, "position": 1657}, "endLoc": {"line": 265, "column": 3, "position": 1756}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 219, "end": 234, "startLoc": {"line": 219, "column": 2, "position": 1479}, "endLoc": {"line": 234, "column": 7, "position": 1578}}}, {"format": "typescript", "lines": 75, "fragment": "}\n\n    return permissions;\n  }\n\n  /**\n   * Validate authorization context\n   */\n  validateAuthorizationContext(context: AuthorizationContext): void {\n    if (!context.user) {\n      throw new AppError({\n        message: 'User context is required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n\n    if (!context.resource) {\n      throw new AppError({\n        message: 'Resource is required',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n\n    if (!context.action) {\n      throw new AppError({\n        message: 'Action is required',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n  }\n\n  /**\n   * Create authorization context from request\n   */\n  createAuthorizationContext(\n    user: any,\n    resource: string,\n    action: string,\n    resourceId?: string\n  ): AuthorizationContext {\n    return {\n      user: {\n        id: user?.id,\n        role: user?.role,\n        merchantId: user?.merchantId,\n      },\n      resource,\n      action,\n      resourceId,\n    };\n  }\n\n  /**\n   * Handle authorization error\n   */\n  handleAuthorizationError(result: PermissionResult): never {\n    const message = result.reason ?? 'Access denied';\n\n    throw new AppError({\n      message,\n      type: ErrorType.AUTHENTICATION,\n      code: ErrorCode.INVALID_CREDENTIALS,\n      details: {\n        requiredRole: result.requiredRole,\n        requiredPermissions: result.requiredPermissions,\n      },\n    });\n  }\n\n  /**\n   * Extract merchant context from request\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 284, "end": 358, "startLoc": {"line": 284, "column": 5, "position": 1963}, "endLoc": {"line": 358, "column": 6, "position": 2373}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 253, "end": 327, "startLoc": {"line": 253, "column": 5, "position": 1763}, "endLoc": {"line": 327, "column": 6, "position": 2173}}}, {"format": "typescript", "lines": 53, "fragment": "{\n  /**\n   * Send success response\n   */\n  static sendSuccess<T>(\n    res: Response,\n    data: T,\n    message?: string,\n    statusCode: number = 200,\n    pagination?: {\n      page: number;\n      limit: number;\n      total: number;\n      totalPages: number;\n    }\n  ): void {\n    const response: SuccessResponse<T> = {\n      success: true,\n      data,\n      message,\n      pagination,\n      timestamp: new Date(),\n      requestId: res.locals.requestId ?? 'unknown',\n    };\n\n    res.status(statusCode).json(response);\n  }\n\n  /**\n   * Send error response\n   */\n  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {\n    let errorResponse: ErrorResponse;\n\n    if (error instanceof AppError) {\n      errorResponse = {\n        success: false,\n        error: {\n          message: error.message,\n          code: error.code,\n          type: error.type,\n          details: error.details,\n        },\n        timestamp: new Date(),\n        requestId: res.locals.requestId ?? 'unknown',\n      };\n\n      statusCode = statusCode ?? error.statusCode ?? 400;\n    } else {\n      errorResponse = {\n        success: false,\n        error: {\n          message: error.message ||", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\mappers\\FraudDetectionResponseMapper.ts", "start": 21, "end": 73, "startLoc": {"line": 21, "column": 2, "position": 73}, "endLoc": {"line": 73, "column": 3, "position": 456}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 21, "end": 73, "startLoc": {"line": 21, "column": 2, "position": 73}, "endLoc": {"line": 73, "column": 2, "position": 456}}}, {"format": "typescript", "lines": 17, "fragment": "'Internal server error',\n          code: 'INTERNAL_SERVER_ERROR',\n          type: 'INTERNAL',\n        },\n        timestamp: new Date(),\n        requestId: res.locals.requestId ?? 'unknown',\n      };\n\n      statusCode = statusCode ?? 500;\n    }\n\n    res.status(statusCode).json(errorResponse);\n  }\n\n  /**\n   * Send risk assessment response\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\mappers\\FraudDetectionResponseMapper.ts", "start": 73, "end": 89, "startLoc": {"line": 73, "column": 2, "position": 458}, "endLoc": {"line": 89, "column": 6, "position": 547}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 73, "end": 89, "startLoc": {"line": 73, "column": 2, "position": 459}, "endLoc": {"line": 89, "column": 6, "position": 548}}}, {"format": "typescript", "lines": 68, "fragment": ");\n  }\n\n  /**\n   * Send validation error response\n   */\n  static sendValidationError(\n    res: Response,\n    errors: any[],\n    message: string = 'Validation failed'\n  ): void {\n    const error = new AppError({\n      message,\n      type: 'VALIDATION' as any,\n      code: 'INVALID_INPUT' as any,\n      details: { errors },\n    });\n\n    this.sendError(res, error, 400);\n  }\n\n  /**\n   * Send authorization error response\n   */\n  static sendAuthorizationError(\n    res: Response,\n    message: string = 'Access denied',\n    requiredRole?: string\n  ): void {\n    const error = new AppError({\n      message,\n      type: 'AUTHENTICATION' as any,\n      code: 'INVALID_CREDENTIALS' as any,\n      details: { requiredRole },\n    });\n\n    this.sendError(res, error, 403);\n  }\n\n  /**\n   * Send not found error response\n   */\n  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {\n    const error = new AppError({\n      message: `${resource} not found`,\n      type: 'NOT_FOUND' as any,\n      code: 'RESOURCE_NOT_FOUND' as any,\n    });\n\n    this.sendError(res, error, 404);\n  }\n\n  /**\n   * Send internal server error response\n   */\n  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {\n    const error = new AppError({\n      message,\n      type: 'INTERNAL' as any,\n      code: 'INTERNAL_SERVER_ERROR' as any,\n    });\n\n    this.sendError(res, error, 500);\n  }\n\n  /**\n   * Handle async controller method\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\mappers\\FraudDetectionResponseMapper.ts", "start": 157, "end": 224, "startLoc": {"line": 157, "column": 52, "position": 952}, "endLoc": {"line": 224, "column": 6, "position": 1388}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 204, "end": 271, "startLoc": {"line": 204, "column": 36, "position": 1323}, "endLoc": {"line": 271, "column": 6, "position": 1759}}}, {"format": "typescript", "lines": 24, "fragment": ";\n  }\n\n  /**\n   * Handle async controller method\n   */\n  static asyncHandler(fn: Function) {\n    return (req: any, res: Response, next: Function) => {\n      Promise.resolve(fn(req, res, next)).catch((error) => next(error));\n    };\n  }\n\n  /**\n   * Set response headers for API\n   */\n  static setApiHeaders(res: Response): void {\n    res.setHeader('Content-Type', 'application/json');\n    res.setHeader('X-API-Version', '1.0');\n    res.setHeader('X-Response-Time', Date.now());\n  }\n\n  /**\n   * Format risk level for display\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\mappers\\FraudDetectionResponseMapper.ts", "start": 219, "end": 242, "startLoc": {"line": 219, "column": 2, "position": 1381}, "endLoc": {"line": 242, "column": 6, "position": 1533}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 293, "end": 316, "startLoc": {"line": 293, "column": 2, "position": 1905}, "endLoc": {"line": 316, "column": 6, "position": 2057}}}, {"format": "typescript", "lines": 26, "fragment": ";\n}\n\n/**\n * API response wrapper\n */\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n/**\n * Pagination parameters\n */\nexport interface PaginationParams {\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * Filter parameters for aggregation rules\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\types\\AlertAggregationTypes.ts", "start": 109, "end": 134, "startLoc": {"line": 109, "column": 5, "position": 582}, "endLoc": {"line": 134, "column": 4, "position": 687}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 176, "end": 201, "startLoc": {"line": 176, "column": 2, "position": 741}, "endLoc": {"line": 201, "column": 4, "position": 846}}}, {"format": "typescript", "lines": 42, "fragment": ";\n  search?: string;\n}\n\n/**\n * Validation error interface\n */\nexport interface ValidationError {\n  field: string;\n  message: string;\n  value?: any;\n}\n\n/**\n * Controller method options\n */\nexport interface ControllerMethodOptions {\n  requireAuth?: boolean;\n  requiredRole?: string;\n  validateInput?: boolean;\n  logRequest?: boolean;\n}\n\n/**\n * Request context interface\n */\nexport interface RequestContext {\n  user?: {\n    id: string;\n    role: string;\n    merchantId?: string;\n  };\n  requestId: string;\n  timestamp: Date;\n  ip: string;\n  userAgent: string;\n}\n\n/**\n * Service dependencies interface\n */\nexport interface AlertAggregationServiceDependencies", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\types\\AlertAggregationTypes.ts", "start": 146, "end": 187, "startLoc": {"line": 146, "column": 8, "position": 756}, "endLoc": {"line": 187, "column": 36, "position": 929}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 209, "end": 250, "startLoc": {"line": 209, "column": 5, "position": 922}, "endLoc": {"line": 250, "column": 40, "position": 1095}}}, {"format": "typescript", "lines": 53, "fragment": ";\n}\n\n/**\n * Error response format\n */\nexport interface ErrorResponse {\n  success: false;\n  error: {\n    message: string;\n    code: string;\n    type: string;\n    details?: any;\n  };\n  timestamp: Date;\n  requestId: string;\n}\n\n/**\n * Success response format\n */\nexport interface SuccessResponse<T = any> {\n  success: true;\n  data: T;\n  message?: string;\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n  timestamp: Date;\n  requestId: string;\n}\n\n/**\n * Controller method result\n */\nexport type ControllerResult<T = any> = Promise<SuccessResponse<T> | ErrorResponse>;\n\n/**\n * Middleware function type\n */\nexport type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) => void | Promise<void>;\n\n/**\n * Controller method type\n */\nexport type ControllerMethod<T = any> = (req: AuthenticatedRequest, res: Response) => ControllerResult<T>;\n\n/**\n * Validation schema interface\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\types\\AlertAggregationTypes.ts", "start": 239, "end": 291, "startLoc": {"line": 239, "column": 8, "position": 1190}, "endLoc": {"line": 291, "column": 4, "position": 1475}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 283, "end": 335, "startLoc": {"line": 283, "column": 7, "position": 1283}, "endLoc": {"line": 335, "column": 4, "position": 1568}}}, {"format": "typescript", "lines": 25, "fragment": "}\n\n/**\n * Authorization context\n */\nexport interface AuthorizationContext {\n  user: {\n    id: string;\n    role: string;\n    merchantId?: string;\n  };\n  resource: string;\n  action: string;\n  resourceId?: string;\n}\n\n/**\n * Permission check result\n */\nexport interface PermissionResult {\n  allowed: boolean;\n  reason?: string;\n  requiredRole?: string;\n  requiredPermissions?: string[];\n}", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\types\\AlertAggregationTypes.ts", "start": 304, "end": 328, "startLoc": {"line": 304, "column": 1, "position": 1602}, "endLoc": {"line": 328, "column": 2, "position": 1715}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 377, "end": 401, "startLoc": {"line": 377, "column": 1, "position": 1789}, "endLoc": {"line": 401, "column": 2, "position": 1902}}}, {"format": "typescript", "lines": 12, "fragment": "this.validateEnabledField(data.enabled, errors);\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    const", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "start": 72, "end": 83, "startLoc": {"line": 72, "column": 2, "position": 677}, "endLoc": {"line": 83, "column": 6, "position": 761}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "start": 34, "end": 45, "startLoc": {"line": 34, "column": 5, "position": 233}, "endLoc": {"line": 45, "column": 7, "position": 317}}}, {"format": "typescript", "lines": 17, "fragment": ",\n    };\n  }\n\n  /**\n   * Validate ID parameter\n   */\n  validateId(id: any, fieldName: string = 'id'): string {\n    if (!id) {\n      throw new AppError({\n        message: `${fieldName} is required`,\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n\n    if (typeof", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "start": 156, "end": 172, "startLoc": {"line": 156, "column": 9, "position": 1488}, "endLoc": {"line": 172, "column": 7, "position": 1586}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 441, "end": 457, "startLoc": {"line": 441, "column": 2, "position": 4014}, "endLoc": {"line": 457, "column": 2, "position": 4112}}}, {"format": "typescript", "lines": 35, "fragment": ";\n  }\n\n  /**\n   * Validate pagination parameters\n   */\n  validatePaginationParams(query: any): {\n    page: number;\n    limit: number;\n    sortBy?: string;\n    sortOrder?: 'asc' | 'desc';\n  } {\n    const page = query.page ? parseInt(query.page, 10) : 1;\n    const limit = query.limit ? parseInt(query.limit, 10) : 10;\n\n    if (isNaN(page) || page < 1) {\n      throw new AppError({\n        message: 'Page must be a positive integer',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n      });\n    }\n\n    if (isNaN(limit) || limit < 1 || limit > 100) {\n      throw new AppError({\n        message: 'Limit must be between 1 and 100',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n      });\n    }\n\n    const result: any = { page, limit };\n\n    if (query.sortBy) {\n      const validSortFields = ['name'", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "start": 190, "end": 224, "startLoc": {"line": 190, "column": 2, "position": 1741}, "endLoc": {"line": 224, "column": 7, "position": 2032}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 465, "end": 499, "startLoc": {"line": 465, "column": 3, "position": 4174}, "endLoc": {"line": 499, "column": 12, "position": 4465}}}, {"format": "typescript", "lines": 28, "fragment": "];\n      if (!validSortFields.includes(query.sortBy)) {\n        throw new AppError({\n          message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,\n          type: ErrorType.VALIDATION,\n          code: ErrorCode.INVALID_INPUT,\n        });\n      }\n      result.sortBy = query.sortBy;\n    }\n\n    if (query.sortOrder) {\n      if (!['asc', 'desc'].includes(query.sortOrder)) {\n        throw new AppError({\n          message: 'Sort order must be either \"asc\" or \"desc\"',\n          type: ErrorType.VALIDATION,\n          code: ErrorCode.INVALID_INPUT,\n        });\n      }\n      result.sortOrder = query.sortOrder;\n    }\n\n    return result;\n  }\n\n  /**\n   * Helper validation methods\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "start": 224, "end": 251, "startLoc": {"line": 224, "column": 10, "position": 2042}, "endLoc": {"line": 251, "column": 6, "position": 2231}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 499, "end": 526, "startLoc": {"line": 499, "column": 7, "position": 4475}, "endLoc": {"line": 526, "column": 6, "position": 4664}}}, {"format": "typescript", "lines": 40, "fragment": ", 'ADMIN', 'SUPER_ADMIN'];\n\n  /**\n   * Check if user is authorized for the given action\n   */\n  async checkPermission(context: AuthorizationContext): Promise<PermissionResult> {\n    const { user, resource, action } = context;\n\n    if (!user) {\n      return {\n        allowed: false,\n        reason: 'User not authenticated',\n      };\n    }\n\n    // Check role-based permissions\n    const rolePermission = this.checkRolePermission(user.role, resource, action);\n    if (!rolePermission.allowed) {\n      return rolePermission;\n    }\n\n    // Check resource-specific permissions\n    const resourcePermission = await this.checkResourcePermission(context);\n    if (!resourcePermission.allowed) {\n      return resourcePermission;\n    }\n\n    return { allowed: true };\n  }\n\n  /**\n   * Check role-based permissions\n   */\n  private checkRolePermission(\n    userRole: string,\n    resource: string,\n    action: string\n  ): PermissionResult {\n    switch (resource) {\n      case 'aggregation-rules'", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 16, "end": 55, "startLoc": {"line": 16, "column": 10, "position": 100}, "endLoc": {"line": 55, "column": 20, "position": 346}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 16, "end": 59, "startLoc": {"line": 16, "column": 11, "position": 100}, "endLoc": {"line": 59, "column": 15, "position": 356}}}, {"format": "typescript", "lines": 19, "fragment": ";\n      case 'create':\n      case 'update':\n      case 'delete':\n        if (this.adminRoles.includes(userRole)) {\n          return { allowed: true };\n        }\n        return {\n          allowed: false,\n          reason: 'Admin role required for write operations',\n          requiredRole: 'ADMIN',\n        };\n      default:\n        return {\n          allowed: false,\n          reason: `Unknown action: ${action}`,\n        };\n    }\n  }", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 115, "end": 133, "startLoc": {"line": 115, "column": 2, "position": 746}, "endLoc": {"line": 133, "column": 2, "position": 863}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 76, "end": 95, "startLoc": {"line": 76, "column": 6, "position": 495}, "endLoc": {"line": 95, "column": 7, "position": 613}}}, {"format": "typescript", "lines": 22, "fragment": "return { allowed: true };\n    }\n\n    return { allowed: true };\n  }\n\n  /**\n   * Require admin role\n   */\n  requireAdmin(userRole?: string): void {\n    if (!userRole || !this.adminRoles.includes(userRole)) {\n      throw new AppError({\n        message: 'Admin role required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Require manager role or higher\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 147, "end": 168, "startLoc": {"line": 147, "column": 7, "position": 942}, "endLoc": {"line": 168, "column": 6, "position": 1062}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 197, "end": 218, "startLoc": {"line": 197, "column": 7, "position": 1299}, "endLoc": {"line": 218, "column": 6, "position": 1419}}}, {"format": "typescript", "lines": 27, "fragment": ",\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Require authenticated user\n   */\n  requireAuthenticated(userRole?: string): void {\n    if (!userRole || !this.userRoles.includes(userRole)) {\n      throw new AppError({\n        message: 'Authentication required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Check if user has specific role\n   */\n  hasRole(userRole: string, requiredRole: string): boolean {\n    const roleHierarchy: Record<string, number> = {\n      USER: 1,\n      MANAGER", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 172, "end": 198, "startLoc": {"line": 172, "column": 24, "position": 1115}, "endLoc": {"line": 198, "column": 8, "position": 1282}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 222, "end": 248, "startLoc": {"line": 222, "column": 25, "position": 1472}, "endLoc": {"line": 248, "column": 9, "position": 1639}}}, {"format": "typescript", "lines": 19, "fragment": ": 2,\n      ADMIN: 3,\n      SUPER_ADMIN: 4,\n    };\n\n    const userLevel = roleHierarchy[userRole] || 0;\n    const requiredLevel = roleHierarchy[requiredRole] || 0;\n\n    return userLevel >= requiredLevel;\n  }\n\n  /**\n   * Get user permissions for a resource\n   */\n  getUserPermissions(userRole: string, resource: string): string[] {\n    const permissions: string[] = [];\n\n    switch (resource) {\n      case 'aggregation-rules'", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 198, "end": 216, "startLoc": {"line": 198, "column": 8, "position": 1283}, "endLoc": {"line": 216, "column": 20, "position": 1410}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 248, "end": 235, "startLoc": {"line": 248, "column": 9, "position": 1640}, "endLoc": {"line": 235, "column": 15, "position": 1589}}}, {"format": "typescript", "lines": 75, "fragment": "'create', 'update', 'delete');\n        }\n        break;\n    }\n\n    return permissions;\n  }\n\n  /**\n   * Validate authorization context\n   */\n  validateAuthorizationContext(context: AuthorizationContext): void {\n    if (!context.user) {\n      throw new AppError({\n        message: 'User context is required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n\n    if (!context.resource) {\n      throw new AppError({\n        message: 'Resource is required',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n\n    if (!context.action) {\n      throw new AppError({\n        message: 'Action is required',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n  }\n\n  /**\n   * Create authorization context from request\n   */\n  createAuthorizationContext(\n    user: any,\n    resource: string,\n    action: string,\n    resourceId?: string\n  ): AuthorizationContext {\n    return {\n      user: {\n        id: user?.id,\n        role: user?.role,\n        merchantId: user?.merchantId,\n      },\n      resource,\n      action,\n      resourceId,\n    };\n  }\n\n  /**\n   * Handle authorization error\n   */\n  handleAuthorizationError(result: PermissionResult): never {\n    const message = result.reason ?? 'Access denied';\n\n    throw new AppError({\n      message,\n      type: ErrorType.AUTHENTICATION,\n      code: ErrorCode.INVALID_CREDENTIALS,\n      details: {\n        requiredRole: result.requiredRole,\n        requiredPermissions: result.requiredPermissions,\n      },\n    });\n  }\n}", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 229, "end": 303, "startLoc": {"line": 229, "column": 2, "position": 1534}, "endLoc": {"line": 303, "column": 2, "position": 1960}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 250, "end": 327, "startLoc": {"line": 250, "column": 2, "position": 1745}, "endLoc": {"line": 327, "column": 6, "position": 2173}}}, {"format": "typescript", "lines": 12, "fragment": ",\n        orderBy: { createdAt: 'desc' },\n      };\n\n      // Apply pagination\n      if (pagination) {\n        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;\n        queryOptions.skip = (page - 1) * limit;\n        queryOptions.take = limit;\n\n        if (sortBy) {\n          queryOptions.orderBy = { [sortBy]: sortOrder ||", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AlertAggregationBusinessService.ts", "start": 59, "end": 70, "startLoc": {"line": 59, "column": 6, "position": 387}, "endLoc": {"line": 70, "column": 3, "position": 507}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionBusinessService.ts", "start": 278, "end": 289, "startLoc": {"line": 278, "column": 2, "position": 2174}, "endLoc": {"line": 289, "column": 2, "position": 2294}}}, {"format": "typescript", "lines": 16, "fragment": "> {\n    try {\n      // Check if rule exists\n      const existingRule = await this.prisma.alertAggregationRule.findUnique({\n        where: { id },\n      });\n\n      if (!existingRule) {\n        throw new AppError({\n          message: 'Aggregation rule not found',\n          type: ErrorType.NOT_FOUND,\n          code: ErrorCode.RESOURCE_NOT_FOUND,\n        });\n      }\n\n      await", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AlertAggregationBusinessService.ts", "start": 249, "end": 264, "startLoc": {"line": 249, "column": 5, "position": 1963}, "endLoc": {"line": 264, "column": 6, "position": 2065}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\services\\AlertAggregationBusinessService.ts", "start": 181, "end": 196, "startLoc": {"line": 181, "column": 24, "position": 1332}, "endLoc": {"line": 196, "column": 53, "position": 1434}}}, {"format": "typescript", "lines": 31, "fragment": "if (filters?.enabled !== undefined) {\n        where.enabled = filters.enabled;\n      }\n\n      if (filters?.search) {\n        where.OR = [\n          { name: { contains: filters.search, mode: 'insensitive' } },\n          { description: { contains: filters.search, mode: 'insensitive' } },\n        ];\n      }\n\n      // Build query options\n      const queryOptions: any = {\n        where,\n        orderBy: { createdAt: 'desc' },\n      };\n\n      // Apply pagination\n      if (pagination) {\n        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;\n        queryOptions.skip = (page - 1) * limit;\n        queryOptions.take = limit;\n\n        if (sortBy) {\n          queryOptions.orderBy = { [sortBy]: sortOrder || 'desc' };\n        }\n      }\n\n      // Execute queries\n      const [rules, total] = await Promise.all([\n        this.prisma.alertCorrelationRule", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\services\\AlertAggregationBusinessService.ts", "start": 291, "end": 321, "startLoc": {"line": 291, "column": 7, "position": 2274}, "endLoc": {"line": 321, "column": 21, "position": 2565}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\services\\AlertAggregationBusinessService.ts", "start": 46, "end": 76, "startLoc": {"line": 46, "column": 7, "position": 258}, "endLoc": {"line": 76, "column": 21, "position": 549}}}, {"format": "typescript", "lines": 69, "fragment": "{\n  /**\n   * Send success response\n   */\n  static sendSuccess<T>(\n    res: Response,\n    data: T,\n    message?: string,\n    statusCode: number = 200,\n    pagination?: {\n      page: number;\n      limit: number;\n      total: number;\n      totalPages: number;\n    }\n  ): void {\n    const response: SuccessResponse<T> = {\n      success: true,\n      data,\n      message,\n      pagination,\n      timestamp: new Date(),\n      requestId: res.locals.requestId ?? 'unknown',\n    };\n\n    res.status(statusCode).json(response);\n  }\n\n  /**\n   * Send error response\n   */\n  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {\n    let errorResponse: ErrorResponse;\n\n    if (error instanceof AppError) {\n      errorResponse = {\n        success: false,\n        error: {\n          message: error.message,\n          code: error.code,\n          type: error.type,\n          details: error.details,\n        },\n        timestamp: new Date(),\n        requestId: res.locals.requestId ?? 'unknown',\n      };\n\n      statusCode = statusCode ?? error.statusCode ?? 400;\n    } else {\n      errorResponse = {\n        success: false,\n        error: {\n          message: error.message || 'Internal server error',\n          code: 'INTERNAL_SERVER_ERROR',\n          type: 'INTERNAL',\n        },\n        timestamp: new Date(),\n        requestId: res.locals.requestId ?? 'unknown',\n      };\n\n      statusCode = statusCode ?? 500;\n    }\n\n    res.status(statusCode).json(errorResponse);\n  }\n\n  /**\n   * Send aggregation rules list response\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\mappers\\ResponseMapper.ts", "start": 20, "end": 88, "startLoc": {"line": 20, "column": 2, "position": 69}, "endLoc": {"line": 88, "column": 6, "position": 543}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 21, "end": 89, "startLoc": {"line": 21, "column": 2, "position": 73}, "endLoc": {"line": 89, "column": 6, "position": 548}}}, {"format": "typescript", "lines": 95, "fragment": ");\n  }\n\n  /**\n   * Send validation error response\n   */\n  static sendValidationError(\n    res: Response,\n    errors: any[],\n    message: string = 'Validation failed'\n  ): void {\n    const error = new AppError({\n      message,\n      type: 'VALIDATION' as any,\n      code: 'INVALID_INPUT' as any,\n      details: { errors },\n    });\n\n    this.sendError(res, error, 400);\n  }\n\n  /**\n   * Send authorization error response\n   */\n  static sendAuthorizationError(\n    res: Response,\n    message: string = 'Access denied',\n    requiredRole?: string\n  ): void {\n    const error = new AppError({\n      message,\n      type: 'AUTHENTICATION' as any,\n      code: 'INVALID_CREDENTIALS' as any,\n      details: { requiredRole },\n    });\n\n    this.sendError(res, error, 403);\n  }\n\n  /**\n   * Send not found error response\n   */\n  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {\n    const error = new AppError({\n      message: `${resource} not found`,\n      type: 'NOT_FOUND' as any,\n      code: 'RESOURCE_NOT_FOUND' as any,\n    });\n\n    this.sendError(res, error, 404);\n  }\n\n  /**\n   * Send internal server error response\n   */\n  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {\n    const error = new AppError({\n      message,\n      type: 'INTERNAL' as any,\n      code: 'INTERNAL_SERVER_ERROR' as any,\n    });\n\n    this.sendError(res, error, 500);\n  }\n\n  /**\n   * Format pagination metadata\n   */\n  static formatPagination(\n    page: number,\n    limit: number,\n    total: number\n  ): {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  } {\n    const totalPages = Math.ceil(total / limit);\n\n    return {\n      page,\n      limit,\n      total,\n      totalPages,\n      hasNext: page < totalPages,\n      hasPrev: page > 1,\n    };\n  }\n\n  /**\n   * Create API response wrapper\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\mappers\\ResponseMapper.ts", "start": 179, "end": 273, "startLoc": {"line": 179, "column": 40, "position": 1167}, "endLoc": {"line": 273, "column": 6, "position": 1756}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 204, "end": 298, "startLoc": {"line": 204, "column": 36, "position": 1323}, "endLoc": {"line": 298, "column": 6, "position": 1912}}}, {"format": "typescript", "lines": 29, "fragment": ",\n    };\n  }\n\n  /**\n   * Handle async controller method\n   */\n  static asyncHandler(fn: Function) {\n    return (req: any, res: Response, next: Function) => {\n      Promise.resolve(fn(req, res, next)).catch((error) => next(error));\n    };\n  }\n\n  /**\n   * Set response headers for API\n   */\n  static setApiHeaders(res: Response): void {\n    res.setHeader('Content-Type', 'application/json');\n    res.setHeader('X-API-Version', '1.0');\n    res.setHeader('X-Response-Time', Date.now());\n  }\n\n  /**\n   * Log response for debugging\n   */\n  static logResponse(method: string, url: string, statusCode: number, responseTime: number): void {\n    console.log(`${method} ${url} - ${statusCode} - ${responseTime}ms`);\n  }\n}", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\mappers\\ResponseMapper.ts", "start": 284, "end": 312, "startLoc": {"line": 284, "column": 6, "position": 1827}, "endLoc": {"line": 312, "column": 2, "position": 2047}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 292, "end": 323, "startLoc": {"line": 292, "column": 2, "position": 1901}, "endLoc": {"line": 323, "column": 6, "position": 2123}}}, {"format": "typescript", "lines": 28, "fragment": ": boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n/**\n * API response wrapper\n */\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\n/**\n * Pagination parameters\n */\nexport interface PaginationParams {\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\n/**\n * Admin user filters\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\types\\AdminControllerTypes.ts", "start": 134, "end": 161, "startLoc": {"line": 134, "column": 9, "position": 612}, "endLoc": {"line": 161, "column": 4, "position": 734}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\types\\AlertAggregationTypes.ts", "start": 107, "end": 201, "startLoc": {"line": 107, "column": 8, "position": 565}, "endLoc": {"line": 201, "column": 4, "position": 846}}}, {"format": "typescript", "lines": 42, "fragment": "?: boolean;\n  search?: string;\n}\n\n/**\n * Validation error interface\n */\nexport interface ValidationError {\n  field: string;\n  message: string;\n  value?: any;\n}\n\n/**\n * Controller method options\n */\nexport interface ControllerMethodOptions {\n  requireAuth?: boolean;\n  requiredRole?: string;\n  validateInput?: boolean;\n  logRequest?: boolean;\n}\n\n/**\n * Request context interface\n */\nexport interface RequestContext {\n  user?: {\n    id: string;\n    role: string;\n    merchantId?: string;\n  };\n  requestId: string;\n  timestamp: Date;\n  ip: string;\n  userAgent: string;\n}\n\n/**\n * Service dependencies interface\n */\nexport interface AdminServiceDependencies", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\types\\AdminControllerTypes.ts", "start": 185, "end": 226, "startLoc": {"line": 185, "column": 9, "position": 856}, "endLoc": {"line": 226, "column": 25, "position": 1033}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\types\\AlertAggregationTypes.ts", "start": 146, "end": 250, "startLoc": {"line": 146, "column": 8, "position": 752}, "endLoc": {"line": 250, "column": 40, "position": 1095}}}, {"format": "typescript", "lines": 53, "fragment": ";\n}\n\n/**\n * Error response format\n */\nexport interface ErrorResponse {\n  success: false;\n  error: {\n    message: string;\n    code: string;\n    type: string;\n    details?: any;\n  };\n  timestamp: Date;\n  requestId: string;\n}\n\n/**\n * Success response format\n */\nexport interface SuccessResponse<T = any> {\n  success: true;\n  data: T;\n  message?: string;\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n  timestamp: Date;\n  requestId: string;\n}\n\n/**\n * Controller method result\n */\nexport type ControllerResult<T = any> = Promise<SuccessResponse<T> | ErrorResponse>;\n\n/**\n * Middleware function type\n */\nexport type MiddlewareFunction = (req: AuthenticatedRequest, res: Response, next: Function) => void | Promise<void>;\n\n/**\n * Controller method type\n */\nexport type ControllerMethod<T = any> = (req: AuthenticatedRequest, res: Response) => ControllerResult<T>;\n\n/**\n * Admin user status enum\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\types\\AdminControllerTypes.ts", "start": 261, "end": 313, "startLoc": {"line": 261, "column": 4, "position": 1235}, "endLoc": {"line": 313, "column": 4, "position": 1520}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 283, "end": 335, "startLoc": {"line": 283, "column": 7, "position": 1283}, "endLoc": {"line": 335, "column": 4, "position": 1568}}}, {"format": "typescript", "lines": 29, "fragment": "}\n\n/**\n * Authorization context\n */\nexport interface AuthorizationContext {\n  user: {\n    id: string;\n    role: string;\n    merchantId?: string;\n  };\n  resource: string;\n  action: string;\n  resourceId?: string;\n}\n\n/**\n * Permission check result\n */\nexport interface PermissionResult {\n  allowed: boolean;\n  reason?: string;\n  requiredRole?: string;\n  requiredPermissions?: string[];\n}\n\n/**\n * Dashboard statistics\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\types\\AdminControllerTypes.ts", "start": 351, "end": 379, "startLoc": {"line": 351, "column": 1, "position": 1709}, "endLoc": {"line": 379, "column": 4, "position": 1825}}, "secondFile": {"name": "src\\controllers\\identity-verification\\types\\IdentityVerificationControllerTypes.ts", "start": 377, "end": 327, "startLoc": {"line": 377, "column": 1, "position": 1789}, "endLoc": {"line": 327, "column": 4, "position": 1678}}}, {"format": "typescript", "lines": 6, "fragment": "if (typeof data.name !== 'string' || data.name.trim().length === 0) {\n        errors.push({ field: 'name', message: 'Name must be a non-empty string' });\n      } else if (data.name.length > 100) {\n        errors.push({ field: 'name', message: 'Name must be less than 100 characters' });\n      }\n    }", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 92, "end": 97, "startLoc": {"line": 92, "column": 7, "position": 846}, "endLoc": {"line": 97, "column": 2, "position": 947}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 38, "end": 44, "startLoc": {"line": 38, "column": 2, "position": 254}, "endLoc": {"line": 44, "column": 3, "position": 356}}}, {"format": "typescript", "lines": 12, "fragment": "if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    return {\n      name: data.name.trim(),\n      type", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 180, "end": 191, "startLoc": {"line": 180, "column": 5, "position": 1921}, "endLoc": {"line": 191, "column": 5, "position": 2008}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\services\\ValidationService.ts", "start": 36, "end": 47, "startLoc": {"line": 36, "column": 5, "position": 248}, "endLoc": {"line": 47, "column": 12, "position": 335}}}, {"format": "typescript", "lines": 6, "fragment": "if (typeof data.name !== 'string' || data.name.trim().length === 0) {\n        errors.push({ field: 'name', message: 'Name must be a non-empty string' });\n      } else if (data.name.length > 50) {\n        errors.push({ field: 'name', message: 'Name must be less than 50 characters' });\n      }\n    }", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 204, "end": 209, "startLoc": {"line": 204, "column": 7, "position": 2096}, "endLoc": {"line": 209, "column": 2, "position": 2197}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 135, "end": 141, "startLoc": {"line": 135, "column": 2, "position": 1347}, "endLoc": {"line": 141, "column": 3, "position": 1449}}}, {"format": "typescript", "lines": 9, "fragment": "if (typeof data.description !== 'string' || data.description.trim().length === 0) {\n        errors.push({ field: 'description', message: 'Description must be a non-empty string' });\n      } else if (data.description.length > 500) {\n        errors.push({\n          field: 'description',\n          message: 'Description must be less than 500 characters',\n        });\n      }\n    }", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 212, "end": 220, "startLoc": {"line": 212, "column": 7, "position": 2216}, "endLoc": {"line": 220, "column": 2, "position": 2321}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 153, "end": 162, "startLoc": {"line": 153, "column": 2, "position": 1600}, "endLoc": {"line": 162, "column": 3, "position": 1706}}}, {"format": "typescript", "lines": 13, "fragment": "});\n      } else {\n        data.permissions.forEach((permissionId: any, index: number) => {\n          if (!this.isValidUUID(permissionId)) {\n            errors.push({\n              field: `permissions[${index}]`,\n              message: 'Invalid permission ID format',\n              value: permissionId,\n            });\n          }\n        });\n      }\n    }", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 224, "end": 236, "startLoc": {"line": 224, "column": 2, "position": 2374}, "endLoc": {"line": 236, "column": 2, "position": 2473}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 167, "end": 180, "startLoc": {"line": 167, "column": 2, "position": 1821}, "endLoc": {"line": 180, "column": 3, "position": 1921}}}, {"format": "typescript", "lines": 18, "fragment": "});\n      }\n    }\n\n    if (data.isActive !== undefined && typeof data.isActive !== 'boolean') {\n      errors.push({ field: 'isActive', message: 'isActive must be a boolean' });\n    }\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    const result: UpdateRoleRequest", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 234, "end": 251, "startLoc": {"line": 234, "column": 9, "position": 2465}, "endLoc": {"line": 251, "column": 18, "position": 2604}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 101, "end": 118, "startLoc": {"line": 101, "column": 2, "position": 1008}, "endLoc": {"line": 118, "column": 23, "position": 1147}}}, {"format": "typescript", "lines": 12, "fragment": "{\n    const errors: ValidationError[] = [];\n\n    if (!data.name) {\n      errors.push({ field: 'name', message: 'Name is required' });\n    } else if (typeof data.name !== 'string' || data.name.trim().length === 0) {\n      errors.push({ field: 'name', message: 'Name must be a non-empty string' });\n    } else if (data.name.length > 50) {\n      errors.push({ field: 'name', message: 'Name must be less than 50 characters' });\n    }\n\n    if (!data.description", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 263, "end": 274, "startLoc": {"line": 263, "column": 2, "position": 2742}, "endLoc": {"line": 274, "column": 12, "position": 2908}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 130, "end": 141, "startLoc": {"line": 130, "column": 2, "position": 1289}, "endLoc": {"line": 141, "column": 5, "position": 1455}}}, {"format": "typescript", "lines": 8, "fragment": "});\n    }\n\n    if (!data.description) {\n      errors.push({ field: 'description', message: 'Description is required' });\n    } else if (typeof data.description !== 'string' || data.description.trim().length === 0) {\n      errors.push({ field: 'description', message: 'Description must be a non-empty string' });\n    } else if (data.description.length > 200", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 271, "end": 278, "startLoc": {"line": 271, "column": 2, "position": 2893}, "endLoc": {"line": 278, "column": 4, "position": 3010}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 148, "end": 155, "startLoc": {"line": 148, "column": 7, "position": 1553}, "endLoc": {"line": 155, "column": 4, "position": 1670}}}, {"format": "typescript", "lines": 16, "fragment": "});\n    }\n\n    if (errors.length > 0) {\n      throw new AppError({\n        message: 'Validation failed',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n        details: { errors },\n      });\n    }\n\n    return {\n      name: data.name.trim(),\n      description: data.description.trim(),\n      resource", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 304, "end": 319, "startLoc": {"line": 304, "column": 7, "position": 3265}, "endLoc": {"line": 319, "column": 9, "position": 3374}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 177, "end": 48, "startLoc": {"line": 177, "column": 7, "position": 1912}, "endLoc": {"line": 48, "column": 5, "position": 348}}}, {"format": "typescript", "lines": 59, "fragment": ",\n    };\n  }\n\n  /**\n   * Validate ID parameter\n   */\n  validateId(id: any, fieldName: string = 'id'): string {\n    if (!id) {\n      throw new AppError({\n        message: `${fieldName} is required`,\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n\n    if (!this.isValidUUID(id)) {\n      throw new AppError({\n        message: `${fieldName} must be a valid UUID`,\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n      });\n    }\n\n    return id;\n  }\n\n  /**\n   * Validate pagination parameters\n   */\n  validatePaginationParams(query: any): {\n    page: number;\n    limit: number;\n    sortBy?: string;\n    sortOrder?: 'asc' | 'desc';\n  } {\n    const page = query.page ? parseInt(query.page, 10) : 1;\n    const limit = query.limit ? parseInt(query.limit, 10) : 10;\n\n    if (isNaN(page) || page < 1) {\n      throw new AppError({\n        message: 'Page must be a positive integer',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n      });\n    }\n\n    if (isNaN(limit) || limit < 1 || limit > 100) {\n      throw new AppError({\n        message: 'Limit must be between 1 and 100',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.INVALID_INPUT,\n      });\n    }\n\n    const result: any = { page, limit };\n\n    if (query.sortBy) {\n      const validSortFields = ['name', 'email'", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 320, "end": 378, "startLoc": {"line": 320, "column": 7, "position": 3389}, "endLoc": {"line": 378, "column": 8, "position": 3843}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 441, "end": 224, "startLoc": {"line": 441, "column": 2, "position": 4014}, "endLoc": {"line": 224, "column": 12, "position": 2035}}}, {"format": "typescript", "lines": 28, "fragment": "];\n      if (!validSortFields.includes(query.sortBy)) {\n        throw new AppError({\n          message: `Invalid sort field. Must be one of: ${validSortFields.join(', ')}`,\n          type: ErrorType.VALIDATION,\n          code: ErrorCode.INVALID_INPUT,\n        });\n      }\n      result.sortBy = query.sortBy;\n    }\n\n    if (query.sortOrder) {\n      if (!['asc', 'desc'].includes(query.sortOrder)) {\n        throw new AppError({\n          message: 'Sort order must be either \"asc\" or \"desc\"',\n          type: ErrorType.VALIDATION,\n          code: ErrorCode.INVALID_INPUT,\n        });\n      }\n      result.sortOrder = query.sortOrder;\n    }\n\n    return result;\n  }\n\n  /**\n   * Check if string is a valid email\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminValidationService.ts", "start": 378, "end": 405, "startLoc": {"line": 378, "column": 9, "position": 3853}, "endLoc": {"line": 405, "column": 6, "position": 4042}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationValidationService.ts", "start": 499, "end": 526, "startLoc": {"line": 499, "column": 7, "position": 4475}, "endLoc": {"line": 526, "column": 6, "position": 4664}}}, {"format": "typescript", "lines": 21, "fragment": ": true },\n              },\n            },\n          },\n        },\n        orderBy: { createdAt: 'desc' },\n      };\n\n      // Apply pagination\n      if (pagination) {\n        const { page = 1, limit = 10, sortBy, sortOrder } = pagination;\n        queryOptions.skip = (page - 1) * limit;\n        queryOptions.take = limit;\n\n        if (sortBy) {\n          queryOptions.orderBy = { [sortBy]: sortOrder || 'desc' };\n        }\n      }\n\n      // Execute queries\n      const [users", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminBusinessService.ts", "start": 184, "end": 204, "startLoc": {"line": 184, "column": 12, "position": 1371}, "endLoc": {"line": 204, "column": 6, "position": 1533}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionBusinessService.ts", "start": 274, "end": 75, "startLoc": {"line": 274, "column": 5, "position": 2153}, "endLoc": {"line": 75, "column": 6, "position": 528}}}, {"format": "typescript", "lines": 16, "fragment": "},\n          include: {\n            user: true,\n          },\n        });\n\n        return admin;\n      });\n\n      return this.mapAdminUserToResponse(result);\n    } catch (error) {\n      if (error instanceof AppError) {\n        throw error;\n      }\n      throw new AppError({\n        message: 'Failed to update admin user'", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminBusinessService.ts", "start": 380, "end": 395, "startLoc": {"line": 380, "column": 11, "position": 2944}, "endLoc": {"line": 395, "column": 30, "position": 3039}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminBusinessService.ts", "start": 300, "end": 315, "startLoc": {"line": 300, "column": 11, "position": 2293}, "endLoc": {"line": 315, "column": 30, "position": 2388}}}, {"format": "typescript", "lines": 40, "fragment": "'MANAGER', 'ADMIN', 'SUPER_ADMIN'];\n\n  /**\n   * Check if user is authorized for the given action\n   */\n  async checkPermission(context: AuthorizationContext): Promise<PermissionResult> {\n    const { user, resource, action } = context;\n\n    if (!user) {\n      return {\n        allowed: false,\n        reason: 'User not authenticated',\n      };\n    }\n\n    // Check role-based permissions\n    const rolePermission = this.checkRolePermission(user.role, resource, action);\n    if (!rolePermission.allowed) {\n      return rolePermission;\n    }\n\n    // Check resource-specific permissions\n    const resourcePermission = await this.checkResourcePermission(context);\n    if (!resourcePermission.allowed) {\n      return resourcePermission;\n    }\n\n    return { allowed: true };\n  }\n\n  /**\n   * Check role-based permissions\n   */\n  private checkRolePermission(\n    userRole: string,\n    resource: string,\n    action: string\n  ): PermissionResult {\n    switch (resource) {\n      case 'dashboard'", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "start": 16, "end": 55, "startLoc": {"line": 16, "column": 2, "position": 90}, "endLoc": {"line": 55, "column": 12, "position": 337}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\services\\AuthorizationService.ts", "start": 16, "end": 59, "startLoc": {"line": 16, "column": 2, "position": 99}, "endLoc": {"line": 59, "column": 15, "position": 356}}}, {"format": "typescript", "lines": 31, "fragment": "}\n\n  /**\n   * Require admin role\n   */\n  requireAdmin(userRole?: string): void {\n    if (!userRole || !this.adminRoles.includes(userRole)) {\n      throw new AppError({\n        message: 'Admin role required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Require manager role or higher\n   */\n  requireManager(userRole?: string): void {\n    if (!userRole || !this.managerRoles.includes(userRole)) {\n      throw new AppError({\n        message: 'Manager role required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n  }\n\n  /**\n   * Check if user has specific role\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "start": 247, "end": 277, "startLoc": {"line": 247, "column": 3, "position": 1629}, "endLoc": {"line": 277, "column": 6, "position": 1805}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 201, "end": 181, "startLoc": {"line": 201, "column": 3, "position": 1329}, "endLoc": {"line": 181, "column": 6, "position": 1148}}}, {"format": "typescript", "lines": 16, "fragment": ": 3,\n    };\n\n    const userLevel = roleHierarchy[userRole] || 0;\n    const requiredLevel = roleHierarchy[requiredRole] || 0;\n\n    return userLevel >= requiredLevel;\n  }\n\n  /**\n   * Get user permissions for a resource\n   */\n  getUserPermissions(userRole: string, resource: string): string[] {\n    const permissions: string[] = [];\n\n    if (resource === 'dashboard'", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "start": 282, "end": 297, "startLoc": {"line": 282, "column": 12, "position": 1861}, "endLoc": {"line": 297, "column": 12, "position": 1970}}, "secondFile": {"name": "src\\controllers\\identity-verification\\services\\IdentityVerificationAuthService.ts", "start": 219, "end": 265, "startLoc": {"line": 219, "column": 2, "position": 1476}, "endLoc": {"line": 265, "column": 18, "position": 1763}}}, {"format": "typescript", "lines": 8, "fragment": ") {\n      if (this.adminRoles.includes(userRole)) {\n        permissions.push('read');\n      }\n      if (this.superAdminRoles.includes(userRole)) {\n        permissions.push('create', 'update', 'delete');\n      }\n    } else if (resource === 'system'", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "start": 308, "end": 315, "startLoc": {"line": 308, "column": 14, "position": 2103}, "endLoc": {"line": 315, "column": 9, "position": 2181}}, "secondFile": {"name": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "start": 301, "end": 308, "startLoc": {"line": 301, "column": 14, "position": 2016}, "endLoc": {"line": 308, "column": 8, "position": 2094}}}, {"format": "typescript", "lines": 74, "fragment": ");\n      }\n    }\n\n    return permissions;\n  }\n\n  /**\n   * Validate authorization context\n   */\n  validateAuthorizationContext(context: AuthorizationContext): void {\n    if (!context.user) {\n      throw new AppError({\n        message: 'User context is required',\n        type: ErrorType.AUTHENTICATION,\n        code: ErrorCode.INVALID_CREDENTIALS,\n      });\n    }\n\n    if (!context.resource) {\n      throw new AppError({\n        message: 'Resource is required',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n\n    if (!context.action) {\n      throw new AppError({\n        message: 'Action is required',\n        type: ErrorType.VALIDATION,\n        code: ErrorCode.MISSING_REQUIRED_FIELD,\n      });\n    }\n  }\n\n  /**\n   * Create authorization context from request\n   */\n  createAuthorizationContext(\n    user: any,\n    resource: string,\n    action: string,\n    resourceId?: string\n  ): AuthorizationContext {\n    return {\n      user: {\n        id: user?.id,\n        role: user?.role,\n        merchantId: user?.merchantId,\n      },\n      resource,\n      action,\n      resourceId,\n    };\n  }\n\n  /**\n   * Handle authorization error\n   */\n  handleAuthorizationError(result: PermissionResult): never {\n    const message = result.reason ?? 'Access denied';\n\n    throw new AppError({\n      message,\n      type: ErrorType.AUTHENTICATION,\n      code: ErrorCode.INVALID_CREDENTIALS,\n      details: {\n        requiredRole: result.requiredRole,\n        requiredPermissions: result.requiredPermissions,\n      },\n    });\n  }\n}", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\services\\AdminAuthorizationService.ts", "start": 317, "end": 390, "startLoc": {"line": 317, "column": 9, "position": 2211}, "endLoc": {"line": 390, "column": 2, "position": 2626}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionAuthService.ts", "start": 282, "end": 327, "startLoc": {"line": 282, "column": 18, "position": 1956}, "endLoc": {"line": 327, "column": 6, "position": 2173}}}, {"format": "typescript", "lines": 23, "fragment": "{\n  /**\n   * Send success response\n   */\n  static sendSuccess<T>(\n    res: Response,\n    data: T,\n    message?: string,\n    statusCode: number = 200,\n    pagination?: {\n      page: number;\n      limit: number;\n      total: number;\n      totalPages: number;\n    }\n  ): void {\n    const response: SuccessResponse<T> = {\n      success: true,\n      data,\n      message,\n      pagination,\n      timestamp: new Date(),\n      requestId: res.locals.requestId ||", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\mappers\\AdminResponseMapper.ts", "start": 23, "end": 45, "startLoc": {"line": 23, "column": 2, "position": 81}, "endLoc": {"line": 45, "column": 3, "position": 229}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 21, "end": 43, "startLoc": {"line": 21, "column": 2, "position": 73}, "endLoc": {"line": 43, "column": 2, "position": 221}}}, {"format": "typescript", "lines": 23, "fragment": "'unknown',\n    };\n\n    res.status(statusCode).json(response);\n  }\n\n  /**\n   * Send error response\n   */\n  static sendError(res: Response, error: AppError | Error, statusCode?: number): void {\n    let errorResponse: ErrorResponse;\n\n    if (error instanceof AppError) {\n      errorResponse = {\n        success: false,\n        error: {\n          message: error.message,\n          code: error.code,\n          type: error.type,\n          details: error.details,\n        },\n        timestamp: new Date(),\n        requestId: res.locals.requestId ||", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\mappers\\AdminResponseMapper.ts", "start": 45, "end": 67, "startLoc": {"line": 45, "column": 2, "position": 231}, "endLoc": {"line": 67, "column": 3, "position": 397}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 43, "end": 65, "startLoc": {"line": 43, "column": 2, "position": 224}, "endLoc": {"line": 65, "column": 2, "position": 390}}}, {"format": "typescript", "lines": 83, "fragment": ");\n  }\n\n  /**\n   * Send validation error response\n   */\n  static sendValidationError(\n    res: Response,\n    errors: any[],\n    message: string = 'Validation failed'\n  ): void {\n    const error = new AppError({\n      message,\n      type: 'VALIDATION' as any,\n      code: 'INVALID_INPUT' as any,\n      details: { errors },\n    });\n\n    this.sendError(res, error, 400);\n  }\n\n  /**\n   * Send authorization error response\n   */\n  static sendAuthorizationError(\n    res: Response,\n    message: string = 'Access denied',\n    requiredRole?: string\n  ): void {\n    const error = new AppError({\n      message,\n      type: 'AUTHENTICATION' as any,\n      code: 'INVALID_CREDENTIALS' as any,\n      details: { requiredRole },\n    });\n\n    this.sendError(res, error, 403);\n  }\n\n  /**\n   * Send not found error response\n   */\n  static sendNotFoundError(res: Response, resource: string = 'Resource'): void {\n    const error = new AppError({\n      message: `${resource} not found`,\n      type: 'NOT_FOUND' as any,\n      code: 'RESOURCE_NOT_FOUND' as any,\n    });\n\n    this.sendError(res, error, 404);\n  }\n\n  /**\n   * Send internal server error response\n   */\n  static sendInternalServerError(res: Response, message: string = 'Internal server error'): void {\n    const error = new AppError({\n      message,\n      type: 'INTERNAL' as any,\n      code: 'INTERNAL_SERVER_ERROR' as any,\n    });\n\n    this.sendError(res, error, 500);\n  }\n\n  /**\n   * Handle async controller method\n   */\n  static asyncHandler(fn: Function) {\n    return (req: any, res: Response, next: Function) => {\n      Promise.resolve(fn(req, res, next)).catch((error) => next(error));\n    };\n  }\n\n  /**\n   * Set response headers for API\n   */\n  static setApiHeaders(res: Response): void {\n    res.setHeader('Content-Type', 'application/json');\n    res.setHeader('X-API-Version', '1.0');\n    res.setHeader('X-Response-Time', Date.now());\n  }\n}", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\mappers\\AdminResponseMapper.ts", "start": 253, "end": 335, "startLoc": {"line": 253, "column": 11, "position": 1666}, "endLoc": {"line": 335, "column": 2, "position": 2245}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 204, "end": 316, "startLoc": {"line": 204, "column": 36, "position": 1323}, "endLoc": {"line": 316, "column": 6, "position": 2057}}}, {"format": "typescript", "lines": 14, "fragment": ", originalError?: Error): AppError {\n    const errorId = uuidv4();\n\n    const error: any = new AppError({\n      message,\n      type: ErrorType.NETWORK,\n      code: ErrorCode.NETWORK_ERROR,\n      statusCode: 503,\n      originalError,\n      requestId: errorId\n    });\n\n    if (originalError) {\n      logger.error(`[${errorId}] Service unavailable: ", "tokens": 0, "firstFile": {"name": "src\\utils\\errors\\ErrorFactory.ts", "start": 351, "end": 364, "startLoc": {"line": 351, "column": 22, "position": 1995}, "endLoc": {"line": 364, "column": 24, "position": 2101}}, "secondFile": {"name": "src\\utils\\errors\\ErrorFactory.ts", "start": 293, "end": 306, "startLoc": {"line": 293, "column": 16, "position": 1669}, "endLoc": {"line": 306, "column": 18, "position": 1775}}}, {"format": "typescript", "lines": 12, "fragment": "extends AppError {\n  constructor(options: {\n    message: string;\n    code?: ErrorCode;\n    details?: any;\n    path?: string;\n    requestId?: string;\n    originalError?: Error;\n  }) {\n    super({\n      message: options.message,\n      type: ErrorType.AUTHENTICATION", "tokens": 0, "firstFile": {"name": "src\\utils\\errors\\AppError.ts", "start": 233, "end": 244, "startLoc": {"line": 233, "column": 2, "position": 1476}, "endLoc": {"line": 244, "column": 15, "position": 1563}}, "secondFile": {"name": "src\\utils\\errors\\AppError.ts", "start": 206, "end": 217, "startLoc": {"line": 206, "column": 2, "position": 1298}, "endLoc": {"line": 217, "column": 11, "position": 1385}}}, {"format": "typescript", "lines": 12, "fragment": "extends AppError {\n  constructor(options: {\n    message: string;\n    code?: ErrorCode;\n    details?: any;\n    path?: string;\n    requestId?: string;\n    originalError?: Error;\n  }) {\n    super({\n      message: options.message,\n      type: ErrorType.AUTHORIZATION", "tokens": 0, "firstFile": {"name": "src\\utils\\errors\\AppError.ts", "start": 260, "end": 271, "startLoc": {"line": 260, "column": 2, "position": 1654}, "endLoc": {"line": 271, "column": 14, "position": 1741}}, "secondFile": {"name": "src\\utils\\errors\\AppError.ts", "start": 206, "end": 217, "startLoc": {"line": 206, "column": 2, "position": 1298}, "endLoc": {"line": 217, "column": 11, "position": 1385}}}, {"format": "typescript", "lines": 12, "fragment": "extends AppError {\n  constructor(options: {\n    message: string;\n    code?: ErrorCode;\n    details?: any;\n    path?: string;\n    requestId?: string;\n    originalError?: Error;\n  }) {\n    super({\n      message: options.message,\n      type: ErrorType.NOT_FOUND", "tokens": 0, "firstFile": {"name": "src\\utils\\errors\\AppError.ts", "start": 287, "end": 298, "startLoc": {"line": 287, "column": 2, "position": 1832}, "endLoc": {"line": 298, "column": 10, "position": 1919}}, "secondFile": {"name": "src\\utils\\errors\\AppError.ts", "start": 206, "end": 217, "startLoc": {"line": 206, "column": 2, "position": 1298}, "endLoc": {"line": 217, "column": 11, "position": 1385}}}, {"format": "typescript", "lines": 11, "fragment": "?: any;\n  expectedError?: any;\n  description?: string;\n  setup?: () => void;\n  cleanup?: () => void;\n  beforeEach?: () => void;\n  afterEach?: () => void;\n  timeout?: number;\n  skip?: boolean;\n  only?: boolean;\n}", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 135, "end": 145, "startLoc": {"line": 135, "column": 15, "position": 980}, "endLoc": {"line": 145, "column": 2, "position": 1078}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 117, "end": 127, "startLoc": {"line": 117, "column": 17, "position": 847}, "endLoc": {"line": 127, "column": 5, "position": 946}}}, {"format": "typescript", "lines": 6, "fragment": "= {\n    status: jest.fn().mockReturnThis(),\n    json: jest.fn().mockReturnThis(),\n    send: jest.fn().mockReturnThis(),\n    end: jest.fn().mockReturnThis(),\n    locals", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 173, "end": 178, "startLoc": {"line": 173, "column": 2, "position": 1255}, "endLoc": {"line": 178, "column": 7, "position": 1320}}, "secondFile": {"name": "src\\tests\\utils\\factories\\MockFactories.ts", "start": 91, "end": 96, "startLoc": {"line": 91, "column": 2, "position": 756}, "endLoc": {"line": 96, "column": 9, "position": 821}}}, {"format": "typescript", "lines": 23, "fragment": ");\n    }\n\n    if (options.afterEach) {\n      options.afterEach();\n    }\n\n    return result;\n  } catch (error) {\n    if (options.expectedError) {\n      expect(error).toEqual(options.expectedError);\n    } else {\n      throw error;\n    }\n  }\n}\n\n/**\n * Create a test suite for a controller\n * @param name Test suite name\n * @param controllerClass Controller class\n * @param tests Test definitions\n */", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 385, "end": 407, "startLoc": {"line": 385, "column": 11, "position": 2660}, "endLoc": {"line": 407, "column": 4, "position": 2754}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 327, "end": 350, "startLoc": {"line": 327, "column": 8, "position": 2265}, "endLoc": {"line": 350, "column": 4, "position": 2359}}}, {"format": "typescript", "lines": 12, "fragment": "= {}\n): Promise<{ req: MockRequest; res: MockResponse; next: jest.Mock }> {\n  const req = options.req || createMockRequest();\n  const res: any =options.res || createMockResponse();\n  const next: any =options.next || createMockNext();\n\n  if (options.beforeEach) {\n    options.beforeEach();\n  }\n\n  if (options.setup) {\n    options.setup(req", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 511, "end": 522, "startLoc": {"line": 511, "column": 2, "position": 3423}, "endLoc": {"line": 522, "column": 4, "position": 3557}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 247, "end": 258, "startLoc": {"line": 247, "column": 2, "position": 1642}, "endLoc": {"line": 258, "column": 11, "position": 1776}}}, {"format": "typescript", "lines": 12, "fragment": "(req, res, next);\n\n    if (options.expectedStatus) {\n      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);\n    }\n\n    if (options.expectedResponse) {\n      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);\n    }\n\n    if (options.cleanup) {\n      options.cleanup(req", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 526, "end": 537, "startLoc": {"line": 526, "column": 11, "position": 3580}, "endLoc": {"line": 537, "column": 4, "position": 3670}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 262, "end": 273, "startLoc": {"line": 262, "column": 2, "position": 1802}, "endLoc": {"line": 273, "column": 11, "position": 1892}}}, {"format": "typescript", "lines": 23, "fragment": ");\n    }\n\n    if (options.afterEach) {\n      options.afterEach();\n    }\n  } catch (error) {\n    if (options.expectedError) {\n      expect(error).toEqual(options.expectedError);\n    } else {\n      throw error;\n    }\n  }\n\n  return { req, res, next };\n}\n\n/**\n * Test validator\n * @param validator Validator function\n * @param options Test options\n * @returns Test result\n */", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 537, "end": 559, "startLoc": {"line": 537, "column": 5, "position": 3677}, "endLoc": {"line": 559, "column": 4, "position": 3781}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 273, "end": 296, "startLoc": {"line": 273, "column": 11, "position": 1893}, "endLoc": {"line": 296, "column": 4, "position": 1997}}}, {"format": "typescript", "lines": 10, "fragment": "=options.req || createMockRequest();\n  const res: any =options.res || createMockResponse();\n  const next: any =options.next || createMockNext();\n\n  if (options.beforeEach) {\n    options.beforeEach();\n  }\n\n  if (options.setup) {\n    options.setup()", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 565, "end": 574, "startLoc": {"line": 565, "column": 2, "position": 3845}, "endLoc": {"line": 574, "column": 2, "position": 3937}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 249, "end": 258, "startLoc": {"line": 249, "column": 2, "position": 1683}, "endLoc": {"line": 258, "column": 11, "position": 1776}}}, {"format": "typescript", "lines": 12, "fragment": ");\n\n    if (options.expectedStatus) {\n      expect(res.status).toHaveBeenCalledWith(options.expectedStatus);\n    }\n\n    if (options.expectedResponse) {\n      expect(res.json).toHaveBeenCalledWith(options.expectedResponse);\n    }\n\n    if (options.cleanup) {\n      options.cleanup()", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 578, "end": 589, "startLoc": {"line": 578, "column": 5, "position": 3973}, "endLoc": {"line": 589, "column": 2, "position": 4055}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 262, "end": 273, "startLoc": {"line": 262, "column": 5, "position": 1810}, "endLoc": {"line": 273, "column": 11, "position": 1892}}}, {"format": "typescript", "lines": 23, "fragment": ");\n    }\n\n    if (options.afterEach) {\n      options.afterEach();\n    }\n\n    return result;\n  } catch (error) {\n    if (options.expectedError) {\n      expect(error).toEqual(options.expectedError);\n    } else {\n      throw error;\n    }\n  }\n}\n\n/**\n * Test utility function\n * @param utility Utility function\n * @param options Test options\n * @returns Test result\n */", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 589, "end": 611, "startLoc": {"line": 589, "column": 2, "position": 4055}, "endLoc": {"line": 611, "column": 4, "position": 4149}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 327, "end": 350, "startLoc": {"line": 327, "column": 8, "position": 2265}, "endLoc": {"line": 350, "column": 4, "position": 2359}}}, {"format": "typescript", "lines": 27, "fragment": ");\n    }\n\n    if (options.cleanup) {\n      options.cleanup();\n    }\n\n    if (options.afterEach) {\n      options.afterEach();\n    }\n\n    return result;\n  } catch (error) {\n    if (options.expectedError) {\n      expect(error).toEqual(options.expectedError);\n    } else {\n      throw error;\n    }\n  }\n}\n\n/**\n * Create a test suite for middleware\n * @param name Test suite name\n * @param middleware Middleware function\n * @param tests Test definitions\n */", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 630, "end": 656, "startLoc": {"line": 630, "column": 15, "position": 4302}, "endLoc": {"line": 656, "column": 4, "position": 4419}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 585, "end": 350, "startLoc": {"line": 585, "column": 17, "position": 4032}, "endLoc": {"line": 350, "column": 4, "position": 2359}}}, {"format": "typescript", "lines": 12, "fragment": "}\n): void {\n  describe(name, () => {\n    Object.entries(tests).forEach(([testName, test]) => {\n      const testFn: any =test.skip ? it.skip : test.only ? it.only : it;\n\n      testFn(test.description || testName, async () => {\n        if (test.beforeEach) {\n          test.beforeEach();\n        }\n\n        await testValidator", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 698, "end": 709, "startLoc": {"line": 698, "column": 3, "position": 4706}, "endLoc": {"line": 709, "column": 14, "position": 4835}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 662, "end": 673, "startLoc": {"line": 662, "column": 3, "position": 4460}, "endLoc": {"line": 673, "column": 15, "position": 4589}}}, {"format": "typescript", "lines": 12, "fragment": "}\n): void {\n  describe(name, () => {\n    Object.entries(tests).forEach(([testName, test]) => {\n      const testFn: any =test.skip ? it.skip : test.only ? it.only : it;\n\n      testFn(test.description || testName, async () => {\n        if (test.beforeEach) {\n          test.beforeEach();\n        }\n\n        await testUtility", "tokens": 0, "firstFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 734, "end": 745, "startLoc": {"line": 734, "column": 3, "position": 4952}, "endLoc": {"line": 745, "column": 12, "position": 5081}}, "secondFile": {"name": "src\\tests\\utils\\TestUtility.ts", "start": 662, "end": 673, "startLoc": {"line": 662, "column": 3, "position": 4460}, "endLoc": {"line": 673, "column": 15, "position": 4589}}}, {"format": "typescript", "lines": 16, "fragment": ";\nimport { IVerificationStrategy, VerificationRequest, VerificationResult } from \"../../interfaces/verification/IVerificationStrategy\";\nimport { VerificationStrategyFactory } from \"../../factories/verification/VerificationStrategyFactory\";\nimport { logger } from \"../../lib/logger\";\nimport { VerificationPreProcessor } from \"./processors/VerificationPreProcessor\";\nimport { VerificationPostProcessor } from \"./processors/VerificationPostProcessor\";\nimport { VerificationChain, VerificationChainResult } from \"./VerificationChain\";\nimport { VerificationPolicy } from \"./policy/VerificationPolicy\";\nimport { verificationPolicyManager } from \"./policy/VerificationPolicyManager\";\nimport { eventBus } from \"../../lib/EventBus\";\nimport { VerificationResult } from '../types';\n\n\n/**\n * Verification service\n */", "tokens": 0, "firstFile": {"name": "src\\services\\verification\\VerificationService.ts", "start": 18, "end": 33, "startLoc": {"line": 18, "column": 11, "position": 155}, "endLoc": {"line": 33, "column": 4, "position": 298}}, "secondFile": {"name": "src\\services\\verification\\VerificationService.ts", "start": 8, "end": 19, "startLoc": {"line": 8, "column": 17, "position": 16}, "endLoc": {"line": 19, "column": 7, "position": 157}}}, {"format": "typescript", "lines": 37, "fragment": ",\n                    status: BinanceVerificationStatus.FAILED\n                };\n            }\n\n            // Check transaction status\n            if (transaction.status === BinanceTransactionStatus.COMPLETED) {\n                return {\n                    success: true,\n                    transaction,\n                    message: \"Transaction verified\",\n                    status: BinanceVerificationStatus.VERIFIED\n                };\n            } else if (transaction.status === BinanceTransactionStatus.PENDING) {\n                return {\n                    success: false,\n                    transaction,\n                    message: \"Transaction is still pending\",\n                    status: BinanceVerificationStatus.PENDING\n                };\n            } else if (transaction.status === BinanceTransactionStatus.EXPIRED) {\n                return {\n                    success: false,\n                    transaction,\n                    message: \"Transaction has expired\",\n                    status: BinanceVerificationStatus.EXPIRED\n                };\n            } else {\n                return {\n                    success: false,\n                    transaction,\n                    message: `Transaction failed with status: ${transaction.status}`,\n                    status: BinanceVerificationStatus.FAILED\n                };\n            }\n        } catch (error) {\n            logger.error(`Error verifying Binance TRC20 transaction: ", "tokens": 0, "firstFile": {"name": "src\\services\\verification\\binance-verification.service.ts", "start": 173, "end": 209, "startLoc": {"line": 173, "column": 39, "position": 1053}, "endLoc": {"line": 209, "column": 45, "position": 1305}}, "secondFile": {"name": "src\\services\\verification\\binance-verification.service.ts", "start": 79, "end": 115, "startLoc": {"line": 79, "column": 30, "position": 475}, "endLoc": {"line": 115, "column": 43, "position": 727}}}, {"format": "typescript", "lines": 19, "fragment": ";\nimport { logger } from \"../../lib/logger\";\nimport { container } from \"../../lib/DIContainer\";\nimport { moduleRegistry } from \"../../lib/ModuleRegistry\";\nimport { eventBus } from \"../../lib/EventBus\";\nimport { RBACInitializer } from \"../rbac/RBACInitializer\";\nimport { VerificationService } from \"../verification/VerificationService\";\nimport { PREDEFINED_VERIFICATION_POLICIES } from \"../../config/verification/PredefinedVerificationPolicies\";\nimport { EnhancedPaymentService } from \"../payment/EnhancedPaymentService\";\nimport { EnhancedSubscriptionService } from \"../enhanced-subscription.service\";\nimport { FeeCalculator } from \"../payment/fees/FeeCalculator\";\nimport { PercentageFeeStrategy, TieredFeeStrategy, FixedFeeStrategy } from \"../payment/fees/strategies/CommonFeeStrategies\";\nimport { PaymentRouter } from \"../payment/routing/PaymentRouter\";\nimport { CountryBasedRule, AmountBasedRule, SuccessRateRule } from \"../payment/routing/rules/CommonRoutingRules\";\nimport { OperationalModeService, OperationalMode } from \"./OperationalModeService\";\n\n/**\n * System initializer service\n */", "tokens": 0, "firstFile": {"name": "src\\services\\system\\SystemInitializer.ts", "start": 22, "end": 40, "startLoc": {"line": 22, "column": 27, "position": 213}, "endLoc": {"line": 40, "column": 4, "position": 413}}, "secondFile": {"name": "src\\services\\system\\SystemInitializer.ts", "start": 8, "end": 23, "startLoc": {"line": 8, "column": 17, "position": 16}, "endLoc": {"line": 23, "column": 7, "position": 215}}}, {"format": "typescript", "lines": 14, "fragment": ";\nimport { IPaymentMethod, PaymentRequest, PaymentResult } from \"../../interfaces/payment/IPaymentMethod\";\nimport { PaymentMethodFactory } from \"../../factories/payment/PaymentMethodFactory\";\nimport { PaymentGatewayFactory } from \"../../factories/payment/PaymentGatewayFactory\";\nimport { PaymentMethodType } from \"../../types/payment-method.types\";\nimport { logger } from \"../../lib/logger\";\nimport { SubscriptionService } from \"../subscription.service\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { Merchant, PaymentMethodType } from '../types';\n\n\n/**\n * Payment pre-processor interface\n */", "tokens": 0, "firstFile": {"name": "src\\services\\payment\\EnhancedPaymentService.ts", "start": 16, "end": 29, "startLoc": {"line": 16, "column": 11, "position": 133}, "endLoc": {"line": 29, "column": 4, "position": 254}}, "secondFile": {"name": "src\\services\\payment\\EnhancedPaymentService.ts", "start": 8, "end": 17, "startLoc": {"line": 8, "column": 17, "position": 16}, "endLoc": {"line": 17, "column": 7, "position": 135}}}, {"format": "typescript", "lines": 8, "fragment": ");\n\n          Object.entries(methodDistribution).forEach((([method, data])) => {\n            const methodData: any = data as Record<string, number>;\n\n            if (!methodMetrics[method]) {\n              methodMetrics[method] = {\n                attempts", "tokens": 0, "firstFile": {"name": "src\\services\\monitoring\\verification-alert.service.ts", "start": 338, "end": 345, "startLoc": {"line": 338, "column": 5, "position": 2646}, "endLoc": {"line": 345, "column": 9, "position": 2723}}, "secondFile": {"name": "src\\services\\optimization\\verification-optimization.service.ts", "start": 182, "end": 189, "startLoc": {"line": 182, "column": 5, "position": 775}, "endLoc": {"line": 189, "column": 11, "position": 854}}}, {"format": "typescript", "lines": 18, "fragment": ",\n        timestamp: Date.now(),\n        recvWindow: 5000,\n      };\n\n      // Generate signature\n      const queryString: any = this.buildQueryString(params);\n      const signature: any = crypto\n        .createHmac('sha256', merchantSecretKey)\n        .update(queryString)\n        .digest('hex');\n\n      // Prepare headers\n      const headers: any = {\n        'X-MBX-APIKEY': merchantApiKey,\n      };\n\n      const response: any = await this", "tokens": 0, "firstFile": {"name": "src\\services\\blockchain\\binance-api.service.ts", "start": 485, "end": 502, "startLoc": {"line": 485, "column": 2, "position": 2788}, "endLoc": {"line": 502, "column": 5, "position": 2911}}, "secondFile": {"name": "src\\services\\blockchain\\binance-api.service.ts", "start": 347, "end": 364, "startLoc": {"line": 347, "column": 6, "position": 1858}, "endLoc": {"line": 364, "column": 6, "position": 1981}}}, {"format": "typescript", "lines": 22, "fragment": "} from '@prisma/client';\nimport { Prisma } from '@prisma/client';\nimport { logger, ErrorFactory } from \"../../utils\";\nimport { authMiddleware } from '../../middlewares/auth.middleware';\n\n// Extend the Express Request type to include user property\ndeclare global {\n  namespace Express {\n    interface Request {\n      user?: {\n        id: string;\n        role: string;\n        merchantId?: string;\n      };\n    }\n  }\n}\n\n/**\n * Payment Module\n * This module provides payment functionality with zero duplication\n */", "tokens": 0, "firstFile": {"name": "src\\modules\\payment\\payment.module.ts", "start": 8, "end": 29, "startLoc": {"line": 8, "column": 2, "position": 86}, "endLoc": {"line": 29, "column": 4, "position": 200}}, "secondFile": {"name": "src\\modules\\webhook\\webhook.module.ts", "start": 8, "end": 29, "startLoc": {"line": 8, "column": 2, "position": 86}, "endLoc": {"line": 29, "column": 4, "position": 200}}}, {"format": "typescript", "lines": 12, "fragment": ");\n\n    // Get router, repository, service, and controller from factory\n    const { router, repository, service, controller } = this.moduleFactory.build();\n\n    // Configure router\n    router\n      .addRoute('get', '/:id', controller.getById)\n      .addRoute('post', '/', controller.create)\n      .addRoute('put', '/:id', controller.update)\n      .addRoute('delete', '/:id', controller.delete)\n      .addRoute('get', '/merchant/:merchantId', controller.getByMerchantId", "tokens": 0, "firstFile": {"name": "src\\modules\\payment\\payment.module.ts", "start": 47, "end": 58, "startLoc": {"line": 47, "column": 5, "position": 335}, "endLoc": {"line": 58, "column": 16, "position": 450}}, "secondFile": {"name": "src\\modules\\webhook\\webhook.module.ts", "start": 47, "end": 58, "startLoc": {"line": 47, "column": 5, "position": 335}, "endLoc": {"line": 58, "column": 24, "position": 450}}}, {"format": "typescript", "lines": 11, "fragment": ")\n      .addMiddleware(authMiddleware);\n\n    // Add custom repository methods\n    this.moduleFactory.addRepositoryMethod(\n      'findByMerchantId',\n      async (merchantId: string, options: { limit?: number; offset?: number } = {}) => {\n        try {\n          return await repository.findByFieldWithPagination('merchantId', merchantId, options);\n        } catch (error) {\n          logger.error(`Error finding payments by merchant ID ", "tokens": 0, "firstFile": {"name": "src\\modules\\payment\\payment.module.ts", "start": 59, "end": 69, "startLoc": {"line": 59, "column": 14, "position": 466}, "endLoc": {"line": 69, "column": 40, "position": 572}}, "secondFile": {"name": "src\\modules\\webhook\\webhook.module.ts", "start": 59, "end": 69, "startLoc": {"line": 59, "column": 15, "position": 466}, "endLoc": {"line": 69, "column": 40, "position": 572}}}, {"format": "typescript", "lines": 9, "fragment": ");\n          }\n\n          // Parse pagination parameters\n          const limit: any =parseInt(req.query.limit as string) || 10;\n          const page: any =parseInt(req.query.page as string) || 1;\n          const offset: any =(page - 1) * limit;\n\n          // Get payments", "tokens": 0, "firstFile": {"name": "src\\modules\\payment\\payment.module.ts", "start": 129, "end": 137, "startLoc": {"line": 129, "column": 52, "position": 1003}, "endLoc": {"line": 137, "column": 16, "position": 1091}}, "secondFile": {"name": "src\\modules\\webhook\\webhook.module.ts", "start": 124, "end": 132, "startLoc": {"line": 124, "column": 52, "position": 951}, "endLoc": {"line": 132, "column": 16, "position": 1039}}}, {"format": "typescript", "lines": 6, "fragment": "// Parse pagination parameters\n          const limit: any =parseInt(req.query.limit as string) || 10;\n          const page: any =parseInt(req.query.page as string) || 1;\n          const offset: any =(page - 1) * limit;\n\n          // Get merchant payments", "tokens": 0, "firstFile": {"name": "src\\modules\\merchant\\merchant.module.ts", "start": 311, "end": 316, "startLoc": {"line": 311, "column": 11, "position": 2303}, "endLoc": {"line": 316, "column": 25, "position": 2383}}, "secondFile": {"name": "src\\modules\\webhook\\webhook.module.ts", "start": 127, "end": 132, "startLoc": {"line": 127, "column": 11, "position": 959}, "endLoc": {"line": 132, "column": 16, "position": 1039}}}, {"format": "typescript", "lines": 12, "fragment": ");\n\n    // Get router, repository, service, and controller from factory\n    const { router, repository, service, controller } = this.moduleFactory.build();\n\n    // Configure router\n    router\n      .addRoute('get', '/:id', controller.getById)\n      .addRoute('post', '/', controller.create)\n      .addRoute('put', '/:id', controller.update)\n      .addRoute('delete', '/:id', controller.delete)\n      .addRoute('get', '/search/:query'", "tokens": 0, "firstFile": {"name": "src\\modules\\example\\example.module.ts", "start": 34, "end": 45, "startLoc": {"line": 34, "column": 5, "position": 271}, "endLoc": {"line": 45, "column": 17, "position": 381}}, "secondFile": {"name": "src\\modules\\webhook\\webhook.module.ts", "start": 47, "end": 58, "startLoc": {"line": 47, "column": 5, "position": 335}, "endLoc": {"line": 58, "column": 24, "position": 445}}}, {"format": "typescript", "lines": 10, "fragment": "} = req.params;\n          const { limit, offset } = req.query;\n\n          // Parse pagination options\n          const options: any = {\n            limit: limit ? parseInt(limit as string, 10) : undefined,\n            offset: offset ? parseInt(offset as string, 10) : undefined\n          };\n\n          // Get examples by category", "tokens": 0, "firstFile": {"name": "src\\modules\\example\\example.module.ts", "start": 332, "end": 341, "startLoc": {"line": 332, "column": 2, "position": 2784}, "endLoc": {"line": 341, "column": 28, "position": 2883}}, "secondFile": {"name": "src\\modules\\example\\example.module.ts", "start": 300, "end": 309, "startLoc": {"line": 300, "column": 2, "position": 2505}, "endLoc": {"line": 309, "column": 19, "position": 2604}}}, {"format": "typescript", "lines": 10, "fragment": "} = req.params;\n          const { limit, offset } = req.query;\n\n          // Parse pagination options\n          const options: any = {\n            limit: limit ? parseInt(limit as string, 10) : undefined,\n            offset: offset ? parseInt(offset as string, 10) : undefined\n          };\n\n          // Get examples by status", "tokens": 0, "firstFile": {"name": "src\\modules\\example\\example.module.ts", "start": 364, "end": 373, "startLoc": {"line": 364, "column": 2, "position": 3063}, "endLoc": {"line": 373, "column": 26, "position": 3162}}, "secondFile": {"name": "src\\modules\\example\\example.module.ts", "start": 300, "end": 309, "startLoc": {"line": 300, "column": 2, "position": 2505}, "endLoc": {"line": 309, "column": 19, "position": 2604}}}, {"format": "typescript", "lines": 16, "fragment": "<T> = {\n      success: true,\n      data,\n      message,\n      pagination,\n      timestamp: new Date(),\n      requestId: res.locals.requestId ?? 'unknown',\n    };\n\n    res.status(statusCode).json(response);\n  }\n\n  /**\n   * Send error response\n   */\n  static sendError(res: Response, error: Error", "tokens": 0, "firstFile": {"name": "src\\controllers\\shared\\BaseResponseMapper.ts", "start": 56, "end": 71, "startLoc": {"line": 56, "column": 12, "position": 310}, "endLoc": {"line": 71, "column": 6, "position": 404}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 37, "end": 52, "startLoc": {"line": 37, "column": 16, "position": 173}, "endLoc": {"line": 52, "column": 9, "position": 267}}}, {"format": "typescript", "lines": 21, "fragment": ";\n\n    if (error instanceof AppError) {\n      errorResponse = {\n        success: false,\n        error: {\n          message: error.message,\n          code: error.code,\n          type: error.type,\n          details: error.details,\n        },\n        timestamp: new Date(),\n        requestId: res.locals.requestId ?? 'unknown',\n      };\n\n      statusCode = statusCode ?? error.statusCode ?? 400;\n    } else {\n      errorResponse = {\n        success: false,\n        error: {\n          message: error.message,", "tokens": 0, "firstFile": {"name": "src\\controllers\\shared\\BaseResponseMapper.ts", "start": 72, "end": 92, "startLoc": {"line": 72, "column": 17, "position": 430}, "endLoc": {"line": 92, "column": 2, "position": 592}}, "secondFile": {"name": "src\\controllers\\identity-verification\\mappers\\IdentityVerificationResponseMapper.ts", "start": 53, "end": 73, "startLoc": {"line": 53, "column": 14, "position": 293}, "endLoc": {"line": 73, "column": 2, "position": 456}}}, {"format": "typescript", "lines": 28, "fragment": "(req: Request, res: Response) {\n    try {\n      const { startDate, endDate, period = 'day' } = req.query;\n\n      // Parse dates\n      const parsedStartDate: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n      const parsedEndDate: any = endDate ? new Date(endDate as string) : new Date();\n\n      // Validate dates\n      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {\n        return res.status(400).json({\n          success: false,\n          message: 'Invalid date format',\n        });\n      }\n\n      // Get metrics from database\n      const metrics: any = await prisma.verificationMetrics.findMany({\n        where: { timestamp: {\n            gte: parsedStartDate,\n            lte: parsedEndDate,\n          },\n        },\n        orderBy: { timestamp: 'asc',\n        },\n      });\n\n      // Aggregate method distribution", "tokens": 0, "firstFile": {"name": "src\\controllers\\monitoring\\verification-monitoring.controller.ts", "start": 88, "end": 115, "startLoc": {"line": 88, "column": 29, "position": 610}, "endLoc": {"line": 115, "column": 33, "position": 900}}, "secondFile": {"name": "src\\controllers\\monitoring\\verification-monitoring.controller.ts", "start": 29, "end": 56, "startLoc": {"line": 29, "column": 23, "position": 100}, "endLoc": {"line": 56, "column": 21, "position": 390}}}, {"format": "typescript", "lines": 13, "fragment": ";\nimport { asyncHand<PERSON> } from \"../../middleware/asyncHandler\";\nimport { CrudController } from \"../base/CrudController\";\nimport { MerchantService } from \"../../services/refactored/merchant.service\";\nimport { Merchant, Prisma } from \"@prisma/client\";\nimport { AppError } from \"../../utils/appError\";\nimport { ServiceFactory } from \"../../factories/ServiceFactory\";\nimport { Merchant } from '../types';\n\n\n/**\n * Merchant controller\n */", "tokens": 0, "firstFile": {"name": "src\\controllers\\merchant\\MerchantController.ts", "start": 9, "end": 21, "startLoc": {"line": 9, "column": 11, "position": 110}, "endLoc": {"line": 21, "column": 4, "position": 208}}, "secondFile": {"name": "src\\controllers\\merchant\\MerchantController.ts", "start": 2, "end": 10, "startLoc": {"line": 2, "column": 10, "position": 16}, "endLoc": {"line": 10, "column": 7, "position": 112}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: AuthenticatedRequest, res: Response) => {\n    try {\n      // Authorization\n      const authContext = this.authService.createAuthorizationContext(\n        req.user,\n        'verification',\n        'create'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      this.validationService.validateERC1484Identity", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 73, "end": 87, "startLoc": {"line": 73, "column": 2, "position": 491}, "endLoc": {"line": 87, "column": 24, "position": 607}}, "secondFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 37, "end": 51, "startLoc": {"line": 37, "column": 2, "position": 237}, "endLoc": {"line": 51, "column": 26, "position": 353}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: AuthenticatedRequest, res: Response) => {\n    try {\n      // Authorization\n      const authContext = this.authService.createAuthorizationContext(\n        req.user,\n        'verification',\n        'create'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      this.validationService.validateERC725Identity", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 108, "end": 122, "startLoc": {"line": 108, "column": 2, "position": 729}, "endLoc": {"line": 122, "column": 23, "position": 845}}, "secondFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 37, "end": 51, "startLoc": {"line": 37, "column": 2, "position": 237}, "endLoc": {"line": 51, "column": 26, "position": 353}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: AuthenticatedRequest, res: Response) => {\n    try {\n      // Authorization\n      const authContext = this.authService.createAuthorizationContext(\n        req.user,\n        'verification',\n        'create'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      this.validationService.validateENSVerification", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 143, "end": 157, "startLoc": {"line": 143, "column": 2, "position": 967}, "endLoc": {"line": 157, "column": 24, "position": 1083}}, "secondFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 37, "end": 51, "startLoc": {"line": 37, "column": 2, "position": 237}, "endLoc": {"line": 51, "column": 26, "position": 353}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: AuthenticatedRequest, res: Response) => {\n    try {\n      // Authorization\n      const authContext = this.authService.createAuthorizationContext(\n        req.user,\n        'verification',\n        'create'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      this.validationService.validatePolygonID", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 178, "end": 192, "startLoc": {"line": 178, "column": 2, "position": 1205}, "endLoc": {"line": 192, "column": 18, "position": 1321}}, "secondFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 37, "end": 51, "startLoc": {"line": 37, "column": 2, "position": 237}, "endLoc": {"line": 51, "column": 26, "position": 353}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: AuthenticatedRequest, res: Response) => {\n    try {\n      // Authorization\n      const authContext = this.authService.createAuthorizationContext(\n        req.user,\n        'verification',\n        'create'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      this.validationService.validateWorldcoin", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 213, "end": 227, "startLoc": {"line": 213, "column": 2, "position": 1443}, "endLoc": {"line": 227, "column": 18, "position": 1559}}, "secondFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 37, "end": 51, "startLoc": {"line": 37, "column": 2, "position": 237}, "endLoc": {"line": 51, "column": 26, "position": 353}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: AuthenticatedRequest, res: Response) => {\n    try {\n      // Authorization\n      const authContext = this.authService.createAuthorizationContext(\n        req.user,\n        'verification',\n        'create'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      this.validationService.validateUnstoppableDomains", "tokens": 0, "firstFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 248, "end": 262, "startLoc": {"line": 248, "column": 2, "position": 1681}, "endLoc": {"line": 262, "column": 27, "position": 1797}}, "secondFile": {"name": "src\\controllers\\identity-verification\\IdentityVerificationController.ts", "start": 37, "end": 51, "startLoc": {"line": 37, "column": 2, "position": 237}, "endLoc": {"line": 51, "column": 26, "position": 353}}}, {"format": "typescript", "lines": 11, "fragment": ",\n        req.params.merchantId\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const merchantId = this.validationService.validateMerchantId(req.params.merchantId);\n      const", "tokens": 0, "firstFile": {"name": "src\\controllers\\fraud-detection\\FraudDetectionController.ts", "start": 132, "end": 142, "startLoc": {"line": 132, "column": 16, "position": 948}, "endLoc": {"line": 142, "column": 6, "position": 1032}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\FraudDetectionController.ts", "start": 102, "end": 113, "startLoc": {"line": 102, "column": 14, "position": 727}, "endLoc": {"line": 113, "column": 18, "position": 812}}}, {"format": "typescript", "lines": 11, "fragment": ",\n        req.params.id\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');\n      const", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 148, "end": 158, "startLoc": {"line": 148, "column": 9, "position": 1116}, "endLoc": {"line": 158, "column": 6, "position": 1203}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 89, "end": 100, "startLoc": {"line": 89, "column": 7, "position": 681}, "endLoc": {"line": 100, "column": 18, "position": 769}}}, {"format": "typescript", "lines": 13, "fragment": ",\n        req.params.id\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');\n\n      // Business logic\n      await", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 179, "end": 191, "startLoc": {"line": 179, "column": 9, "position": 1362}, "endLoc": {"line": 191, "column": 6, "position": 1453}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 89, "end": 101, "startLoc": {"line": 89, "column": 7, "position": 681}, "endLoc": {"line": 101, "column": 6, "position": 772}}}, {"format": "typescript", "lines": 13, "fragment": ",\n        'read'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const pagination = this.validationService.validatePaginationParams(req.query);\n\n      // Build filters\n      const filters: CorrelationRuleFilters", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 208, "end": 220, "startLoc": {"line": 208, "column": 20, "position": 1573}, "endLoc": {"line": 220, "column": 23, "position": 1660}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 46, "end": 58, "startLoc": {"line": 46, "column": 20, "position": 304}, "endLoc": {"line": 58, "column": 23, "position": 391}}}, {"format": "typescript", "lines": 6, "fragment": ";\n      if (req.query.enabled !== undefined) filters.enabled = req.query.enabled === 'true';\n      if (req.query.search) filters.search = req.query.search as string;\n\n      // Business logic\n      const result = await this.businessService.getCorrelationRules", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 220, "end": 225, "startLoc": {"line": 220, "column": 2, "position": 1666}, "endLoc": {"line": 225, "column": 20, "position": 1745}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 60, "end": 65, "startLoc": {"line": 60, "column": 4, "position": 453}, "endLoc": {"line": 65, "column": 20, "position": 532}}}, {"format": "typescript", "lines": 14, "fragment": ",\n        'read',\n        req.params.id\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const ruleId = this.validationService.validateId(req.params.id, 'Rule ID');\n\n      // Business logic\n      const rule = await this.businessService.getCorrelationRule", "tokens": 0, "firstFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 248, "end": 261, "startLoc": {"line": 248, "column": 20, "position": 1890}, "endLoc": {"line": 261, "column": 19, "position": 1997}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 88, "end": 101, "startLoc": {"line": 88, "column": 20, "position": 677}, "endLoc": {"line": 101, "column": 19, "position": 784}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: AuthenticatedRequest, res: Response) => {\n    try {\n      // Authorization\n      const authContext = this.authService.createAuthorizationContext(\n        req.user,\n        'dashboard',\n        'read'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Business logic\n      const statistics", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\AdminController.ts", "start": 68, "end": 82, "startLoc": {"line": 68, "column": 2, "position": 436}, "endLoc": {"line": 82, "column": 11, "position": 550}}, "secondFile": {"name": "src\\controllers\\admin\\AdminController.ts", "start": 42, "end": 56, "startLoc": {"line": 42, "column": 2, "position": 249}, "endLoc": {"line": 56, "column": 14, "position": 363}}}, {"format": "typescript", "lines": 13, "fragment": ",\n        'read'\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const pagination = this.validationService.validatePaginationParams(req.query);\n\n      // Build filters\n      const filters: AdminUserFilters", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\AdminController.ts", "start": 99, "end": 111, "startLoc": {"line": 99, "column": 14, "position": 676}, "endLoc": {"line": 111, "column": 17, "position": 763}}, "secondFile": {"name": "src\\controllers\\alert-aggregation\\AlertAggregationController.ts", "start": 46, "end": 58, "startLoc": {"line": 46, "column": 20, "position": 304}, "endLoc": {"line": 58, "column": 23, "position": 391}}}, {"format": "typescript", "lines": 11, "fragment": ",\n        req.params.id\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const userId = this.validationService.validateId(req.params.id, 'User ID');\n      const", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\AdminController.ts", "start": 202, "end": 212, "startLoc": {"line": 202, "column": 9, "position": 1530}, "endLoc": {"line": 212, "column": 6, "position": 1617}}, "secondFile": {"name": "src\\controllers\\admin\\AdminController.ts", "start": 143, "end": 154, "startLoc": {"line": 143, "column": 7, "position": 1087}, "endLoc": {"line": 154, "column": 18, "position": 1175}}}, {"format": "typescript", "lines": 13, "fragment": ",\n        req.params.id\n      );\n      const permission = await this.authService.checkPermission(authContext);\n      if (!permission.allowed) {\n        this.authService.handleAuthorizationError(permission);\n      }\n\n      // Validation\n      const userId = this.validationService.validateId(req.params.id, 'User ID');\n\n      // Business logic\n      await", "tokens": 0, "firstFile": {"name": "src\\controllers\\admin\\AdminController.ts", "start": 233, "end": 245, "startLoc": {"line": 233, "column": 9, "position": 1776}, "endLoc": {"line": 245, "column": 6, "position": 1867}}, "secondFile": {"name": "src\\controllers\\admin\\AdminController.ts", "start": 143, "end": 155, "startLoc": {"line": 143, "column": 7, "position": 1087}, "endLoc": {"line": 155, "column": 6, "position": 1178}}}, {"format": "typescript", "lines": 9, "fragment": ": {\n      findUnique: jest.fn(),\n      findMany: jest.fn(),\n      create: jest.fn(),\n      update: jest.fn(),\n      delete: jest.fn(),\n      count: jest.fn(),\n    },\n    transaction", "tokens": 0, "firstFile": {"name": "tests\\setup\\jest.setup.ts", "start": 116, "end": 124, "startLoc": {"line": 116, "column": 9, "position": 864}, "endLoc": {"line": 124, "column": 12, "position": 939}}, "secondFile": {"name": "tests\\setup\\jest.setup.ts", "start": 108, "end": 116, "startLoc": {"line": 108, "column": 5, "position": 788}, "endLoc": {"line": 116, "column": 9, "position": 863}}}, {"format": "typescript", "lines": 9, "fragment": ": {\n      findUnique: jest.fn(),\n      findMany: jest.fn(),\n      create: jest.fn(),\n      update: jest.fn(),\n      delete: jest.fn(),\n      count: jest.fn(),\n    },\n    identityVerification", "tokens": 0, "firstFile": {"name": "tests\\setup\\jest.setup.ts", "start": 124, "end": 132, "startLoc": {"line": 124, "column": 12, "position": 940}, "endLoc": {"line": 132, "column": 21, "position": 1015}}, "secondFile": {"name": "tests\\setup\\jest.setup.ts", "start": 108, "end": 116, "startLoc": {"line": 108, "column": 5, "position": 788}, "endLoc": {"line": 116, "column": 9, "position": 863}}}, {"format": "typescript", "lines": 9, "fragment": ": {\n      findUnique: jest.fn(),\n      findMany: jest.fn(),\n      create: jest.fn(),\n      update: jest.fn(),\n      delete: jest.fn(),\n      count: jest.fn(),\n    },\n    riskAssessment", "tokens": 0, "firstFile": {"name": "tests\\setup\\jest.setup.ts", "start": 132, "end": 140, "startLoc": {"line": 132, "column": 21, "position": 1016}, "endLoc": {"line": 140, "column": 15, "position": 1091}}, "secondFile": {"name": "tests\\setup\\jest.setup.ts", "start": 108, "end": 116, "startLoc": {"line": 108, "column": 5, "position": 788}, "endLoc": {"line": 116, "column": 9, "position": 863}}}, {"format": "typescript", "lines": 8, "fragment": ": {\n      findUnique: jest.fn(),\n      findMany: jest.fn(),\n      create: jest.fn(),\n      update: jest.fn(),\n      delete: jest.fn(),\n      count: jest.fn(),\n      aggregate", "tokens": 0, "firstFile": {"name": "tests\\setup\\jest.setup.ts", "start": 140, "end": 147, "startLoc": {"line": 140, "column": 15, "position": 1092}, "endLoc": {"line": 147, "column": 10, "position": 1163}}, "secondFile": {"name": "tests\\setup\\jest.setup.ts", "start": 108, "end": 115, "startLoc": {"line": 108, "column": 5, "position": 788}, "endLoc": {"line": 115, "column": 2, "position": 859}}}, {"format": "typescript", "lines": 9, "fragment": "// Print database connection info\n    console.log('\\n📊 Database connection info:');\n    console.log(`🔗 URL: ${process.env.DATABASE_URL?.replace(/:[^:]*@/, ':****@')}`);\n    console.log(`🏠 Host: ${process.env.DB_HOST || 'localhost'}`);\n    console.log(`🔢 Port: ${process.env.DB_PORT || '5432'}`);\n    console.log(`👤 Username: ${process.env.DB_USERNAME || 'postgres'}`);\n    console.log(`📚 Database: ${process.env.DB_NAME || 'amazingpay'}`);\n\n  }", "tokens": 0, "firstFile": {"name": "src\\utils\\test-connection.ts", "start": 48, "end": 56, "startLoc": {"line": 48, "column": 5, "position": 345}, "endLoc": {"line": 56, "column": 2, "position": 468}}, "secondFile": {"name": "src\\utils\\test-db-connection.ts", "start": 26, "end": 34, "startLoc": {"line": 26, "column": 5, "position": 154}, "endLoc": {"line": 34, "column": 24, "position": 277}}}, {"format": "typescript", "lines": 17, "fragment": "} catch (error) {\n    console.error('❌ Database connection failed:', error);\n    console.log('\\n⚠️ Please check your database configuration in the .env file.');\n    console.log('⚠️ Make sure PostgreSQL is running and accessible.');\n    console.log('⚠️ You may need to create the database: CREATE DATABASE amazingpay;');\n  } finally {\n    try {\n      await prisma.$disconnect();\n      console.log('🔌 Database connection closed.');\n    } catch (disconnectError) {\n      console.error('❌ Error disconnecting from database:', disconnectError);\n    }\n  }\n}\n\n// Run the test\ntestConnection", "tokens": 0, "firstFile": {"name": "src\\utils\\test-connection.ts", "start": 56, "end": 72, "startLoc": {"line": 56, "column": 3, "position": 468}, "endLoc": {"line": 72, "column": 15, "position": 582}}, "secondFile": {"name": "src\\utils\\test-db-connection.ts", "start": 45, "end": 61, "startLoc": {"line": 45, "column": 3, "position": 372}, "endLoc": {"line": 61, "column": 23, "position": 486}}}, {"format": "typescript", "lines": 36, "fragment": ", {\n    path: req.path,\n    method: req.method,\n    error: err.message,\n    stack: err.stack,\n    requestId: req.id\n  });\n\n  // Create error response\n  const errorResponse: ErrorResponse = {\n    status: 'error',\n    statusCode: err instanceof AppError ? err.statusCode : 500,\n    message: err.message || 'Internal server error',\n    timestamp: new Date().toISOString(),\n    path: req.originalUrl || req.url\n  };\n\n  // Add request ID if available\n  if (req.id) {\n    errorResponse.requestId = req.id;\n  }\n\n  // Add error code if available\n  if (err instanceof AppError && err.code) {\n    errorResponse.code = err.code;\n  }\n\n  // Add error details if available\n  if (err instanceof AppError && err.details) {\n    errorResponse.details = err.details;\n  }\n\n  // Add stack trace in development\n  if (!isProduction()) {\n    errorResponse.stack = err.stack;\n    errorResponse", "tokens": 0, "firstFile": {"name": "src\\utils\\error-handler.ts", "start": 128, "end": 163, "startLoc": {"line": 128, "column": 24, "position": 809}, "endLoc": {"line": 163, "column": 14, "position": 1087}}, "secondFile": {"name": "src\\utils\\error-handler.ts", "start": 47, "end": 82, "startLoc": {"line": 47, "column": 20, "position": 251}, "endLoc": {"line": 82, "column": 2, "position": 529}}}, {"format": "typescript", "lines": 13, "fragment": "const verificationMethod: any = await prisma.verificationMethod.findUnique({\n      where: { id },\n    });\n\n    if (!verificationMethod) {\n      throw new AppError({\n        message: 'Verification method not found',\n        type: ErrorType.NOT_FOUND,\n        code: ErrorCode.RESOURCE_NOT_FOUND,\n      });\n    }\n\n    // Delete verification method", "tokens": 0, "firstFile": {"name": "src\\services\\verification-method.service.ts", "start": 186, "end": 198, "startLoc": {"line": 186, "column": 5, "position": 1491}, "endLoc": {"line": 198, "column": 30, "position": 1581}}, "secondFile": {"name": "src\\services\\verification-method.service.ts", "start": 134, "end": 146, "startLoc": {"line": 134, "column": 5, "position": 1045}, "endLoc": {"line": 146, "column": 22, "position": 1135}}}, {"format": "typescript", "lines": 13, "fragment": "prisma.transaction.findUnique({\n      where: { id: data.transactionId },\n    });\n\n    if (!transaction) {\n      throw new AppError({\n        message: 'Transaction not found',\n        type: ErrorType.NOT_FOUND,\n        code: ErrorCode.RESOURCE_NOT_FOUND,\n      });\n    }\n\n    // Check if transaction is for the correct payment method", "tokens": 0, "firstFile": {"name": "src\\services\\verification-method.service.ts", "start": 262, "end": 274, "startLoc": {"line": 262, "column": 2, "position": 2041}, "endLoc": {"line": 274, "column": 58, "position": 2125}}, "secondFile": {"name": "src\\controllers\\fraud-detection\\services\\FraudDetectionBusinessService.ts", "start": 41, "end": 53, "startLoc": {"line": 41, "column": 2, "position": 216}, "endLoc": {"line": 53, "column": 16, "position": 300}}}, {"format": "typescript", "lines": 13, "fragment": ": string): Promise<{ success: boolean }> {\n    try {\n      // Get user\n      const user: any = await this.prisma.user.findUnique({\n        where: { id: userId },\n        select: { id: true, twoFactorEnabled: true, twoFactorSecret: true },\n      });\n\n      if (!user) {\n        throw createNotFoundError('User not found');\n      }\n\n      if (!", "tokens": 0, "firstFile": {"name": "src\\services\\two-factor-auth.service.ts", "start": 190, "end": 202, "startLoc": {"line": 190, "column": 6, "position": 1232}, "endLoc": {"line": 202, "column": 2, "position": 1355}}, "secondFile": {"name": "src\\services\\two-factor-auth.service.ts", "start": 142, "end": 155, "startLoc": {"line": 142, "column": 7, "position": 890}, "endLoc": {"line": 155, "column": 5, "position": 1015}}}, {"format": "typescript", "lines": 12, "fragment": "= true;\n\n    while (hasMoreData) {\n      const batch = await this.getDataBatch(reportType, parameters, offset, this.BATCH_SIZE);\n      \n      if (batch.length === 0) {\n        hasMoreData = false;\n        break;\n      }\n\n      if (isFirstBatch && batch.length > 0) {\n        // Add headers for the first batch", "tokens": 0, "firstFile": {"name": "src\\services\\report-optimization.service.ts", "start": 107, "end": 118, "startLoc": {"line": 107, "column": 2, "position": 882}, "endLoc": {"line": 118, "column": 35, "position": 978}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 48, "end": 59, "startLoc": {"line": 48, "column": 2, "position": 375}, "endLoc": {"line": 59, "column": 37, "position": 471}}}, {"format": "typescript", "lines": 15, "fragment": ");\n    \n    let offset = 0;\n    let hasMoreData = true;\n    let isFirstBatch = true;\n\n    while (hasMoreData) {\n      const batch = await this.getDataBatch(reportType, parameters, offset, this.BATCH_SIZE);\n      \n      if (batch.length === 0) {\n        hasMoreData = false;\n        break;\n      }\n\n      for", "tokens": 0, "firstFile": {"name": "src\\services\\report-optimization.service.ts", "start": 158, "end": 172, "startLoc": {"line": 158, "column": 4, "position": 1250}, "endLoc": {"line": 172, "column": 4, "position": 1357}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 103, "end": 117, "startLoc": {"line": 103, "column": 9, "position": 852}, "endLoc": {"line": 117, "column": 3, "position": 959}}}, {"format": "typescript", "lines": 7, "fragment": ".findMany({\n      where: {\n        ...(startDate && { createdAt: { gte: new Date(startDate) } }),\n        ...(endDate && { createdAt: { lte: new Date(endDate) } }),\n        ...(status && { status })\n      },\n      include", "tokens": 0, "firstFile": {"name": "src\\services\\report-optimization.service.ts", "start": 354, "end": 360, "startLoc": {"line": 354, "column": 13, "position": 2854}, "endLoc": {"line": 360, "column": 8, "position": 2944}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 277, "end": 283, "startLoc": {"line": 277, "column": 9, "position": 2203}, "endLoc": {"line": 283, "column": 8, "position": 2293}}}, {"format": "typescript", "lines": 8, "fragment": "({\n      where: {\n        ...(startDate && { createdAt: { gte: new Date(startDate) } }),\n        ...(endDate && { createdAt: { lte: new Date(endDate) } }),\n        ...(merchantId && { merchantId }),\n        ...(status && { status })\n      }\n    }", "tokens": 0, "firstFile": {"name": "src\\services\\report-optimization.service.ts", "start": 436, "end": 443, "startLoc": {"line": 436, "column": 6, "position": 3532}, "endLoc": {"line": 443, "column": 2, "position": 3634}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 233, "end": 239, "startLoc": {"line": 233, "column": 9, "position": 1835}, "endLoc": {"line": 239, "column": 2, "position": 1935}}}, {"format": "typescript", "lines": 7, "fragment": "({\n      where: {\n        ...(startDate && { createdAt: { gte: new Date(startDate) } }),\n        ...(endDate && { createdAt: { lte: new Date(endDate) } }),\n        ...(status && { status })\n      }\n    }", "tokens": 0, "firstFile": {"name": "src\\services\\report-optimization.service.ts", "start": 449, "end": 455, "startLoc": {"line": 449, "column": 6, "position": 3693}, "endLoc": {"line": 455, "column": 2, "position": 3780}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 277, "end": 282, "startLoc": {"line": 277, "column": 9, "position": 2205}, "endLoc": {"line": 282, "column": 2, "position": 2290}}}, {"format": "typescript", "lines": 7, "fragment": "({\n      where: {\n        ...(startDate && { createdAt: { gte: new Date(startDate) } }),\n        ...(endDate && { createdAt: { lte: new Date(endDate) } }),\n        ...(type && { type })\n      }\n    }", "tokens": 0, "firstFile": {"name": "src\\services\\report-optimization.service.ts", "start": 461, "end": 467, "startLoc": {"line": 461, "column": 6, "position": 3839}, "endLoc": {"line": 467, "column": 2, "position": 3926}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 311, "end": 316, "startLoc": {"line": 311, "column": 9, "position": 2501}, "endLoc": {"line": 316, "column": 2, "position": 2586}}}, {"format": "typescript", "lines": 12, "fragment": ".count({\n      where: {\n        ...(startDate && { createdAt: { gte: new Date(startDate) } }),\n        ...(endDate && { createdAt: { lte: new Date(endDate) } }),\n        ...(status && { status })\n      }\n    });\n  }\n\n  /**\n   * Get average record size in bytes for different report types\n   */", "tokens": 0, "firstFile": {"name": "src\\services\\report-optimization.service.ts", "start": 473, "end": 484, "startLoc": {"line": 473, "column": 13, "position": 3983}, "endLoc": {"line": 484, "column": 6, "position": 4081}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 449, "end": 458, "startLoc": {"line": 449, "column": 9, "position": 3691}, "endLoc": {"line": 458, "column": 8, "position": 3789}}}, {"format": "typescript", "lines": 14, "fragment": "> {\n    try {\n      // Get user with roles and permissions\n      const user: any = await this.prisma.user.findUnique({\n        where: { id: userId },\n        include: {\n          roles: {\n            include: { permissions: true },\n          },\n        },\n      });\n\n      if (!user || !user.roles || user.roles.length === 0) {\n        return [", "tokens": 0, "firstFile": {"name": "src\\services\\rbac.service.ts", "start": 182, "end": 195, "startLoc": {"line": 182, "column": 2, "position": 1360}, "endLoc": {"line": 195, "column": 2, "position": 1480}}, "secondFile": {"name": "src\\services\\rbac.service.ts", "start": 137, "end": 150, "startLoc": {"line": 137, "column": 8, "position": 1051}, "endLoc": {"line": 150, "column": 6, "position": 1171}}}, {"format": "typescript", "lines": 26, "fragment": "let successCount: number = 0;\n\n      for (const sub of subscriptions) {\n        try {\n          const subscription: any = JSON.parse(sub.subscription);\n          const success: any = await this.sendNotification(\n            subscription,\n            title,\n            body,\n            icon,\n            data,\n            url\n          );\n\n          if (success) {\n            successCount++;\n          }\n        } catch (error) {\n          logger.error('Error sending notification to subscription', {\n            error,\n            subscriptionId: sub.id,\n          });\n        }\n      }\n\n      logger.info('Push notifications sent to merchant'", "tokens": 0, "firstFile": {"name": "src\\services\\push-notification.service.ts", "start": 188, "end": 213, "startLoc": {"line": 188, "column": 7, "position": 1128}, "endLoc": {"line": 213, "column": 38, "position": 1294}}, "secondFile": {"name": "src\\services\\push-notification.service.ts", "start": 117, "end": 142, "startLoc": {"line": 117, "column": 7, "position": 706}, "endLoc": {"line": 142, "column": 34, "position": 872}}}, {"format": "typescript", "lines": 10, "fragment": ": string,\n        amount: string,\n        currency: string,\n        merchantApiKey?: string,\n        merchantSecretKey?: string\n    ): Promise<PaymentVerificationResult> {\n        return this.executeDbOperation(\n            async () => {\n                if (!merchantApiKey || !merchantSecretKey) {\n                    throw this.paymentError(\"Merchant API key and secret key are required for Binance C2C verification\"", "tokens": 0, "firstFile": {"name": "src\\services\\payment-verification.service.ts", "start": 169, "end": 178, "startLoc": {"line": 169, "column": 5, "position": 1009}, "endLoc": {"line": 178, "column": 76, "position": 1094}}, "secondFile": {"name": "src\\services\\payment-verification.service.ts", "start": 123, "end": 132, "startLoc": {"line": 123, "column": 14, "position": 760}, "endLoc": {"line": 132, "column": 76, "position": 845}}}, {"format": "typescript", "lines": 18, "fragment": "},\n                include: { merchant: {\n                        select: { id: true,\n                            name: true,\n                            email: true,\n                            businessName: true\n                        }\n                    }\n                }\n            });\n\n            if (!paymentPage) {\n                return null;\n            }\n\n            return paymentPage;\n        } catch (error) {\n            console.error(\"Error getting payment page by slug:\"", "tokens": 0, "firstFile": {"name": "src\\services\\payment-page.service.ts", "start": 178, "end": 195, "startLoc": {"line": 178, "column": 17, "position": 1456}, "endLoc": {"line": 195, "column": 38, "position": 1559}}, "secondFile": {"name": "src\\services\\payment-page.service.ts", "start": 148, "end": 165, "startLoc": {"line": 148, "column": 2, "position": 1264}, "endLoc": {"line": 165, "column": 36, "position": 1367}}}, {"format": "typescript", "lines": 10, "fragment": "for (const paymentMethodId of data.paymentMethodIds) {\n                const paymentMethod: any = await prisma.paymentMethod.findUnique({\n                    where: { id: paymentMethodId }\n                });\n\n                if (!paymentMethod) {\n                    throw new AppError(`Payment method with ID ${paymentMethodId} not found`, 404);\n                }\n\n                if (paymentMethod.merchantId !== paymentPage", "tokens": 0, "firstFile": {"name": "src\\services\\payment-page.service.ts", "start": 230, "end": 239, "startLoc": {"line": 230, "column": 13, "position": 1844}, "endLoc": {"line": 239, "column": 12, "position": 1941}}, "secondFile": {"name": "src\\services\\payment-page.service.ts", "start": 86, "end": 95, "startLoc": {"line": 86, "column": 13, "position": 662}, "endLoc": {"line": 95, "column": 5, "position": 759}}}, {"format": "typescript", "lines": 13, "fragment": "const paymentPage: any = await prisma.paymentPage.findUnique({\n            where: { id }\n        });\n\n        if (!paymentPage) {\n            throw new AppError({\n            message: \"Payment page not found\",\n            type: ErrorType.NOT_FOUND,\n            code: ErrorCode.RESOURCE_NOT_FOUND\n        });\n        }\n\n        // Check if there are any transactions using this payment page", "tokens": 0, "firstFile": {"name": "src\\services\\payment-page.service.ts", "start": 295, "end": 307, "startLoc": {"line": 295, "column": 9, "position": 2440}, "endLoc": {"line": 307, "column": 63, "position": 2528}}, "secondFile": {"name": "src\\services\\payment-page.service.ts", "start": 202, "end": 214, "startLoc": {"line": 202, "column": 9, "position": 1603}, "endLoc": {"line": 214, "column": 54, "position": 1691}}}, {"format": "typescript", "lines": 12, "fragment": "): Promise<MerchantWebhook> {\n        try {\n            // Check if webhook exists\n            const webhook: any = await prisma.merchantWebhook.findUnique({\n                where: { id: webhookId }\n            });\n\n            if (!webhook) {\n                throw this.genericError(\"Webhook not found\", 404, ApiErrorCode.NOT_FOUND);\n            }\n\n            const", "tokens": 0, "firstFile": {"name": "src\\services\\merchant-self-service.service.ts", "start": 317, "end": 328, "startLoc": {"line": 317, "column": 7, "position": 2005}, "endLoc": {"line": 328, "column": 6, "position": 2096}}, "secondFile": {"name": "src\\services\\merchant-self-service.service.ts", "start": 267, "end": 278, "startLoc": {"line": 267, "column": 5, "position": 1636}, "endLoc": {"line": 278, "column": 3, "position": 1727}}}, {"format": "typescript", "lines": 12, "fragment": "> {\n        try {\n            // Check if merchant exists\n            const merchant: any = await this.prisma.merchant.findUnique({\n                where: { id: merchantId }\n            });\n\n            if (!merchant) {\n                throw this.genericError(\"Merchant not found\", 404, ApiErrorCode.NOT_FOUND);\n            }\n\n            // Create ticket", "tokens": 0, "firstFile": {"name": "src\\services\\merchant-relationship.service.ts", "start": 239, "end": 250, "startLoc": {"line": 239, "column": 26, "position": 1367}, "endLoc": {"line": 250, "column": 17, "position": 1454}}, "secondFile": {"name": "src\\services\\merchant-segmentation.service.ts", "start": 182, "end": 193, "startLoc": {"line": 182, "column": 9, "position": 799}, "endLoc": {"line": 193, "column": 28, "position": 886}}}, {"format": "typescript", "lines": 12, "fragment": "> {\n        try {\n            // Check if ticket exists\n            const ticket: any = await this.prisma.merchantSupportTicket.findUnique({\n                where: { id: ticketId }\n            });\n\n            if (!ticket) {\n                throw this.genericError(\"Support ticket not found\", 404, ApiErrorCode.NOT_FOUND);\n            }\n\n            // Update ticket", "tokens": 0, "firstFile": {"name": "src\\services\\merchant-relationship.service.ts", "start": 372, "end": 383, "startLoc": {"line": 372, "column": 22, "position": 2252}, "endLoc": {"line": 383, "column": 17, "position": 2339}}, "secondFile": {"name": "src\\services\\merchant-relationship.service.ts", "start": 321, "end": 332, "startLoc": {"line": 321, "column": 23, "position": 1913}, "endLoc": {"line": 332, "column": 18, "position": 2000}}}, {"format": "typescript", "lines": 12, "fragment": "> {\n        try {\n            // Check if merchant exists\n            const merchant: any = await this.prisma.merchant.findUnique({\n                where: { id: merchantId }\n            });\n\n            if (!merchant) {\n                throw this.genericError(\"Merchant not found\", 404, ApiErrorCode.NOT_FOUND);\n            }\n\n            // Check if onboarding already exists", "tokens": 0, "firstFile": {"name": "src\\services\\merchant-relationship.service.ts", "start": 419, "end": 430, "startLoc": {"line": 419, "column": 20, "position": 2607}, "endLoc": {"line": 430, "column": 38, "position": 2694}}, "secondFile": {"name": "src\\services\\merchant-segmentation.service.ts", "start": 182, "end": 193, "startLoc": {"line": 182, "column": 9, "position": 799}, "endLoc": {"line": 193, "column": 28, "position": 886}}}, {"format": "typescript", "lines": 31, "fragment": ";\n\n\n/**\n * Risk level enum\n */\nexport enum RiskLevel {\n  LOW = \"LOW\",\n  MEDIUM = \"MEDIUM\",\n  HIGH = \"HIGH\",\n  CRITICAL = \"CRITICAL\",\n}\n\n/**\n * Risk factor enum\n */\nexport enum RiskFactor {\n  AMOUNT = \"AMOUNT\",\n  LOCATION = \"LOCATION\",\n  FREQUENCY = \"FREQUENCY\",\n  TIME = \"TIME\",\n  IP = \"IP\",\n  DEVICE = \"DEVICE\",\n  PAYMENT_METHOD = \"PAYMENT_METHOD\",\n  BEHAVIOR = \"BEHAVIOR\",\n  HISTORY = \"HISTORY\",\n}\n\n/**\n * Risk score interface\n */", "tokens": 0, "firstFile": {"name": "src\\services\\fraud-detection.service.ts", "start": 6, "end": 36, "startLoc": {"line": 6, "column": 11, "position": 70}, "endLoc": {"line": 36, "column": 4, "position": 204}}, "secondFile": {"name": "src\\services\\fraud-detection\\core\\FraudDetectionTypes.ts", "start": 7, "end": 36, "startLoc": {"line": 7, "column": 17, "position": 20}, "endLoc": {"line": 36, "column": 4, "position": 153}}}, {"format": "typescript", "lines": 54, "fragment": ";\n}\n\n/**\n * Fraud detection configuration\n */\nexport interface FraudDetectionConfig {\n  /**\n   * Threshold for flagging transactions (0-100)\n   */\n  flagThreshold: number;\n\n  /**\n   * Threshold for blocking transactions (0-100)\n   */\n  blockThreshold: number;\n\n  /**\n   * Whether to automatically block high-risk transactions\n   */\n  autoBlock: boolean;\n\n  /**\n   * Factor weights (0-1)\n   */\n  factorWeights: {\n    [key in RiskFactor]?: number;\n  };\n\n  /**\n   * High-risk countries\n   */\n  highRiskCountries: string[];\n\n  /**\n   * High-risk IP ranges\n   */\n  highRiskIpRanges: string[];\n\n  /**\n   * Maximum transaction amount before flagging\n   */\n  maxTransactionAmount: number;\n\n  /**\n   * Maximum transactions per hour before flagging\n   */\n  maxTransactionsPerHour: number;\n\n  /**\n   * Maximum transactions per day before flagging\n   */\n  maxTransactionsPerDay: number;\n}", "tokens": 0, "firstFile": {"name": "src\\services\\fraud-detection.service.ts", "start": 94, "end": 147, "startLoc": {"line": 94, "column": 7, "position": 360}, "endLoc": {"line": 147, "column": 2, "position": 494}}, "secondFile": {"name": "src\\services\\fraud-detection\\core\\FraudDetectionTypes.ts", "start": 112, "end": 168, "startLoc": {"line": 112, "column": 7, "position": 358}, "endLoc": {"line": 168, "column": 6, "position": 494}}}, {"format": "typescript", "lines": 22, "fragment": "[]> {\n        try {\n            // Get merchant with subscription\n            const merchant: any = await prisma.merchant.findUnique({\n                where: { id: merchantId },\n                include: { subscription: true }\n            });\n\n            if (!merchant || !merchant.subscription) {\n                return [];\n            }\n\n            // Get subscription plan\n            const plan: any = await prisma.subscriptionPlan.findUnique({\n                where: { id: merchant.subscription.planId }\n            });\n\n            if (!plan) {\n                return [];\n            }\n\n            const", "tokens": 0, "firstFile": {"name": "src\\services\\enhanced-subscription.service.ts", "start": 121, "end": 142, "startLoc": {"line": 121, "column": 23, "position": 724}, "endLoc": {"line": 142, "column": 6, "position": 887}}, "secondFile": {"name": "src\\services\\enhanced-subscription.service.ts", "start": 83, "end": 104, "startLoc": {"line": 83, "column": 18, "position": 467}, "endLoc": {"line": 104, "column": 34, "position": 630}}}, {"format": "typescript", "lines": 16, "fragment": ",\n    startTime: Date,\n    endTime: Date\n  ): Promise<any[]> {\n    try {\n      // Build where clause\n      const where: any = {\n        createdAt: {\n          gte: startTime,\n          lte: endTime,\n        },\n        status: AlertStatus.ACTIVE,\n      };\n\n      // Add type filter if not ANY\n      if (type", "tokens": 0, "firstFile": {"name": "src\\services\\alert-aggregation.service.ts", "start": 398, "end": 413, "startLoc": {"line": 398, "column": 6, "position": 2744}, "endLoc": {"line": 413, "column": 5, "position": 2837}}, "secondFile": {"name": "src\\services\\alert-aggregation.service.ts", "start": 350, "end": 365, "startLoc": {"line": 350, "column": 21, "position": 2452}, "endLoc": {"line": 365, "column": 5, "position": 2545}}}, {"format": "typescript", "lines": 15, "fragment": ";\n      }\n\n      // Exclude aggregated and correlated alerts\n      where.source = { notIn: ['aggregation', 'correlation'] };\n\n      // Get alerts\n      const alerts: any = await prisma.alert.findMany({\n        where,\n        orderBy: { createdAt: 'desc' },\n      });\n\n      return alerts;\n    } catch (error) {\n      logger.error('Error getting secondary alerts for correlation'", "tokens": 0, "firstFile": {"name": "src\\services\\alert-aggregation.service.ts", "start": 458, "end": 472, "startLoc": {"line": 458, "column": 2, "position": 3167}, "endLoc": {"line": 472, "column": 49, "position": 3268}}, "secondFile": {"name": "src\\services\\alert-aggregation.service.ts", "start": 414, "end": 428, "startLoc": {"line": 414, "column": 5, "position": 2854}, "endLoc": {"line": 428, "column": 39, "position": 2955}}}, {"format": "typescript", "lines": 9, "fragment": "=> ({\n      id: transaction.id,\n      reference: transaction.reference,\n      amount: transaction.amount,\n      currency: transaction.currency,\n      status: transaction.status,\n      paymentMethod: transaction.paymentMethod,\n      merchantName: transaction.merchant?.businessName || 'Unknown',\n      merchantEmail", "tokens": 0, "firstFile": {"name": "src\\services\\advanced-report.service.ts", "start": 290, "end": 298, "startLoc": {"line": 290, "column": 2, "position": 2238}, "endLoc": {"line": 298, "column": 14, "position": 2314}}, "secondFile": {"name": "src\\services\\report-optimization.service.ts", "start": 254, "end": 262, "startLoc": {"line": 254, "column": 2, "position": 2015}, "endLoc": {"line": 262, "column": 10, "position": 2091}}}, {"format": "typescript", "lines": 7, "fragment": ",\n      merchantName: transaction.merchant?.businessName || 'Unknown',\n      merchantEmail: transaction.merchant?.contactEmail || 'Unknown',\n      description: transaction.description || '',\n      createdAt: dayjs(transaction.createdAt).format('YYYY-MM-DD HH:mm:ss'),\n      updatedAt: dayjs(transaction.updatedAt).format('YYYY-MM-DD HH:mm:ss'),\n    }", "tokens": 0, "firstFile": {"name": "src\\services\\advanced-report.service.ts", "start": 296, "end": 302, "startLoc": {"line": 296, "column": 14, "position": 2295}, "endLoc": {"line": 302, "column": 2, "position": 2377}}, "secondFile": {"name": "src\\services\\reporting\\generators\\TransactionReportGenerator.ts", "start": 224, "end": 230, "startLoc": {"line": 224, "column": 3, "position": 1664}, "endLoc": {"line": 230, "column": 34, "position": 1746}}}, {"format": "typescript", "lines": 21, "fragment": ", userId, userRole } = parameters;\n\n    // Build where clause for merchant\n    const merchantWhere: any = {};\n\n    if (merchantId) {\n      merchantWhere.id = merchantId;\n    } else if (userRole !== 'ADMIN') {\n      // If not admin and no merchant ID specified, get user's merchant\n      const merchant = await prisma.merchant.findFirst({\n        where: { userId },\n      });\n\n      if (!merchant) {\n        throw new Error('Merchant profile not found');\n      }\n\n      merchantWhere.id = merchant.id;\n    }\n\n    // Get payment methods", "tokens": 0, "firstFile": {"name": "src\\services\\advanced-report.service.ts", "start": 367, "end": 387, "startLoc": {"line": 367, "column": 5, "position": 2934}, "endLoc": {"line": 387, "column": 23, "position": 3082}}, "secondFile": {"name": "src\\services\\advanced-report.service.ts", "start": 309, "end": 329, "startLoc": {"line": 309, "column": 7, "position": 2427}, "endLoc": {"line": 329, "column": 17, "position": 2575}}}, {"format": "typescript", "lines": 6, "fragment": ".findMany({\n      where: {\n        merchantId: merchantWhere.id,\n        ...(startDate && { createdAt: { gte: new Date(startDate) } }),\n        ...(endDate && { createdAt: { lte: new Date(endDate) } }),\n        ...(type", "tokens": 0, "firstFile": {"name": "src\\services\\advanced-report.service.ts", "start": 388, "end": 393, "startLoc": {"line": 388, "column": 14, "position": 3096}, "endLoc": {"line": 393, "column": 5, "position": 3179}}, "secondFile": {"name": "src\\services\\advanced-report.service.ts", "start": 330, "end": 335, "startLoc": {"line": 330, "column": 9, "position": 2589}, "endLoc": {"line": 335, "column": 7, "position": 2672}}}, {"format": "typescript", "lines": 22, "fragment": "(parameters: any): Promise<any[]> {\n    const { startDate, endDate, merchantId, status, userId, userRole } = parameters;\n\n    // Build where clause for merchant\n    const merchantWhere: any = {};\n\n    if (merchantId) {\n      merchantWhere.id = merchantId;\n    } else if (userRole !== 'ADMIN') {\n      // If not admin and no merchant ID specified, get user's merchant\n      const merchant = await prisma.merchant.findFirst({\n        where: { userId },\n      });\n\n      if (!merchant) {\n        throw new Error('Merchant profile not found');\n      }\n\n      merchantWhere.id = merchant.id;\n    }\n\n    // Get subscriptions", "tokens": 0, "firstFile": {"name": "src\\services\\advanced-report.service.ts", "start": 436, "end": 457, "startLoc": {"line": 436, "column": 26, "position": 3507}, "endLoc": {"line": 457, "column": 21, "position": 3687}}, "secondFile": {"name": "src\\services\\advanced-report.service.ts", "start": 308, "end": 329, "startLoc": {"line": 308, "column": 22, "position": 2395}, "endLoc": {"line": 329, "column": 17, "position": 2575}}}, {"format": "typescript", "lines": 14, "fragment": ".findMany({\n      where: {\n        merchantId: merchantWhere.id,\n        ...(startDate && { createdAt: { gte: new Date(startDate) } }),\n        ...(endDate && { createdAt: { lte: new Date(endDate) } }),\n        ...(status && { status }),\n      },\n      include: {\n        merchant: {\n          select: {\n            businessName: true,\n          },\n        },\n        customer", "tokens": 0, "firstFile": {"name": "src\\services\\advanced-report.service.ts", "start": 458, "end": 471, "startLoc": {"line": 458, "column": 13, "position": 3701}, "endLoc": {"line": 471, "column": 9, "position": 3834}}, "secondFile": {"name": "src\\services\\advanced-report.service.ts", "start": 330, "end": 343, "startLoc": {"line": 330, "column": 9, "position": 2589}, "endLoc": {"line": 343, "column": 2, "position": 2722}}}, {"format": "typescript", "lines": 10, "fragment": ".forEach((issue) => {\n        report += `- **${issue.test}**: ${issue.message}\\n`;\n        if (issue.recommendation) {\n          report += `  - Recommendation: ${issue.recommendation}\\n`;\n        }\n      });\n      report += '\\n';\n    }\n\n    if (warnings", "tokens": 0, "firstFile": {"name": "src\\security\\security-audit.ts", "start": 399, "end": 408, "startLoc": {"line": 399, "column": 11, "position": 2540}, "endLoc": {"line": 408, "column": 9, "position": 2620}}, "secondFile": {"name": "src\\security\\security-audit.ts", "start": 388, "end": 397, "startLoc": {"line": 388, "column": 15, "position": 2439}, "endLoc": {"line": 397, "column": 11, "position": 2519}}}, {"format": "typescript", "lines": 10, "fragment": ";\nimport { authenticate, authorize } from \"../middlewares/auth.middleware\";\nimport { validate } from \"../middlewares/validation.middleware\";\nimport { body, param } from \"express-validator\";\nimport { authenticate, authorize } from \"../middlewares/auth.middleware\";\nimport { validate } from \"../middlewares/validation.middleware\";\n\nconst router: any =Router();\n\n// Get all subscription plans - public route", "tokens": 0, "firstFile": {"name": "src\\routes\\subscription.routes.ts", "start": 5, "end": 14, "startLoc": {"line": 5, "column": 41, "position": 39}, "endLoc": {"line": 14, "column": 45, "position": 130}}, "secondFile": {"name": "src\\routes\\system.routes.ts", "start": 5, "end": 14, "startLoc": {"line": 5, "column": 35, "position": 39}, "endLoc": {"line": 14, "column": 41, "position": 130}}}, {"format": "typescript", "lines": 11, "fragment": ";\nimport { authMiddleware } from '../middlewares/authMiddleware';\nimport { roleMiddleware } from '../middlewares/roleMiddleware';\nimport { Merchant } from '../types';\nimport { authMiddleware } from '../middlewares/authMiddleware';\nimport { roleMiddleware } from '../middlewares/roleMiddleware';\nimport { Merchant } from '../types';\n\n\nconst router: any =express.Router();\nconst merchantRelationshipController", "tokens": 0, "firstFile": {"name": "src\\routes\\merchant-relationship.routes.ts", "start": 9, "end": 19, "startLoc": {"line": 9, "column": 50, "position": 25}, "endLoc": {"line": 19, "column": 31, "position": 124}}, "secondFile": {"name": "src\\routes\\merchant-segmentation.routes.ts", "start": 9, "end": 19, "startLoc": {"line": 9, "column": 50, "position": 25}, "endLoc": {"line": 19, "column": 31, "position": 124}}}, {"format": "typescript", "lines": 11, "fragment": ";\nimport { authMiddleware } from '../middlewares/authMiddleware';\nimport { roleMiddleware } from '../middlewares/roleMiddleware';\nimport { Merchant } from '../types';\nimport { authMiddleware } from '../middlewares/authMiddleware';\nimport { roleMiddleware } from '../middlewares/roleMiddleware';\nimport { Merchant } from '../types';\n\n\nconst router: any =express.Router();\nconst enhancedRiskEngineController", "tokens": 0, "firstFile": {"name": "src\\routes\\enhanced-risk-engine.routes.ts", "start": 9, "end": 19, "startLoc": {"line": 9, "column": 49, "position": 25}, "endLoc": {"line": 19, "column": 29, "position": 124}}, "secondFile": {"name": "src\\routes\\merchant-segmentation.routes.ts", "start": 9, "end": 19, "startLoc": {"line": 9, "column": 50, "position": 25}, "endLoc": {"line": 19, "column": 31, "position": 124}}}, {"format": "typescript", "lines": 18, "fragment": ";\n  }>\n): Middleware => {\n  return (req: Request, res: Response, next: NextFunction) => {\n    for (const param of params) {\n      const source: any = param.source || 'query';\n      const value: any =\n        source === 'query'\n          ? (req.query[param.name] as string)\n          : source === 'body'\n          ? req.body[param.name]\n          : req.params[param.name];\n\n      if (param.required && (value === undefined || value === null || value === '')) {\n        return next(new AppError(`${param.name} is required`, 400));\n      }\n\n      if (value !== undefined && value !== null && value !== '' &&", "tokens": 0, "firstFile": {"name": "src\\middlewares\\validation.middleware.ts", "start": 140, "end": 157, "startLoc": {"line": 140, "column": 2, "position": 1317}, "endLoc": {"line": 157, "column": 3, "position": 1552}}, "secondFile": {"name": "src\\middlewares\\validation.middleware.ts", "start": 95, "end": 112, "startLoc": {"line": 95, "column": 7, "position": 828}, "endLoc": {"line": 112, "column": 2, "position": 1062}}}, {"format": "typescript", "lines": 12, "fragment": ") => {\n    return async (req: Request, res: Response, next: NextFunction) => {\n        try {\n            if (!req.user) {\n                return next(new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        }));\n            }\n\n            for", "tokens": 0, "firstFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 80, "end": 91, "startLoc": {"line": 80, "column": 2, "position": 625}, "endLoc": {"line": 91, "column": 4, "position": 722}}, "secondFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 43, "end": 54, "startLoc": {"line": 43, "column": 7, "position": 305}, "endLoc": {"line": 54, "column": 6, "position": 402}}}, {"format": "typescript", "lines": 12, "fragment": ") => {\n    return async (req: Request, res: Response, next: NextFunction) => {\n        try {\n            if (!req.user) {\n                return next(new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        }));\n            }\n\n            const user", "tokens": 0, "firstFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 118, "end": 129, "startLoc": {"line": 118, "column": 2, "position": 948}, "endLoc": {"line": 129, "column": 5, "position": 1047}}, "secondFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 43, "end": 54, "startLoc": {"line": 43, "column": 7, "position": 305}, "endLoc": {"line": 54, "column": 14, "position": 404}}}, {"format": "typescript", "lines": 21, "fragment": ",\n            type: ErrorType.AUTHORIZATION,\n            code: ErrorCode.FORBIDDEN\n        }));\n            }\n\n            next();\n        } catch (error) {\n            logger.error(\"Error in RBAC middleware:\", error);\n            next(new AppError({\n            message: \"Internal server error\",\n            type: ErrorType.INTERNAL,\n            code: ErrorCode.INTERNAL_SERVER_ERROR\n        }));\n        }\n    };\n};\n\n/**\n * Middleware to attach user permissions to request\n */", "tokens": 0, "firstFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 147, "end": 167, "startLoc": {"line": 147, "column": 31, "position": 1231}, "endLoc": {"line": 167, "column": 4, "position": 1339}}, "secondFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 59, "end": 79, "startLoc": {"line": 59, "column": 38, "position": 483}, "endLoc": {"line": 79, "column": 4, "position": 591}}}, {"format": "typescript", "lines": 7, "fragment": "${req.ip}`, {\n      ip: req.ip,\n      path: req.path,\n      method: req.method,\n      requestId: req.requestId,\n      userAgent: req.headers['user-agent'],\n      email: req.body?.email, // Log the email being used for password reset", "tokens": 0, "firstFile": {"name": "src\\middlewares\\rate-limit.middleware.ts", "start": 115, "end": 121, "startLoc": {"line": 115, "column": 45, "position": 845}, "endLoc": {"line": 121, "column": 47, "position": 915}}, "secondFile": {"name": "src\\middlewares\\rate-limit.middleware.ts", "start": 83, "end": 89, "startLoc": {"line": 83, "column": 45, "position": 599}, "endLoc": {"line": 89, "column": 47, "position": 669}}}, {"format": "typescript", "lines": 10, "fragment": ";\nimport { PrismaClient } from \"@prisma/client\";\nimport { logger } from \"../utils/logger\";\nimport { AppError } from \"../utils/appError\";\nimport { RBACService } from \"../services/rbac.service\";\nimport { AuditService } from \"../services/audit.service\";\nimport { verifyToken } from \"../utils/jwt.utils\";\nimport { Middleware } from '../types/express';\n\n// Extend the Express Request type to include user property", "tokens": 0, "firstFile": {"name": "src\\middlewares\\enhanced-auth.middleware.ts", "start": 16, "end": 25, "startLoc": {"line": 16, "column": 19, "position": 122}, "endLoc": {"line": 25, "column": 60, "position": 216}}, "secondFile": {"name": "src\\middlewares\\enhanced-auth.middleware.ts", "start": 9, "end": 17, "startLoc": {"line": 9, "column": 15, "position": 31}, "endLoc": {"line": 17, "column": 7, "position": 124}}}, {"format": "typescript", "lines": 19, "fragment": ";\nimport { Middleware } from '../types/express';\n\n// Extend the Express Request type to include user property\ndeclare global {\n  namespace Express {\n    interface Request {\n      user?: {\n        id: string;\n        role: string;\n        merchantId?: string;\n      };\n    }\n  }\n}\n\nconst prisma: any = new PrismaClient();\nconst rbacService: any = new RBACService(prisma);\nconst", "tokens": 0, "firstFile": {"name": "src\\middlewares\\enhanced-auth.middleware.ts", "start": 22, "end": 40, "startLoc": {"line": 22, "column": 21, "position": 200}, "endLoc": {"line": 40, "column": 6, "position": 313}}, "secondFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 19, "end": 42, "startLoc": {"line": 19, "column": 11, "position": 165}, "endLoc": {"line": 42, "column": 4, "position": 281}}}, {"format": "typescript", "lines": 10, "fragment": "(req: Request, res: Response, next: NextFunction) => {\n    if (!req.user) {\n      return next(new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        }));\n    }\n\n    const", "tokens": 0, "firstFile": {"name": "src\\middlewares\\authorization.middleware.ts", "start": 95, "end": 104, "startLoc": {"line": 95, "column": 2, "position": 688}, "endLoc": {"line": 104, "column": 6, "position": 769}}, "secondFile": {"name": "src\\middlewares\\authorization.middleware.ts", "start": 25, "end": 33, "startLoc": {"line": 25, "column": 2, "position": 151}, "endLoc": {"line": 33, "column": 5, "position": 231}}}, {"format": "typescript", "lines": 6, "fragment": ": any =(req: Request, res: Response, next: NextFunction) => {\n    if (!req.user) {\n        return res.status(401).json({ message: \"Authentication required. Please log in.\" });\n    }\n\n    if (req.user.role !== \"MERCHANT\"", "tokens": 0, "firstFile": {"name": "src\\middlewares\\auth.ts", "start": 114, "end": 119, "startLoc": {"line": 114, "column": 18, "position": 793}, "endLoc": {"line": 119, "column": 11, "position": 872}}, "secondFile": {"name": "src\\middlewares\\auth.ts", "start": 98, "end": 103, "startLoc": {"line": 98, "column": 8, "position": 648}, "endLoc": {"line": 103, "column": 8, "position": 727}}}, {"format": "typescript", "lines": 10, "fragment": "${req.user.id}`, {\n      // Fixed: using id instead of userId\n      userId: req.user.id, // Fixed: using id instead of userId\n      role: req.user.role,\n      requestId: req.requestId,\n      path: req.path,\n      method: req.method,\n    });\n\n    return next(new AppError('Merchant access required.'", "tokens": 0, "firstFile": {"name": "src\\middlewares\\auth.middleware.ts", "start": 115, "end": 124, "startLoc": {"line": 115, "column": 47, "position": 895}, "endLoc": {"line": 124, "column": 28, "position": 976}}, "secondFile": {"name": "src\\middlewares\\auth.middleware.ts", "start": 91, "end": 100, "startLoc": {"line": 91, "column": 44, "position": 694}, "endLoc": {"line": 100, "column": 25, "position": 775}}}, {"format": "typescript", "lines": 7, "fragment": ": any = (req: Request, res: Response, next: NextFunction) => {\n  if (!req.user) {\n    return next(new AppError('Authentication required. Please log in.', 401, true));\n  }\n\n  if (\n    req.user.role !== 'MERCHANT' &&", "tokens": 0, "firstFile": {"name": "src\\middlewares\\auth.middleware.ts", "start": 133, "end": 139, "startLoc": {"line": 133, "column": 18, "position": 1008}, "endLoc": {"line": 139, "column": 3, "position": 1089}}, "secondFile": {"name": "src\\middlewares\\auth.middleware.ts", "start": 109, "end": 114, "startLoc": {"line": 109, "column": 11, "position": 807}, "endLoc": {"line": 114, "column": 2, "position": 885}}}, {"format": "typescript", "lines": 10, "fragment": "${req.user.id}`, {\n      // Fixed: using id instead of userId\n      userId: req.user.id, // Fixed: using id instead of userId\n      role: req.user.role,\n      requestId: req.requestId,\n      path: req.path,\n      method: req.method,\n    });\n\n    return next(new AppError('Merchant or admin access required.'", "tokens": 0, "firstFile": {"name": "src\\middlewares\\auth.middleware.ts", "start": 143, "end": 152, "startLoc": {"line": 143, "column": 53, "position": 1126}, "endLoc": {"line": 152, "column": 37, "position": 1207}}, "secondFile": {"name": "src\\middlewares\\auth.middleware.ts", "start": 91, "end": 100, "startLoc": {"line": 91, "column": 44, "position": 694}, "endLoc": {"line": 100, "column": 25, "position": 775}}}, {"format": "typescript", "lines": 19, "fragment": ";\nimport { Middleware } from '../types/express';\n\n// Extend the Express Request type to include user property\ndeclare global {\n  namespace Express {\n    interface Request {\n      user?: {\n        id: string;\n        role: string;\n        merchantId?: string;\n      };\n    }\n  }\n}\n\n\nconst prisma: any = new PrismaClient();\nconst auditService", "tokens": 0, "firstFile": {"name": "src\\middlewares\\audit.middleware.ts", "start": 15, "end": 33, "startLoc": {"line": 15, "column": 16, "position": 113}, "endLoc": {"line": 33, "column": 13, "position": 212}}, "secondFile": {"name": "src\\middlewares\\rbac.middleware.ts", "start": 19, "end": 38, "startLoc": {"line": 19, "column": 11, "position": 165}, "endLoc": {"line": 38, "column": 12, "position": 265}}}, {"format": "typescript", "lines": 18, "fragment": ";\n\n    // Configure logging based on environment\n    const prismaOptions: Prisma.PrismaClientOptions = {\n      datasources: {\n        db: {\n          url: databaseUrl,\n        },\n      },\n      log: isProd ? [] : [\n        { level: 'query', emit: 'event' } as Prisma.LogDefinition,\n        { level: 'info', emit: 'event' } as Prisma.LogDefinition,\n        { level: 'warn', emit: 'event' } as Prisma.LogDefinition,\n        { level: 'error', emit: 'event' } as Prisma.LogDefinition\n      ]\n    };\n\n    global", "tokens": 0, "firstFile": {"name": "src\\lib\\prisma.ts", "start": 92, "end": 109, "startLoc": {"line": 92, "column": 13, "position": 653}, "endLoc": {"line": 109, "column": 7, "position": 815}}, "secondFile": {"name": "src\\lib\\prisma.ts", "start": 38, "end": 55, "startLoc": {"line": 38, "column": 2, "position": 223}, "endLoc": {"line": 55, "column": 6, "position": 385}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: Request, res: Response) => {\n    try {\n        // Get user role\n        const userRole: any =req.user?.role;\n\n        // Check if user is authorized\n        if (!userRole || userRole !== \"ADMIN\") {\n            throw new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        });\n        }\n\n        // Delete webhook", "tokens": 0, "firstFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 120, "end": 134, "startLoc": {"line": 120, "column": 2, "position": 837}, "endLoc": {"line": 134, "column": 18, "position": 950}}, "secondFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 62, "end": 76, "startLoc": {"line": 62, "column": 2, "position": 438}, "endLoc": {"line": 76, "column": 19, "position": 551}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: Request, res: Response) => {\n    try {\n        // Get user role\n        const userRole: any =req.user?.role;\n\n        // Check if user is authorized\n        if (!userRole || userRole !== \"ADMIN\") {\n            throw new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        });\n        }\n\n        // Get webhook info", "tokens": 0, "firstFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 166, "end": 180, "startLoc": {"line": 166, "column": 2, "position": 1160}, "endLoc": {"line": 180, "column": 20, "position": 1273}}, "secondFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 62, "end": 76, "startLoc": {"line": 62, "column": 2, "position": 438}, "endLoc": {"line": 76, "column": 19, "position": 551}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: Request, res: Response) => {\n    try {\n        // Get user role\n        const userRole: any =req.user?.role;\n\n        // Check if user is authorized\n        if (!userRole || userRole !== \"ADMIN\") {\n            throw new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        });\n        }\n\n        // Get bot info", "tokens": 0, "firstFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 212, "end": 226, "startLoc": {"line": 212, "column": 2, "position": 1483}, "endLoc": {"line": 226, "column": 16, "position": 1596}}, "secondFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 62, "end": 76, "startLoc": {"line": 62, "column": 2, "position": 438}, "endLoc": {"line": 76, "column": 19, "position": 551}}}, {"format": "typescript", "lines": 15, "fragment": "});\n            }\n\n            // Parse dates if provided\n            const parsedStartDate: any =startDate ? new Date(startDate as string) : undefined;\n            const parsedEndDate: any =endDate ? new Date(endDate as string) : undefined;\n\n            // Get subscription history\n            const history: any =await subscriptionService.getSubscriptionHistory(\n                merchantId,\n                parsedStartDate,\n                parsedEndDate\n            );\n\n            // Convert to CSV", "tokens": 0, "firstFile": {"name": "src\\controllers\\subscription-history.controller.ts", "start": 81, "end": 95, "startLoc": {"line": 81, "column": 17, "position": 612}, "endLoc": {"line": 95, "column": 18, "position": 718}}, "secondFile": {"name": "src\\controllers\\subscription-history.controller.ts", "start": 39, "end": 53, "startLoc": {"line": 39, "column": 17, "position": 276}, "endLoc": {"line": 53, "column": 7, "position": 382}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: Request, res: Response) => {\n    try {\n        // Get user role\n        const userRole: any =req.user?.role;\n\n        // Check if user is authorized\n        if (!userRole || userRole !== \"ADMIN\") {\n            throw new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        });\n        }\n\n        // Get phone number from body", "tokens": 0, "firstFile": {"name": "src\\controllers\\sms.controller.ts", "start": 37, "end": 51, "startLoc": {"line": 37, "column": 2, "position": 224}, "endLoc": {"line": 51, "column": 30, "position": 337}}, "secondFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 62, "end": 76, "startLoc": {"line": 62, "column": 2, "position": 438}, "endLoc": {"line": 76, "column": 19, "position": 551}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: Request, res: Response) => {\n    try {\n        // Get user role\n        const userRole: any =req.user?.role;\n\n        // Check if user is authorized\n        if (!userRole || userRole !== \"ADMIN\") {\n            throw new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        });\n        }\n\n        // Get SMS data from body", "tokens": 0, "firstFile": {"name": "src\\controllers\\sms.controller.ts", "start": 96, "end": 110, "startLoc": {"line": 96, "column": 2, "position": 629}, "endLoc": {"line": 110, "column": 26, "position": 742}}, "secondFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 62, "end": 76, "startLoc": {"line": 62, "column": 2, "position": 438}, "endLoc": {"line": 76, "column": 19, "position": 551}}}, {"format": "typescript", "lines": 15, "fragment": "= asyncHandler(async (req: Request, res: Response) => {\n    try {\n        // Get user role\n        const userRole: any =req.user?.role;\n\n        // Check if user is authorized\n        if (!userRole || userRole !== \"ADMIN\") {\n            throw new AppError({\n            message: \"Unauthorized\",\n            type: ErrorType.AUTHENTICATION,\n            code: ErrorCode.INVALID_CREDENTIALS\n        });\n        }\n\n        // Create SMS service", "tokens": 0, "firstFile": {"name": "src\\controllers\\sms.controller.ts", "start": 155, "end": 169, "startLoc": {"line": 155, "column": 2, "position": 1045}, "endLoc": {"line": 169, "column": 22, "position": 1158}}, "secondFile": {"name": "src\\controllers\\telegram-webhook.controller.ts", "start": 62, "end": 76, "startLoc": {"line": 62, "column": 2, "position": 438}, "endLoc": {"line": 76, "column": 19, "position": 551}}}, {"format": "typescript", "lines": 10, "fragment": ");\n\n            return res.status(200).json({\n                status: \"success\",\n                data: payments\n            });\n        } catch (error) {\n            return res.status(500).json({\n                status: \"error\",\n                message: error instanceof Error ? (error as Error).message : \"Failed to get payments\"", "tokens": 0, "firstFile": {"name": "src\\controllers\\payment.controller.ts", "start": 60, "end": 69, "startLoc": {"line": 60, "column": 11, "position": 472}, "endLoc": {"line": 69, "column": 25, "position": 564}}, "secondFile": {"name": "src\\controllers\\payment.controller.ts", "start": 12, "end": 21, "startLoc": {"line": 12, "column": 2, "position": 83}, "endLoc": {"line": 21, "column": 30, "position": 175}}}, {"format": "typescript", "lines": 11, "fragment": "] : [],\n                    amount: !amount ? [\"Amount is required\"] : [],\n                    currency: !currency ? [\"Currency is required\"] : [],\n                    merchantApiKey: !merchantApiKey ? [\"Merchant API key is required\"] : [],\n                    merchantSecretKey: !merchantSecretKey ? [\"Merchant secret key is required\"] : []\n                });\n            }\n\n            // Verify payment\n            const result: any =await this.paymentVerificationService.verifyPayment(\n                PaymentMethod.BINANCE_C2C", "tokens": 0, "firstFile": {"name": "src\\controllers\\payment-verification.controller.ts", "start": 155, "end": 165, "startLoc": {"line": 155, "column": 19, "position": 1235}, "endLoc": {"line": 165, "column": 12, "position": 1351}}, "secondFile": {"name": "src\\controllers\\payment-verification.controller.ts", "start": 113, "end": 123, "startLoc": {"line": 113, "column": 29, "position": 907}, "endLoc": {"line": 123, "column": 12, "position": 1023}}}, {"format": "typescript", "lines": 7, "fragment": ") {\n                return this.sendValidationError(res, {\n                    txHash: !txHash ? [\"Transaction hash is required\"] : [],\n                    amount: !amount ? [\"Amount is required\"] : [],\n                    currency: !currency ? [\"Currency is required\"] : [],\n                    recipientAddress: !recipientAddress ? [\"Recipient address is required\"] : []\n                }", "tokens": 0, "firstFile": {"name": "src\\controllers\\payment-verification.controller.ts", "start": 238, "end": 244, "startLoc": {"line": 238, "column": 17, "position": 1883}, "endLoc": {"line": 244, "column": 2, "position": 1975}}, "secondFile": {"name": "src\\controllers\\payment-verification.controller.ts", "start": 196, "end": 201, "startLoc": {"line": 196, "column": 18, "position": 1545}, "endLoc": {"line": 201, "column": 2, "position": 1635}}}, {"format": "typescript", "lines": 15, "fragment": ");\n\n            // Send success response\n            this.sendSuccess(res, recommendations, \"Payment method recommendations retrieved\");\n        } catch (error) {\n            this.sendError(res, error instanceof ServiceError ? error : new ServiceError(\n                (error as Error).message || \"Failed to get payment method recommendations\",\n                500\n            ));\n        }\n    });\n\n    /**\n   * Update merchant recommendation weights\n   */", "tokens": 0, "firstFile": {"name": "src\\controllers\\payment-recommendation.controller.ts", "start": 98, "end": 112, "startLoc": {"line": 98, "column": 13, "position": 712}, "endLoc": {"line": 112, "column": 6, "position": 804}}, "secondFile": {"name": "src\\controllers\\payment-recommendation.controller.ts", "start": 53, "end": 67, "startLoc": {"line": 53, "column": 13, "position": 352}, "endLoc": {"line": 67, "column": 6, "position": 444}}}, {"format": "typescript", "lines": 13, "fragment": ": any =asyncHandler(async (req: Request, res: Response) => {\n    // Get user ID from authenticated user\n    const userId: any =req.user?.id // Fixed: using id instead of userId;\n\n    if (!userId) {\n        throw new AppError({\n            message: \"User ID is required\",\n            type: ErrorType.VALIDATION,\n            code: ErrorCode.MISSING_REQUIRED_FIELD\n        });\n    }\n\n    // Disable MFA", "tokens": 0, "firstFile": {"name": "src\\controllers\\multi-factor-auth.controller.ts", "start": 94, "end": 106, "startLoc": {"line": 94, "column": 11, "position": 656}, "endLoc": {"line": 106, "column": 15, "position": 757}}, "secondFile": {"name": "src\\controllers\\multi-factor-auth.controller.ts", "start": 31, "end": 43, "startLoc": {"line": 31, "column": 27, "position": 211}, "endLoc": {"line": 43, "column": 28, "position": 312}}}, {"format": "typescript", "lines": 8, "fragment": "if (req.user?.role !== \"admin\" && req.user?.merchantId !== merchantId) {\n                return res.status(403).json({\n                    status: \"error\",\n                    message: \"You are not authorized to manage this merchant subscription\"\n                });\n            }\n\n            const result: any =await subscriptionService.cancelSubscription", "tokens": 0, "firstFile": {"name": "src\\controllers\\merchant-subscription.controller.ts", "start": 62, "end": 69, "startLoc": {"line": 62, "column": 13, "position": 471}, "endLoc": {"line": 69, "column": 19, "position": 550}}, "secondFile": {"name": "src\\controllers\\merchant-subscription.controller.ts", "start": 30, "end": 37, "startLoc": {"line": 30, "column": 13, "position": 215}, "endLoc": {"line": 37, "column": 18, "position": 294}}}, {"format": "typescript", "lines": 11, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { tierId } = req.params;\n\n      // Validate required parameters\n      if (!tierId) {\n        ApiResponse.error(res, 'Missing required parameters', 400);\n        return;\n      }\n\n      await", "tokens": 0, "firstFile": {"name": "src\\controllers\\fee-management.controller.ts", "start": 194, "end": 204, "startLoc": {"line": 194, "column": 2, "position": 1302}, "endLoc": {"line": 204, "column": 6, "position": 1388}}, "secondFile": {"name": "src\\controllers\\fee-management.controller.ts", "start": 128, "end": 138, "startLoc": {"line": 128, "column": 2, "position": 833}, "endLoc": {"line": 138, "column": 6, "position": 919}}}, {"format": "typescript", "lines": 11, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { merchantId } = req.params;\n\n      // Validate required parameters\n      if (!merchantId) {\n        ApiResponse.error(res, 'Missing required parameters', 400);\n        return;\n      }\n\n      const result: any = await prisma", "tokens": 0, "firstFile": {"name": "src\\controllers\\fee-management.controller.ts", "start": 321, "end": 331, "startLoc": {"line": 321, "column": 2, "position": 2241}, "endLoc": {"line": 331, "column": 7, "position": 2338}}, "secondFile": {"name": "src\\controllers\\payment-routing.controller.ts", "start": 142, "end": 152, "startLoc": {"line": 142, "column": 2, "position": 939}, "endLoc": {"line": 152, "column": 5, "position": 1036}}}, {"format": "typescript", "lines": 7, "fragment": "} = req.query;\n\n            // Parse dates\n            const parsedStartDate: any = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n            const parsedEndDate: any = endDate ? new Date(endDate as string) : new Date();\n\n            // Get risk assessments", "tokens": 0, "firstFile": {"name": "src\\controllers\\enhanced-risk-engine.controller.ts", "start": 210, "end": 216, "startLoc": {"line": 210, "column": 2, "position": 1708}, "endLoc": {"line": 216, "column": 24, "position": 1816}}, "secondFile": {"name": "src\\controllers\\monitoring\\verification-monitoring.controller.ts", "start": 31, "end": 37, "startLoc": {"line": 31, "column": 2, "position": 137}, "endLoc": {"line": 37, "column": 18, "position": 245}}}, {"format": "typescript", "lines": 10, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      const {", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 96, "end": 105, "startLoc": {"line": 96, "column": 2, "position": 743}, "endLoc": {"line": 105, "column": 2, "position": 841}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 11, "end": 20, "startLoc": {"line": 11, "column": 2, "position": 77}, "endLoc": {"line": 20, "column": 11, "position": 175}}}, {"format": "typescript", "lines": 11, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { id } = req.params;\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      // Check if dashboard exists and belongs to user", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 133, "end": 143, "startLoc": {"line": 133, "column": 2, "position": 1043}, "endLoc": {"line": 143, "column": 49, "position": 1155}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 48, "end": 58, "startLoc": {"line": 48, "column": 2, "position": 354}, "endLoc": {"line": 58, "column": 6, "position": 466}}}, {"format": "typescript", "lines": 26, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { id } = req.params;\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      // Check if dashboard exists and belongs to user\n      const existingDashboard = await prisma.dashboard.findUnique({\n        where: { id }\n      });\n      \n      if (!existingDashboard) {\n        res.status(404).json({ success: false, message: 'Dashboard not found' });\n        return;\n      }\n      \n      if (existingDashboard.createdById !== userId) {\n        res.status(403).json({ success: false, message: 'Access denied' });\n        return;\n      }\n      \n      // Delete all widgets first", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 186, "end": 211, "startLoc": {"line": 186, "column": 2, "position": 1499}, "endLoc": {"line": 211, "column": 28, "position": 1745}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 48, "end": 158, "startLoc": {"line": 48, "column": 2, "position": 354}, "endLoc": {"line": 158, "column": 6, "position": 1289}}}, {"format": "typescript", "lines": 11, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { id } = req.params;\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      const widget", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 56, "end": 66, "startLoc": {"line": 56, "column": 2, "position": 466}, "endLoc": {"line": 66, "column": 7, "position": 580}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 48, "end": 58, "startLoc": {"line": 48, "column": 2, "position": 354}, "endLoc": {"line": 58, "column": 10, "position": 468}}}, {"format": "typescript", "lines": 11, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { dashboardId } = req.params;\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      // Check if dashboard exists and belongs to user", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 98, "end": 108, "startLoc": {"line": 98, "column": 2, "position": 839}, "endLoc": {"line": 108, "column": 49, "position": 951}}, "secondFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 10, "end": 20, "startLoc": {"line": 10, "column": 2, "position": 60}, "endLoc": {"line": 20, "column": 49, "position": 172}}}, {"format": "typescript", "lines": 10, "fragment": "const dashboard = await prisma.dashboard.findUnique({\n        where: { id: dashboardId }\n      });\n      \n      if (!dashboard) {\n        res.status(404).json({ success: false, message: 'Dashboard not found' });\n        return;\n      }\n      \n      if (dashboard", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 109, "end": 118, "startLoc": {"line": 109, "column": 7, "position": 954}, "endLoc": {"line": 118, "column": 10, "position": 1040}}, "secondFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 21, "end": 30, "startLoc": {"line": 21, "column": 7, "position": 175}, "endLoc": {"line": 30, "column": 2, "position": 261}}}, {"format": "typescript", "lines": 11, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { id } = req.params;\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      // Check if widget exists and user has access", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 162, "end": 172, "startLoc": {"line": 162, "column": 2, "position": 1408}, "endLoc": {"line": 172, "column": 46, "position": 1520}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 48, "end": 58, "startLoc": {"line": 48, "column": 2, "position": 354}, "endLoc": {"line": 58, "column": 6, "position": 466}}}, {"format": "typescript", "lines": 11, "fragment": "const widget = await prisma.dashboardWidget.findUnique({\n        where: { id },\n        include: { dashboard: true }\n      });\n      \n      if (!widget) {\n        res.status(404).json({ success: false, message: 'Widget not found' });\n        return;\n      }\n      \n      if", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 173, "end": 183, "startLoc": {"line": 173, "column": 7, "position": 1523}, "endLoc": {"line": 183, "column": 3, "position": 1617}}, "secondFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 66, "end": 76, "startLoc": {"line": 66, "column": 7, "position": 578}, "endLoc": {"line": 76, "column": 45, "position": 672}}}, {"format": "typescript", "lines": 27, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { id } = req.params;\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      // Check if widget exists and user has access\n      const widget = await prisma.dashboardWidget.findUnique({\n        where: { id },\n        include: { dashboard: true }\n      });\n      \n      if (!widget) {\n        res.status(404).json({ success: false, message: 'Widget not found' });\n        return;\n      }\n      \n      if (widget.dashboard.createdById !== userId) {\n        res.status(403).json({ success: false, message: 'Access denied' });\n        return;\n      }\n      \n      await", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 218, "end": 244, "startLoc": {"line": 218, "column": 2, "position": 1962}, "endLoc": {"line": 244, "column": 6, "position": 2224}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 48, "end": 188, "startLoc": {"line": 48, "column": 2, "position": 354}, "endLoc": {"line": 188, "column": 6, "position": 1670}}}, {"format": "typescript", "lines": 26, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { dashboardId } = req.params;\n      const userId = req.user?.id;\n      \n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n      \n      // Check if dashboard exists and belongs to user\n      const dashboard = await prisma.dashboard.findUnique({\n        where: { id: dashboardId }\n      });\n      \n      if (!dashboard) {\n        res.status(404).json({ success: false, message: 'Dashboard not found' });\n        return;\n      }\n      \n      if (dashboard.createdById !== userId) {\n        res.status(403).json({ success: false, message: 'Access denied' });\n        return;\n      }\n      \n      const { widgets", "tokens": 0, "firstFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 264, "end": 289, "startLoc": {"line": 264, "column": 2, "position": 2354}, "endLoc": {"line": 289, "column": 8, "position": 2607}}, "secondFile": {"name": "src\\controllers\\dashboard-widget.controller.ts", "start": 10, "end": 123, "startLoc": {"line": 10, "column": 2, "position": 60}, "endLoc": {"line": 123, "column": 6, "position": 1092}}}, {"format": "typescript", "lines": 9, "fragment": ";\nimport { BaseController } from './base/BaseController';\nimport { logger } from \"../utils/logger\";\nimport { BlockchainNetwork } from \"../types/blockchain\";\nimport { VerificationStatus } from \"../types/verification\";\nimport { BlockchainApiService } from \"../services/blockchain/blockchain-api.service\";\nimport { BinanceApiService } from \"../services/blockchain/binance-api.service\";\nimport { verificationEvents } from \"../services/websocket/verification-websocket.service\";\nimport {", "tokens": 0, "firstFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 11, "end": 19, "startLoc": {"line": 11, "column": 11, "position": 135}, "endLoc": {"line": 19, "column": 2, "position": 230}}, "secondFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 2, "end": 10, "startLoc": {"line": 2, "column": 10, "position": 16}, "endLoc": {"line": 10, "column": 7, "position": 111}}}, {"format": "typescript", "lines": 33, "fragment": "});\n            }\n\n            // Get merchant\n            const merchant: any = await prisma.merchant.findUnique({\n                where: { id: merchantId }\n            });\n\n            if (!merchant) {\n                return res.status(404).json({\n                    success: false,\n                    message: \"Merchant not found\"\n                });\n            }\n\n            // Get transaction\n            const transaction: any = await prisma.transaction.findUnique({\n                where: { id: paymentId }\n            });\n\n            if (!transaction) {\n                return res.status(404).json({\n                    success: false,\n                    message: \"Transaction not found\"\n                });\n            }\n\n            // Get payment method\n            const paymentMethod: any = paymentMethodId ? await prisma.paymentMethod.findUnique({\n                where: { id: paymentMethodId }\n            }) : null;\n\n            const", "tokens": 0, "firstFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 203, "end": 235, "startLoc": {"line": 203, "column": 17, "position": 1771}, "endLoc": {"line": 235, "column": 6, "position": 2006}}, "secondFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 61, "end": 93, "startLoc": {"line": 61, "column": 17, "position": 541}, "endLoc": {"line": 93, "column": 4, "position": 776}}}, {"format": "typescript", "lines": 10, "fragment": ": verificationResult.toAddress || toAddress || paymentMethod?.address,\n                        amount: verificationResult.amount || parseFloat(amount.toString()),\n                        currency,\n                        timestamp: verificationResult.timestamp || Date.now(),\n                        status: verificationResult.success ? \"success\" : \"failed\"\n                    })\n                }\n            });\n\n            // Emit verification event", "tokens": 0, "firstFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 281, "end": 290, "startLoc": {"line": 281, "column": 10, "position": 2460}, "endLoc": {"line": 290, "column": 27, "position": 2549}}, "secondFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 263, "end": 272, "startLoc": {"line": 263, "column": 10, "position": 2273}, "endLoc": {"line": 272, "column": 30, "position": 2362}}}, {"format": "typescript", "lines": 14, "fragment": ",\n                timestamp: new Date()\n            });\n\n            return res.status(200).json({\n                success: true,\n                message: verificationResult.success ? \"Transaction verified successfully\" : \"Transaction verification failed\",\n                data: { transaction: updatedTransaction,\n                    verification,\n                    verificationResult\n                }\n            });\n        } catch (error) {\n            logger.error(`Error in verifyBinanceTransaction: ", "tokens": 0, "firstFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 295, "end": 308, "startLoc": {"line": 295, "column": 16, "position": 2605}, "endLoc": {"line": 308, "column": 37, "position": 2704}}, "secondFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 158, "end": 171, "startLoc": {"line": 158, "column": 2, "position": 1432}, "endLoc": {"line": 171, "column": 40, "position": 1531}}}, {"format": "typescript", "lines": 9, "fragment": "${error instanceof Error ? (error as <PERSON>rror).message : 'Unknown error'}`);\n            return res.status(500).json({\n                success: false,\n                message: \"Internal server error\",\n                error: error instanceof Error ? (error as Error).message : \"Unknown error\"\n            });\n        }\n    }\n}", "tokens": 0, "firstFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 308, "end": 316, "startLoc": {"line": 308, "column": 37, "position": 2705}, "endLoc": {"line": 316, "column": 2, "position": 2793}}, "secondFile": {"name": "src\\controllers\\blockchain-verification.controller.ts", "start": 171, "end": 182, "startLoc": {"line": 171, "column": 40, "position": 1532}, "endLoc": {"line": 182, "column": 8, "position": 1622}}}, {"format": "typescript", "lines": 13, "fragment": ": any =asyncHandler(async (req: Request, res: Response) => {\n    const { apiKey, apiSecret } = req.body;\n  \n    // Validate required fields\n    if (!apiKey || !apiSecret) {\n        throw new AppError({\n            message: \"API key and secret are required\",\n            type: ErrorType.VALIDATION,\n            code: ErrorCode.MISSING_REQUIRED_FIELD\n        });\n    }\n  \n    // Get account information", "tokens": 0, "firstFile": {"name": "src\\controllers\\binance.controller.ts", "start": 38, "end": 50, "startLoc": {"line": 38, "column": 15, "position": 312}, "endLoc": {"line": 50, "column": 27, "position": 421}}, "secondFile": {"name": "src\\controllers\\binance.controller.ts", "start": 9, "end": 21, "startLoc": {"line": 9, "column": 15, "position": 84}, "endLoc": {"line": 21, "column": 19, "position": 193}}}, {"format": "typescript", "lines": 22, "fragment": "// Parse filter parameters\n            const filter: AnalyticsFilter = {};\n\n            if (req.query.merchantId) {\n                filter.merchantId = req.query.merchantId as string;\n            }\n\n            if (req.query.startDate) {\n                filter.startDate = new Date(req.query.startDate as string);\n            }\n\n            if (req.query.endDate) {\n                filter.endDate = new Date(req.query.endDate as string);\n            }\n\n            if (req.query.period) {\n                filter.period = req.query.period as AnalyticsPeriod;\n            } else {\n                filter.period = AnalyticsPeriod.MONTH; // Default to month\n            }\n\n            const analytics: any =await paymentAnalyticsService.getPaymentMethodAnalytics", "tokens": 0, "firstFile": {"name": "src\\controllers\\analytics.controller.ts", "start": 75, "end": 96, "startLoc": {"line": 75, "column": 13, "position": 571}, "endLoc": {"line": 96, "column": 26, "position": 772}}, "secondFile": {"name": "src\\controllers\\analytics.controller.ts", "start": 35, "end": 56, "startLoc": {"line": 35, "column": 13, "position": 208}, "endLoc": {"line": 56, "column": 20, "position": 409}}}, {"format": "typescript", "lines": 15, "fragment": "if (req.query.startDate) {\n                filter.startDate = new Date(req.query.startDate as string);\n            }\n\n            if (req.query.endDate) {\n                filter.endDate = new Date(req.query.endDate as string);\n            }\n\n            if (req.query.period) {\n                filter.period = req.query.period as AnalyticsPeriod;\n            } else {\n                filter.period = AnalyticsPeriod.MONTH; // Default to month\n            }\n\n            const analytics: any =await paymentAnalyticsService.getMerchantAnalytics", "tokens": 0, "firstFile": {"name": "src\\controllers\\analytics.controller.ts", "start": 124, "end": 138, "startLoc": {"line": 124, "column": 13, "position": 970}, "endLoc": {"line": 138, "column": 21, "position": 1118}}, "secondFile": {"name": "src\\controllers\\analytics.controller.ts", "start": 42, "end": 56, "startLoc": {"line": 42, "column": 13, "position": 261}, "endLoc": {"line": 56, "column": 20, "position": 409}}}, {"format": "typescript", "lines": 17, "fragment": "};\n\n            if (req.query.startDate) {\n                filter.startDate = new Date(req.query.startDate as string);\n            }\n\n            if (req.query.endDate) {\n                filter.endDate = new Date(req.query.endDate as string);\n            }\n\n            if (req.query.period) {\n                filter.period = req.query.period as AnalyticsPeriod;\n            } else {\n                filter.period = AnalyticsPeriod.MONTH; // Default to month\n            }\n\n            // Get analytics based on user role", "tokens": 0, "firstFile": {"name": "src\\controllers\\analytics.controller.ts", "start": 155, "end": 171, "startLoc": {"line": 155, "column": 2, "position": 1282}, "endLoc": {"line": 171, "column": 36, "position": 1423}}, "secondFile": {"name": "src\\controllers\\analytics.controller.ts", "start": 122, "end": 56, "startLoc": {"line": 122, "column": 13, "position": 965}, "endLoc": {"line": 56, "column": 6, "position": 397}}}, {"format": "typescript", "lines": 10, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const userId = req.user?.id;\n\n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n\n      const includeSystem", "tokens": 0, "firstFile": {"name": "src\\controllers\\advanced-report.controller.ts", "start": 51, "end": 60, "startLoc": {"line": 51, "column": 2, "position": 374}, "endLoc": {"line": 60, "column": 14, "position": 470}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 11, "end": 20, "startLoc": {"line": 11, "column": 2, "position": 77}, "endLoc": {"line": 20, "column": 11, "position": 175}}}, {"format": "typescript", "lines": 10, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const userId = req.user?.id;\n\n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n\n      const data", "tokens": 0, "firstFile": {"name": "src\\controllers\\advanced-report.controller.ts", "start": 107, "end": 116, "startLoc": {"line": 107, "column": 2, "position": 833}, "endLoc": {"line": 116, "column": 5, "position": 929}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 11, "end": 20, "startLoc": {"line": 11, "column": 2, "position": 77}, "endLoc": {"line": 20, "column": 11, "position": 175}}}, {"format": "typescript", "lines": 10, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const userId = req.user?.id;\n\n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n\n      const reports", "tokens": 0, "firstFile": {"name": "src\\controllers\\advanced-report.controller.ts", "start": 183, "end": 192, "startLoc": {"line": 183, "column": 2, "position": 1431}, "endLoc": {"line": 192, "column": 8, "position": 1527}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 11, "end": 20, "startLoc": {"line": 11, "column": 2, "position": 77}, "endLoc": {"line": 20, "column": 11, "position": 175}}}, {"format": "typescript", "lines": 15, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const userId = req.user?.id;\n\n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n\n      const data = {\n        ...req.body,\n        createdById: userId,\n      };\n\n      const report", "tokens": 0, "firstFile": {"name": "src\\controllers\\advanced-report.controller.ts", "start": 237, "end": 251, "startLoc": {"line": 237, "column": 2, "position": 1868}, "endLoc": {"line": 251, "column": 7, "position": 1992}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 11, "end": 121, "startLoc": {"line": 11, "column": 2, "position": 77}, "endLoc": {"line": 121, "column": 9, "position": 957}}}, {"format": "typescript", "lines": 10, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const userId = req.user?.id;\n\n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n\n      const reports = await this.reportService.getSavedReports", "tokens": 0, "firstFile": {"name": "src\\controllers\\advanced-report.controller.ts", "start": 335, "end": 344, "startLoc": {"line": 335, "column": 2, "position": 2640}, "endLoc": {"line": 344, "column": 16, "position": 2746}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 11, "end": 192, "startLoc": {"line": 11, "column": 2, "position": 77}, "endLoc": {"line": 192, "column": 20, "position": 1537}}}, {"format": "typescript", "lines": 11, "fragment": "= async (req: Request, res: Response): Promise<void> => {\n    try {\n      const { id } = req.params;\n      const userId = req.user?.id;\n\n      if (!userId) {\n        res.status(401).json({ success: false, message: 'Unauthorized' });\n        return;\n      }\n\n      // Get the saved report", "tokens": 0, "firstFile": {"name": "src\\controllers\\advanced-report.controller.ts", "start": 411, "end": 421, "startLoc": {"line": 411, "column": 2, "position": 3245}, "endLoc": {"line": 421, "column": 24, "position": 3355}}, "secondFile": {"name": "src\\controllers\\dashboard.controller.ts", "start": 48, "end": 58, "startLoc": {"line": 48, "column": 2, "position": 354}, "endLoc": {"line": 58, "column": 6, "position": 466}}}, {"format": "javascript", "lines": 11, "fragment": ");\n    if (fs.existsSync(reportPath)) {\n      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));\n      \n      console.log('\\nDuplication Statistics:');\n      console.log(`- Duplication Percentage: ${report.statistics.total.percentage}%`);\n      console.log(`- Duplicated Lines: ${report.statistics.total.duplicatedLines} of ${report.statistics.total.lines}`);\n      console.log(`- Clones Found: ${report.duplicates.length}`);\n      \n      if (report.duplicates.length > 0) {\n        console.log('\\nTop Duplications:'", "tokens": 0, "firstFile": {"name": "scripts\\test-duplication.js", "start": 43, "end": 53, "startLoc": {"line": 43, "column": 20, "position": 264}, "endLoc": {"line": 53, "column": 22, "position": 404}}, "secondFile": {"name": "scripts\\test-new-code-duplication.js", "start": 72, "end": 82, "startLoc": {"line": 72, "column": 12, "position": 501}, "endLoc": {"line": 82, "column": 35, "position": 641}}}, {"format": "javascript", "lines": 6, "fragment": ".forEach((dup, index) => {\n          console.log(`\\n${index + 1}. Between ${dup.firstFile.name} and ${dup.secondFile.name}`);\n          console.log(`   First file: Lines ${dup.firstFile.start}-${dup.firstFile.end}`);\n          console.log(`   Second file: Lines ${dup.secondFile.start}-${dup.secondFile.end}`);\n          console.log(`   Size: ${dup.lines} lines, ${dup.tokens} tokens`);\n        }", "tokens": 0, "firstFile": {"name": "scripts\\test-duplication.js", "start": 56, "end": 61, "startLoc": {"line": 56, "column": 2, "position": 425}, "endLoc": {"line": 61, "column": 2, "position": 543}}, "secondFile": {"name": "scripts\\test-new-code-duplication.js", "start": 84, "end": 90, "startLoc": {"line": 84, "column": 11, "position": 651}, "endLoc": {"line": 90, "column": 41, "position": 771}}}, {"format": "javascript", "lines": 7, "fragment": "const lines = result.split('\\n');\n      const summaryLine = lines.find((line) => line.includes('Tests:'));\n\n      if (summaryLine) {\n        const matches = summaryLine.match(/(\\d+) passed.*?(\\d+) total/);\n        if (matches) {\n          this.results.integration", "tokens": 0, "firstFile": {"name": "scripts\\run-comprehensive-tests.js", "start": 234, "end": 240, "startLoc": {"line": 234, "column": 7, "position": 1584}, "endLoc": {"line": 240, "column": 12, "position": 1663}}, "secondFile": {"name": "scripts\\run-comprehensive-tests.js", "start": 187, "end": 193, "startLoc": {"line": 187, "column": 7, "position": 1172}, "endLoc": {"line": 193, "column": 5, "position": 1251}}}, {"format": "javascript", "lines": 11, "fragment": "encoding: 'utf8',\n      });\n\n      // Parse results\n      const lines = result.split('\\n');\n      const summaryLine = lines.find((line) => line.includes('Tests:'));\n\n      if (summaryLine) {\n        const matches = summaryLine.match(/(\\d+) passed.*?(\\d+) total/);\n        if (matches) {\n          this.results.performance", "tokens": 0, "firstFile": {"name": "scripts\\run-comprehensive-tests.js", "start": 278, "end": 288, "startLoc": {"line": 278, "column": 9, "position": 1983}, "endLoc": {"line": 288, "column": 12, "position": 2078}}, "secondFile": {"name": "scripts\\run-comprehensive-tests.js", "start": 230, "end": 193, "startLoc": {"line": 230, "column": 9, "position": 1568}, "endLoc": {"line": 193, "column": 5, "position": 1251}}}, {"format": "javascript", "lines": 8, "fragment": "{\n      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));\n      duplications = report.duplicates || [];\n      duplicationFound = duplications.length > 0;\n      \n      // Clean up temporary report\n      fs.rmSync('.jscpd-temp', { recursive: true, force: true });\n    } catch", "tokens": 0, "firstFile": {"name": "scripts\\fix-duplication.js", "start": 44, "end": 51, "startLoc": {"line": 44, "column": 2, "position": 316}, "endLoc": {"line": 51, "column": 6, "position": 403}}, "secondFile": {"name": "scripts\\fix-duplication.js", "start": 32, "end": 40, "startLoc": {"line": 32, "column": 2, "position": 181}, "endLoc": {"line": 40, "column": 2, "position": 268}}}], "filename": "F:\\Amazing pay flow\\node_modules\\@jscpd\\html-reporter\\dist\\templates\\main.pug"}