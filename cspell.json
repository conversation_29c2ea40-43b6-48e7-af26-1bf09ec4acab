{"version": "0.2", "language": "en", "words": ["Amazingpay", "jscpd", "tsconfig", "eslint", "sonarlint", "prisma", "jsonwebtoken", "middlewares", "nullish", "coalescing", "typeof", "instanceof", "readonly", "keyof", "infer", "typeof", "satisfies", "const", "enum", "namespace", "declare", "module", "global", "export", "import", "default", "async", "await", "promise", "unknown", "never", "void", "undefined", "null", "boolean", "number", "string", "object", "symbol", "bigint", "any"], "ignorePaths": ["node_modules/**", "dist/**", "coverage/**", "*.min.js", "*.map", "package-lock.json", "yarn.lock"], "ignoreRegExpList": ["/\\b[A-Z]{2,}\\b/g", "/\\b\\d+\\b/g"]}